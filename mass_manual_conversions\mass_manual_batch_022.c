// 大规模手工转换批次 22 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_78A0A
 * @note 指令数: 29
 */
void func_78a0a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003678 = (volatile uint32_t *)0x20003678;
    volatile uint32_t *addr_2000367C = (volatile uint32_t *)0x2000367C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_78A46
 * @note 指令数: 163
 */
void func_78a46(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003670 = (volatile uint32_t *)0x20003670;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_200036EC = (volatile uint32_t *)0x200036EC;
    volatile uint32_t *addr_20000170 = (volatile uint32_t *)0x20000170;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_78B94
 * @note 指令数: 40
 */
void func_78b94(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200036EC = (volatile uint32_t *)0x200036EC;
    volatile uint32_t *addr_20003688 = (volatile uint32_t *)0x20003688;
    volatile uint32_t *addr_20003732 = (volatile uint32_t *)0x20003732;
    volatile uint32_t *addr_20003684 = (volatile uint32_t *)0x20003684;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_78C1C
 * @note 指令数: 24
 */
void func_78c1c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8001800 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *addr_8001804 = (volatile uint32_t *)0x8001804;
    volatile uint32_t *addr_2000371B = (volatile uint32_t *)0x2000371B;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_78C4A
 * @note 指令数: 3
 */
uint8_t func_78c4a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000371B = (volatile uint32_t *)0x2000371B;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_78C5C
 * @note 指令数: 388
 */
void func_78c5c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_5A = (volatile uint32_t *)0x5A;
    volatile uint32_t *addr_8011B08 = (volatile uint32_t *)0x8011B08;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_79350
 * @note 指令数: 348
 */
void func_79350(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_200036D4 = (volatile uint32_t *)0x200036D4;
    volatile uint32_t *addr_3C0 = (volatile uint32_t *)0x3C0;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_796F4
 * @note 指令数: 43
 */
uint32_t func_796f4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200036D4 = (volatile uint32_t *)0x200036D4;
    volatile uint32_t *addr_200036DC = (volatile uint32_t *)0x200036DC;
    volatile uint32_t *addr_20003660 = (volatile uint32_t *)0x20003660;
    volatile uint32_t *addr_2000371A = (volatile uint32_t *)0x2000371A;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_79754
 * @note 指令数: 141
 */
void func_79754(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_60 = (volatile uint32_t *)0x60;
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;
    volatile uint32_t *addr_2000371D = (volatile uint32_t *)0x2000371D;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7988A
 * @note 指令数: 6
 */
uint32_t func_7988a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200036DC = (volatile uint32_t *)0x200036DC;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_798C8
 * @note 指令数: 144
 */
void func_798c8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20003660 = (volatile uint32_t *)0x20003660;
    volatile uint32_t *addr_2000371A = (volatile uint32_t *)0x2000371A;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_79A44
 * @note 指令数: 22
 */
void func_79a44(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8011548 = (volatile uint32_t *)0x8011548;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_79A72
 * @note 指令数: 31
 */
void func_79a72(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_8011548 = (volatile uint32_t *)0x8011548;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_80000 = (volatile uint32_t *)0x80000;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_79ABC
 * @note 指令数: 26
 */
void func_79abc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8011CC8 = (volatile uint32_t *)0x8011CC8;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_79AF4
 * @note 指令数: 12
 */
uint32_t func_79af4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20000 = (volatile uint32_t *)0x20000;
    volatile uint32_t *addr_8011CC8 = (volatile uint32_t *)0x8011CC8;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_8011CCC = (volatile uint32_t *)0x8011CCC;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_79B10
 * @note 指令数: 277
 */
void func_79b10(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_79DC0
 * @note 指令数: 22
 */
void func_79dc0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80113DC = (volatile uint32_t *)0x80113DC;
    volatile uint32_t *addr_80113E0 = (volatile uint32_t *)0x80113E0;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_79DFC
 * @note 指令数: 14
 */
void func_79dfc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_AF = (volatile uint32_t *)0xAF;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_AE = (volatile uint32_t *)0xAE;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_79E1C
 * @note 指令数: 35
 */
void func_79e1c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_34 = (volatile uint32_t *)0x34;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_79E70
 * @note 指令数: 43
 */
void func_79e70(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_4C = (volatile uint32_t *)0x4C;
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_79ED8
 * @note 指令数: 53
 */
uint32_t func_79ed8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8011730 = (volatile uint32_t *)0x8011730;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_79F54
 * @note 指令数: 68
 */
void func_79f54(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_48000414 = (volatile uint32_t *)0x48000414;
    volatile uint32_t *addr_48000014 = (volatile uint32_t *)0x48000014;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_FF7F = (volatile uint32_t *)0xFF7F;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_79FE2
 * @note 指令数: 71
 */
void func_79fe2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_48000414 = (volatile uint32_t *)0x48000414;
    volatile uint32_t *addr_48000014 = (volatile uint32_t *)0x48000014;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_FF7F = (volatile uint32_t *)0xFF7F;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7A090
 * @note 指令数: 32
 */
void func_7a090(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_75 = (volatile uint32_t *)0x75;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7A0E0
 * @note 指令数: 20
 */
uint32_t func_7a0e0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7A106
 * @note 指令数: 1
 */
void func_7a106(void)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7A108
 * @note 指令数: 10
 */
void func_7a108(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_38000000 = (volatile uint32_t *)0x38000000;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7A120
 * @note 指令数: 4
 */
uint32_t func_7a120(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7A13A
 * @note 指令数: 194
 */
void func_7a13a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7A2BE
 * @note 指令数: 46
 */
uint32_t func_7a2be(void)
{
    // 内存地址定义
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_380 = (volatile uint32_t *)0x380;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7A320
 * @note 指令数: 86
 */
uint32_t func_7a320(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_FE = (volatile uint32_t *)0xFE;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7A3D0
 * @note 指令数: 29
 */
uint32_t func_7a3d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7A40C
 * @note 指令数: 53
 */
uint32_t func_7a40c(void)
{
    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7A476
 * @note 指令数: 1
 */
void func_7a476(void)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7A478
 * @note 指令数: 23
 */
void func_7a478(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_20003702 = (volatile uint32_t *)0x20003702;
    volatile uint32_t *addr_20003700 = (volatile uint32_t *)0x20003700;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7A4A4
 * @note 指令数: 40
 */
void func_7a4a4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_C0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7A510
 * @note 指令数: 359
 */
void func_7a510(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_80123A8 = (volatile uint32_t *)0x80123A8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7A960
 * @note 指令数: 149
 */
void func_7a960(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_52 = (volatile uint32_t *)0x52;
    volatile uint32_t *addr_80116F0 = (volatile uint32_t *)0x80116F0;
    volatile uint32_t *addr_200033A4 = (volatile uint32_t *)0x200033A4;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7B2F8
 * @note 指令数: 30
 */
void func_7b2f8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000374B = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_80123C0 = (volatile uint32_t *)0x80123C0;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7B370
 * @note 指令数: 2
 */
void func_7b370(void)
{
    // 内存地址定义
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7B378
 * @note 指令数: 60
 */
uint32_t func_7b378(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_200033A4 = (volatile uint32_t *)0x200033A4;
    volatile uint32_t *addr_20003702 = (volatile uint32_t *)0x20003702;
    volatile uint32_t *addr_20003750 = (volatile uint32_t *)0x20003750;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7B440
 * @note 指令数: 329
 */
void func_7b440(void)
{
    // 内存地址定义
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7B6D4
 * @note 指令数: 5
 */
uint32_t func_7b6d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003538 = (volatile uint32_t *)0x20003538;
    volatile uint32_t *addr_20000130 = (volatile uint32_t *)0x20000130;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7B6F0
 * @note 指令数: 63
 */
uint32_t func_7b6f0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_200033A4 = (volatile uint32_t *)0x200033A4;
    volatile uint32_t *addr_20003702 = (volatile uint32_t *)0x20003702;
    volatile uint32_t *addr_20003750 = (volatile uint32_t *)0x20003750;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7B774
 * @note 指令数: 34
 */
uint32_t func_7b774(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003634 = (volatile uint32_t *)0x20003634;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_20000978 = (volatile uint32_t *)0x20000978;
    volatile uint32_t *addr_20003700 = (volatile uint32_t *)0x20003700;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7B7BA
 * @note 指令数: 34
 */
void func_7b7ba(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_2000374B = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *addr_20003706 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *addr_200036B4 = (volatile uint32_t *)0x200036B4;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7B7FE
 * @note 指令数: 9
 */
void func_7b7fe(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200033A4 = (volatile uint32_t *)0x200033A4;
    volatile uint32_t *addr_E1 = (volatile uint32_t *)0xE1;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7B812
 * @note 指令数: 73
 */
uint32_t func_7b812(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_20000978 = (volatile uint32_t *)0x20000978;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7B8CC
 * @note 指令数: 93
 */
void func_7b8cc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003634 = (volatile uint32_t *)0x20003634;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_20003704 = (volatile uint32_t *)0x20003704;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7B9B4
 * @note 指令数: 158
 */
void func_7b9b4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003634 = (volatile uint32_t *)0x20003634;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_20003753 = (volatile uint32_t *)0x20003753;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

