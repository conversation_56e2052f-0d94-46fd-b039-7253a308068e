/**
 * @file web_server.h
 * @brief Web服务器功能头文件 - 从汇编代码分析转换
 * @date 2024
 * 
 * 基于汇编代码分析的Web页面生成和HTTP服务器功能
 * 包含设备信息页面、配置页面等Web界面
 */

#ifndef WEB_SERVER_H
#define WEB_SERVER_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>

// =============================================================================
// Web服务器常量定义 (从汇编代码字符串分析)
// =============================================================================

// 公司和产品信息
#define COMPANY_NAME            "Shenzhen MEKi"
#define PRODUCT_SERIES          "KXM Series"
#define PRODUCT_MODEL           "KXM-16P"
#define COMPANY_WEBSITE         "http://www.mek-i.com"

// Web页面相关常量
#define MAX_HTTP_RESPONSE_SIZE  2048
#define MAX_URL_LENGTH          256
#define MAX_PARAM_LENGTH        128

// HTTP状态码
#define HTTP_OK                 200
#define HTTP_NOT_FOUND          404
#define HTTP_BAD_REQUEST        400
#define HTTP_INTERNAL_ERROR     500

// Web页面类型 (从汇编代码分析的页面ID)
#define PAGE_TYPE_MAIN          0x00    // 主页面
#define PAGE_TYPE_INFO          0x01    // 设备信息页面
#define PAGE_TYPE_CONFIG        0x02    // 配置页面
#define PAGE_TYPE_NETWORK       0x03    // 网络设置页面
#define PAGE_TYPE_SYSTEM        0x04    // 系统信息页面
#define PAGE_TYPE_FIRMWARE      0x05    // 固件信息页面
#define PAGE_TYPE_STATUS        0x06    // 状态页面

// 特殊页面ID (从汇编代码0x80-0x82分析)
#define PAGE_TYPE_PARAM1        0x80    // 参数页面1
#define PAGE_TYPE_PARAM2        0x81    // 参数页面2
#define PAGE_TYPE_PARAM3        0x82    // 参数页面3

// =============================================================================
// Web页面数据结构
// =============================================================================

// HTTP请求结构
typedef struct {
    char method[8];                     // GET, POST等
    char url[MAX_URL_LENGTH];           // 请求URL
    char params[MAX_PARAM_LENGTH];      // 参数字符串
    uint16_t content_length;            // 内容长度
    uint8_t page_type;                  // 页面类型
    uint8_t param_id;                   // 参数ID
} http_request_t;

// HTTP响应结构
typedef struct {
    uint16_t status_code;               // HTTP状态码
    char content_type[32];              // 内容类型
    uint16_t content_length;            // 内容长度
    uint8_t* content;                   // 响应内容
} http_response_t;

// Web页面生成器状态 (从汇编代码sub_800D7E0分析)
typedef struct {
    uint8_t state;                      // 当前状态
    uint8_t page_type;                  // 页面类型
    uint8_t section_type;               // 段落类型
    uint8_t param_index;                // 参数索引
    uint8_t field_count;                // 字段计数
    uint16_t buffer_index;              // 缓冲区索引
    uint8_t* output_buffer;             // 输出缓冲区
} web_page_generator_t;

// 设备信息结构 (用于Web页面显示)
typedef struct {
    char company_name[32];              // 公司名称
    char product_series[32];            // 产品系列
    char product_model[32];             // 产品型号
    char firmware_version[16];          // 固件版本
    char build_date[16];                // 构建日期
    char build_time[16];                // 构建时间
    char website_url[64];               // 公司网站
    uint32_t device_id;                 // 设备ID
    uint32_t serial_number;             // 序列号
} device_info_t;

// 网络配置结构
typedef struct {
    uint8_t ip_address[4];              // IP地址
    uint8_t subnet_mask[4];             // 子网掩码
    uint8_t gateway[4];                 // 网关
    uint8_t dns_server[4];              // DNS服务器
    uint8_t mac_address[6];             // MAC地址
    uint16_t http_port;                 // HTTP端口
    bool dhcp_enabled;                  // DHCP启用状态
} network_config_t;

// =============================================================================
// 函数原型声明
// =============================================================================

// Web服务器核心函数
void web_server_init(void);
void web_server_process(void);
bool web_server_handle_request(const uint8_t* request_data, uint16_t request_length);

// HTTP请求处理
bool parse_http_request(const uint8_t* data, uint16_t length, http_request_t* request);
void generate_http_response(const http_request_t* request, http_response_t* response);
void send_http_response(const http_response_t* response);

// Web页面生成函数 (从汇编代码sub_800D7E0等函数转换)
uint16_t generate_web_page(uint8_t page_type, uint8_t param_id, uint8_t* buffer, uint16_t buffer_size);
uint16_t generate_main_page(uint8_t* buffer, uint16_t buffer_size);
uint16_t generate_device_info_page(uint8_t* buffer, uint16_t buffer_size);
uint16_t generate_config_page(uint8_t* buffer, uint16_t buffer_size);
uint16_t generate_network_page(uint8_t* buffer, uint16_t buffer_size);
uint16_t generate_system_page(uint8_t* buffer, uint16_t buffer_size);
uint16_t generate_firmware_page(uint8_t* buffer, uint16_t buffer_size);

// 页面内容生成辅助函数 (从汇编代码分析)
uint16_t add_html_header(uint8_t* buffer, uint16_t offset, const char* title);
uint16_t add_html_footer(uint8_t* buffer, uint16_t offset);
uint16_t add_navigation_menu(uint8_t* buffer, uint16_t offset);
uint16_t add_device_info_section(uint8_t* buffer, uint16_t offset);
uint16_t add_network_info_section(uint8_t* buffer, uint16_t offset);
uint16_t add_system_status_section(uint8_t* buffer, uint16_t offset);

// 数据格式化函数
uint16_t format_ip_address(uint8_t* buffer, const uint8_t* ip);
uint16_t format_mac_address(uint8_t* buffer, const uint8_t* mac);
uint16_t format_device_info(uint8_t* buffer, const device_info_t* info);
uint16_t format_network_config(uint8_t* buffer, const network_config_t* config);

// 参数处理函数 (从汇编代码0x3E8, 0x3EB等参数分析)
void process_config_parameter(uint16_t param_id, const char* value);
const char* get_config_parameter(uint16_t param_id);
void save_config_parameters(void);
void load_config_parameters(void);

// 设备信息获取函数
void get_device_info(device_info_t* info);
void get_network_config(network_config_t* config);
void set_network_config(const network_config_t* config);

// Web页面模板函数
const char* get_html_template(uint8_t page_type);
uint16_t substitute_template_variables(uint8_t* buffer, uint16_t buffer_size, 
                                      const char* template_str, const device_info_t* info);

// 工具函数
uint16_t html_encode_string(uint8_t* output, const char* input, uint16_t max_length);
bool validate_ip_address(const char* ip_str);
bool validate_mac_address(const char* mac_str);
uint16_t url_decode(char* output, const char* input, uint16_t max_length);

// =============================================================================
// 全局变量声明
// =============================================================================

extern device_info_t g_device_info;
extern network_config_t g_network_config;
extern web_page_generator_t g_web_generator;

// =============================================================================
// HTML模板常量 (从汇编代码分析的Web页面结构)
// =============================================================================

// HTML页面头部模板
#define HTML_HEADER_TEMPLATE \
    "<!DOCTYPE html>\r\n" \
    "<html>\r\n" \
    "<head>\r\n" \
    "<meta charset=\"UTF-8\">\r\n" \
    "<title>%s - %s</title>\r\n" \
    "<style>\r\n" \
    "body { font-family: Arial, sans-serif; margin: 20px; }\r\n" \
    ".header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }\r\n" \
    ".content { margin: 20px 0; }\r\n" \
    ".footer { text-align: center; color: #666; font-size: 12px; }\r\n" \
    "table { border-collapse: collapse; width: 100%; }\r\n" \
    "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\r\n" \
    "th { background-color: #f2f2f2; }\r\n" \
    "</style>\r\n" \
    "</head>\r\n" \
    "<body>\r\n"

// HTML页面尾部模板
#define HTML_FOOTER_TEMPLATE \
    "<div class=\"footer\">\r\n" \
    "<p>&copy; 2024 %s. All rights reserved.</p>\r\n" \
    "<p>Website: <a href=\"%s\">%s</a></p>\r\n" \
    "</div>\r\n" \
    "</body>\r\n" \
    "</html>\r\n"

// 导航菜单模板
#define NAVIGATION_MENU_TEMPLATE \
    "<div class=\"header\">\r\n" \
    "<h1>%s %s</h1>\r\n" \
    "<nav>\r\n" \
    "<a href=\"/\">主页</a> | \r\n" \
    "<a href=\"/info\">设备信息</a> | \r\n" \
    "<a href=\"/config\">配置</a> | \r\n" \
    "<a href=\"/network\">网络设置</a> | \r\n" \
    "<a href=\"/system\">系统状态</a>\r\n" \
    "</nav>\r\n" \
    "</div>\r\n"

#endif // WEB_SERVER_H
