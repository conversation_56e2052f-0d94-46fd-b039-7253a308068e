@echo off
echo ========================================
echo Keil v5 ARM编译器诊断和修复
echo ========================================
echo.

set KEIL_PATH=C:\Keil_v5

echo 🔍 诊断Keil v5安装...
echo.

REM 检查Keil安装目录
echo [1/5] 检查Keil安装目录...
if exist "%KEIL_PATH%" (
    echo ✅ Keil v5安装目录存在: %KEIL_PATH%
) else (
    echo ❌ Keil v5安装目录不存在: %KEIL_PATH%
    goto :error
)

echo.

REM 检查ARM编译器目录
echo [2/5] 检查ARM编译器安装...
set ARMCC_FOUND=0

if exist "%KEIL_PATH%\ARM\ARMCC" (
    echo ✅ 找到ARMCC目录: %KEIL_PATH%\ARM\ARMCC
    set ARMCC_FOUND=1
    dir "%KEIL_PATH%\ARM\ARMCC" /b
)

if exist "%KEIL_PATH%\ARM\ARMCLANG" (
    echo ✅ 找到ARMCLANG目录: %KEIL_PATH%\ARM\ARMCLANG
    dir "%KEIL_PATH%\ARM\ARMCLANG" /b
)

if exist "%KEIL_PATH%\ARMCC" (
    echo ✅ 找到ARMCC目录: %KEIL_PATH%\ARMCC
    set ARMCC_FOUND=1
    dir "%KEIL_PATH%\ARMCC" /b
)

if %ARMCC_FOUND% equ 0 (
    echo ❌ 未找到ARM编译器安装
    echo.
    echo 可能的编译器位置:
    echo   %KEIL_PATH%\ARM\ARMCC
    echo   %KEIL_PATH%\ARM\ARMCLANG
    echo   %KEIL_PATH%\ARMCC
)

echo.

REM 检查编译器可执行文件
echo [3/5] 检查编译器可执行文件...

for %%d in (
    "%KEIL_PATH%\ARM\ARMCC\bin"
    "%KEIL_PATH%\ARM\ARMCLANG\bin"
    "%KEIL_PATH%\ARMCC\bin"
) do (
    if exist "%%d\armcc.exe" (
        echo ✅ 找到armcc.exe: %%d\armcc.exe
        "%%d\armcc.exe" --version_number 2>nul || echo   版本检查失败
    )
    if exist "%%d\armclang.exe" (
        echo ✅ 找到armclang.exe: %%d\armclang.exe
        "%%d\armclang.exe" --version 2>nul || echo   版本检查失败
    )
)

echo.

REM 检查器件包
echo [4/5] 检查AT32器件包...
if exist "%KEIL_PATH%\ARM\Pack\ArteryTek" (
    echo ✅ 找到ArteryTek器件包
    dir "%KEIL_PATH%\ARM\Pack\ArteryTek" /b
) else (
    echo ❌ 未找到AT32器件包
    echo 需要安装ArteryTek.AT32A403A_DFP器件包
)

echo.

REM 检查项目配置
echo [5/5] 检查项目配置...
if exist "keil\at32f403avg_firmware.uvprojx" (
    echo ✅ 找到项目文件
    echo 检查项目配置...
    findstr /i "toolset" "keil\at32f403avg_firmware.uvprojx" 2>nul || echo 无法读取工具链配置
) else (
    echo ❌ 项目文件不存在
)

echo.
echo ========================================
echo 🔧 修复建议
echo ========================================
echo.

if %ARMCC_FOUND% equ 0 (
    echo ❌ 主要问题: ARM编译器未安装
    echo.
    echo 🎯 解决方案1: 安装ARM编译器
    echo.
    echo 1. 下载ARM编译器:
    echo    - ARM Compiler 5.06 (推荐)
    echo    - ARM Compiler 6.x (较新版本)
    echo.
    echo 2. 安装位置应为:
    echo    %KEIL_PATH%\ARM\ARMCC\
    echo.
    echo 3. 或者重新安装完整的Keil MDK
    echo.
    echo 🎯 解决方案2: 使用在线安装
    echo.
    echo 1. 打开Keil Pack Installer
    echo 2. 在线下载ARM编译器
    echo 3. 安装到默认位置
    echo.
) else (
    echo ✅ ARM编译器已安装
    echo.
    echo 🎯 可能的问题: 项目配置
    echo.
    echo 修复步骤:
    echo 1. 在Keil中打开项目
    echo 2. 右键项目 → Options for Target
    echo 3. Target选项卡 → 选择正确的编译器版本
    echo 4. 确保选择了已安装的编译器版本
)

echo.
echo ========================================
echo 🚀 快速修复方法
echo ========================================
echo.

echo 选择修复方法:
echo [1] 打开Keil项目设置 (手动修复)
echo [2] 创建简化项目配置
echo [3] 下载ARM编译器
echo [4] 检查Pack Manager
echo [5] 退出
echo.

set /p choice=请选择 (1-5): 

if "%choice%"=="1" (
    echo.
    echo 🚀 打开Keil项目设置...
    start "" "C:\Keil_v5\UV4\UV4.exe" "keil\at32f403avg_firmware.uvprojx"
    echo.
    echo 📋 手动修复步骤:
    echo 1. 等待Keil加载项目
    echo 2. 右键项目名称 → "Options for Target"
    echo 3. 在"Target"选项卡中:
    echo    - 检查"ARM Compiler"下拉菜单
    echo    - 选择可用的编译器版本
    echo    - 如果没有可用版本，需要安装编译器
    echo 4. 点击OK保存设置
    echo 5. 重新编译项目 (F7)
    
) else if "%choice%"=="2" (
    echo.
    echo 🔧 创建简化项目配置...
    echo 这将创建一个使用默认编译器的项目配置
    echo.
    REM 这里可以添加创建简化配置的代码
    echo 功能开发中...
    
) else if "%choice%"=="3" (
    echo.
    echo 🌐 打开ARM编译器下载页面...
    start "" "https://developer.arm.com/downloads/-/arm-compiler-for-embedded"
    echo.
    echo 📋 下载和安装步骤:
    echo 1. 下载ARM Compiler 5.06或6.x
    echo 2. 运行安装程序
    echo 3. 选择安装到Keil目录
    echo 4. 重启Keil并重新编译
    
) else if "%choice%"=="4" (
    echo.
    echo 📦 打开Pack Manager...
    start "" "C:\Keil_v5\UV4\PackInstaller.exe"
    echo.
    echo 📋 Pack Manager操作:
    echo 1. 搜索"ArteryTek"
    echo 2. 安装AT32A403A_DFP器件包
    echo 3. 检查ARM编译器是否可用
    echo 4. 安装缺失的组件
    
) else if "%choice%"=="5" (
    echo 退出修复脚本
    goto :end
    
) else (
    echo ❌ 无效选择
)

goto :end

:error
echo.
echo ❌ 诊断失败，请检查Keil v5安装
goto :end

:end
echo.
echo ========================================
echo 诊断完成
echo ========================================
pause
