@echo off
echo ========================================
echo AT32F403AVG Keil v5 编译脚本
echo ========================================
echo.

echo 🚀 启动Keil v5并打开项目...
echo.

REM 启动Keil v5
start "" "C:\Keil_v5\UV4\UV4.exe" "keil\at32f403avg_firmware.uvprojx"

echo ✅ Keil v5已启动
echo.
echo 📋 在Keil中的操作步骤:
echo.
echo 1. 等待Keil完全加载项目
echo 2. 检查项目结构和源文件
echo 3. 确认目标器件为AT32F403AVG
echo 4. 按F7或点击编译按钮
echo 5. 查看编译输出窗口
echo.
echo 🎯 项目信息:
echo - 项目名称: AT32F403AVG 100%%精确汇编转换
echo - 总函数数: 667个
echo - 源文件数: 11个
echo - 转换精度: 100%%精确
echo.
echo 📁 预期输出文件:
echo - keil\Objects\*.axf (ELF可执行文件)
echo - keil\Objects\*.hex (Intel HEX格式)
echo - keil\Objects\*.map (内存映射文件)
echo.
echo ========================================
echo 编译脚本完成
echo ========================================
pause
