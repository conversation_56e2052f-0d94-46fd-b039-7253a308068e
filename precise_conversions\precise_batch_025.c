// 精确转换批次 25 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_802BC
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_802bc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R12, LR
    // ADDS    R2, R1, R1
    // 算术运算
    // BCS     loc_802CE
    // BL      sub_802E4
    // 调用函数: sub_802E4();
    // BPL     loc_802CC
    // ASRS    R0, R0, #0x1E
    // LSRS    R0, R0, #1
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_802E0
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_802e0(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADDS    R2, R1, R1
    // 算术运算
    // BCS     loc_8030A
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_802E4
 * @note 指令数: 15, 标签数: 0
 */
uint32_t precise_func_802e4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x400;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LSRS    R0, R0, #0x15
    // LSLS    R1, R1, #0xB
    // ORRS    R0, R1
    // MOVS    R1, #0x80000000
    // R1 = 0x80000000;
    // ORRS    R0, R1
    // LSRS    R2, R2, #0x15
    // MOVS    R1, #0x400
    // R1 = 0x400;
    // SUBS    R2, R2, R1
    // 算术运算
    // ADDS    R2, R2, #1
    // 算术运算
    // BMI     loc_8030A
    // NEGS    R2, R2
    // ADDS    R2, #0x1F
    // 算术运算
    // BMI     loc_80306
    // LSRS    R0, R2
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8030E
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_8030e(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_8030E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80310
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_80310(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // BPL     sub_80328
    // MOV     R12, LR
    // NEGS    R0, R0
    // BL      sub_80328
    // 调用函数: sub_80328();
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ORRS    R1, R2
    // BX      R12
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80324
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_80324(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // NOP
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80328
 * @note 指令数: 7, 标签数: 1
 */
void precise_func_80328(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x41D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0x41D
    // R2 = 0x41D;
    // TST     R0, R0
    // 比较操作
    // BEQ     locret_80342
    // 条件跳转
    // BMI     loc_8033A
    // SUBS    R2, R2, #1
    // 算术运算
    // ADDS    R0, R0, R0
    // 算术运算
    // BPL     loc_80334
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_803FE
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_803fe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // MOVS    R5, #0x80000000
    // R5 = 0x80000000;
    // MOVS    R7, R1
    // EORS    R7, R3
    // BMI     loc_803E8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_804EC
 * @note 指令数: 33, 标签数: 0
 */
void precise_func_804ec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // MOVS    R6, R1
    // EORS    R6, R3
    // MOVS    R5, #0x80000000
    // R5 = 0x80000000;
    // ANDS    R6, R5
    // MOV     R12, R6
    // LSRS    R6, R5, #0xA
    // ADDS    R7, R3, R3
    // 算术运算
    // ADDS    R4, R1, R1
    // 算术运算
    // CMN     R4, R6
    // 比较操作
    // BCS     loc_8057C
    // CMN     R7, R6
    // 比较操作
    // BCS     loc_8058C
    // LSRS    R4, R4, #0x15
    // BEQ     loc_80594
    // 条件跳转
    // LSRS    R7, R7, #0x15
    // BEQ     loc_80582
    // 条件跳转
    // SUBS    R4, R4, R7
    // 算术运算
    // LSLS    R1, R1, #0xB
    // LSLS    R3, R3, #0xB
    // ORRS    R1, R5
    // ORRS    R3, R5
    // LSRS    R7, R1, #0xB
    // LSRS    R3, R3, #0xB
    // MOVS    R6, R0
    // SUBS    R6, R6, R2
    // 算术运算
    // SBCS    R7, R3
    // BCS     loc_80530
    // SUBS    R4, R4, #1
    // 算术运算
    // ADDS    R6, R6, R6
    // 算术运算
    // ADCS    R7, R7
    // ADDS    R6, R6, R2
    // 算术运算
    // ADCS    R7, R3
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_805AE
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_805ae(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R2-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R5, R2
    // MOVS    R0, R6
    // MOVS    R1, R5
    // BL      sub_76398
    // 调用函数: sub_76398();
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MULS    R5, R0
    // SUBS    R0, R6, R5
    // 算术运算
    // STR     R0, [SP,#0x18+var_14]
    // 内存存储操作
    // MOV     R0, SP
    // LDM     R0, {R0,R1}
    // STM     R4!, {R0,R1}
    // POP     {R1,R2,R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_805CE
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_805ce(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_805CE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_805D0
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_805d0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R6, R0
    // MOVS    R7, R2
    // MOVS    R0, #1
    // R0 = 1;
    // NOP
    // MOV     R1, SP
    // STRH    R0, [R1,#0x20+var_20]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8062A
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_8062a(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8066C
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_8066c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80119B8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LSLS    R5, R4, #0x1C
    // LSRS    R5, R5, #0x1C
    // LDR     R0, =0x80119B8
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MULS    R1, R5
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0x2C]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_80690
    // 条件跳转
    // ASRS    R0, R4, #8
    // LDR     R1, =0x80119B8
    // 内存加载操作
    // MOVS    R2, #0x30 ; '0'
    // R2 = 0x30;
    // MULS    R2, R5
    // ADDS    R1, R1, R2
    // 算术运算
    // LDR     R1, [R1,#0x2C]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80698
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_80698(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R0, R5
    // BL      sub_80718
    // 调用函数: sub_80718();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_806AE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // B       locret_806CA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_806CC
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_806cc(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // BL      sub_80704
    // 调用函数: sub_80704();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_806E0
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // B       locret_80702
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80704
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_80704(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // LDRH    R0, [R1,#4]
    // 内存加载操作
    // LDRH    R2, [R1,#6]
    // 内存加载操作
    // CMP     R0, R2
    // 比较操作
    // BNE     loc_80714
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_80716
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80718
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_80718(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // LDRH    R0, [R1,#4]
    // 内存加载操作
    // MOVS    R2, R0
    // ADDS    R2, R2, #1
    // 算术运算
    // LDRH    R0, [R1,#8]
    // 内存加载操作
    // UXTH    R2, R2
    // 数据扩展操作
    // CMP     R2, R0
    // 比较操作
    // BNE     loc_8072E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R2, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8073E
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_8073e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDRH    R1, [R0,#4]
    // 内存加载操作
    // LDRH    R2, [R0,#6]
    // 内存加载操作
    // CMP     R1, R2
    // 比较操作
    // BCC     loc_80752
    // LDRH    R1, [R0,#4]
    // 内存加载操作
    // LDRH    R0, [R0,#6]
    // 内存加载操作
    // SUBS    R0, R1, R0
    // 算术运算
    // UXTH    R0, R0
    // 数据扩展操作
    // B       locret_8075E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80760
 * @note 指令数: 25, 标签数: 0
 */
void precise_func_80760(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021004;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // LDRB    R0, [R4]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_80814
    // LDR     R0, =0x40021004
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0xC
    // R1 = 0xC;
    // ANDS    R1, R0
    // CMP     R1, #4
    // 比较操作
    // BEQ     loc_80796
    // 条件跳转
    // LDR     R0, =0x40021004
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0xC
    // R1 = 0xC;
    // ANDS    R1, R0
    // CMP     R1, #8
    // 比较操作
    // BNE     loc_807A8
    // 条件跳转
    // LDR     R0, =0x40021004
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x18000
    // R1 = 0x18000;
    // ANDS    R1, R0
    // MOVS    R0, #0x10000
    // R0 = 0x10000;
    // CMP     R1, R0
    // 比较操作
    // BNE     loc_807A8
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80B84
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_80b84(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R0, =0x40022000
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // LSLS    R1, R1, #0x1F
    // LSRS    R1, R1, #0x1F
    // CMP     R1, R4
    // 比较操作
    // BCS     loc_80C82
    // LDR     R0, =0x40022000
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // BICS    R0, R1
    // ORRS    R0, R4
    // LDR     R1, =0x40022000
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x40022000
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // LSLS    R1, R1, #0x1F
    // LSRS    R1, R1, #0x1F
    // CMP     R1, R4
    // 比较操作
    // BEQ     loc_80BBA
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_80D82
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80D98
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_80d98(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R7, #0
    // R7 = 0;
    // LDR     R0, =0x40021004
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R4, R0
    // MOVS    R0, #0xC
    // R0 = 0xC;
    // ANDS    R0, R4
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_80DBA
    // 条件跳转
    // CMP     R0, #8
    // 比较操作
    // BEQ     loc_80DC0
    // 条件跳转
    // B       loc_80E06
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80E34
 * @note 指令数: 7, 标签数: 0
 */
uint32_t precise_func_80e34(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R2, R2
    // 数据扩展操作
    // SUBS    R2, #0x20 ; ' '
    // 算术运算
    // BCC     loc_80E42
    // MOVS    R0, R1
    // LSRS    R0, R2
    // MOVS    R1, #0
    // R1 = 0;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80E52
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_80e52(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_80E52
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80E54
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_80e54(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3E8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // BL      sub_7FED0
    // 调用函数: sub_7FED0();
    // MOVS    R1, #0x3E8
    // R1 = 0x3E8;
    // BL      sub_7639E
    // 调用函数: sub_7639E();
    // BL      sub_7FEAA
    // 调用函数: sub_7FEAA();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, R4
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // BL      sub_7FE88
    // 调用函数: sub_7FE88();
    // MOVS    R0, #0
    // R0 = 0;
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80E78
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_80e78(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036D0;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x200036D0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81784
 * @note 指令数: 29, 标签数: 0
 */
void precise_func_81784(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000359A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x22;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C200;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000148;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036C0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R1, =0x2000359A
    // 内存加载操作
    // MOVS    R0, #0x1C200
    // R0 = 0x1C200;
    // BL      sub_78930
    // 调用函数: sub_78930();
    // MOVS    R0, #8
    // R0 = 8;
    // LDR     R1, =0x20003598
    // 内存加载操作
    // STRB    R0, [R1,#6]
    // 内存存储操作
    // MOVS    R0, #2
    // R0 = 2;
    // LDR     R1, =0x20003598
    // 内存加载操作
    // STRB    R0, [R1,#7]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20003598
    // 内存加载操作
    // STRB    R0, [R1,#8]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003598
    // 内存加载操作
    // STRB    R0, [R1,#1]
    // 内存存储操作
    // MOVS    R0, #0x22 ; '"'
    // R0 = 0x22;
    // LDR     R1, =0x20003598
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #3
    // R0 = 3;
    // LDR     R1, =0x20003598
    // 内存加载操作
    // STRB    R0, [R1,#9]
    // 内存存储操作
    // LDR     R2, =0x20003598
    // 内存加载操作
    // LDR     R1, =0x20000148
    // 内存加载操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_7F778
    // 调用函数: sub_7F778();
    // LDR     R1, =0x200036C0
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_817C4
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_817c4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003755;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x20003755
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     locret_817D8
    // 条件跳转
    // LDR     R0, =0x20003755
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x20003755
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_817DA
 * @note 指令数: 42, 标签数: 0
 */
void precise_func_817da(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003644;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000364C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003758;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003757;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000370C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20003759
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_8183A
    // 条件跳转
    // BL      sub_81784
    // 调用函数: sub_81784();
    // LDR     R1, =0x80107E1
    // 内存加载操作
    // LDR     R0, =0x200036C0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7FDA8
    // 调用函数: sub_7FDA8();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x2000363C
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // LDR     R0, =0x8010431
    // 内存加载操作
    // BL      sub_789C8
    // 调用函数: sub_789C8();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20003644
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x2000364C
    // 内存加载操作
    // BL      sub_78A0A
    // 调用函数: sub_78A0A();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003758
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003757
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003755
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003756
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000370A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000370C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20003759
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8183C
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_8183c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
    // arg_4=  4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_818FC
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_818fc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003759;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // LDR     R0, =0x20003759
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_8190A
    // 条件跳转
    // B       locret_81A40
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81A64
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_81a64(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000370C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // ADDS    R0, R0, #4
    // 算术运算
    // BL      sub_81CD8
    // 调用函数: sub_81CD8();
    // MOVS    R5, R0
    // LDRB    R0, [R4]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_81A88
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000370C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_81ABA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81AC4
 * @note 指令数: 36, 标签数: 0
 */
void precise_func_81ac4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003708;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000016A;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003644;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003756;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R6, R1
    // SUBS    R1, R6, #2
    // 算术运算
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R5
    // BL      sub_81CD8
    // 调用函数: sub_81CD8();
    // MOVS    R7, R0
    // UXTH    R6, R6
    // 数据扩展操作
    // ADDS    R0, R5, R6
    // 算术运算
    // SUBS    R0, R0, #2
    // 算术运算
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R4, R0
    // UXTH    R4, R4
    // 数据扩展操作
    // UXTH    R6, R6
    // 数据扩展操作
    // ADDS    R0, R5, R6
    // 算术运算
    // SUBS    R0, R0, #1
    // 算术运算
    // LDRB    R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // ADDS    R4, R4, R0
    // 算术运算
    // UXTH    R7, R7
    // 数据扩展操作
    // UXTH    R4, R4
    // 数据扩展操作
    // CMP     R7, R4
    // 比较操作
    // BEQ     loc_81B0E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003708
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003756
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000016A
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20003644
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_81B52
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81B74
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_81b74(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #1
    // 比较操作
    // BEQ     loc_81B82
    // 条件跳转
    // B       loc_81C8E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81CD8
 * @note 指令数: 24, 标签数: 1
 */
void precise_func_81cd8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011234;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011134;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // MOVS    R2, R0
    // MOVS    R0, #0xFF
    // R0 = 0xFF;
    // MOVS    R4, #0xFF
    // R4 = 0xFF;
    // MOVS    R5, R1
    // SUBS    R1, R5, #1
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_81D08
    // 条件跳转
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRB    R5, [R2]
    // 内存加载操作
    // EORS    R5, R4
    // MOVS    R3, R5
    // ADDS    R2, R2, #1
    // 算术运算
    // LDR     R5, =0x8011134
    // 内存加载操作
    // UXTH    R3, R3
    // 数据扩展操作
    // LDRB    R5, [R5,R3]
    // 内存加载操作
    // EORS    R5, R0
    // MOVS    R4, R5
    // LDR     R5, =0x8011234
    // 内存加载操作
    // UXTH    R3, R3
    // 数据扩展操作
    // LDRB    R5, [R5,R3]
    // 内存加载操作
    // MOVS    R0, R5
    // B       loc_81CE0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81D1C
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_81d1c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003712;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #5
    // R0 = 5;
    // LDR     R1, =0x20003712
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81D24
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_81d24(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_F= -0xF
    // var_A= -0xA
    // var_9= -9
    // var_8= -8
    // var_7= -7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81D90
 * @note 指令数: 34, 标签数: 0
 */
void precise_func_81d90(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x66;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8010B11;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003710;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000375D;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003654;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_81DE2
    // 条件跳转
    // BL      sub_81D24
    // 调用函数: sub_81D24();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x200036C8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7F87A
    // 调用函数: sub_7F87A();
    // LDR     R1, =0x8010B11
    // 内存加载操作
    // LDR     R0, =0x200036C8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7FDA8
    // 调用函数: sub_7FDA8();
    // LDR     R0, =0x8010BB9
    // 内存加载操作
    // BL      sub_789C2
    // 调用函数: sub_789C2();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20003654
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000375A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000375D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003716
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003710
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x66 ; 'f'
    // R0 = 0x66;
    // LDR     R1, =0x20003714
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BL      sub_81D1C
    // 调用函数: sub_81D1C();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81DE4
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_81de4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036C4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036C8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003710;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003654;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_81EA2
    // 条件跳转
    // LDR     R0, =0x200036C8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_8066C
    // 调用函数: sub_8066C();
    // LDR     R0, =0x20003710
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_81E28
    // 条件跳转
    // LDR     R0, =0x20003654
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_81E26
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R0, =0x20003710
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // NEGS    R2, R0
    // LDR     R0, =0x200036C4
    // 内存加载操作
    // LDR     R1, [R0]
    // 内存加载操作
    // LDR     R0, =0x200036C8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_8062A
    // 调用函数: sub_8062A();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003710
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // B       loc_81E28
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81EA4
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_81ea4(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003710;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200031BC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003716;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000375D;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000375A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_81F34
    // 条件跳转
    // LDR     R2, =0x20003710
    // 内存加载操作
    // LDRH    R2, [R2]
    // 内存加载操作
    // CMP     R2, #0
    // 比较操作
    // BNE     locret_81F4A
    // 条件跳转
    // LDR     R2, =0x2000375D
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // CMP     R2, #0
    // 比较操作
    // BNE     loc_81ED4
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R3, =0x2000375D
    // 内存加载操作
    // STRB    R2, [R3]
    // 内存存储操作
    // LDR     R2, =0x200031BC
    // 内存加载操作
    // STRB    R1, [R2]
    // 内存存储操作
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R3, =0x20003716
    // 内存加载操作
    // STRH    R2, [R3]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x2000375A
    // 内存加载操作
    // STRB    R2, [R3]
    // 内存存储操作
    // B       locret_81F4A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81F4C
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_81f4c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000375A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x2000375A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     locret_81F60
    // 条件跳转
    // LDR     R0, =0x2000375A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x2000375A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_81F90
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_81f90(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000375D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R7, R1
    // UXTH    R7, R7
    // 数据扩展操作
    // CMP     R7, #4
    // 比较操作
    // BGE     loc_81FA6
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000375D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_82048
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82054
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_82054(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_81CD8
    // 调用函数: sub_81CD8();
    // MOVS    R5, R0
    // LDRB    R0, [R4]
    // 内存加载操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_820BC
 * @note 指令数: 8, 标签数: 0
 */
uint32_t precise_func_820bc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // LDRB    R0, [R2]
    // 内存加载操作
    // MOVS    R3, #0x80
    // R3 = 0x80;
    // ORRS    R3, R0
    // STRB    R3, [R2]
    // 内存存储操作
    // STRB    R1, [R2,#1]
    // 内存存储操作
    // MOVS    R0, #2
    // R0 = 2;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_820CC
 * @note 指令数: 25, 标签数: 0
 */
void precise_func_820cc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R1-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R5, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R5, R5, R0
    // 算术运算
    // LDRB    R0, [R4,#3]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R7, R0
    // LDRB    R0, [R4,#4]
    // 内存加载操作
    // ADDS    R7, R7, R0
    // 算术运算
    // MOV     R0, SP
    // LDRH    R0, [R0,#0x20+var_18]
    // 内存加载操作
    // UXTH    R5, R5
    // 数据扩展操作
    // UXTH    R7, R7
    // 数据扩展操作
    // ADDS    R1, R5, R7
    // 算术运算
    // CMP     R0, R1
    // 比较操作
    // BGE     loc_820FC
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R4
    // BL      sub_820BC
    // 调用函数: sub_820BC();
    // B       locret_82174
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82176
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_82176(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011C3C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R6, R1
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R4, R0
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // ADDS    R4, R4, R0
    // 算术运算
    // MOVS    R3, #7
    // R3 = 7;
    // LDR     R2, =0x8011C3C
    // 内存加载操作
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R5
    // BL      sub_820CC
    // 调用函数: sub_820CC();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82196
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_82196(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R7, R2
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R5, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R5, R5, R0
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #8
    // 比较操作
    // BLT     loc_821B8
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R4
    // BL      sub_820BC
    // 调用函数: sub_820BC();
    // B       locret_821FE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82200
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_82200(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH    {R0-R2,R4-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // LDRB    R0, [R6,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R5, R0
    // LDRB    R0, [R6,#2]
    // 内存加载操作
    // ADDS    R5, R5, R0
    // 算术运算
    // LDRB    R0, [R6,#3]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R7, R0
    // LDRB    R0, [R6,#4]
    // 内存加载操作
    // ADDS    R7, R7, R0
    // 算术运算
    // MOV     R0, SP
    // STRH    R5, [R0,#0x20+var_20]
    // 内存存储操作
    // UXTH    R5, R5
    // 数据扩展操作
    // UXTH    R7, R7
    // 数据扩展操作
    // ADDS    R0, R5, R7
    // 算术运算
    // CMP     R0, #8
    // 比较操作
    // BLT     loc_82230
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R6
    // BL      sub_820BC
    // 调用函数: sub_820BC();
    // B       locret_822A8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_822AA
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_822aa(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDRB    R0, [R4]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BEQ     loc_822E2
    // 条件跳转
    // CMP     R0, #6
    // 比较操作
    // BEQ     loc_822C2
    // 条件跳转
    // CMP     R0, #0x10
    // 比较操作
    // BEQ     loc_822D2
    // 条件跳转
    // B       loc_822EE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82308
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_82308(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // BL      sub_79ABC
    // 调用函数: sub_79ABC();
    // MOVS    R0, #1
    // R0 = 1;
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

