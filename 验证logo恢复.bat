@echo off
echo ========================================
echo AT32F403AVG Logo恢复验证脚本
echo ========================================
echo.

echo 1. 检查汇编文件中的logo数据...
findstr /n "646F0E60" keil\AT32F403AVG-FLASH-J201.asm
if %errorlevel% == 0 (
    echo ✅ 第40137行数据已恢复
) else (
    echo ❌ 第40137行数据未找到
)

findstr /n "435064F4" keil\AT32F403AVG-FLASH-J201.asm
if %errorlevel% == 0 (
    echo ✅ 第40144行数据已恢复
) else (
    echo ❌ 第40144行数据未找到
)

echo.
echo 2. 检查Magic Number常量...
findstr /n "AA55AA55" keil\AT32F403AVG-FLASH-J201.asm
if %errorlevel% == 0 (
    echo ✅ Magic Number常量存在
) else (
    echo ❌ Magic Number常量缺失
)

echo.
echo 3. 验证文件完整性...
for %%f in (keil\AT32F403AVG-FLASH-J201.asm) do echo 文件大小: %%~zf 字节

echo.
echo 4. 检查关键函数...
findstr /n "sub_8000454" keil\AT32F403AVG-FLASH-J201.asm | head -1
if %errorlevel% == 0 (
    echo ✅ Magic Number验证函数存在
) else (
    echo ❌ Magic Number验证函数缺失
)

echo.
echo ========================================
echo 验证完成！
echo ========================================
echo.
echo 如果所有项目都显示✅，说明logo数据已成功恢复到原始状态
echo 现在可以尝试重新编译项目
echo.
pause
