@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   AT32F403AVG Keil项目启动器
echo ========================================
echo.
echo 正在启动Keil MDK项目...
echo 项目文件: keil\at32f403avg_firmware.uvprojx
echo.

REM 检查Keil项目文件是否存在
if not exist "keil\at32f403avg_firmware.uvprojx" (
    echo ❌ 错误: 找不到Keil项目文件!
    echo 请确保在正确的目录中运行此脚本。
    echo.
    pause
    exit /b 1
)

REM 尝试启动Keil项目
echo 🚀 启动Keil MDK...
start "" "keil\at32f403avg_firmware.uvprojx"

if %errorlevel% equ 0 (
    echo ✅ Keil项目已成功启动!
    echo.
    echo 📋 项目信息:
    echo    - 目标芯片: AT32F403AVG7
    echo    - 编译器: ARM Compiler v5/v6
    echo    - 项目类型: 引导加载程序固件
    echo    - 语言: C语言 (从汇编转换)
    echo.
    echo 💡 使用提示:
    echo    1. 按F7编译项目
    echo    2. 按F8下载程序
    echo    3. 按F5开始调试
    echo    4. 查看README_KEIL_PROJECT.md获取详细说明
    echo.
) else (
    echo ❌ 启动Keil项目失败!
    echo 可能的原因:
    echo    1. 未安装Keil MDK
    echo    2. 文件关联问题
    echo    3. 权限不足
    echo.
    echo 💡 解决方案:
    echo    1. 手动打开Keil MDK
    echo    2. 在Keil中打开项目文件: keil\at32f403avg_firmware.uvprojx
    echo.
)

echo 按任意键退出...
pause >nul
