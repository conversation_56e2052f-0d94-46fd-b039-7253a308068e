Dependencies for Project 'at32f403avg_conversion', Target 'AT32F403AVG_Conversion': (DO NOT MODIFY !)
F (..\src\exact_core_functions.c)(0x68419CB8)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\exact_core_functions.o --omf_browse .\objects\exact_core_functions.crf --depend .\objects\exact_core_functions.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\system_management_functions.c)(0x68419DC3)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\system_management_functions.o --omf_browse .\objects\system_management_functions.crf --depend .\objects\system_management_functions.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\main_application_loop.c)(0x68419E15)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\main_application_loop.o --omf_browse .\objects\main_application_loop.crf --depend .\objects\main_application_loop.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\interrupt_service_routines.c)(0x68419E4B)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\interrupt_service_routines.o --omf_browse .\objects\interrupt_service_routines.crf --depend .\objects\interrupt_service_routines.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\system_initialization.c)(0x6841A129)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\system_initialization.o --omf_browse .\objects\system_initialization.crf --depend .\objects\system_initialization.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\application_functions.c)(0x6841A5AE)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\application_functions.o --omf_browse .\objects\application_functions.crf --depend .\objects\application_functions.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\batch_conversion_functions.c)(0x6841A5E5)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\batch_conversion_functions.o --omf_browse .\objects\batch_conversion_functions.crf --depend .\objects\batch_conversion_functions.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\default_interrupt_handlers.c)(0x6841A56A)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\default_interrupt_handlers.o --omf_browse .\objects\default_interrupt_handlers.crf --depend .\objects\default_interrupt_handlers.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\mass_conversion_generator.c)(0x6841A62A)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\mass_conversion_generator.o --omf_browse .\objects\mass_conversion_generator.crf --depend .\objects\mass_conversion_generator.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\final_conversion_completion.c)(0x6841A68A)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\final_conversion_completion.o --omf_browse .\objects\final_conversion_completion.crf --depend .\objects\final_conversion_completion.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\startup_at32f403avg.c)(0x6841A1A2)(--c99 -c --cpu Cortex-M4.fp -g -O0 --apcs=interwork --split_sections -I..\src --c99

-I D:\work\20240708_at32f403avg_zhou_v0.1.0\20240708_at32f403avg_zhou_v0.1.0\keil5\RTE

-I C:\Keil_v5\ARM\PACK\ArteryTek\AT32A403A_DFP\2.0.5\Device\Include

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DAT32A403AVGT7 -DUSE_STDPERIPH_DRIVER -DAT32F403AVG

-o .\objects\startup_at32f403avg.o --omf_browse .\objects\startup_at32f403avg.crf --depend .\objects\startup_at32f403avg.d)
I (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x599EDB3E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x599EDB3E)
F (..\src\at32f403avg_assembly_conversion.h)(0x68419C00)()
F (..\src\at32f403avg.sct)(0x684172DD)()
