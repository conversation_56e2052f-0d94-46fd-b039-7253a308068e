// 精确转换批次 6 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CFB8
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_1cfb8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000817D;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000817D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CFF4
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_1cff4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000817D;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // LDR.W   R1, =0x2000817D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CFFE
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_1cffe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200080EE;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #5
    // R0 = 5;
    // LDR.W   R1, =0x200080EE
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D008
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_1d008(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008169;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007CD8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x32;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2580;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_E= -0xE
    // PUSH    {R5-R7,LR}
    // 栈操作
    // MOV     R0, SP
    // BL      sub_1C15A
    // 调用函数: sub_1C15A();
    // MOV     R2, SP
    // LDR.W   R1, =0x20007CD8
    // 内存加载操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_185B8
    // 调用函数: sub_185B8();
    // LDR.W   R1, =0x20008078
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // ADD.W   R1, SP, #0x10+var_E
    // LDR     R0, [R1]
    // 内存加载操作
    // CMP.W   R0, #0x2580
    // BNE     loc_1D040
    // 条件跳转
    // MOVS    R0, #0x1E
    // R0 = 0x1E;
    // LDR.W   R1, =0x20008168
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x32 ; '2'
    // R0 = 0x32;
    // LDR.W   R1, =0x20008169
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_1D050
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D052
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_1d052(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008166;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008078;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007F58;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_1D008
    // 调用函数: sub_1D008();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R0, =0x20008078
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_186FE
    // 调用函数: sub_186FE();
    // ADR.W   R1, (sub_1D220+1)
    // LDR.W   R0, =0x20008078
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_18C26
    // 调用函数: sub_18C26();
    // BL      sub_1CFA8
    // 调用函数: sub_1CFA8();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1D08E
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R0, =0x20007F58
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008166
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       loc_1D096
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D102
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_1d102(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008166;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008078;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007F58;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // LDR.W   R0, =0x20008078
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_19104
    // 调用函数: sub_19104();
    // LDR.W   R0, =0x20007F58
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1D13A
    // 条件跳转
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_1933A
    // 调用函数: sub_1933A();
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_1933A
    // 调用函数: sub_1933A();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_1933A
    // 调用函数: sub_1933A();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008166
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x64 ; 'd'
    // R0 = 0x64;
    // LDR.W   R1, =0x20007F58
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D220
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_1d220(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200068D4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080EC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000816A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200080F0;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20008167;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #1
    // 比较操作
    // BNE     loc_1D2C4
    // 条件跳转
    // LDR     R0, =0x200080EC
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_1D30E
    // 条件跳转
    // LDR     R0, =0x2000816A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1D254
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000816A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200068D4
    // 内存加载操作
    // STRB    R5, [R0]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200080F0
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20008167
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       loc_1D2B0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D310
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1d310(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008167;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20008167
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     locret_1D326
    // 条件跳转
    // CPSID   I
    // LDR     R0, =0x20008167
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x20008167
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // CPSIE   I
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D328
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1d328(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000816A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #4
    // 比较操作
    // BGE     loc_1D340
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000816A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_1D46E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D472
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_1d472(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200080EC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R1, R5
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_21C4C
    // 调用函数: sub_21C4C();
    // MOVS    R6, R0
    // LDRB    R0, [R4]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1D494
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200080EC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_1D4B0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D500
 * @note 指令数: 16, 标签数: 1
 */
void precise_func_1d500(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20001270;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20A;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20A0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
    // CMP     R5, #0x10
    // 比较操作
    // BGE     loc_1D52A
    // 条件跳转
    // LDR.W   R0, =0x20001270
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVW    R1, #0x20A0
    // R1 = 0x20A0;
    // MLA.W   R0, R1, R4, R0
    // MOVW    R1, #0x20A
    // R1 = 0x20A;
    // MLA.W   R0, R1, R5, R0
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0]
    // 内存存储操作
    // ADDS    R5, R5, #1
    // 算术运算
    // B       loc_1D508
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D672
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_1d672(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x12;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_17= -0x17
    // var_16= -0x16
    // var_12= -0x12
    // var_11= -0x11
    // var_10= -0x10
    // var_F= -0xF
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D74E
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_1d74e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008088;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008128;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x7F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_1D672
    // 调用函数: sub_1D672();
    // ADR.W   R1, (sub_1D98C+1)
    // LDR.W   R0, =0x20008088
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDR.W   R0, [R0,R4,LSL#2]
    // 内存加载操作
    // BL      sub_18C26
    // 调用函数: sub_18C26();
    // ADR.W   R1, (sub_1DA48+1)
    // LDR.W   R0, =0x20008088
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDR.W   R0, [R0,R4,LSL#2]
    // 内存加载操作
    // BL      sub_186FE
    // 调用函数: sub_186FE();
    // LDR.W   R0, =0x20008128
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ANDS.W  R0, R0, #0x7F
    // LDR.W   R1, =0x20008128
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20008128
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1D7A0
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR.W   R1, =0x20008128
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D80A
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_1d80a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008172;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007F80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // LDR.W   R0, =0x20007F80
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1D83E
    // 条件跳转
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_1933A
    // 调用函数: sub_1933A();
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_1933A
    // 调用函数: sub_1933A();
    // LDR.W   R0, =0x20008172
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1D836
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_1933A
    // 调用函数: sub_1933A();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D938
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_1d938(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000808C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008173;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // MOVS    R1, R0
    // LDRB.W  R2, [R1,#0x40]
    // MOVS    R3, #1
    // R3 = 1;
    // LDR.W   R4, =0x20008173
    // 内存加载操作
    // UXTB    R2, R2
    // 数据扩展操作
    // STRB    R3, [R2,R4]
    // 内存存储操作
    // LDR.W   R3, =0x2000808C
    // 内存加载操作
    // UXTB    R2, R2
    // 数据扩展操作
    // LDR.W   R3, [R3,R2,LSL#2]
    // 内存加载操作
    // MOVS    R0, R3
    // MOVS    R3, #0
    // R3 = 0;
    // LDR.W   R4, =0x20008173
    // 内存加载操作
    // UXTB    R2, R2
    // 数据扩展操作
    // STRB    R3, [R2,R4]
    // 内存存储操作
    // POP     {R4}
    // 栈操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D964
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_1d964(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000808C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008173;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDRB.W  R1, [R0,#0x40]
    // MOVS    R2, #1
    // R2 = 1;
    // LDR.W   R3, =0x20008173
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // STRB    R2, [R1,R3]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR.W   R3, =0x2000808C
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // STR.W   R2, [R3,R1,LSL#2]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR.W   R3, =0x20008173
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // STRB    R2, [R1,R3]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D98C
 * @note 指令数: 36, 标签数: 0
 */
void precise_func_1d98c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007280;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008172;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #1
    // 比较操作
    // BNE     loc_1D9EC
    // 条件跳转
    // LDR.W   R0, =0x20007280
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R1, #0x44 ; 'D'
    // R1 = 0x44;
    // MLA.W   R0, R1, R6, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // ORRS.W  R0, R0, #0x20000
    // LDR.W   R1, =0x20007280
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R2, #0x44 ; 'D'
    // R2 = 0x44;
    // MLA.W   R1, R2, R6, R1
    // STR     R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20007280
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R1, #0x44 ; 'D'
    // R1 = 0x44;
    // MLA.W   R0, R1, R6, R0
    // STRB    R5, [R0,#3]
    // 内存存储操作
    // LDR.W   R0, =0x20007280
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R1, #0x44 ; 'D'
    // R1 = 0x44;
    // MLA.W   R0, R1, R6, R0
    // BL      sub_21FB0
    // 调用函数: sub_21FB0();
    // MOVS    R2, #1
    // R2 = 1;
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_192D0
    // 调用函数: sub_192D0();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008172
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_1DA46
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DA48
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1da48(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007280;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080F8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x44;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x200080F8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRH.W  R0, [R1,R4,LSL#1]
    // 内存存储操作
    // LDR.W   R0, =0x20007280
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x44 ; 'D'
    // R1 = 0x44;
    // MLA.W   R0, R1, R4, R0
    // BL      sub_1D964
    // 调用函数: sub_1D964();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DA6A
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_1da6a(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
    // PUSH.W  {R3-R11,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // LDR.W   R8, [SP,#0x28+arg_0]
    // 内存加载操作
    // MOVS.W  R10, #4
    // MOVS.W  R11, #0
    // MOV     R2, R11
    // MOV     R1, R10
    // MOV     R0, R8
    // BL      sub_17CB8
    // 调用函数: sub_17CB8();
    // STRB.W  R6, [R8,#0xB]
    // MOVS    R0, #1
    // R0 = 1;
    // STRB.W  R0, [R8,#0xA]
    // UXTB    R7, R7
    // 数据扩展操作
    // CMP     R7, #0
    // 比较操作
    // BEQ     loc_1DAA2
    // 条件跳转
    // ADDS.W  R2, R8, #0xA
    // 算术运算
    // B       loc_1DAA4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DC0C
 * @note 指令数: 33, 标签数: 0
 */
void precise_func_1dc0c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007DCC;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // LDRB.W  R6, [R4,#0x40]
    // LDR.W   R0, =0x20007DCC
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R6, R0
    // BL      sub_21E6A
    // 调用函数: sub_21E6A();
    // MOVS    R5, R0
    // LDRH    R0, [R4,#8]
    // 内存加载操作
    // STRH    R0, [R5,#4]
    // 内存存储操作
    // LDRB    R0, [R4,#0xC]
    // 内存加载操作
    // STRB    R0, [R5,#1]
    // 内存存储操作
    // LDRB.W  R0, [R4,#0x28]
    // STRB    R0, [R5,#2]
    // 内存存储操作
    // LDRB    R0, [R4,#0xA]
    // 内存加载操作
    // STRB    R0, [R5,#3]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // STRB    R0, [R5]
    // 内存存储操作
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // CMP     R0, #5
    // 比较操作
    // BNE     loc_1DC5C
    // 条件跳转
    // LDR.W   R0, =0x20007BFC
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // ADDS.W  R0, R0, R6,LSL#5
    // 算术运算
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R3, #1
    // R3 = 1;
    // LDRB    R2, [R5,#2]
    // 内存加载操作
    // LDRH    R1, [R5,#4]
    // 内存加载操作
    // ADDS    R0, R5, #6
    // 算术运算
    // BL      sub_1DA6A
    // 调用函数: sub_1DA6A();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DCA8
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_1dca8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007DCC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x20007DCC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R4, R0
    // BL      sub_21E40
    // 调用函数: sub_21E40();
    // MOVS    R5, R0
    // LDRB    R0, [R5]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_1DCD6
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R0, =0x20007DCC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0x14
    // R2 = 0x14;
    // MLA.W   R0, R2, R4, R0
    // BL      sub_21E96
    // 调用函数: sub_21E96();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DCD8
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_1dcd8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007DCC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x20007DCC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R4, R0
    // BL      sub_21E40
    // 调用函数: sub_21E40();
    // MOVS    R5, R0
    // LDRB    R0, [R5]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1DCF6
    // 条件跳转
    // MOVS    R0, R5
    // B       locret_1DCF8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DCFA
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_1dcfa(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007DB8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDR     R0, =0x20007DB8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R4, R0
    // BL      sub_21DEA
    // 调用函数: sub_21DEA();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1DD26
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20007DB8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0x14
    // R2 = 0x14;
    // MLA.W   R0, R2, R4, R0
    // BL      sub_21E96
    // 调用函数: sub_21E96();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DD4E
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_1dd4e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008090;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200080F8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x204;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000633C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // LDRB.W  R3, [R0,#0x40]
    // MOVS    R4, #1
    // R4 = 1;
    // LDR     R5, =0x200080F8
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // STRH.W  R4, [R5,R3,LSL#1]
    // 内存存储操作
    // LDR     R4, =0x200080FA
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // STRH.W  R2, [R4,R3,LSL#1]
    // 内存存储操作
    // LDR     R4, =0x2000633C
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // MOV.W   R5, #0x204
    // MLA.W   R4, R5, R3, R4
    // LDR     R5, =0x20008090
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // STR.W   R4, [R5,R3,LSL#2]
    // 内存存储操作
    // POP     {R4,R5}
    // 栈操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DD7E
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_1dd7e(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_2C= -0x2C
    // var_28= -0x28
    // arg_0=  0
    // arg_4=  4
    // arg_8=  8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DE9E
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_1de9e(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // arg_0=  0
    // arg_4=  4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DEEA
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1deea(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_39= -0x39
    // var_32= -0x32
    // var_20= -0x20
    // var_1F= -0x1F
    // var_1E= -0x1E
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DFEE
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_1dfee(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007DB8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // SUB     SP, SP, #0x10
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB.W  R8, [R4,#0x40]
    // LDR     R0, =0x20007DB8
    // 内存加载操作
    // UXTB.W  R8, R8
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R8, R0
    // BL      sub_21E40
    // 调用函数: sub_21E40();
    // MOVS    R6, R0
    // CMP     R6, #0
    // 比较操作
    // BNE     loc_1E016
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_1E048
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1E0A0
 * @note 指令数: 55, 标签数: 1
 */
float precise_func_1e0a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007AFC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007B1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007ABC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007A9C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200076C4;

    // 局部变量
    float result = 0.0f;
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #8
    // 比较操作
    // BGE     loc_1E14A
    // 条件跳转
    // LDR.W   R0, =0x20007A7C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0
    // R1 = 0;
    // STR.W   R1, [R0,R4,LSL#2]
    // 内存存储操作
    // BL      sub_21D60
    // 调用函数: sub_21D60();
    // LDR.W   R0, =0x20007A9C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FSTS    S0, [R0]
    // 浮点数内存操作
    // BL      sub_21D60
    // 调用函数: sub_21D60();
    // LDR.W   R0, =0x20007ABC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FSTS    S0, [R0]
    // 浮点数内存操作
    // LDR.W   R0, =0x20007ADC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0
    // R1 = 0;
    // STR.W   R1, [R0,R4,LSL#2]
    // 内存存储操作
    // LDR.W   R0, =0x20007AFC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDR.W   R1, =0x42C80000
    // 内存加载操作
    // STR.W   R1, [R0,R4,LSL#2]
    // 内存存储操作
    // LDR.W   R0, =0x20007B1C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDR.W   R1, =0x42C80000
    // 内存加载操作
    // STR.W   R1, [R0,R4,LSL#2]
    // 内存存储操作
    // LDR.W   R0, =0x20007B3C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0
    // R1 = 0;
    // STR.W   R1, [R0,R4,LSL#2]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20007E10
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRH.W  R0, [R1,R4,LSL#1]
    // 内存存储操作
    // LDR.W   R0, =0x20007BDC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDR.W   R1, =0x3DCCCCCD
    // 内存加载操作
    // STR.W   R1, [R0,R4,LSL#2]
    // 内存存储操作
    // MOVS    R0, #0x62 ; 'b'
    // R0 = 0x62;
    // LDR.W   R1, =0x200076C4
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R1, R1, R4,LSL#3
    // 算术运算
    // STRH    R0, [R1,#4]
    // 内存存储操作
    // LDR.W   R0, =0x200076C4
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDR.W   R1, =0x3DCCCCCD
    // 内存加载操作
    // STR.W   R1, [R0,R4,LSL#3]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_1E0A6
    // 无条件跳转

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1E18C
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_1e18c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // BL      sub_22F7C
    // 调用函数: sub_22F7C();
    // BL      sub_1E0A0
    // 调用函数: sub_1E0A0();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1E240
 * @note 指令数: 53, 标签数: 0
 */
float precise_func_1e240(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007AFC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007A9C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20006540;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    float result = 0.0f;
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // VPUSH   {D8}
    // MOVS    R4, R0
    // FLDS    S17, =0.0
    // 浮点数内存操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, #8
    // R0 = 8;
    // SDIV.W  R0, R4, R0
    // MOVS    R6, R0
    // MOVS    R0, #1
    // R0 = 1;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R2, R4, R1
    // MLS.W   R2, R2, R1, R4
    // LSLS    R0, R2
    // MOVS    R5, R0
    // LDR.W   R0, =0x20007A9C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FLDS    S0, [R0]
    // 浮点数内存操作
    // BL      sub_22FB2
    // 调用函数: sub_22FB2();
    // CMP     R0, #1
    // 比较操作
    // BGE     loc_1E2FC
    // 条件跳转
    // LDR.W   R0, =0x20007A9C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FLDS    S0, [R0]
    // 浮点数内存操作
    // LDR.W   R0, =0x20007ADC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FLDS    S1, [R0]
    // 浮点数内存操作
    // FCMPS   S0, S1
    // VMRS    APSR_nzcv, FPSCR
    // BLT     loc_1E2E6
    // 条件跳转
    // LDR.W   R0, =0x20007AFC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FLDS    S0, [R0]
    // 浮点数内存操作
    // LDR.W   R0, =0x20007A9C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FLDS    S1, [R0]
    // 浮点数内存操作
    // FCMPS   S0, S1
    // VMRS    APSR_nzcv, FPSCR
    // BLT     loc_1E2E6
    // 条件跳转
    // LDR.W   R0, =0x20006540
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x40 ; '@'
    // R1 = 0x40;
    // MLA.W   R0, R1, R4, R0
    // LDR.W   R1, =0x20007A9C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDR.W   R1, [R1,R4,LSL#2]
    // 内存加载操作
    // STR     R1, [R0,#0x1C]
    // 内存存储操作
    // B       loc_1E310
    // 无条件跳转

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1E8A8
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_1e8a8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000813E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008140;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007EB8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000813F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // LDR.W   R0, =0x20008140
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R0, R0, R1
    // MOVS    R5, R0
    // MOVS    R0, #1
    // R0 = 1;
    // LDR.W   R1, =0x20008140
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // MOVS    R2, #8
    // R2 = 8;
    // SDIV.W  R3, R1, R2
    // MLS.W   R3, R3, R2, R1
    // LSLS    R0, R3
    // MOVS    R6, R0
    // LDR.W   R0, =0x20007EB8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE.W   locret_1EAE8
    // LDR.W   R0, =0x2000813F
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR.W   R1, =0x2000813E
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BEQ     loc_1E932
    // 条件跳转
    // MOVS    R0, #4
    // R0 = 4;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1EB6C
 * @note 指令数: 12, 标签数: 1
 */
void precise_func_1eb6c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     locret_1EB86
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_22F98
    // 调用函数: sub_22F98();
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_1EB72
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1EB98
 * @note 指令数: 19, 标签数: 1
 */
void precise_func_1eb98(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007D28;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007D40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007D58;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0xC
    // 比较操作
    // BGE     loc_1EBCA
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20007D58
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRH.W  R1, [R2,R0,LSL#1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20007D40
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRH.W  R1, [R2,R0,LSL#1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20007D28
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRH.W  R1, [R2,R0,LSL#1]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_1EB9C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1EC2A
 * @note 指令数: 13, 标签数: 1
 */
void precise_func_1ec2a(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // BL      sub_22FEC
    // 调用函数: sub_22FEC();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
    // UXTB    R7, R7
    // 数据扩展操作
    // CMP     R7, #4
    // 比较操作
    // BGE     loc_1EC48
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R7
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_23034
    // 调用函数: sub_23034();
    // ADDS    R7, R7, #1
    // 算术运算
    // B       loc_1EC34
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1ED8C
 * @note 指令数: 25, 标签数: 0
 */
void precise_func_1ed8c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200080CC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, #8
    // R0 = 8;
    // SDIV.W  R0, R4, R0
    // MOVS    R6, R0
    // MOVS    R0, #1
    // R0 = 1;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R2, R4, R1
    // MLS.W   R2, R2, R1, R4
    // LSLS    R0, R2
    // MOVS    R5, R0
    // LDR.W   R0, =0x200080CC
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // LDRB    R0, [R6,R0]
    // 内存加载操作
    // TST     R0, R5
    // 比较操作
    // BEQ     loc_1EDCA
    // 条件跳转
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     loc_1EDC8
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_23034
    // 调用函数: sub_23034();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1EF1E
 * @note 指令数: 35, 标签数: 0
 */
void precise_func_1ef1e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078F8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007D40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20006F94;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // MOVS    R1, R0
    // MOVS    R3, #0x10
    // R3 = 0x10;
    // MOVS    R0, #0
    // R0 = 0;
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R6, #8
    // R6 = 8;
    // SDIV.W  R6, R1, R6
    // MOVS    R5, R6
    // MOVS    R6, #1
    // R6 = 1;
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R7, #8
    // R7 = 8;
    // SDIV.W  R12, R1, R7
    // MLS.W   R12, R12, R7, R1
    // LSLS.W  R6, R6, R12
    // MOVS    R4, R6
    // LDR.W   R6, =0x20006F94
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // ADDS.W  R6, R6, R1,LSL#4
    // 算术运算
    // LDRB    R6, [R6,#5]
    // 内存加载操作
    // CMP     R6, #1
    // 比较操作
    // BNE     loc_1EF7C
    // 条件跳转
    // LDR.W   R6, =0x200078F8
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR.W   R6, [R6,R1,LSL#2]
    // 内存加载操作
    // LDR.W   R7, =0x20007D40
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // LDRH.W  R7, [R7,R1,LSL#1]
    // 内存加载操作
    // CMP     R6, R7
    // 比较操作
    // BCC     loc_1EFB2
    // MOVS    R6, #2
    // R6 = 2;
    // LDR.W   R7, =0x20006F94
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // ADDS.W  R7, R7, R1,LSL#4
    // 算术运算
    // STRB    R6, [R7,#5]
    // 内存存储操作
    // B       loc_1EFB2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F0A6
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1f0a6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008155;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008154;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // LDR.W   R0, =0x20008155
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR.W   R1, =0x20008154
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BEQ     loc_1F112
    // 条件跳转
    // MOVS    R0, #4
    // R0 = 4;
    // MOV     R9, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F550
 * @note 指令数: 12, 标签数: 1
 */
void precise_func_1f550(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     locret_1F56A
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_23034
    // 调用函数: sub_23034();
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_1F556
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F57C
 * @note 指令数: 7, 标签数: 1
 */
void precise_func_1f57c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000816C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_1C0EE
    // 调用函数: sub_1C0EE();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1F58E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x2000816C
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F598
 * @note 指令数: 7, 标签数: 0
 */
uint32_t precise_func_1f598(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // LDRB    R0, [R2]
    // 内存加载操作
    // ORRS.W  R0, R0, #0x80
    // STRB    R0, [R2]
    // 内存存储操作
    // STRB    R1, [R2,#1]
    // 内存存储操作
    // MOVS    R0, #2
    // R0 = 2;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F5A8
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_1f5a8(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, #0
    // R7 = 0;
    // MOVS.W  R8, #0
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOV     R9, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS.W  R9, R9, R0
    // 算术运算
    // LDRB    R0, [R4,#3]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     loc_1F5D0
    // 条件跳转
    // LDRB    R0, [R4,#3]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1F5D6
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F6C0
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_1f6c0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R0-R2,R4-R11,LR}
    // 栈操作
    // MOV     R11, R0
    // LDRB.W  R0, [R11,#1]
    // LSLS    R0, R0, #8
    // MOV     R10, R0
    // LDRB.W  R0, [R11,#2]
    // ADDS.W  R10, R10, R0
    // 算术运算
    // LDRB.W  R0, [R11,#3]
    // LSLS    R0, R0, #8
    // MOVS    R6, R0
    // LDRB.W  R0, [R11,#4]
    // ADDS    R6, R6, R0
    // 算术运算
    // UXTH    R6, R6
    // 数据扩展操作
    // CMP     R6, #0
    // 比较操作
    // BNE     loc_1F6EE
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F82C
 * @note 指令数: 23, 标签数: 0
 */
void precise_func_1f82c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // arg_0=  0
    // PUSH.W  {R1,R4-R11,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOV     R11, R2
    // MOVS    R5, R3
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R6, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R6, R6, R0
    // 算术运算
    // LDRB    R0, [R4,#3]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R7, R0
    // LDRB    R0, [R4,#4]
    // 内存加载操作
    // ADDS    R7, R7, R0
    // 算术运算
    // UXTH    R7, R7
    // 数据扩展操作
    // CMP     R7, #0
    // 比较操作
    // BNE     loc_1F856
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // MOVS    R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F90A
 * @note 指令数: 28, 标签数: 0
 */
void precise_func_1f90a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016938;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8008236;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFA00;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R6, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R6, R6, R0
    // 算术运算
    // UXTH    R6, R6
    // 数据扩展操作
    // CMP.W   R6, #0xFA00
    // BLT     loc_1F94A
    // 条件跳转
    // ADDS.W  R6, R6, #0x600
    // 算术运算
    // MOVS    R0, R6
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // STRB    R6, [R4,#2]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // LDR.W   R3, =0x8016938
    // 内存加载操作
    // LDR.W   R2, =0x8008236
    // 内存加载操作
    // MOVS    R1, R5
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_1F82C
    // 调用函数: sub_1F82C();
    // B       locret_1F992
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F994
 * @note 指令数: 29, 标签数: 0
 */
void precise_func_1f994(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80167E4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80167F0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x15A0;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xEA60;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R6, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R6, R6, R0
    // 算术运算
    // UXTH    R6, R6
    // 数据扩展操作
    // MOVW    R0, #0xEA60
    // R0 = 0xEA60;
    // CMP     R6, R0
    // 比较操作
    // BLT     loc_1F9D6
    // 条件跳转
    // ADDS.W  R6, R6, #0x15A0
    // 算术运算
    // MOVS    R0, R6
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // STRB    R6, [R4,#2]
    // 内存存储操作
    // MOVS    R0, #5
    // R0 = 5;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // LDR.W   R3, =0x80167F0
    // 内存加载操作
    // LDR.W   R2, =0x80167E4
    // 内存加载操作
    // MOVS    R1, R5
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_1F82C
    // 调用函数: sub_1F82C();
    // B       locret_1F9EC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1F9EE
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_1f9ee(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOV     R8, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS.W  R8, R8, R0
    // 算术运算
    // LDRB    R0, [R4,#3]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOV     R9, R0
    // LDRB    R0, [R4,#4]
    // 内存加载操作
    // ADDS.W  R9, R9, R0
    // 算术运算
    // UXTH    R7, R7
    // 数据扩展操作
    // UXTH.W  R9, R9
    // UXTAH.W R0, R9, R8
    // CMP     R7, R0
    // 比较操作
    // BGE     loc_1FA2A
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R4
    // BL      sub_1F598
    // 调用函数: sub_1F598();
    // B       locret_1FAA4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1FAA8
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_1faa8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015DAC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFA00;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x21;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R6, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R6, R6, R0
    // 算术运算
    // UXTH    R6, R6
    // 数据扩展操作
    // CMP.W   R6, #0xFA00
    // BLT     loc_1FAE0
    // 条件跳转
    // ADDS.W  R6, R6, #0x600
    // 算术运算
    // MOVS    R0, R6
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // STRB    R6, [R4,#2]
    // 内存存储操作
    // MOVS    R3, #0x21 ; '!'
    // R3 = 0x21;
    // LDR.W   R2, =0x8015DAC
    // 内存加载操作
    // MOVS    R1, R5
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_1F9EE
    // 调用函数: sub_1F9EE();
    // B       locret_1FB1C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1FB1E
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_1fb1e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80152C4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xB5;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xFA00;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R7, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R7, R7, R0
    // 算术运算
    // UXTH    R7, R7
    // 数据扩展操作
    // CMP.W   R7, #0xFA00
    // BLT     loc_1FB56
    // 条件跳转
    // ADDS.W  R7, R7, #0x600
    // 算术运算
    // MOVS    R0, R7
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // STRB    R7, [R4,#2]
    // 内存存储操作
    // MOVS    R3, #0xB5
    // R3 = 0xB5;
    // LDR.W   R2, =0x80152C4
    // 内存加载操作
    // MOVS    R1, R5
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_1F9EE
    // 调用函数: sub_1F9EE();
    // B       locret_1FBD8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1FBDA
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_1fbda(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFA00;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xB6;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R7, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R7, R7, R0
    // 算术运算
    // UXTH    R7, R7
    // 数据扩展操作
    // CMP.W   R7, #0xFA00
    // BLT     loc_1FC5E
    // 条件跳转
    // ADDS.W  R7, R7, #0x600
    // 算术运算
    // UXTH    R7, R7
    // 数据扩展操作
    // CMP     R7, #0xB6
    // 比较操作
    // BLT     loc_1FC08
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R4
    // BL      sub_1F598
    // 调用函数: sub_1F598();
    // B       locret_1FCCC
    // 无条件跳转
}

