// 精确转换批次 7 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1FCCE
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_1fcce(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFA00;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xB6;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOV     R8, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS.W  R8, R8, R0
    // 算术运算
    // LDRB    R0, [R4,#3]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOV     R10, R0
    // LDRB    R0, [R4,#4]
    // 内存加载操作
    // ADDS.W  R10, R10, R0
    // 算术运算
    // MOV     R9, R8
    // UXTH.W  R8, R8
    // CMP.W   R8, #0xFA00
    // BLT     loc_1FDCE
    // 条件跳转
    // ADDS.W  R8, R8, #0x600
    // 算术运算
    // UXTH.W  R10, R10
    // UXTAH.W R0, R10, R8
    // CMP     R0, #0xB6
    // 比较操作
    // BLT     loc_1FD16
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R4
    // BL      sub_1F598
    // 调用函数: sub_1F598();
    // B       locret_1FEC8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1FECC
 * @note 指令数: 60, 标签数: 0
 */
void precise_func_1fecc(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3EB;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000084;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xD1;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_D8= -0xD8
    // var_97= -0x97
    // var_56= -0x56
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0xE8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // ADDS    R6, R4, #1
    // 算术运算
    // LDR.W   R1, =0x80166B0
    // 内存加载操作
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #0x40 ; '@'
    // 算术运算
    // LDR.W   R0, =0x2000025C
    // 内存加载操作
    // LDRH    R2, [R0]
    // 内存加载操作
    // ADR     R1, dword_1FF6C
    // MOV     R0, SP
    // BL      sub_23140
    // 调用函数: sub_23140();
    // MOV     R1, SP
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #8
    // 算术运算
    // ADD     R0, SP, #0xF8+var_D8
    // 算术运算
    // BL      sub_23096
    // 调用函数: sub_23096();
    // ADD.W   R1, SP, #0xF8+var_56
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #8
    // 算术运算
    // LDR.W   R1, =0x80167D8
    // 内存加载操作
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #0x20 ; ' '
    // 算术运算
    // LDR.W   R1, =0x80168D4
    // 内存加载操作
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #0x20 ; ' '
    // 算术运算
    // LDR.W   R1, =0x20000084
    // 内存加载操作
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #0x20 ; ' '
    // 算术运算
    // MOV     R1, SP
    // MOV.W   R0, #0x3E8
    // BL      sub_21CFE
    // 调用函数: sub_21CFE();
    // MOV     R1, SP
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #8
    // 算术运算
    // MOV     R1, SP
    // MOVW    R0, #0x3EB
    // R0 = 0x3EB;
    // BL      sub_21CFE
    // 调用函数: sub_21CFE();
    // MOV     R1, SP
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #8
    // 算术运算
    // ADD.W   R1, SP, #0xF8+var_97
    // MOVS    R0, R6
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // ADDS    R6, #0x10
    // 算术运算
    // MOVS    R0, #0xD1
    // R0 = 0xD1;
    // ADD     SP, SP, #0xE8
    // 算术运算
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1FF74
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_1ff74(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x9F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x5E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xE0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_E0= -0xE0
    // var_9F= -0x9F
    // var_5E= -0x5E
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // SUB     SP, SP, #0xE8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // CMP     R0, #0xE
    // 比较操作
    // BNE     loc_1FF9C
    // 条件跳转
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_1FFB0
    // 条件跳转
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_1FFB0
    // 条件跳转
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BEQ     loc_1FFB0
    // 条件跳转
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_1FFB0
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_202E0
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_202e0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_2030A
    // 条件跳转
    // LDRB    R1, [R4,#3]
    // 内存加载操作
    // ADDS    R0, R4, #4
    // 算术运算
    // BL      sub_1C764
    // 调用函数: sub_1C764();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_202FE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#3]
    // 内存存储操作
    // B       loc_20302
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20344
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_20344(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R1, [R4,#1]
    // 内存加载操作
    // ADDS    R0, R4, #2
    // 算术运算
    // BL      sub_1C764
    // 调用函数: sub_1C764();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_2035C
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // B       loc_20360
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20364
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_20364(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R1, [R4,#1]
    // 内存加载操作
    // ADDS    R0, R4, #2
    // 算术运算
    // BL      sub_1C8C8
    // 调用函数: sub_1C8C8();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_2037C
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // B       loc_20380
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20384
 * @note 指令数: 35, 标签数: 0
 */
void precise_func_20384(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x26;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20001F00;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x25;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x37;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // CPSID   I
    // MOVS    R0, #0x25 ; '%'
    // R0 = 0x25;
    // BL      sub_18278
    // 调用函数: sub_18278();
    // MOVS    R0, #0x26 ; '&'
    // R0 = 0x26;
    // BL      sub_18278
    // 调用函数: sub_18278();
    // MOVS    R0, #0x27 ; '''
    // R0 = 0x27;
    // BL      sub_18278
    // 调用函数: sub_18278();
    // MOVS    R0, #0x4E ; 'N'
    // R0 = 0x4E;
    // BL      sub_18278
    // 调用函数: sub_18278();
    // MOVS    R0, #0x12
    // R0 = 0x12;
    // BL      sub_18278
    // 调用函数: sub_18278();
    // MOVS    R0, #0x1D
    // R0 = 0x1D;
    // BL      sub_18278
    // 调用函数: sub_18278();
    // MOVS    R0, #0x37 ; '7'
    // R0 = 0x37;
    // BL      sub_18278
    // 调用函数: sub_18278();
    // MOVS    R6, #4
    // R6 = 4;
    // LDR.W   R7, =0x80168DC
    // 内存加载操作
    // LDR.W   R8, =0x20001F00
    // 内存加载操作
    // MOVS    R2, R6
    // MOVS    R1, R7
    // MOV     R0, R8
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_203E6
    // 条件跳转
    // MOVS    R6, #4
    // R6 = 4;
    // LDR.W   R7, =0x80168E4
    // 内存加载操作
    // LDR.W   R8, =0x20001F50
    // 内存加载操作
    // MOVS    R2, R6
    // MOVS    R1, R7
    // MOV     R0, R8
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20400
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_20400(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x26;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008128;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x21;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_26= -0x26
    // var_21= -0x21
    // var_20= -0x20
    // PUSH.W  {R1-R9,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_2047A
    // 条件跳转
    // LDR.W   R0, =0x20008128
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // STRB    R0, [R4,#2]
    // 内存存储操作
    // MOV     R0, SP
    // BL      sub_1C15A
    // 调用函数: sub_1C15A();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20540
 * @note 指令数: 34, 标签数: 0
 */
void precise_func_20540(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x45;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x6E;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x70;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2B;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x41;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDRB    R0, [R4]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_205A4
    // 条件跳转
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_205B0
    // 条件跳转
    // CMP     R0, #3
    // 比较操作
    // BEQ     loc_205E8
    // 条件跳转
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_205BC
    // 条件跳转
    // CMP     R0, #5
    // 比较操作
    // BEQ     loc_20584
    // 条件跳转
    // CMP     R0, #6
    // 比较操作
    // BEQ     loc_205C8
    // 条件跳转
    // CMP     R0, #0xF
    // 比较操作
    // BEQ     loc_20594
    // 条件跳转
    // CMP     R0, #0x10
    // 比较操作
    // BEQ     loc_205D8
    // 条件跳转
    // CMP     R0, #0x2B ; '+'
    // 比较操作
    // BEQ     loc_205F4
    // 条件跳转
    // CMP     R0, #0x41 ; 'A'
    // 比较操作
    // BEQ     loc_20630
    // 条件跳转
    // CMP     R0, #0x43 ; 'C'
    // 比较操作
    // BEQ     loc_2060C
    // 条件跳转
    // CMP     R0, #0x45 ; 'E'
    // 比较操作
    // BEQ     loc_20618
    // 条件跳转
    // CMP     R0, #0x6E ; 'n'
    // 比较操作
    // BEQ     loc_20600
    // 条件跳转
    // CMP     R0, #0x70 ; 'p'
    // 比较操作
    // BEQ     loc_20624
    // 条件跳转
    // B       loc_2063C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20690
 * @note 指令数: 35, 标签数: 1
 */
void precise_func_20690(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000800C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008010;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007FF8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008008;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20008004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #4
    // 比较操作
    // BGE     loc_206E4
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20008010
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R0,R2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20007FF8
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R0,R2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20007FFC
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R0,R2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20008000
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R0,R2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20008004
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R0,R2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20008008
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R0,R2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x2000800C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R0,R2]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_20694
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20700
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_20700(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008153;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008151;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007F08;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // BL      sub_23174
    // 调用函数: sub_23174();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008153
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x20007F08
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008151
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_20690
    // 调用函数: sub_20690();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20804
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_20804(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007CBC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // UXTB    R2, R2
    // 数据扩展操作
    // CMP     R2, #0
    // 比较操作
    // BNE     loc_20886
    // 条件跳转
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_2084A
    // 条件跳转
    // LDR.W   R5, =0x20007CBC
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LDRSB   R5, [R0,R5]
    // CMP     R5, #0
    // 比较操作
    // BPL     loc_2082A
    // MOVS    R5, #1
    // R5 = 1;
    // LDR.W   R6, =0x20007CBC
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R5, [R0,R6]
    // 内存存储操作
    // B       loc_20ACC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20AD0
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_20ad0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000803C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R0, #8
    // R0 = 8;
    // SDIV.W  R0, R1, R0
    // MOVS    R3, R0
    // MOVS    R0, #1
    // R0 = 1;
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R4, #8
    // R4 = 8;
    // SDIV.W  R5, R1, R4
    // MLS.W   R5, R5, R4, R1
    // LSLS    R0, R5
    // MOVS    R2, R0
    // LDR.W   R0, =0x2000803C
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // LDRB    R0, [R3,R0]
    // 内存加载操作
    // TST     R0, R2
    // 比较操作
    // BEQ     loc_20B00
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_20B02
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20B06
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_20b06(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008034;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008018;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #8
    // R3 = 8;
    // SDIV.W  R3, R0, R3
    // MOVS    R2, R3
    // MOVS    R3, #1
    // R3 = 1;
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #8
    // R4 = 8;
    // SDIV.W  R5, R0, R4
    // MLS.W   R5, R5, R4, R0
    // LSLS    R3, R5
    // MOVS    R1, R3
    // LDR.W   R3, =0x20008034
    // 内存加载操作
    // UXTB    R2, R2
    // 数据扩展操作
    // LDRB    R3, [R2,R3]
    // 内存加载操作
    // TST     R3, R1
    // 比较操作
    // BEQ     loc_20B54
    // 条件跳转
    // LDR.W   R3, =0x20008018
    // 内存加载操作
    // UXTB    R2, R2
    // 数据扩展操作
    // LDRB    R3, [R2,R3]
    // 内存加载操作
    // BICS    R3, R1
    // LDR.W   R4, =0x20008018
    // 内存加载操作
    // UXTB    R2, R2
    // 数据扩展操作
    // STRB    R3, [R2,R4]
    // 内存存储操作
    // LDR.W   R3, =0x20008034
    // 内存加载操作
    // UXTB    R2, R2
    // 数据扩展操作
    // LDRB    R3, [R2,R3]
    // 内存加载操作
    // BICS    R3, R1
    // LDR.W   R4, =0x20008034
    // 内存加载操作
    // UXTB    R2, R2
    // 数据扩展操作
    // STRB    R3, [R2,R4]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20C80
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_20c80(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008153;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008151;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008150;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // LDR.W   R0, =0x20008153
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R0, R0, R1
    // MOVS    R4, R0
    // MOVS    R0, #1
    // R0 = 1;
    // LDR.W   R1, =0x20008153
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // MOVS    R2, #8
    // R2 = 8;
    // SDIV.W  R3, R1, R2
    // MLS.W   R3, R3, R2, R1
    // LSLS    R0, R3
    // MOVS    R5, R0
    // LDR.W   R0, =0x20008151
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR.W   R1, =0x20008150
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BEQ     loc_20D00
    // 条件跳转
    // MOVS    R0, #8
    // R0 = 8;
    // MOVS    R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20F72
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_20f72(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008152;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R1, =0x20008152
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_20F7A
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_20f7a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008152;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R0, =0x20008152
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21004
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_21004(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000817B;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR.W   R0, =0x2000817B
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #5
    // 比较操作
    // BLT     locret_2109E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_210A0
 * @note 指令数: 15, 标签数: 1
 */
void precise_func_210a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007E20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3F800000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007E40;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007E30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // CMP     R0, #4
    // 比较操作
    // BGE     loc_210CE
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20007E40
    // 内存加载操作
    // STR.W   R1, [R2,R0,LSL#2]
    // 内存存储操作
    // LDR.W   R1, =0x20007E20
    // 内存加载操作
    // MOVS.W  R2, #0x3F800000
    // STR.W   R2, [R1,R0,LSL#2]
    // 内存存储操作
    // LDR.W   R1, =0x20007E30
    // 内存加载操作
    // MOVS.W  R2, #0x3F800000
    // STR.W   R2, [R1,R0,LSL#2]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_210A4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_210E4
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_210e4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x800E871;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // BL      sub_210A0
    // 调用函数: sub_210A0();
    // BL      sub_23262
    // 调用函数: sub_23262();
    // LDR.W   R0, =0x800E871
    // 内存加载操作
    // BL      sub_232B8
    // 调用函数: sub_232B8();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21200
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_21200(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000809C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008176;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008178;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // VPUSH   {D8}
    // LDR.W   R0, =0x20008178
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R0, R0, R1
    // MOVS    R4, R0
    // MOVS    R0, #1
    // R0 = 1;
    // LDR.W   R1, =0x20008178
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // MOVS    R2, #8
    // R2 = 8;
    // SDIV.W  R3, R1, R2
    // MLS.W   R3, R3, R2, R1
    // LSLS    R0, R3
    // MOVS    R5, R0
    // LDR.W   R0, =0x20008176
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRB    R0, [R4,R0]
    // 内存加载操作
    // TST     R0, R5
    // 比较操作
    // BEQ     loc_21248
    // 条件跳转
    // MOVS    R0, #0xFF
    // R0 = 0xFF;
    // LDR.W   R1, =0x2000809C
    // 内存加载操作
    // LDR.W   R2, =0x20008178
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // STRB    R0, [R2,R1]
    // 内存存储操作
    // B       loc_21256
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_213C2
 * @note 指令数: 25, 标签数: 0
 */
void precise_func_213c2(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007FA0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080A0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008179;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007E40;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MLS.W   R2, R2, R1, R6
    // LSLS    R0, R2
    // MOVS    R5, R0
    // LDR.W   R0, =0x20008177
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRB    R0, [R4,R0]
    // 内存加载操作
    // TST     R0, R5
    // 比较操作
    // BEQ     loc_213FE
    // 条件跳转
    // LDR.W   R0, =0x20008179
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRB    R0, [R4,R0]
    // 内存加载操作
    // TST     R0, R5
    // 比较操作
    // BNE     loc_213FE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007E40
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // STR.W   R0, [R1,R6,LSL#2]
    // 内存存储操作
    // MOVS    R0, #0x1E
    // R0 = 0x1E;
    // LDR     R1, =0x20007FA0
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // STRH.W  R0, [R1,R6,LSL#1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200080A0
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // STRB    R0, [R6,R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_214DE
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_214de(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000817C;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x2000817C
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_214E4
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_214e4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000817C;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x2000817C
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2153C
 * @note 指令数: 42, 标签数: 0
 */
void precise_func_2153c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000814C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080C6;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x180003;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x180002;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007EF8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =dword_180004
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180005
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // LDR     R1, =0x8016788
    // 内存加载操作
    // LDR     R0, =0x8016784
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007EF8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20007F00
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200080C6
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000814E
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000814D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000814C
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20007F00
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007EF8
    // 内存加载操作
    // BL      sub_164A4
    // 调用函数: sub_164A4();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2159E
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_2159e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000814D;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000814C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x2000814C
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_215BA
    // 条件跳转
    // LDR     R0, =0x2000814D
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_215BA
    // 条件跳转
    // LDR     R0, =0x2000814C
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000814D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_215C4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_215C6
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_215c6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007EF8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20007EF8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xA
    // 比较操作
    // BLT     loc_215D2
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_215D4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_215D6
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_215d6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007F00;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016784;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x20007F00
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_21644
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20007F00
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x8016784
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016784
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_18496
    // 调用函数: sub_18496();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_215FC
    // 条件跳转
    // MOVS    R4, #0
    // R4 = 0;
    // B       loc_215FE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21678
 * @note 指令数: 49, 标签数: 0
 */
void precise_func_21678(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016690;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008048;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80168A4;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #0x18
    // 算术运算
    // MOVS    R4, R0
    // LDR     R1, =0x8016680
    // 内存加载操作
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // LDR     R0, =0x80168A4
    // 内存加载操作
    // LDRH    R0, [R0,#4]
    // 内存加载操作
    // SUBS.W  R1, R0, #0xE
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_190A6
    // 调用函数: sub_190A6();
    // LDR     R0, =0x8016690
    // 内存加载操作
    // BL      sub_1A2BC
    // 调用函数: sub_1A2BC();
    // MOVS.W  R1, #0xFFFFFFFF
    // STR     R1, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADDS    R2, R0, #1
    // 算术运算
    // LDR     R1, =0x8016690
    // 内存加载操作
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1900E
    // 调用函数: sub_1900E();
    // LDR     R0, =0x80168A4
    // 内存加载操作
    // LDRH    R0, [R0,#4]
    // 内存加载操作
    // SUBS.W  R1, R0, #0xE
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_190A6
    // 调用函数: sub_190A6();
    // LDR     R0, =0x8016690
    // 内存加载操作
    // BL      sub_1A2BC
    // 调用函数: sub_1A2BC();
    // MOVS.W  R1, #0xFFFFFFFF
    // STR     R1, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADDS    R2, R0, #1
    // 算术运算
    // ADD     R1, SP, #0x20+var_1C
    // 算术运算
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1905A
    // 调用函数: sub_1905A();
    // LDR     R1, =0x200080E2
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R1, =0x8016690
    // 内存加载操作
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // BL      sub_196B4
    // 调用函数: sub_196B4();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_216EE
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_216F0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_216F4
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_216f4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008159;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008048;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x801679C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80168A4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R2, =0x80168A4
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x801679C
    // 内存加载操作
    // BL      sub_18F84
    // 调用函数: sub_18F84();
    // LDR     R1, =0x20008048
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20008159
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_21678
    // 调用函数: sub_21678();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_2171C
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008159
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_2172C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2172E
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_2172e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008048;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80166A0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFFFFFFFF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008159;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_190A6
    // 调用函数: sub_190A6();
    // LDR     R0, =0x80166A0
    // 内存加载操作
    // BL      sub_1A2BC
    // 调用函数: sub_1A2BC();
    // MOVS.W  R1, #0xFFFFFFFF
    // STR     R1, [SP,#0x10+var_10]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADDS    R2, R0, #1
    // 算术运算
    // LDR     R1, =0x80166A0
    // 内存加载操作
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1900E
    // 调用函数: sub_1900E();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_21764
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008159
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_2176A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2176C
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_2176c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008159;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #0x18
    // 算术运算
    // MOVS    R4, R0
    // LDR     R0, =0x20008159
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_2177E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_217AE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_217B2
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_217b2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008159;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDR     R0, =0x20008159
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_217C8
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_217F4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_217F6
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_217f6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008159;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x68;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_68= -0x68
    // var_64= -0x64
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // SUB     SP, SP, #0x48
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDR     R0, =0x20008159
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_2180C
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_21884
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2188A
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_2188a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008159;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDR     R0, =0x20008159
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_218A0
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_218CC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_218CE
 * @note 指令数: 25, 标签数: 0
 */
void precise_func_218ce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016690;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008048;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFFFFFFFF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008159;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_190A6
    // 调用函数: sub_190A6();
    // LDR     R0, =0x8016690
    // 内存加载操作
    // BL      sub_1A2BC
    // 调用函数: sub_1A2BC();
    // MOVS.W  R1, #0xFFFFFFFF
    // STR     R1, [SP,#0x10+var_10]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADDS    R2, R0, #1
    // 算术运算
    // LDR     R1, =0x8016690
    // 内存加载操作
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1900E
    // 调用函数: sub_1900E();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_21906
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008159
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_2190E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21910
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_21910(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008048;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x20008048
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_190D6
    // 调用函数: sub_190D6();
    // UXTB    R0, R0
    // 数据扩展操作
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21920
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_21920(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80168A4;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // LDR     R0, =0x80168A4
    // 内存加载操作
    // LDRB    R0, [R0,#2]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21928
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_21928(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008159;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // LDR     R0, =0x20008159
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21954
 * @note 指令数: 32, 标签数: 0
 */
void precise_func_21954(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80162D0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015E30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016570;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8015B88;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // PUSH.W  {R3-R11,LR}
    // 栈操作
    // MOVS    R0, #0xC0
    // R0 = 0xC0;
    // STR     R0, [SP,#0x28+var_28]
    // 内存存储操作
    // LDR     R4, =0x8015B88
    // 内存加载操作
    // LDR     R5, =0x20006ED4
    // 内存加载操作
    // LDR     R2, [SP,#0x28+var_28]
    // 内存加载操作
    // MOVS    R1, R4
    // MOVS    R0, R5
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // MOVS    R5, #0x30 ; '0'
    // R5 = 0x30;
    // LDR     R6, =0x80162D0
    // 内存加载操作
    // LDR     R7, =0x200078C8
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // MOVS    R7, #0x60 ; '`'
    // R7 = 0x60;
    // LDR.W   R8, =0x8015E30
    // 内存加载操作
    // LDR.W   R9, =0x20007220
    // 内存加载操作
    // MOVS    R2, R7
    // MOV     R1, R8
    // MOV     R0, R9
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // MOVS.W  R9, #0x18
    // LDR.W   R10, =0x8016570
    // 内存加载操作
    // LDR.W   R11, =0x20007D10
    // 内存加载操作
    // MOV     R2, R9
    // MOV     R1, R10
    // MOV     R0, R11
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // POP.W   {R0,R4-R11,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_219A8
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_219a8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008146;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20008146
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_1C0EE
    // 调用函数: sub_1C0EE();
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_219BC
    // 条件跳转
    // BL      sub_21954
    // 调用函数: sub_21954();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_219BE
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_219be(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008146;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20008146
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ANDS.W  R0, R0, #1
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_219EC
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_219ec(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CPSID   I
    // BL      sub_23434
    // 调用函数: sub_23434();
    // MOVS    R0, R4
    // BL      sub_23480
    // 调用函数: sub_23480();
    // BL      sub_2345E
    // 调用函数: sub_2345E();
    // CPSIE   I
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21A04
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_21a04(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // CPSID   I
    // BL      sub_23434
    // 调用函数: sub_23434();
    // MOVS    R1, R5
    // MOVS    R0, R4
    // BL      sub_235AA
    // 调用函数: sub_235AA();
    // CMP     R0, #3
    // 比较操作
    // BEQ     loc_21A24
    // 条件跳转
    // BL      sub_2345E
    // 调用函数: sub_2345E();
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_21A2C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21A30
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_21a30(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x803F804;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8001804;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x803F800;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // LDR     R4, =0x8001800
    // 内存加载操作
    // MOVS    R0, R4
    // LDR     R4, =0x8001804
    // 内存加载操作
    // MOVS    R1, R4
    // LDR     R4, =0x803F800
    // 内存加载操作
    // MOVS    R2, R4
    // LDR     R4, =0x803F804
    // 内存加载操作
    // MOVS    R3, R4
    // LDR     R4, [R0]
    // 内存加载操作
    // CMN.W   R4, #1
    // BNE     loc_21A52
    // 条件跳转
    // LDR     R4, [R1]
    // 内存加载操作
    // CMN.W   R4, #1
    // BEQ     loc_21A62
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21A74
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_21a74(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008165;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20008165
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21A90
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_21a90(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_20700
    // 调用函数: sub_20700();
    // BL      sub_1EC2A
    // 调用函数: sub_1EC2A();
    // BL      sub_14CB4
    // 调用函数: sub_14CB4();
    // BL      sub_1E18C
    // 调用函数: sub_1E18C();
    // BL      sub_1F57C
    // 调用函数: sub_1F57C();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_1CCFC
    // 调用函数: sub_1CCFC();
    // BL      sub_210E4
    // 调用函数: sub_210E4();
    // BL      sub_23752
    // 调用函数: sub_23752();
    // BL      sub_21D70
    // 调用函数: sub_21D70();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21ABA
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_21aba(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2200;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_21BE0
    // 调用函数: sub_21BE0();
    // MOV.W   R1, #0x2200
    // MOVS.W  R0, #0x8000000
    // BL      sub_18286
    // 调用函数: sub_18286();
    // BL      sub_21A30
    // 调用函数: sub_21A30();
    // BL      sub_1942E
    // 调用函数: sub_1942E();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_18E6C
    // 调用函数: sub_18E6C();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // CPSIE   I
    // BL      sub_1BD80
    // 调用函数: sub_1BD80();
    // BL      sub_219A8
    // 调用函数: sub_219A8();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_24144
    // 调用函数: sub_24144();
    // CPSID   I
    // BL      sub_16444
    // 调用函数: sub_16444();
    // BL      sub_19130
    // 调用函数: sub_19130();
    // BL      sub_24A84
    // 调用函数: sub_24A84();
    // BL      sub_1B9C8
    // 调用函数: sub_1B9C8();
    // BL      sub_21A90
    // 调用函数: sub_21A90();
    // BL      sub_1C0EE
    // 调用函数: sub_1C0EE();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_21B16
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_1C6D8
    // 调用函数: sub_1C6D8();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21B24
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_21b24(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_164D6
    // 调用函数: sub_164D6();
    // BL      sub_24A9C
    // 调用函数: sub_24A9C();
    // BL      sub_20C80
    // 调用函数: sub_20C80();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_19162
    // 调用函数: sub_19162();
    // BL      sub_15D3C
    // 调用函数: sub_15D3C();
    // BL      sub_1BA5C
    // 调用函数: sub_1BA5C();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21B48
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_21b48(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_1F0A6
    // 调用函数: sub_1F0A6();
    // BL      sub_1BE8C
    // 调用函数: sub_1BE8C();
    // POP     {R0,PC}
    // 栈操作
}

