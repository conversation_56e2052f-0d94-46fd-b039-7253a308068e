// 完整精确转换批次 24 - 100%对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4756E
 * @note 指令数: 25, 标签数: 1
 * @note 内存引用: 3, 函数调用: 2
 */
void precise_func_4756e(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x4000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4100;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015FE0;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_4866A(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_4866A();
    sub_4866A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_475AC
 * @note 指令数: 4, 标签数: 0
 * @note 内存引用: 0, 函数调用: 1
 */
void precise_func_475ac(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_48516(void);

    // 汇编逻辑实现

    // 函数调用
    sub_48516();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_475B6
 * @note 指令数: 16, 标签数: 0
 * @note 内存引用: 2, 函数调用: 1
 */
void precise_func_475b6(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A00;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015FE8;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_4866A(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_4866A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_475E4
 * @note 指令数: 5, 标签数: 0
 * @note 内存引用: 1, 函数调用: 1
 */
void precise_func_475e4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x21;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_48764(void);

    // 汇编逻辑实现

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_48764();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_475F0
 * @note 指令数: 156, 标签数: 17
 * @note 内存引用: 18, 函数调用: 5
 */
void precise_func_475f0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x8016008;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_47FB4(void);
    extern void sub_46FE8(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_46FE8();
    sub_47FB4();
    sub_47FB4();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47736
 * @note 指令数: 7, 标签数: 0
 * @note 内存引用: 3, 函数调用: 0
 */
uint32_t precise_func_47736(uint32_t param0, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000018C;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47744
 * @note 指令数: 104, 标签数: 9
 * @note 内存引用: 9, 函数调用: 6
 */
void precise_func_47744(uint32_t param0, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_48774(void);
    extern void sub_48C9C(void);
    extern void sub_4781C(void);
    extern void sub_46FE8(void);
    extern void sub_455DA(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_48774();
    sub_46FE8();
    sub_48C9C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4781C
 * @note 指令数: 44, 标签数: 2
 * @note 内存引用: 5, 函数调用: 1
 */
void precise_func_4781c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_49034(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_49034();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47876
 * @note 指令数: 21, 标签数: 3
 * @note 内存引用: 3, 函数调用: 2
 */
void precise_func_47876(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000018C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_491E8(void);
    extern void sub_48FA0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_491E8();
    sub_48FA0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_478A4
 * @note 指令数: 9, 标签数: 0
 * @note 内存引用: 3, 函数调用: 1
 */
void precise_func_478a4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000018C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_491B0(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_491B0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_478BC
 * @note 指令数: 12, 标签数: 0
 * @note 内存引用: 0, 函数调用: 2
 */
void precise_func_478bc(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_48F4A(void);
    extern void sub_4781C(void);

    // 汇编逻辑实现

    // 函数调用
    sub_4781C();
    sub_48F4A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_478D8
 * @note 指令数: 141, 标签数: 17
 * @note 内存引用: 9, 函数调用: 9
 */
void precise_func_478d8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFE;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000324;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x13;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_48FF8(void);
    extern void sub_48F44(void);
    extern void sub_48F4A(void);
    extern void sub_491B0(void);
    extern void sub_47C22(void);
    extern void sub_49082(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_48F44();
    sub_491B0();
    sub_48F4A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47A18
 * @note 指令数: 141, 标签数: 14
 * @note 内存引用: 11, 函数调用: 7
 */
void precise_func_47a18(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFFFFA002;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_48FCC(void);
    extern void sub_48F4A(void);
    extern void sub_4917C(void);
    extern void sub_4781C(void);
    extern void sub_48FA0(void);
    extern void sub_49244(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_49244();
    sub_4781C();
    sub_48FA0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47B36
 * @note 指令数: 91, 标签数: 8
 * @note 内存引用: 10, 函数调用: 2
 */
void precise_func_47b36(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_49222(void);
    extern void sub_491B0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_49222();
    sub_491B0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47BE6
 * @note 指令数: 12, 标签数: 0
 * @note 内存引用: 3, 函数调用: 1
 */
void precise_func_47be6(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000018C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_4917C(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_4917C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47C00
 * @note 指令数: 16, 标签数: 2
 * @note 内存引用: 0, 函数调用: 1
 */
void precise_func_47c00(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void nullsub_28(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 函数调用
    nullsub_28();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47C22
 * @note 指令数: 35, 标签数: 1
 * @note 内存引用: 5, 函数调用: 2
 */
void precise_func_47c22(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_47876(void);
    extern void sub_49082(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_47876();
    sub_49082();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47C70
 * @note 指令数: 8, 标签数: 0
 * @note 内存引用: 0, 函数调用: 1
 */
void precise_func_47c70(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_490D8(void);

    // 汇编逻辑实现

    // 函数调用
    sub_490D8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47C8C
 * @note 指令数: 37, 标签数: 2
 * @note 内存引用: 6, 函数调用: 3
 */
void precise_func_47c8c(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007630;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8015A8C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_47D2A(void);
    extern void sub_470BC(void);
    extern void sub_455DA(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_47D2A();
    sub_470BC();
    sub_455DA();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47CE0
 * @note 指令数: 21, 标签数: 2
 * @note 内存引用: 1, 函数调用: 2
 */
void precise_func_47ce0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015A8C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_4750E(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_4750E();
    sub_4750E();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47D0E
 * @note 指令数: 13, 标签数: 2
 * @note 内存引用: 3, 函数调用: 1
 */
void precise_func_47d0e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015A8C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_474E8(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_474E8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47D2A
 * @note 指令数: 192, 标签数: 18
 * @note 内存引用: 15, 函数调用: 10
 */
void precise_func_47d2a(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80153DC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x80153E0;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_474E8(void);
    extern void sub_4750E(void);
    extern void sub_470BC(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_470BC();
    sub_470BC();
    sub_474E8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47EC4
 * @note 指令数: 22, 标签数: 3
 * @note 内存引用: 5, 函数调用: 3
 */
void precise_func_47ec4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015D20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8015D1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_47F18(void);
    extern void sub_46AC4(void);
    extern void sub_470BC(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_470BC();
    sub_47F18();
    sub_46AC4();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47F18
 * @note 指令数: 2, 标签数: 0
 * @note 内存引用: 0, 函数调用: 0
 */
uint32_t precise_func_47f18(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47F1C
 * @note 指令数: 5, 标签数: 0
 * @note 内存引用: 0, 函数调用: 1
 */
void precise_func_47f1c(uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_49254(void);

    // 汇编逻辑实现

    // 函数调用
    sub_49254();
}

