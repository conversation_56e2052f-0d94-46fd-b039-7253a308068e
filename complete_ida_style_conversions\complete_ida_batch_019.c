// 完整IDA风格转换批次 19 - 专业级转换
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_543DE
 * @note 指令数: 32
 * @note 类型: control_function
 */
uint32_t ida_543de(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_801546C = (volatile uint32_t *)0x801546C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;
    volatile uint32_t *addr_34 = (volatile uint32_t *)0x34;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_54424
 * @note 指令数: 60
 * @note 类型: control_function
 */
uint32_t ida_54424(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000 = (volatile uint32_t *)0x80000;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_40000 = (volatile uint32_t *)0x40000;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_544AA
 * @note 指令数: 8
 * @note 类型: computation
 */
void ida_544aa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_544BA
 * @note 指令数: 61
 * @note 类型: control_function
 */
void ida_544ba(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;
    volatile uint32_t *addr_8015948 = (volatile uint32_t *)0x8015948;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_54542
 * @note 指令数: 61
 * @note 类型: control_function
 */
void ida_54542(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_8015948 = (volatile uint32_t *)0x8015948;
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_545D8
 * @note 指令数: 7
 * @note 类型: control_function
 */
uint32_t ida_545d8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_8016068 = (volatile uint32_t *)0x8016068;
    volatile uint32_t *addr_20000254 = (volatile uint32_t *)0x20000254;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_545E8
 * @note 指令数: 7
 * @note 类型: control_function
 */
uint32_t ida_545e8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_8016068 = (volatile uint32_t *)0x8016068;
    volatile uint32_t *addr_2000027C = (volatile uint32_t *)0x2000027C;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_545F8
 * @note 指令数: 12
 * @note 类型: control_function
 */
uint32_t ida_545f8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200002CC = (volatile uint32_t *)0x200002CC;
    volatile uint32_t *addr_8015F9C = (volatile uint32_t *)0x8015F9C;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_8016070 = (volatile uint32_t *)0x8016070;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数据访问函数
 * @note 原函数: sub_54628
 * @note 指令数: 13
 * @note 类型: data_access
 */
void ida_54628(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_54642
 * @note 指令数: 22
 * @note 类型: computation
 */
void ida_54642(uint32_t param0)
{
    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_5466E
 * @note 指令数: 19
 * @note 类型: control_function
 */
void ida_5466e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_55866
 * @note 指令数: 55
 * @note 类型: lookup_table
 */
void ida_55866(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_FD = (volatile uint32_t *)0xFD;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_558DE
 * @note 指令数: 98
 * @note 类型: lookup_table
 */
void ida_558de(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_559B4
 * @note 指令数: 274
 * @note 类型: array_access
 */
void ida_559b4(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_FFFF9002 = (volatile uint32_t *)0xFFFF9002;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_20007670 = (volatile uint32_t *)0x20007670;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_55BF0
 * @note 指令数: 17
 * @note 类型: control_function
 */
void ida_55bf0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_55C10
 * @note 指令数: 158
 * @note 类型: array_access
 */
void ida_55c10(void)
{
    // 内存地址定义
    volatile uint32_t *addr_FFFF9002 = (volatile uint32_t *)0xFFFF9002;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_20007670 = (volatile uint32_t *)0x20007670;
    volatile uint32_t *addr_FFFF9000 = (volatile uint32_t *)0xFFFF9000;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_55D60
 * @note 指令数: 24
 * @note 类型: lookup_table
 */
uint8_t ida_55d60(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量
    uint8_t result = 0;

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_55D8E
 * @note 指令数: 68
 * @note 类型: array_access
 */
void ida_55d8e(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20000150 = (volatile uint32_t *)0x20000150;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_57046
 * @note 指令数: 2
 * @note 类型: simple_function
 */
void ida_57046(void)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_5704E
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_5704e(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_57056
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_57056(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_5705E
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_5705e(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_57066
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_57066(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_5706E
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_5706e(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_57076
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_57076(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_5707E
 * @note 指令数: 32
 * @note 类型: control_function
 */
uint32_t ida_5707e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_48000C00 = (volatile uint32_t *)0x48000C00;
    volatile uint32_t *addr_65 = (volatile uint32_t *)0x65;
    volatile uint32_t *addr_200077F8 = (volatile uint32_t *)0x200077F8;
    volatile uint32_t *addr_40001010 = (volatile uint32_t *)0x40001010;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_570D4
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_570d4(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数据访问函数
 * @note 原函数: sub_577B4
 * @note 指令数: 30
 * @note 类型: data_access
 */
uint32_t ida_577b4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_578D4
 * @note 指令数: 16
 * @note 类型: computation
 */
void ida_578d4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_578DC = (volatile uint32_t *)0x578DC;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_57A6A
 * @note 指令数: 2
 * @note 类型: simple_function
 */
uint32_t ida_57a6a(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_57A6E
 * @note 指令数: 2
 * @note 类型: control_function
 */
void ida_57a6e(void)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_57A76
 * @note 指令数: 1
 * @note 类型: simple_function
 */
void ida_57a76(void)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_57A78
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_57a78(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_57A82
 * @note 指令数: 1
 * @note 类型: simple_function
 */
void ida_57a82(void)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_57A84
 * @note 指令数: 8
 * @note 类型: simple_function
 */
void ida_57a84(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_20026 = (volatile uint32_t *)0x20026;
    volatile uint32_t *addr_AB = (volatile uint32_t *)0xAB;

    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_73494
 * @note 指令数: 43
 * @note 类型: array_access
 */
void ida_73494(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_200036F6 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20000108 = (volatile uint32_t *)0x20000108;
    volatile uint32_t *addr_200036B0 = (volatile uint32_t *)0x200036B0;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_734F2
 * @note 指令数: 43
 * @note 类型: array_access
 */
void ida_734f2(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_20003576 = (volatile uint32_t *)0x20003576;
    volatile uint32_t *addr_200036F6 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20000118 = (volatile uint32_t *)0x20000118;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_73552
 * @note 指令数: 41
 * @note 类型: array_access
 */
void ida_73552(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_20003574 = (volatile uint32_t *)0x20003574;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;
    volatile uint32_t *addr_200036F6 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_735AC
 * @note 指令数: 41
 * @note 类型: array_access
 */
void ida_735ac(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_20003574 = (volatile uint32_t *)0x20003574;
    volatile uint32_t *addr_200036F6 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_73638
 * @note 指令数: 129
 * @note 类型: array_access
 */
void ida_73638(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_28 = (volatile uint32_t *)0x28;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_7374A
 * @note 指令数: 182
 * @note 类型: lookup_table
 */
void ida_7374a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_738D0
 * @note 指令数: 193
 * @note 类型: array_access
 */
uint32_t ida_738d0(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_73A64
 * @note 指令数: 182
 * @note 类型: lookup_table
 */
void ida_73a64(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_73BEC
 * @note 指令数: 193
 * @note 类型: array_access
 */
uint32_t ida_73bec(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_73D80
 * @note 指令数: 182
 * @note 类型: lookup_table
 */
void ida_73d80(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_73F08
 * @note 指令数: 193
 * @note 类型: array_access
 */
uint32_t ida_73f08(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_74098
 * @note 指令数: 121
 * @note 类型: array_access
 */
void ida_74098(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_200036F0 = (volatile uint32_t *)0x200036F0;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_741A4
 * @note 指令数: 88
 * @note 类型: array_access
 */
uint32_t ida_741a4(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_10000 = (volatile uint32_t *)0x10000;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_74260
 * @note 指令数: 54
 * @note 类型: lookup_table
 */
void ida_74260(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_8011F18 = (volatile uint32_t *)0x8011F18;
    volatile uint32_t *addr_801245C = (volatile uint32_t *)0x801245C;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_742E0
 * @note 指令数: 49
 * @note 类型: lookup_table
 */
void ida_742e0(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_8011D58 = (volatile uint32_t *)0x8011D58;
    volatile uint32_t *addr_20003734 = (volatile uint32_t *)0x20003734;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

