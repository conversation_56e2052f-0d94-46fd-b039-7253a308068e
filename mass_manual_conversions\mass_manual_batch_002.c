// 大规模手工转换批次 2 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_175CE
 * @note 指令数: 91, 类型: array_operation
 */
void manual_175ce(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008130 = (volatile uint32_t *)0x20008130;
    volatile uint32_t *addr_20008154 = (volatile uint32_t *)0x20008154;
    volatile uint32_t *addr_200080E8 = (volatile uint32_t *)0x200080E8;
    volatile uint32_t *addr_1F4 = (volatile uint32_t *)0x1F4;
    volatile uint32_t *addr_20008150 = (volatile uint32_t *)0x20008150;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_17698
 * @note 指令数: 53, 类型: array_operation
 */
uint32_t manual_17698(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20008160 = (volatile uint32_t *)0x20008160;
    volatile uint32_t *addr_20007F38 = (volatile uint32_t *)0x20007F38;
    volatile uint32_t *addr_200080E8 = (volatile uint32_t *)0x200080E8;
    volatile uint32_t *addr_200080EA = (volatile uint32_t *)0x200080EA;
    volatile uint32_t *addr_20007F48 = (volatile uint32_t *)0x20007F48;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_17714
 * @note 指令数: 167, 类型: array_operation
 */
void manual_17714(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008160 = (volatile uint32_t *)0x20008160;
    volatile uint32_t *addr_8008082 = (volatile uint32_t *)0x8008082;
    volatile uint32_t *addr_200080EA = (volatile uint32_t *)0x200080EA;
    volatile uint32_t *addr_20006A28 = (volatile uint32_t *)0x20006A28;
    volatile uint32_t *addr_20008070 = (volatile uint32_t *)0x20008070;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_17900
 * @note 指令数: 32, 类型: control_function
 */
uint32_t manual_17900(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_C0009 = (volatile uint32_t *)0xC0009;
    volatile uint32_t *addr_40012800 = (volatile uint32_t *)0x40012800;
    volatile uint32_t *addr_40013C00 = (volatile uint32_t *)0x40013C00;
    volatile uint32_t *addr_40012400 = (volatile uint32_t *)0x40012400;
    volatile uint32_t *addr_C000F = (volatile uint32_t *)0xC000F;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1795E
 * @note 指令数: 5, 类型: simple_function
 */
uint32_t manual_1795e(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1796A
 * @note 指令数: 7, 类型: simple_function
 */
uint32_t manual_1796a(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_40012404 = (volatile uint32_t *)0x40012404;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1797E
 * @note 指令数: 9, 类型: simple_function
 */
uint32_t manual_1797e(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_17990
 * @note 指令数: 18, 类型: lookup_table
 */
uint32_t manual_17990(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量
    uint32_t result = 0;

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_179BC
 * @note 指令数: 14, 类型: simple_function
 */
uint32_t manual_179bc(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_179D8
 * @note 指令数: 4, 类型: simple_function
 */
uint32_t manual_179d8(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_179E2
 * @note 指令数: 9, 类型: simple_function
 */
uint32_t manual_179e2(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_179F6
 * @note 指令数: 4, 类型: simple_function
 */
uint32_t manual_179f6(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17A00
 * @note 指令数: 9, 类型: simple_function
 */
uint32_t manual_17a00(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17A14
 * @note 指令数: 231, 类型: simple_function
 */
uint32_t manual_17a14(void)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_19 = (volatile uint32_t *)0x19;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17C26
 * @note 指令数: 23, 类型: simple_function
 */
uint32_t manual_17c26(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_2000000 = (volatile uint32_t *)0x2000000;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17C5E
 * @note 指令数: 5, 类型: simple_function
 */
uint32_t manual_17c5e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17C6A
 * @note 指令数: 3, 类型: simple_function
 */
uint32_t manual_17c6a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_4001244C = (volatile uint32_t *)0x4001244C;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17C94
 * @note 指令数: 13, 类型: simple_function
 */
uint32_t manual_17c94(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17CAE
 * @note 指令数: 4, 类型: simple_function
 */
uint32_t manual_17cae(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_17CB8
 * @note 指令数: 39, 类型: array_operation
 */
uint32_t manual_17cb8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17D20
 * @note 指令数: 37, 类型: simple_function
 */
uint32_t manual_17d20(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_9F0000 = (volatile uint32_t *)0x9F0000;
    volatile uint32_t *addr_40021030 = (volatile uint32_t *)0x40021030;
    volatile uint32_t *addr_FEF2FFFF = (volatile uint32_t *)0xFEF2FFFF;
    volatile uint32_t *addr_40021004 = (volatile uint32_t *)0x40021004;
    volatile uint32_t *addr_40021008 = (volatile uint32_t *)0x40021008;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17D8A
 * @note 指令数: 23, 类型: simple_function
 */
uint32_t manual_17d8a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_40021000 = (volatile uint32_t *)0x40021000;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_17DBE
 * @note 指令数: 24, 类型: control_function
 */
void manual_17dbe(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_FFFF = (volatile uint32_t *)0xFFFF;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17DF4
 * @note 指令数: 27, 类型: simple_function
 */
uint32_t manual_17df4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_40021000 = (volatile uint32_t *)0x40021000;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17E36
 * @note 指令数: 27, 类型: simple_function
 */
uint32_t manual_17e36(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_40021000 = (volatile uint32_t *)0x40021000;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17E78
 * @note 指令数: 46, 类型: simple_function
 */
void manual_17e78(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_40021024 = (volatile uint32_t *)0x40021024;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_40021020 = (volatile uint32_t *)0x40021020;
    volatile uint32_t *addr_40021000 = (volatile uint32_t *)0x40021000;

    // 局部变量

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17EF2
 * @note 指令数: 7, 类型: simple_function
 */
uint32_t manual_17ef2(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40021004 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17F06
 * @note 指令数: 7, 类型: simple_function
 */
uint32_t manual_17f06(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40021004 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17F1A
 * @note 指令数: 7, 类型: simple_function
 */
uint32_t manual_17f1a(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_40021004 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17F2E
 * @note 指令数: 16, 类型: simple_function
 */
uint32_t manual_17f2e(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40021004 = (volatile uint32_t *)0x40021004;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17F54
 * @note 指令数: 52, 类型: simple_function
 */
void manual_17f54(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_10000 = (volatile uint32_t *)0x10000;
    volatile uint32_t *addr_40021004 = (volatile uint32_t *)0x40021004;
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;
    volatile uint32_t *addr_20000 = (volatile uint32_t *)0x20000;

    // 局部变量

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17FCA
 * @note 指令数: 7, 类型: simple_function
 */
uint32_t manual_17fca(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40021004 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_17FDA
 * @note 指令数: 5, 类型: simple_function
 */
uint32_t manual_17fda(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_40021004 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_17FE6
 * @note 指令数: 129, 类型: lookup_table
 */
void manual_17fe6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_801688C = (volatile uint32_t *)0x801688C;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_40021054 = (volatile uint32_t *)0x40021054;
    volatile uint32_t *addr_40021030 = (volatile uint32_t *)0x40021030;
    volatile uint32_t *addr_2DC6C00 = (volatile uint32_t *)0x2DC6C00;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1812C
 * @note 指令数: 15, 类型: simple_function
 */
uint32_t manual_1812c(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_40021054 = (volatile uint32_t *)0x40021054;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1814E
 * @note 指令数: 7, 类型: simple_function
 */
uint32_t manual_1814e(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_40021054 = (volatile uint32_t *)0x40021054;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_181A0
 * @note 指令数: 4, 类型: simple_function
 */
uint32_t manual_181a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_E000ED0C = (volatile uint32_t *)0xE000ED0C;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_181AC
 * @note 指令数: 11, 类型: simple_function
 */
uint32_t manual_181ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_E000E100 = (volatile uint32_t *)0xE000E100;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_181C6
 * @note 指令数: 13, 类型: simple_function
 */
uint32_t manual_181c6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_E000E180 = (volatile uint32_t *)0xE000E180;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_181E8
 * @note 指令数: 17, 类型: data_processing
 */
uint32_t manual_181e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_E000ED18 = (volatile uint32_t *)0xE000ED18;
    volatile uint32_t *addr_E000E400 = (volatile uint32_t *)0xE000E400;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1820E
 * @note 指令数: 27, 类型: data_processing
 */
uint32_t manual_1820e(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1824C
 * @note 指令数: 18, 类型: control_function
 */
void manual_1824c(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_18278
 * @note 指令数: 6, 类型: control_function
 */
void manual_18278(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_18286
 * @note 指令数: 6, 类型: simple_function
 */
uint32_t manual_18286(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1FFFFF80 = (volatile uint32_t *)0x1FFFFF80;
    volatile uint32_t *addr_E000ED08 = (volatile uint32_t *)0xE000ED08;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_182B0
 * @note 指令数: 17, 类型: data_processing
 */
uint32_t manual_182b0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_E000ED18 = (volatile uint32_t *)0xE000ED18;
    volatile uint32_t *addr_E000E400 = (volatile uint32_t *)0xE000E400;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_182D6
 * @note 指令数: 21, 类型: control_function
 */
void manual_182d6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;
    volatile uint32_t *addr_E000E010 = (volatile uint32_t *)0xE000E010;
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_E000E018 = (volatile uint32_t *)0xE000E018;
    volatile uint32_t *addr_FFFFFFFF = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_18306
 * @note 指令数: 8, 类型: control_function
 */
uint32_t manual_18306(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007FE4 = (volatile uint32_t *)0x20007FE4;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_18318
 * @note 指令数: 7, 类型: control_function
 */
void manual_18318(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2710 = (volatile uint32_t *)0x2710;
    volatile uint32_t *addr_20000254 = (volatile uint32_t *)0x20000254;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_18348
 * @note 指令数: 5, 类型: simple_function
 */
uint32_t manual_18348(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_18354
 * @note 指令数: 6, 类型: simple_function
 */
uint32_t manual_18354(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_28 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

