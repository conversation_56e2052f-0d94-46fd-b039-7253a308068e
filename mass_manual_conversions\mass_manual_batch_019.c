// 大规模手工转换批次 19 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_543DE
 * @note 指令数: 33
 */
void func_543de(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_801546C = (volatile uint32_t *)0x801546C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_34 = (volatile uint32_t *)0x34;
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_54424
 * @note 指令数: 60
 */
uint32_t func_54424(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_100000 = (volatile uint32_t *)0x100000;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_8015948 = (volatile uint32_t *)0x8015948;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_544AA
 * @note 指令数: 9
 */
void func_544aa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_544BA
 * @note 指令数: 61
 */
void func_544ba(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8015948 = (volatile uint32_t *)0x8015948;
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_54542
 * @note 指令数: 61
 */
void func_54542(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_8015948 = (volatile uint32_t *)0x8015948;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_34 = (volatile uint32_t *)0x34;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_545D8
 * @note 指令数: 7
 */
uint32_t func_545d8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8016068 = (volatile uint32_t *)0x8016068;
    volatile uint32_t *addr_20000254 = (volatile uint32_t *)0x20000254;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_545E8
 * @note 指令数: 7
 */
uint32_t func_545e8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000027C = (volatile uint32_t *)0x2000027C;
    volatile uint32_t *addr_8016068 = (volatile uint32_t *)0x8016068;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_545F8
 * @note 指令数: 12
 */
uint32_t func_545f8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_8016070 = (volatile uint32_t *)0x8016070;
    volatile uint32_t *addr_8015F9C = (volatile uint32_t *)0x8015F9C;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_54628
 * @note 指令数: 13
 */
void func_54628(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_54642
 * @note 指令数: 23
 */
void func_54642(uint32_t param0)
{
    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_5466E
 * @note 指令数: 20
 */
void func_5466e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_55866
 * @note 指令数: 56
 */
void func_55866(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_FD = (volatile uint32_t *)0xFD;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_20000150 = (volatile uint32_t *)0x20000150;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_558DE
 * @note 指令数: 99
 */
void func_558de(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_FE = (volatile uint32_t *)0xFE;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_559B4
 * @note 指令数: 275
 */
void func_559b4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_FFFF9002 = (volatile uint32_t *)0xFFFF9002;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_55BF0
 * @note 指令数: 17
 */
void func_55bf0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_55C10
 * @note 指令数: 159
 */
void func_55c10(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_FFFF9002 = (volatile uint32_t *)0xFFFF9002;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_55D60
 * @note 指令数: 25
 */
void func_55d60(void)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_55D8E
 * @note 指令数: 69
 */
void func_55d8e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20000150 = (volatile uint32_t *)0x20000150;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_57046
 * @note 指令数: 2
 */
void func_57046(void)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_5704E
 * @note 指令数: 3
 */
void func_5704e(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_57056
 * @note 指令数: 3
 */
void func_57056(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_5705E
 * @note 指令数: 3
 */
void func_5705e(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_57066
 * @note 指令数: 3
 */
void func_57066(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_5706E
 * @note 指令数: 3
 */
void func_5706e(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_57076
 * @note 指令数: 3
 */
void func_57076(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_5707E
 * @note 指令数: 33
 */
void func_5707e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_29 = (volatile uint32_t *)0x29;
    volatile uint32_t *addr_4000100C = (volatile uint32_t *)0x4000100C;
    volatile uint32_t *addr_40001010 = (volatile uint32_t *)0x40001010;
    volatile uint32_t *addr_200077F8 = (volatile uint32_t *)0x200077F8;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_570D4
 * @note 指令数: 3
 */
void func_570d4(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_577B4
 * @note 指令数: 30
 */
uint32_t func_577b4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_578D4
 * @note 指令数: 16
 */
void func_578d4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;
    volatile uint32_t *addr_578DC = (volatile uint32_t *)0x578DC;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_57A6A
 * @note 指令数: 2
 */
uint32_t func_57a6a(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_57A6E
 * @note 指令数: 2
 */
void func_57a6e(void)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_57A76
 * @note 指令数: 1
 */
void func_57a76(void)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_57A78
 * @note 指令数: 3
 */
void func_57a78(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_57A82
 * @note 指令数: 1
 */
void func_57a82(void)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_57A84
 * @note 指令数: 8
 */
void func_57a84(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_AB = (volatile uint32_t *)0xAB;
    volatile uint32_t *addr_20026 = (volatile uint32_t *)0x20026;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_73494
 * @note 指令数: 44
 */
void func_73494(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200036B0 = (volatile uint32_t *)0x200036B0;
    volatile uint32_t *addr_200036F8 = (volatile uint32_t *)0x200036F8;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80120AC = (volatile uint32_t *)0x80120AC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_734F2
 * @note 指令数: 44
 */
void func_734f2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80120BC = (volatile uint32_t *)0x80120BC;
    volatile uint32_t *addr_200036B0 = (volatile uint32_t *)0x200036B0;
    volatile uint32_t *addr_20000118 = (volatile uint32_t *)0x20000118;
    volatile uint32_t *addr_200036F8 = (volatile uint32_t *)0x200036F8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_73552
 * @note 指令数: 42
 */
void func_73552(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000012C = (volatile uint32_t *)0x2000012C;
    volatile uint32_t *addr_200036B0 = (volatile uint32_t *)0x200036B0;
    volatile uint32_t *addr_80120CC = (volatile uint32_t *)0x80120CC;
    volatile uint32_t *addr_200036F8 = (volatile uint32_t *)0x200036F8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_735AC
 * @note 指令数: 42
 */
void func_735ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200036B0 = (volatile uint32_t *)0x200036B0;
    volatile uint32_t *addr_200036F8 = (volatile uint32_t *)0x200036F8;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80120DC = (volatile uint32_t *)0x80120DC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_73638
 * @note 指令数: 129
 */
void func_73638(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7374A
 * @note 指令数: 182
 */
void func_7374a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_2E = (volatile uint32_t *)0x2E;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_738D0
 * @note 指令数: 194
 */
void func_738d0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_73A64
 * @note 指令数: 182
 */
void func_73a64(void)
{
    // 内存地址定义
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;
    volatile uint32_t *addr_8012354 = (volatile uint32_t *)0x8012354;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_20003694 = (volatile uint32_t *)0x20003694;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_73BEC
 * @note 指令数: 194
 */
void func_73bec(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_20003694 = (volatile uint32_t *)0x20003694;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_73D80
 * @note 指令数: 182
 */
void func_73d80(void)
{
    // 内存地址定义
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_2E = (volatile uint32_t *)0x2E;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_73F08
 * @note 指令数: 194
 */
void func_73f08(void)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74098
 * @note 指令数: 121
 */
void func_74098(void)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_200036F0 = (volatile uint32_t *)0x200036F0;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_741A4
 * @note 指令数: 89
 */
void func_741a4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_10000 = (volatile uint32_t *)0x10000;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_200036F0 = (volatile uint32_t *)0x200036F0;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74260
 * @note 指令数: 55
 */
void func_74260(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003734 = (volatile uint32_t *)0x20003734;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_801245C = (volatile uint32_t *)0x801245C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_742E0
 * @note 指令数: 50
 */
void func_742e0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003734 = (volatile uint32_t *)0x20003734;
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;
    volatile uint32_t *addr_8011D58 = (volatile uint32_t *)0x8011D58;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

