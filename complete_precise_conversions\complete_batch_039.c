// 完整精确转换批次 39 - 100%对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74360
 * @note 指令数: 121, 标签数: 6
 * @note 内存引用: 9, 函数调用: 14
 */
void precise_func_74360(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x200036F2;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x8011D70;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7693E(void);
    extern void sub_762E4(void);
    extern void sub_77834(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74470
 * @note 指令数: 88, 标签数: 6
 * @note 内存引用: 9, 函数调用: 4
 */
void precise_func_74470(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036FC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200036F2;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_74360(void);
    extern void sub_7656C(void);
    extern void sub_7832C(void);
    extern void sub_73638(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_73638();
    sub_74360();
    sub_7656C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74534
 * @note 指令数: 39, 标签数: 2
 * @note 内存引用: 7, 函数调用: 6
 */
void precise_func_74534(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801246C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000373C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8011D88;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x80120FC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x8012464;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76BEC(void);
    extern void sub_76D64(void);
    extern void sub_76874(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74598
 * @note 指令数: 66, 标签数: 0
 * @note 内存引用: 11, 函数调用: 10
 */
void precise_func_74598(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003576;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8011F40;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20000118;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x8011F2C;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7832C(void);
    extern void sub_78930(void);
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_7644C(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7693E(void);

    // 汇编逻辑实现

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_78930();
    sub_76874();
    sub_76D64();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74638
 * @note 指令数: 83, 标签数: 4
 * @note 内存引用: 13, 函数调用: 9
 */
void precise_func_74638(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011F54;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003574;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x8012444;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x8011F68;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x2000012C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7832C(void);
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76BEC(void);
    extern void sub_762E4(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_746F8
 * @note 指令数: 74, 标签数: 3
 * @note 内存引用: 14, 函数调用: 8
 */
void precise_func_746f8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011F90;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003574;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x8012474;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x88;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7832C(void);
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76BEC(void);
    extern void sub_762E4(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74798
 * @note 指令数: 12, 标签数: 1
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_74798(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000373A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003738;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_747B0
 * @note 指令数: 12, 标签数: 1
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_747b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000373A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003738;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_747D0
 * @note 指令数: 12, 标签数: 1
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_747d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000373A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003738;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74810
 * @note 指令数: 121, 标签数: 6
 * @note 内存引用: 8, 函数调用: 14
 */
void precise_func_74810(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011D28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200036EE;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7693E(void);
    extern void sub_762E4(void);
    extern void sub_77834(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74938
 * @note 指令数: 95, 标签数: 6
 * @note 内存引用: 10, 函数调用: 5
 */
void precise_func_74938(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036FC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011BE8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_73638(void);
    extern void sub_7832C(void);
    extern void sub_76D64(void);
    extern void sub_74810(void);
    extern void sub_7656C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_73638();
    sub_74810();
    sub_7656C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74A14
 * @note 指令数: 121, 标签数: 6
 * @note 内存引用: 9, 函数调用: 14
 */
void precise_func_74a14(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200031A8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x8011DA0;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7693E(void);
    extern void sub_762E4(void);
    extern void sub_77834(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74B24
 * @note 指令数: 95, 标签数: 6
 * @note 内存引用: 11, 函数调用: 5
 */
void precise_func_74b24(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011DB8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000373A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x3FFFFF;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200031A8;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x200036FC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20003738;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_73638(void);
    extern void sub_7832C(void);
    extern void sub_76D64(void);
    extern void sub_74A14(void);
    extern void sub_7656C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_73638();
    sub_74A14();
    sub_7656C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74BF8
 * @note 指令数: 145, 标签数: 8
 * @note 内存引用: 14, 函数调用: 13
 */
void precise_func_74bf8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x8011DD0;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7693E(void);
    extern void sub_762E4(void);
    extern void sub_77834(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74D3C
 * @note 指令数: 131, 标签数: 12
 * @note 内存引用: 11, 函数调用: 5
 */
void precise_func_74d3c(uint32_t param0, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2D0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2D1;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200036FC;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x200036FA;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_73638(void);
    extern void sub_7832C(void);
    extern void sub_7639E(void);
    extern void sub_74BF8(void);
    extern void sub_7656C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_73638();
    sub_7639E();
    sub_74BF8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74E60
 * @note 指令数: 115, 标签数: 6
 * @note 内存引用: 10, 函数调用: 13
 */
void precise_func_74e60(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8011DE8;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x200036FA;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7693E(void);
    extern void sub_762E4(void);
    extern void sub_77834(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74F58
 * @note 指令数: 87, 标签数: 6
 * @note 内存引用: 9, 函数调用: 4
 */
void precise_func_74f58(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036FC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200031B5;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7656C(void);
    extern void sub_74E60(void);
    extern void sub_7832C(void);
    extern void sub_73638(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_73638();
    sub_74E60();
    sub_7656C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75018
 * @note 指令数: 80, 标签数: 2
 * @note 内存引用: 14, 函数调用: 9
 */
void precise_func_75018(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000108;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000369C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x8011F40;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003739;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xD;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7832C(void);
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7693E(void);
    extern void sub_762E4(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_750C6
 * @note 指令数: 12, 标签数: 1
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_750c6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000373A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003738;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_750EC
 * @note 指令数: 136, 标签数: 8
 * @note 内存引用: 10, 函数调用: 13
 */
void precise_func_750ec(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011FB8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76D64(void);
    extern void sub_76874(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7693E(void);
    extern void sub_762E4(void);
    extern void sub_77834(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75210
 * @note 指令数: 102, 标签数: 6
 * @note 内存引用: 11, 函数调用: 7
 */
void precise_func_75210(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011E00;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8011FCC;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x200036FC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x200036FA;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_73638(void);
    extern void sub_7832C(void);
    extern void sub_76D64(void);
    extern void sub_780E4(void);
    extern void sub_750EC(void);
    extern void sub_7656C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_73638();
    sub_750EC();
    sub_7656C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75304
 * @note 指令数: 59, 标签数: 2
 * @note 内存引用: 9, 函数调用: 8
 */
void precise_func_75304(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011FE0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x23;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8011E78;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x200035E4;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x78;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76BEC(void);
    extern void sub_76D64(void);
    extern void sub_76874(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75394
 * @note 指令数: 63, 标签数: 6
 * @note 内存引用: 9, 函数调用: 9
 */
void precise_func_75394(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x23;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200035E4;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x801210C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003735;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7646C(void);
    extern void sub_764D0(void);
    extern void sub_7832C(void);
    extern void sub_76D64(void);
    extern void sub_7649E(void);
    extern void sub_75304(void);
    extern void sub_76502(void);
    extern void sub_7656C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7646C();
    sub_764D0();
    sub_75304();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75428
 * @note 指令数: 59, 标签数: 2
 * @note 内存引用: 8, 函数调用: 8
 */
void precise_func_75428(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x23;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011C04;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200035E4;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x8011CE0;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20003737;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76BEC(void);
    extern void sub_76D64(void);
    extern void sub_76874(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_754B8
 * @note 指令数: 60, 标签数: 6
 * @note 内存引用: 9, 函数调用: 9
 */
void precise_func_754b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x23;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200035E4;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8012378;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xF;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20003737;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7646C(void);
    extern void sub_764D0(void);
    extern void sub_7832C(void);
    extern void sub_75428(void);
    extern void sub_76D64(void);
    extern void sub_7649E(void);
    extern void sub_76502(void);
    extern void sub_7656C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7646C();
    sub_764D0();
    sub_75428();
}

