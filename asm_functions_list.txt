AT32F403AVG汇编文件函数列表
==================================================

  1. sub_8000240
     地址: 0x8000240
     行号: 125 - 148
     指令数: 19
     首指令: PUSH		{R4}

  2. sub_800026A
     地址: 0x800026A
     行号: 154 - 181
     指令数: 23
     首指令: PUSH		{R4,LR}

  3. sub_80002A0
     地址: 0x80002A0
     行号: 187 - 204
     指令数: 13
     首指令: UXTB		R0, R0

  4. sub_80002BA
     地址: 0x80002BA
     行号: 210 - 263
     指令数: 43
     首指令: PUSH		{R4-R6}

  5. sub_8000308
     地址: 0x8000308
     行号: 269 - 419
     指令数: 134
     首指令: LDR.W		R0, dword_8000A8C

  6. sub_8000454
     地址: 0x8000454
     行号: 425 - 440
     指令数: 11
     首指令: LDR.W		R0, off_8000ACC

  7. sub_800046A
     地址: 0x800046A
     行号: 446 - 455
     指令数: 8
     首指令: PUSH		{R4,LR}

  8. sub_800047C
     地址: 0x800047C
     行号: 461 - 480
     指令数: 15
     首指令: PUSH		{R4,LR}

  9. sub_800049A
     地址: 0x800049A
     行号: 486 - 519
     指令数: 25
     首指令: MOVS		R2, #0

 10. sub_80004C4
     地址: 0x80004C4
     行号: 526 - 1044
     指令数: 449
     首指令: PUSH.W		{R4-R10,LR}

 11. sub_80008AE
     地址: 0x80008AE
     行号: 1050 - 1241
     指令数: 190
     首指令: PUSH		{R7,LR}

 12. sub_8000B78
     地址: 0x8000B78
     行号: 1319 - 1327
     指令数: 6
     首指令: PUSH		{R7,LR}

 13. sub_8000B88
     地址: 0x8000B88
     行号: 1333 - 1379
     指令数: 40
     首指令: MOVS		R3, R0

 14. sub_8000BEA
     地址: 0x8000BEA
     行号: 1385 - 1387
     指令数: 1
     首指令: BX		LR

 15. sub_8000BEC
     地址: 0x8000BEC
     行号: 1394 - 1396
     指令数: 1
     首指令: B		sub_8000BEC

 16. sub_8000BEE
     地址: 0x8000BEE
     行号: 1403 - 1405
     指令数: 1
     首指令: B		sub_8000BEE

 17. sub_8000BF0
     地址: 0x8000BF0
     行号: 1412 - 1414
     指令数: 1
     首指令: B		sub_8000BF0

 18. sub_8000BF2
     地址: 0x8000BF2
     行号: 1421 - 1423
     指令数: 1
     首指令: B		sub_8000BF2

 19. sub_8000BF4
     地址: 0x8000BF4
     行号: 1429 - 1431
     指令数: 1
     首指令: BX		LR

 20. sub_8000BF6
     地址: 0x8000BF6
     行号: 1437 - 1439
     指令数: 1
     首指令: BX		LR

 21. sub_8000BF8
     地址: 0x8000BF8
     行号: 1445 - 1447
     指令数: 1
     首指令: BX		LR

 22. sub_8000BFA
     地址: 0x8000BFA
     行号: 1453 - 1455
     指令数: 1
     首指令: BX		LR

 23. sub_8000BFC
     地址: 0x8000BFC
     行号: 1461 - 1508
     指令数: 42
     首指令: BFI.W		R2, R2,	#8, #8

 24. sub_8000C64
     地址: 0x8000C64
     行号: 1516 - 1566
     指令数: 47
     首指令: LDR		R0, dword_8000CC8

 25. sub_8000D20
     地址: 0x8000D20
     行号: 1624 - 1646
     指令数: 18
     首指令: PUSH		{R4,LR}

 26. sub_8000D48
     地址: 0x8000D48
     行号: 1655 - 1670
     指令数: 12
     首指令: MOVW		R1, #0xED88

 27. sub_8000D7C
     地址: 0x8000D7C
     行号: 1683 - 1695
     指令数: 10
     首指令: MOVS		R0, #1

 28. sub_8000DA8
     地址: 0x8000DA8
     行号: 1722 - 1733
     指令数: 9
     首指令: PUSH		{R7,LR}

 29. sub_8000DF4
     地址: 0x8000DF4
     行号: 1774 - 1776
     指令数: 1
     首指令: B.W		sub_8000DF4

 30. sub_8000DF8
     地址: 0x8000DF8
     行号: 1783 - 1785
     指令数: 1
     首指令: B.W		sub_8000DF8

 31. sub_8000DFC
     地址: 0x8000DFC
     行号: 1792 - 1794
     指令数: 1
     首指令: B.W		sub_8000DFC

 32. sub_8000E00
     地址: 0x8000E00
     行号: 1801 - 1803
     指令数: 1
     首指令: B.W		sub_8000E00

 33. sub_8000E04
     地址: 0x8000E04
     行号: 1810 - 1812
     指令数: 1
     首指令: B.W		sub_8000E04

 34. sub_8000E08
     地址: 0x8000E08
     行号: 1819 - 1821
     指令数: 1
     首指令: B.W		sub_8000E08

 35. sub_8000E0C
     地址: 0x8000E0C
     行号: 1828 - 1830
     指令数: 1
     首指令: B.W		sub_8000E0C

 36. sub_8000E10
     地址: 0x8000E10
     行号: 1837 - 1839
     指令数: 1
     首指令: B.W		sub_8000E10

 37. sub_8000E14
     地址: 0x8000E14
     行号: 1846 - 1848
     指令数: 1
     首指令: B.W		sub_8000E14

 38. sub_8000E18
     地址: 0x8000E18
     行号: 1855 - 1857
     指令数: 1
     首指令: B.W		sub_8000E18

 39. sub_8000E1C
     地址: 0x8000E1C
     行号: 1864 - 1866
     指令数: 1
     首指令: B.W		sub_8000E1C

 40. sub_8000E20
     地址: 0x8000E20
     行号: 1873 - 1875
     指令数: 1
     首指令: B.W		sub_8000E20

 41. sub_8000E24
     地址: 0x8000E24
     行号: 1882 - 1884
     指令数: 1
     首指令: B.W		sub_8000E24

 42. sub_8000E28
     地址: 0x8000E28
     行号: 1891 - 1893
     指令数: 1
     首指令: B.W		sub_8000E28

 43. sub_8000E2C
     地址: 0x8000E2C
     行号: 1900 - 1902
     指令数: 1
     首指令: B.W		sub_8000E2C

 44. sub_8000E30
     地址: 0x8000E30
     行号: 1909 - 1911
     指令数: 1
     首指令: B.W		sub_8000E30

 45. sub_8000E34
     地址: 0x8000E34
     行号: 1918 - 1920
     指令数: 1
     首指令: B.W		sub_8000E34

 46. sub_8000E38
     地址: 0x8000E38
     行号: 1927 - 1929
     指令数: 1
     首指令: B.W		sub_8000E38

 47. sub_8000E3C
     地址: 0x8000E3C
     行号: 1936 - 1938
     指令数: 1
     首指令: B.W		sub_8000E3C

 48. sub_8000E40
     地址: 0x8000E40
     行号: 1945 - 1947
     指令数: 1
     首指令: B.W		sub_8000E40

 49. sub_8000E44
     地址: 0x8000E44
     行号: 1954 - 1956
     指令数: 1
     首指令: B.W		sub_8000E44

 50. sub_8000E48
     地址: 0x8000E48
     行号: 1963 - 1965
     指令数: 1
     首指令: B.W		sub_8000E48

 51. sub_8000E4C
     地址: 0x8000E4C
     行号: 1972 - 1974
     指令数: 1
     首指令: B.W		sub_8000E4C

 52. sub_8000E50
     地址: 0x8000E50
     行号: 1981 - 1983
     指令数: 1
     首指令: B.W		sub_8000E50

 53. sub_8000E54
     地址: 0x8000E54
     行号: 1990 - 1992
     指令数: 1
     首指令: B.W		sub_8000E54

 54. sub_8000E58
     地址: 0x8000E58
     行号: 1999 - 2001
     指令数: 1
     首指令: B.W		sub_8000E58

 55. sub_8000E5C
     地址: 0x8000E5C
     行号: 2008 - 2010
     指令数: 1
     首指令: B.W		sub_8000E5C

 56. sub_8000E60
     地址: 0x8000E60
     行号: 2017 - 2019
     指令数: 1
     首指令: B.W		sub_8000E60

 57. sub_8000E64
     地址: 0x8000E64
     行号: 2026 - 2028
     指令数: 1
     首指令: B.W		sub_8000E64

 58. sub_8000E68
     地址: 0x8000E68
     行号: 2035 - 2037
     指令数: 1
     首指令: B.W		sub_8000E68

 59. sub_8000E6C
     地址: 0x8000E6C
     行号: 2044 - 2046
     指令数: 1
     首指令: B.W		sub_8000E6C

 60. sub_8000E70
     地址: 0x8000E70
     行号: 2053 - 2055
     指令数: 1
     首指令: B.W		sub_8000E70

 61. sub_8000E74
     地址: 0x8000E74
     行号: 2062 - 2064
     指令数: 1
     首指令: B.W		sub_8000E74

 62. sub_8000E78
     地址: 0x8000E78
     行号: 2071 - 2073
     指令数: 1
     首指令: B.W		sub_8000E78

 63. sub_8000E7C
     地址: 0x8000E7C
     行号: 2080 - 2082
     指令数: 1
     首指令: B.W		sub_8000E7C

 64. sub_8000E80
     地址: 0x8000E80
     行号: 2089 - 2091
     指令数: 1
     首指令: B.W		sub_8000E80

 65. sub_8000E84
     地址: 0x8000E84
     行号: 2098 - 2100
     指令数: 1
     首指令: B.W		sub_8000E84

 66. sub_8000E88
     地址: 0x8000E88
     行号: 2107 - 2109
     指令数: 1
     首指令: B.W		sub_8000E88

 67. sub_8000E8C
     地址: 0x8000E8C
     行号: 2116 - 2118
     指令数: 1
     首指令: B.W		sub_8000E8C

 68. sub_8000E90
     地址: 0x8000E90
     行号: 2125 - 2127
     指令数: 1
     首指令: B.W		sub_8000E90

 69. sub_8000E94
     地址: 0x8000E94
     行号: 2134 - 2136
     指令数: 1
     首指令: B.W		sub_8000E94

 70. sub_8000E98
     地址: 0x8000E98
     行号: 2143 - 2145
     指令数: 1
     首指令: B.W		sub_8000E98

 71. sub_8000E9C
     地址: 0x8000E9C
     行号: 2152 - 2154
     指令数: 1
     首指令: B.W		sub_8000E9C

 72. sub_8000EA0
     地址: 0x8000EA0
     行号: 2161 - 2163
     指令数: 1
     首指令: B.W		sub_8000EA0

 73. sub_8000EA4
     地址: 0x8000EA4
     行号: 2170 - 2172
     指令数: 1
     首指令: B.W		sub_8000EA4

 74. sub_8000EA8
     地址: 0x8000EA8
     行号: 2179 - 2181
     指令数: 1
     首指令: B.W		sub_8000EA8

 75. sub_8000EAC
     地址: 0x8000EAC
     行号: 2188 - 2190
     指令数: 1
     首指令: B.W		sub_8000EAC

 76. sub_8000EB0
     地址: 0x8000EB0
     行号: 2197 - 2199
     指令数: 1
     首指令: B.W		sub_8000EB0

 77. sub_8000EB4
     地址: 0x8000EB4
     行号: 2206 - 2208
     指令数: 1
     首指令: B.W		sub_8000EB4

 78. sub_8000EB8
     地址: 0x8000EB8
     行号: 2215 - 2217
     指令数: 1
     首指令: B.W		sub_8000EB8

 79. sub_8000EBC
     地址: 0x8000EBC
     行号: 2224 - 2226
     指令数: 1
     首指令: B.W		sub_8000EBC

 80. sub_8000EC0
     地址: 0x8000EC0
     行号: 2233 - 2235
     指令数: 1
     首指令: B.W		sub_8000EC0

 81. sub_8000EC4
     地址: 0x8000EC4
     行号: 2242 - 2244
     指令数: 1
     首指令: B.W		sub_8000EC4

 82. sub_8000EC8
     地址: 0x8000EC8
     行号: 2251 - 2253
     指令数: 1
     首指令: B.W		sub_8000EC8

 83. sub_8000ECC
     地址: 0x8000ECC
     行号: 2260 - 2262
     指令数: 1
     首指令: B.W		sub_8000ECC

 84. sub_8000ED0
     地址: 0x8000ED0
     行号: 2269 - 2271
     指令数: 1
     首指令: B.W		sub_8000ED0

 85. sub_8000ED4
     地址: 0x8000ED4
     行号: 2278 - 2280
     指令数: 1
     首指令: B.W		sub_8000ED4

 86. sub_8000ED8
     地址: 0x8000ED8
     行号: 2287 - 2289
     指令数: 1
     首指令: B.W		sub_8000ED8

 87. sub_8000EDC
     地址: 0x8000EDC
     行号: 2296 - 2298
     指令数: 1
     首指令: B.W		sub_8000EDC

 88. sub_8000EE0
     地址: 0x8000EE0
     行号: 2305 - 2307
     指令数: 1
     首指令: B.W		sub_8000EE0

 89. sub_8000EE4
     地址: 0x8000EE4
     行号: 2314 - 2316
     指令数: 1
     首指令: B.W		sub_8000EE4

 90. sub_8000EE8
     地址: 0x8000EE8
     行号: 2323 - 2325
     指令数: 1
     首指令: B.W		sub_8000EE8

 91. sub_8000EEC
     地址: 0x8000EEC
     行号: 2332 - 2334
     指令数: 1
     首指令: B.W		sub_8000EEC

 92. sub_8000EF0
     地址: 0x8000EF0
     行号: 2341 - 2343
     指令数: 1
     首指令: B.W		sub_8000EF0

 93. sub_8000EF4
     地址: 0x8000EF4
     行号: 2350 - 2352
     指令数: 1
     首指令: B.W		sub_8000EF4

 94. sub_8000EF8
     地址: 0x8000EF8
     行号: 2359 - 2361
     指令数: 1
     首指令: B.W		sub_8000EF8

 95. sub_8000EFC
     地址: 0x8000EFC
     行号: 2368 - 2370
     指令数: 1
     首指令: B.W		sub_8000EFC

 96. sub_8000F00
     地址: 0x8000F00
     行号: 2377 - 2379
     指令数: 1
     首指令: B.W		sub_8000F00

 97. sub_8000F04
     地址: 0x8000F04
     行号: 2386 - 2388
     指令数: 1
     首指令: B.W		sub_8000F04

 98. sub_8000F08
     地址: 0x8000F08
     行号: 2395 - 2397
     指令数: 1
     首指令: B.W		sub_8000F08

 99. sub_8000F0C
     地址: 0x8000F0C
     行号: 2404 - 2406
     指令数: 1
     首指令: B.W		sub_8000F0C

100. sub_8000F10
     地址: 0x8000F10
     行号: 2413 - 2415
     指令数: 1
     首指令: B.W		sub_8000F10

101. sub_8000F14
     地址: 0x8000F14
     行号: 2422 - 2424
     指令数: 1
     首指令: B.W		sub_8000F14

102. sub_8000F18
     地址: 0x8000F18
     行号: 2431 - 2433
     指令数: 1
     首指令: B.W		sub_8000F18

103. sub_8000F1C
     地址: 0x8000F1C
     行号: 2440 - 2442
     指令数: 1
     首指令: B.W		sub_8000F1C

104. sub_8000F20
     地址: 0x8000F20
     行号: 2449 - 2451
     指令数: 1
     首指令: B.W		sub_8000F20

105. sub_8000F24
     地址: 0x8000F24
     行号: 2458 - 2460
     指令数: 1
     首指令: B.W		sub_8000F24

106. sub_8002384
     地址: 0x8002384
     行号: 2676 - 2692
     指令数: 12
     首指令: UXTB		R0, R0

107. sub_80023A0
     地址: 0x80023A0
     行号: 2698 - 2722
     指令数: 22
     首指令: LDR.W		R1, dword_8002D44

108. sub_80023E0
     地址: 0x80023E0
     行号: 2728 - 2850
     指令数: 115
     首指令: MOVS		R1, #0

109. sub_8002520
     地址: 0x8002520
     行号: 2859 - 3007
     指令数: 135
     首指令: PUSH		{R4,LR}

110. sub_8002674
     地址: 0x8002674
     行号: 3015 - 3243
     指令数: 214
     首指令: PUSH.W		{R4-R10,LR}

111. sub_80028BC
     地址: 0x80028BC
     行号: 3252 - 3668
     指令数: 385
     首指令: PUSH.W		{R4-R10,LR}

112. sub_8002D60
     地址: 0x8002D60
     行号: 3682 - 3922
     指令数: 224
     首指令: PUSH		{R3-R7,LR}

113. sub_800302C
     地址: 0x800302C
     行号: 3938 - 3994
     指令数: 49
     首指令: PUSH		{R4,LR}

114. sub_800315C
     地址: 0x800315C
     行号: 4046 - 4458
     指令数: 382
     首指令: PUSH		{R4,R5}

115. sub_80035A8
     地址: 0x80035A8
     行号: 4471 - 5028
     指令数: 512
     首指令: PUSH.W		{R4-R8,LR}

116. sub_8003B3A
     地址: 0x8003B3A
     行号: 5034 - 5038
     指令数: 3
     首指令: LDR.W		R1, dword_8003BF8

117. sub_8003B42
     地址: 0x8003B42
     行号: 5044 - 5048
     指令数: 3
     首指令: LDR.W		R0, dword_8003BF8

118. sub_8003BFC
     地址: 0x8003BFC
     行号: 5100 - 5170
     指令数: 67
     首指令: PUSH		{R4-R6,LR}

119. sub_8003CB0
     地址: 0x8003CB0
     行号: 5179 - 5195
     指令数: 15
     首指令: PUSH		{R7,LR}

120. sub_8003CD2
     地址: 0x8003CD2
     行号: 5201 - 5205
     指令数: 3
     首指令: LDR		R1, dword_8003E84

121. sub_8003CD8
     地址: 0x8003CD8
     行号: 5211 - 5215
     指令数: 3
     首指令: LDR		R1, dword_8003E8C

122. sub_8003CDE
     地址: 0x8003CDE
     行号: 5221 - 5257
     指令数: 28
     首指令: PUSH		{R4}

123. sub_8003D10
     地址: 0x8003D10
     行号: 5263 - 5299
     指令数: 28
     首指令: PUSH		{R4}

124. sub_8003D42
     地址: 0x8003D42
     行号: 5305 - 5447
     指令数: 122
     首指令: LDR		R0, dword_8003E7C

125. sub_8003E28
     地址: 0x8003E28
     行号: 5453 - 5499
     指令数: 42
     首指令: PUSH		{R7,LR}

126. sub_8003EAC
     地址: 0x8003EAC
     行号: 5520 - 5540
     指令数: 18
     首指令: PUSH		{R7,LR}

127. sub_8003EDC
     地址: 0x8003EDC
     行号: 5546 - 5570
     指令数: 23
     首指令: PUSH		{R7,LR}

128. sub_8003F22
     地址: 0x8003F22
     行号: 5576 - 5600
     指令数: 20
     首指令: PUSH		{R4,LR}

129. sub_8003F52
     地址: 0x8003F52
     行号: 5606 - 5611
     指令数: 4
     首指令: MOVS		R0, #0

130. sub_8003F5C
     地址: 0x8003F5C
     行号: 5617 - 5661
     指令数: 40
     首指令: PUSH		{R7,LR}

131. sub_8003FC8
     地址: 0x8003FC8
     行号: 5667 - 5672
     指令数: 4
     首指令: LDR		R1, dword_800426C

132. sub_8003FD2
     地址: 0x8003FD2
     行号: 5678 - 5689
     指令数: 10
     首指令: LDR		R2, dword_8004270

133. sub_8003FE6
     地址: 0x8003FE6
     行号: 5695 - 5700
     指令数: 4
     首指令: LDR		R1, dword_8004270

134. sub_8003FEE
     地址: 0x8003FEE
     行号: 5706 - 5712
     指令数: 5
     首指令: PUSH		{R7,LR}

135. sub_8003FFA
     地址: 0x8003FFA
     行号: 5718 - 5742
     指令数: 22
     首指令: PUSH		{R7,LR}

136. sub_8004024
     地址: 0x8004024
     行号: 5748 - 5912
     指令数: 126
     首指令: PUSH		{R4,LR}

137. sub_80040FE
     地址: 0x80040FE
     行号: 5918 - 6041
     指令数: 117
     首指令: PUSH		{R7,LR}

138. sub_8004208
     地址: 0x8004208
     行号: 6047 - 6071
     指令数: 21
     首指令: PUSH		{R7,LR}

139. sub_8004284
     地址: 0x8004284
     行号: 6099 - 6135
     指令数: 30
     首指令: MOVS.W		R12, #0xFF

140. sub_8004348
     地址: 0x8004348
     行号: 6206 - 6382
     指令数: 156
     首指令: PUSH		{R4,R5,R7,LR}

141. sub_80044F4
     地址: 0x80044F4
     行号: 6400 - 6459
     指令数: 50
     首指令: MOV.W		R12, R1,ROR#31

142. sub_8004584
     地址: 0x8004584
     行号: 6465 - 6473
     指令数: 7
     首指令: MOVS		R1, R0

143. sub_8004598
     地址: 0x8004598
     行号: 6479 - 6482
     指令数: 2
     首指令: MOVS		R1, R0

144. sub_800459C
     地址: 0x800459C
     行号: 6488 - 6500
     指令数: 10
     首指令: BEQ		locret_80045B2

145. sub_80045B4
     地址: 0x80045B4
     行号: 6506 - 6680
     指令数: 152
     首指令: PUSH		{R4-R7}

146. sub_8004734
     地址: 0x8004734
     行号: 6686 - 6692
     指令数: 5
     首指令: VMOV		R0, R1,	D0

147. sub_8004744
     地址: 0x8004744
     行号: 6698 - 6829
     指令数: 113
     首指令: MOVW		R12, #0x7FF

148. sub_8004834
     地址: 0x8004834
     行号: 6835 - 6948
     指令数: 98
     首指令: PUSH		{R4-R7}

149. sub_800491C
     地址: 0x800491C
     行号: 6954 - 6980
     指令数: 25
     首指令: PUSH		{R5-R7,LR}

150. sub_8004966
     地址: 0x8004966
     行号: 6986 - 7005
     指令数: 15
     首指令: LDR.W		R0, dword_80050E8

151. sub_8004984
     地址: 0x8004984
     行号: 7011 - 7030
     指令数: 15
     首指令: LDR.W		R0, dword_80050E8

152. sub_80049A2
     地址: 0x80049A2
     行号: 7036 - 7055
     指令数: 15
     首指令: LDR.W		R0, dword_80050E8

153. sub_80049C0
     地址: 0x80049C0
     行号: 7061 - 7110
     指令数: 42
     首指令: PUSH.W		{R4-R8,LR}

154. sub_8004A1E
     地址: 0x8004A1E
     行号: 7116 - 7180
     指令数: 62
     首指令: PUSH.W		{R3-R9,LR}

155. sub_8004AE4
     地址: 0x8004AE4
     行号: 7186 - 7272
     指令数: 79
     首指令: PUSH.W		{R3-R9,LR}

156. sub_8004BAC
     地址: 0x8004BAC
     行号: 7278 - 7398
     指令数: 117
     首指令: PUSH.W		{R3-R9,LR}

157. sub_8004CE4
     地址: 0x8004CE4
     行号: 7404 - 7424
     指令数: 16
     首指令: PUSH		{R7,LR}

158. sub_8004D08
     地址: 0x8004D08
     行号: 7430 - 7554
     指令数: 112
     首指令: PUSH.W		{R4-R8,LR}

159. sub_8004E1E
     地址: 0x8004E1E
     行号: 7560 - 7576
     指令数: 14
     首指令: PUSH		{R4,LR}

160. sub_8004E3A
     地址: 0x8004E3A
     行号: 7582 - 7694
     指令数: 99
     首指令: PUSH		{R7,LR}

161. sub_8004F04
     地址: 0x8004F04
     行号: 7700 - 7754
     指令数: 53
     首指令: PUSH		{R7,LR}

162. sub_8004F80
     地址: 0x8004F80
     行号: 7760 - 7978
     指令数: 186
     首指令: PUSH		{R4,LR}

163. sub_800516C
     地址: 0x800516C
     行号: 8021 - 8062
     指令数: 35
     首指令: PUSH		{R4,LR}

164. sub_80051CA
     地址: 0x80051CA
     行号: 8068 - 8074
     指令数: 5
     首指令: UXTB		R1, R1

165. sub_80051D6
     地址: 0x80051D6
     行号: 8080 - 8088
     指令数: 7
     首指令: UXTB		R0, R0

166. sub_80051EA
     地址: 0x80051EA
     行号: 8094 - 8104
     指令数: 9
     首指令: MOVS		R1, #0

167. sub_80051FC
     地址: 0x80051FC
     行号: 8110 - 8129
     指令数: 18
     首指令: LDRB		R2, [R1]

168. sub_8005228
     地址: 0x8005228
     行号: 8135 - 8155
     指令数: 16
     首指令: UXTB		R2, R2

169. sub_8005244
     地址: 0x8005244
     行号: 8161 - 8166
     指令数: 4
     首指令: LDR		R1, [R0,#8]

170. sub_800524E
     地址: 0x800524E
     行号: 8172 - 8187
     指令数: 11
     首指令: MOVS		R1, R0

171. sub_8005262
     地址: 0x8005262
     行号: 8193 - 8198
     指令数: 4
     首指令: LDR		R1, [R0,#8]

172. sub_800526C
     地址: 0x800526C
     行号: 8204 - 8219
     指令数: 11
     首指令: MOVS		R1, R0

173. sub_8005280
     地址: 0x8005280
     行号: 8225 - 8565
     指令数: 267
     首指令: PUSH		{R4}

174. sub_8005492
     地址: 0x8005492
     行号: 8571 - 8600
     指令数: 25
     首指令: UXTB		R1, R1

175. sub_80054CA
     地址: 0x80054CA
     行号: 8606 - 8612
     指令数: 5
     首指令: UXTB		R1, R1

176. sub_80054D6
     地址: 0x80054D6
     行号: 8618 - 8622
     指令数: 3
     首指令: LDR		R0, dword_80054FC

177. sub_8005500
     地址: 0x8005500
     行号: 8638 - 8657
     指令数: 15
     首指令: MOVS		R2, R0

178. sub_800551A
     地址: 0x800551A
     行号: 8663 - 8668
     指令数: 4
     首指令: LDR		R2, [R0]

179. sub_8005524
     地址: 0x8005524
     行号: 8676 - 8723
     指令数: 42
     首指令: BFI.W		R2, R2,	#8, #8

180. sub_800558C
     地址: 0x800558C
     行号: 8731 - 8773
     指令数: 39
     首指令: LDR.W		R0, dword_80059CC

181. sub_80055F6
     地址: 0x80055F6
     行号: 8779 - 8808
     指令数: 25
     首指令: PUSH		{R4}

182. sub_800562A
     地址: 0x800562A
     行号: 8814 - 8849
     指令数: 28
     首指令: PUSH		{R3-R5,LR}

183. sub_8005660
     地址: 0x8005660
     行号: 8855 - 8888
     指令数: 29
     首指令: PUSH		{R4}

184. sub_80056A2
     地址: 0x80056A2
     行号: 8894 - 8927
     指令数: 29
     首指令: PUSH		{R4}

185. sub_80056E4
     地址: 0x80056E4
     行号: 8933 - 8998
     指令数: 52
     首指令: UXTB		R0, R0

186. sub_800575E
     地址: 0x800575E
     行号: 9004 - 9012
     指令数: 7
     首指令: UXTB		R0, R0

187. sub_8005772
     地址: 0x8005772
     行号: 9018 - 9026
     指令数: 7
     首指令: UXTB		R0, R0

188. sub_8005786
     地址: 0x8005786
     行号: 9032 - 9040
     指令数: 7
     首指令: UXTB		R0, R0

189. sub_800579A
     地址: 0x800579A
     行号: 9046 - 9063
     指令数: 16
     首指令: UXTB		R0, R0

190. sub_80057C0
     地址: 0x80057C0
     行号: 9069 - 9130
     指令数: 55
     首指令: PUSH		{R4}

191. sub_8005836
     地址: 0x8005836
     行号: 9136 - 9144
     指令数: 7
     首指令: UXTB		R0, R0

192. sub_8005846
     地址: 0x8005846
     行号: 9150 - 9156
     指令数: 5
     首指令: LDR		R0, dword_80059D0

193. sub_8005852
     地址: 0x8005852
     行号: 9162 - 9326
     指令数: 142
     首指令: PUSH.W		{R3-R11,LR}

194. sub_8005998
     地址: 0x8005998
     行号: 9332 - 9353
     指令数: 17
     首指令: UXTB		R0, R0

195. sub_80059BA
     地址: 0x80059BA
     行号: 9359 - 9367
     指令数: 7
     首指令: UXTB		R0, R0

196. sub_8005A0C
     地址: 0x8005A0C
     行号: 9391 - 9396
     指令数: 4
     首指令: LDR		R0, dword_8005B00

197. sub_8005A18
     地址: 0x8005A18
     行号: 9404 - 9418
     指令数: 12
     首指令: SXTB		R0, R0

198. sub_8005A32
     地址: 0x8005A32
     行号: 9424 - 9440
     指令数: 14
     首指令: SXTB		R0, R0

199. sub_8005A54
     地址: 0x8005A54
     行号: 9446 - 9469
     指令数: 19
     首指令: PUSH		{R4}

200. sub_8005A7A
     地址: 0x8005A7A
     行号: 9475 - 9513
     指令数: 31
     首指令: PUSH		{R4-R6}

201. sub_8005AB8
     地址: 0x8005AB8
     行号: 9519 - 9538
     指令数: 18
     首指令: PUSH		{R3-R7,LR}

202. sub_8005AE4
     地址: 0x8005AE4
     行号: 9544 - 9551
     指令数: 6
     首指令: PUSH		{R4,LR}

203. sub_8005AF2
     地址: 0x8005AF2
     行号: 9557 - 9564
     指令数: 6
     首指令: LDR		R2, dword_8005B14

204. sub_8005B1C
     地址: 0x8005B1C
     行号: 9579 - 9602
     指令数: 19
     首指令: PUSH		{R4}

205. sub_8005B42
     地址: 0x8005B42
     行号: 9608 - 9635
     指令数: 23
     首指令: PUSH		{R4,LR}

206. sub_8005B72
     地址: 0x8005B72
     行号: 9641 - 9650
     指令数: 8
     首指令: PUSH		{R7,LR}

207. sub_8005B84
     地址: 0x8005B84
     行号: 9656 - 9664
     指令数: 7
     首指令: PUSH		{R7,LR}

208. sub_8005BB4
     地址: 0x8005BB4
     行号: 9678 - 9684
     指令数: 5
     首指令: UXTB		R1, R1

209. sub_8005BC0
     地址: 0x8005BC0
     行号: 9690 - 9697
     指令数: 6
     首指令: STR		R1, [R0,#0x2C]

210. sub_8005BCE
     地址: 0x8005BCE
     行号: 9703 - 9709
     指令数: 5
     首指令: UXTB		R1, R1

211. sub_8005BDA
     地址: 0x8005BDA
     行号: 9715 - 9735
     指令数: 16
     首指令: UXTB		R2, R2

212. sub_8005BF6
     地址: 0x8005BF6
     行号: 9741 - 9759
     指令数: 14
     首指令: MOVS		R2, R0

213. sub_8005C0E
     地址: 0x8005C0E
     行号: 9765 - 9770
     指令数: 4
     首指令: LDR		R2, [R0,#0x10]

214. sub_8005C18
     地址: 0x8005C18
     行号: 9778 - 9903
     指令数: 109
     首指令: PUSH		{R4-R7}

215. sub_8005CEA
     地址: 0x8005CEA
     行号: 9909 - 9921
     指令数: 11
     首指令: MOVW		R1, #0xFFFF

216. sub_8005D02
     地址: 0x8005D02
     行号: 9927 - 9948
     指令数: 17
     首指令: MOVS		R2, R0

217. sub_8005D20
     地址: 0x8005D20
     行号: 9954 - 9973
     指令数: 15
     首指令: MOVS		R2, R0

218. sub_8005D3A
     地址: 0x8005D3A
     行号: 9979 - 9994
     指令数: 11
     首指令: UXTB		R2, R2

219. sub_8005D4C
     地址: 0x8005D4C
     行号: 10000 - 10036
     指令数: 34
     首指令: PUSH		{R4-R7}

220. sub_8005DA4
     地址: 0x8005DA4
     行号: 10045 - 10050
     指令数: 4
     首指令: PUSH		{R7,LR}

221. sub_8005DAE
     地址: 0x8005DAE
     行号: 10056 - 10083
     指令数: 24
     首指令: PUSH		{R3-R7,LR}

222. sub_8005DE8
     地址: 0x8005DE8
     行号: 10091 - 10096
     指令数: 4
     首指令: PUSH		{R7,LR}

223. sub_8005DF2
     地址: 0x8005DF2
     行号: 10102 - 10118
     指令数: 15
     首指令: PUSH		{R3-R7,LR}

224. sub_8005E18
     地址: 0x8005E18
     行号: 10126 - 10132
     指令数: 5
     首指令: PUSH		{R0-R4,LR}

225. sub_8005E24
     地址: 0x8005E24
     行号: 10138 - 10300
     指令数: 137
     首指令: PUSH		{R2-R6,LR}

226. sub_8005F6A
     地址: 0x8005F6A
     行号: 10306 - 10313
     指令数: 6
     首指令: ASRS		R0, R0,	#8

227. sub_8005F7A
     地址: 0x8005F7A
     行号: 10319 - 10424
     指令数: 90
     首指令: PUSH.W		{R3-R9,LR}

228. sub_8006056
     地址: 0x8006056
     行号: 10430 - 10471
     指令数: 37
     首指令: PUSH		{R3-R5,LR}

229. sub_80060B8
     地址: 0x80060B8
     行号: 10477 - 10506
     指令数: 23
     首指令: PUSH		{R4,LR}

230. sub_80060E8
     地址: 0x80060E8
     行号: 10512 - 10521
     指令数: 8
     首指令: PUSH		{R4,LR}

231. sub_80060FE
     地址: 0x80060FE
     行号: 10527 - 10540
     指令数: 12
     首指令: PUSH		{R3-R5,LR}

232. sub_800611A
     地址: 0x800611A
     行号: 10546 - 10715
     指令数: 145
     首指令: PUSH		{R4-R6,LR}

233. sub_800624E
     地址: 0x800624E
     行号: 10721 - 10854
     指令数: 113
     首指令: PUSH.W		{R3-R11,LR}

234. sub_8006362
     地址: 0x8006362
     行号: 10860 - 10937
     指令数: 66
     首指令: PUSH.W		{R3-R11,LR}

235. sub_800640C
     地址: 0x800640C
     行号: 10943 - 10955
     指令数: 11
     首指令: PUSH		{R3-R5,LR}

236. sub_8006426
     地址: 0x8006426
     行号: 10961 - 10982
     指令数: 17
     首指令: PUSH		{R4-R6,LR}

237. sub_8006448
     地址: 0x8006448
     行号: 10988 - 11021
     指令数: 31
     首指令: PUSH		{R4,LR}

238. sub_8006492
     地址: 0x8006492
     行号: 11027 - 11036
     指令数: 8
     首指令: PUSH		{R3-R5,LR}

239. sub_80064C0
     地址: 0x80064C0
     行号: 11054 - 11090
     指令数: 32
     首指令: PUSH		{R3-R5,LR}

240. sub_800650A
     地址: 0x800650A
     行号: 11096 - 11123
     指令数: 23
     首指令: PUSH		{R4,LR}

241. sub_8006538
     地址: 0x8006538
     行号: 11129 - 11148
     指令数: 15
     首指令: PUSH		{R7,LR}

242. sub_8006554
     地址: 0x8006554
     行号: 11154 - 11366
     指令数: 187
     首指令: PUSH		{R3-R7,LR}

243. sub_80066D8
     地址: 0x80066D8
     行号: 11372 - 11399
     指令数: 22
     首指令: PUSH		{R4,LR}

244. sub_8006734
     地址: 0x8006734
     行号: 11418 - 11421
     指令数: 2
     首指令: MOVS		R0, #1

245. sub_8006738
     地址: 0x8006738
     行号: 11427 - 11434
     指令数: 6
     首指令: PUSH		{R7,LR}

246. sub_800674C
     地址: 0x800674C
     行号: 11440 - 11460
     指令数: 17
     首指令: LSRS		R0, R0,	#0x15

247. sub_8006778
     地址: 0x8006778
     行号: 11466 - 11530
     指令数: 53
     首指令: CBZ		R2, locret_80067D4

248. sub_80067F0
     地址: 0x80067F0
     行号: 11538 - 11588
     指令数: 41
     首指令: PUSH.W		{R3-R9,LR}

249. sub_800684E
     地址: 0x800684E
     行号: 11594 - 11618
     指令数: 20
     首指令: PUSH		{R3-R5,LR}

250. sub_800687A
     地址: 0x800687A
     行号: 11624 - 11658
     指令数: 30
     首指令: PUSH.W		{R3-R9,LR}

251. sub_80068C6
     地址: 0x80068C6
     行号: 11664 - 11698
     指令数: 30
     首指令: PUSH.W		{R3-R9,LR}

252. sub_8006912
     地址: 0x8006912
     行号: 11704 - 11730
     指令数: 22
     首指令: PUSH		{R4-R6,LR}

253. sub_8006942
     地址: 0x8006942
     行号: 11736 - 11761
     指令数: 21
     首指令: PUSH		{R3-R5,LR}

254. sub_8006970
     地址: 0x8006970
     行号: 11767 - 11786
     指令数: 17
     首指令: PUSH		{R3-R5,LR}

255. sub_800699C
     地址: 0x800699C
     行号: 11795 - 11818
     指令数: 22
     首指令: PUSH		{R4-R6,LR}

256. sub_80069CE
     地址: 0x80069CE
     行号: 11824 - 12005
     指令数: 164
     首指令: PUSH		{R3-R7,LR}

257. sub_8006B3C
     地址: 0x8006B3C
     行号: 12011 - 12048
     指令数: 35
     首指令: PUSH		{R4,R5}

258. sub_8006B8A
     地址: 0x8006B8A
     行号: 12054 - 12070
     指令数: 14
     首指令: LDR		R1, dword_8006BC4

259. sub_8006BA6
     地址: 0x8006BA6
     行号: 12076 - 12092
     指令数: 14
     首指令: LDR		R1, dword_8006BC4

260. sub_8006BD4
     地址: 0x8006BD4
     行号: 12104 - 12178
     指令数: 66
     首指令: PUSH		{R4-R6,LR}

261. sub_8006C62
     地址: 0x8006C62
     行号: 12184 - 12215
     指令数: 27
     首指令: PUSH		{R7,LR}

262. sub_8006C9A
     地址: 0x8006C9A
     行号: 12221 - 12240
     指令数: 18
     首指令: PUSH		{R7,LR}

263. sub_8006CCA
     地址: 0x8006CCA
     行号: 12246 - 12259
     指令数: 12
     首指令: PUSH		{LR}

264. sub_8006D08
     地址: 0x8006D08
     行号: 12275 - 12310
     指令数: 30
     首指令: PUSH		{R3-R5,LR}

265. sub_8006D46
     地址: 0x8006D46
     行号: 12316 - 12471
     指令数: 145
     首指令: PUSH		{R4-R7,LR}

266. sub_8006EB0
     地址: 0x8006EB0
     行号: 12477 - 12511
     指令数: 32
     首指令: PUSH		{R3-R5,LR}

267. sub_8006F14
     地址: 0x8006F14
     行号: 12523 - 12529
     指令数: 2
     首指令: LDR		R0, dword_8006F1C

268. sub_8006F20
     地址: 0x8006F20
     行号: 12538 - 12573
     指令数: 29
     首指令: LSLS		R2, R0,	#0x1E

269. sub_8006F64
     地址: 0x8006F64
     行号: 12581 - 12750
     指令数: 155
     首指令: PUSH		{R7,LR}

270. sub_8007100
     地址: 0x8007100
     行号: 12756 - 12927
     指令数: 157
     首指令: PUSH		{R7,LR}

271. sub_80072A4
     地址: 0x80072A4
     行号: 12933 - 13111
     指令数: 165
     首指令: PUSH		{R4,LR}

272. sub_800745C
     地址: 0x800745C
     行号: 13117 - 13303
     指令数: 171
     首指令: PUSH		{R7,LR}

273. sub_800762A
     地址: 0x800762A
     行号: 13309 - 13316
     指令数: 6
     首指令: PUSH		{R7,LR}

274. sub_800763A
     地址: 0x800763A
     行号: 13322 - 13570
     指令数: 229
     首指令: PUSH.W		{R4-R8,LR}

275. sub_8007880
     地址: 0x8007880
     行号: 13576 - 13580
     指令数: 3
     首指令: MOVS		R1, R0

276. sub_8007886
     地址: 0x8007886
     行号: 13586 - 13638
     指令数: 48
     首指令: PUSH		{R3-R5,LR}

277. sub_80078F0
     地址: 0x80078F0
     行号: 13644 - 13672
     指令数: 24
     首指令: PUSH		{R4,LR}

278. sub_8007924
     地址: 0x8007924
     行号: 13678 - 13691
     指令数: 12
     首指令: PUSH		{R4,LR}

279. sub_8007942
     地址: 0x8007942
     行号: 13697 - 13715
     指令数: 17
     首指令: PUSH		{R4,LR}

280. sub_800796C
     地址: 0x800796C
     行号: 13721 - 13749
     指令数: 22
     首指令: PUSH		{R4-R6,LR}

281. sub_8007998
     地址: 0x8007998
     行号: 13755 - 13764
     指令数: 8
     首指令: PUSH		{R4,LR}

282. sub_80079AA
     地址: 0x80079AA
     行号: 13770 - 13785
     指令数: 14
     首指令: PUSH		{R4,LR}

283. sub_80079CC
     地址: 0x80079CC
     行号: 13791 - 13806
     指令数: 14
     首指令: PUSH		{R4,LR}

284. sub_80079EE
     地址: 0x80079EE
     行号: 13812 - 13819
     指令数: 6
     首指令: LDR		R2, off_8007A10

285. sub_8007A64
     地址: 0x8007A64
     行号: 13852 - 13854
     指令数: 1
     首指令: BX		LR

286. sub_8007A66
     地址: 0x8007A66
     行号: 13860 - 13892
     指令数: 27
     首指令: PUSH		{R4-R6,LR}

287. sub_8007A9A
     地址: 0x8007A9A
     行号: 13898 - 13932
     指令数: 29
     首指令: PUSH		{R4-R6,LR}

288. sub_8007AD2
     地址: 0x8007AD2
     行号: 13938 - 13953
     指令数: 11
     首指令: MOVS		R1, R0

289. sub_8007AE4
     地址: 0x8007AE4
     行号: 13959 - 13985
     指令数: 21
     首指令: MOVS		R1, R0

290. sub_8007B08
     地址: 0x8007B08
     行号: 13991 - 14013
     指令数: 18
     首指令: LDRH		R1, [R0,#4]

291. sub_8007B28
     地址: 0x8007B28
     行号: 14019 - 14051
     指令数: 25
     首指令: ADD.W		R3, R0,	#1

292. sub_8007B60
     地址: 0x8007B60
     行号: 14059 - 14297
     指令数: 222
     首指令: PUSH.W		{R3-R7,R10,R11,LR}

293. sub_8007E48
     地址: 0x8007E48
     行号: 14337 - 14340
     指令数: 2
     首指令: MOVS		R0, #2

294. sub_8007E4C
     地址: 0x8007E4C
     行号: 14346 - 14385
     指令数: 32
     首指令: PUSH		{R4-R6,LR}

295. sub_8007E8A
     地址: 0x8007E8A
     行号: 14391 - 14437
     指令数: 42
     首指令: PUSH		{R4,LR}

296. sub_8007F04
     地址: 0x8007F04
     行号: 14450 - 14468
     指令数: 17
     首指令: MOVS		R1, #0

297. sub_8007F26
     地址: 0x8007F26
     行号: 14474 - 14607
     指令数: 119
     首指令: LDR		R2, [R0,#0x1C]

298. sub_8008038
     地址: 0x8008038
     行号: 14613 - 14619
     指令数: 5
     首指令: UXTB		R1, R1

299. sub_8008044
     地址: 0x8008044
     行号: 14625 - 14631
     指令数: 5
     首指令: UXTB		R1, R1

300. sub_8008050
     地址: 0x8008050
     行号: 14637 - 14643
     指令数: 5
     首指令: UXTB		R1, R1

301. sub_800805C
     地址: 0x800805C
     行号: 14649 - 14653
     指令数: 3
     首指令: UXTH		R1, R1

302. sub_8008062
     地址: 0x8008062
     行号: 14659 - 14663
     指令数: 3
     首指令: LDR		R0, [R0,#0xC]

303. sub_8008068
     地址: 0x8008068
     行号: 14669 - 14687
     指令数: 14
     首指令: MOVS		R2, R0

304. sub_8008084
     地址: 0x8008084
     行号: 14701 - 14767
     指令数: 58
     首指令: PUSH.W		{R4-R9,LR}

305. sub_8008118
     地址: 0x8008118
     行号: 14779 - 14818
     指令数: 33
     首指令: UXTB		R1, R1

306. sub_8008160
     地址: 0x8008160
     行号: 14824 - 14830
     指令数: 5
     首指令: UXTB		R1, R1

307. sub_800816C
     地址: 0x800816C
     行号: 14836 - 14842
     指令数: 5
     首指令: UXTB		R1, R1

308. sub_8008178
     地址: 0x8008178
     行号: 14848 - 14854
     指令数: 5
     首指令: UXTB		R1, R1

309. sub_8008184
     地址: 0x8008184
     行号: 14860 - 14889
     指令数: 25
     首指令: PUSH		{R4,R5}

310. sub_80081B6
     地址: 0x80081B6
     行号: 14895 - 14912
     指令数: 13
     首指令: MOVS		R2, R0

311. sub_80081CE
     地址: 0x80081CE
     行号: 14918 - 14924
     指令数: 5
     首指令: UXTH		R1, R1

312. sub_80081D8
     地址: 0x80081D8
     行号: 14930 - 14934
     指令数: 3
     首指令: LDR		R0, [R0,#4]

313. sub_80081DE
     地址: 0x80081DE
     行号: 14940 - 14954
     指令数: 10
     首指令: MOVS		R2, R0

314. sub_80081EE
     地址: 0x80081EE
     行号: 14960 - 14964
     指令数: 3
     首指令: MVNS		R2, R1

315. sub_80081F4
     地址: 0x80081F4
     行号: 14970 - 15009
     指令数: 34
     首指令: PUSH		{R4,R5}

316. sub_8008238
     地址: 0x8008238
     行号: 15017 - 15040
     指令数: 19
     首指令: MOV.W		R12, #0x200000

317. sub_8008268
     地址: 0x8008268
     行号: 15048 - 15292
     指令数: 220
     首指令: PUSH.W		{R4-R8,LR}

318. sub_80084C0
     地址: 0x80084C0
     行号: 15300 - 15349
     指令数: 42
     首指令: PUSH.W		{R4-R8,LR}

319. sub_800851E
     地址: 0x800851E
     行号: 15355 - 15379
     指令数: 23
     首指令: PUSH		{R5-R7,LR}

320. sub_8008562
     地址: 0x8008562
     行号: 15385 - 15451
     指令数: 64
     首指令: PUSH.W		{R3-R9,LR}

321. sub_800862A
     地址: 0x800862A
     行号: 15457 - 15573
     指令数: 108
     首指令: PUSH.W		{R1-R11,LR}

322. sub_800873E
     地址: 0x800873E
     行号: 15579 - 16436
     指令数: 791
     首指令: PUSH.W		{R4-R11,LR}

323. sub_8008F0A
     地址: 0x8008F0A
     行号: 16442 - 16582
     指令数: 136
     首指令: PUSH.W		{R4-R11,LR}

324. sub_8009070
     地址: 0x8009070
     行号: 16590 - 16759
     指令数: 161
     首指令: PUSH.W		{R4-R11,LR}

325. sub_80091F0
     地址: 0x80091F0
     行号: 16769 - 16803
     指令数: 27
     首指令: PUSH		{R7,LR}

326. sub_8009234
     地址: 0x8009234
     行号: 16813 - 16841
     指令数: 27
     首指令: PUSH		{R7,LR}

327. sub_80092C8
     地址: 0x80092C8
     行号: 16866 - 17246
     指令数: 335
     首指令: PUSH		{R4-R6,LR}

328. sub_80095EC
     地址: 0x80095EC
     行号: 17292 - 17394
     指令数: 96
     首指令: PUSH.W		{R4-R8,LR}

329. sub_80096F8
     地址: 0x80096F8
     行号: 17400 - 17642
     指令数: 224
     首指令: PUSH		{R4,LR}

330. sub_800995A
     地址: 0x800995A
     行号: 17648 - 17670
     指令数: 17
     首指令: PUSH		{R7,LR}

331. sub_800997A
     地址: 0x800997A
     行号: 17676 - 17686
     指令数: 9
     首指令: PUSH		{R4-R6,LR}

332. sub_8009990
     地址: 0x8009990
     行号: 17692 - 17710
     指令数: 16
     首指令: PUSH		{R4-R6,LR}

333. sub_80099B4
     地址: 0x80099B4
     行号: 17716 - 17724
     指令数: 7
     首指令: LDR.W		R2, off_8009E4C

334. sub_80099C6
     地址: 0x80099C6
     行号: 17730 - 17740
     指令数: 9
     首指令: PUSH		{R4-R6,LR}

335. sub_80099DC
     地址: 0x80099DC
     行号: 17746 - 17784
     指令数: 34
     首指令: PUSH.W		{R4-R8,LR}

336. sub_8009A30
     地址: 0x8009A30
     行号: 17790 - 17800
     指令数: 9
     首指令: PUSH		{R4-R6,LR}

337. sub_8009A46
     地址: 0x8009A46
     行号: 17806 - 17844
     指令数: 34
     首指令: PUSH.W		{R4-R8,LR}

338. sub_8009A9A
     地址: 0x8009A9A
     行号: 17850 - 17915
     指令数: 58
     首指令: PUSH.W		{R4-R8,LR}

339. sub_8009B18
     地址: 0x8009B18
     行号: 17921 - 18059
     指令数: 120
     首指令: PUSH.W		{R4-R10,LR}

340. sub_8009C1C
     地址: 0x8009C1C
     行号: 18065 - 18175
     指令数: 97
     首指令: PUSH.W		{R4-R9,LR}

341. sub_8009CF0
     地址: 0x8009CF0
     行号: 18181 - 18221
     指令数: 37
     首指令: PUSH		{R4,R5,LR}

342. sub_8009D4C
     地址: 0x8009D4C
     行号: 18227 - 18337
     指令数: 97
     首指令: PUSH.W		{R4-R9,LR}

343. sub_8009E6C
     地址: 0x8009E6C
     行号: 18363 - 18456
     指令数: 80
     首指令: PUSH		{R4-R7,LR}

344. sub_8009F1C
     地址: 0x8009F1C
     行号: 18466 - 18469
     指令数: 2
     首指令: MOVS		R0, #1

345. sub_8009F20
     地址: 0x8009F20
     行号: 18475 - 18494
     指令数: 16
     首指令: PUSH		{R7,LR}

346. sub_8009F44
     地址: 0x8009F44
     行号: 18500 - 18559
     指令数: 51
     首指令: PUSH		{R3-R7,LR}

347. sub_8009FD0
     地址: 0x8009FD0
     行号: 18571 - 18619
     指令数: 40
     首指令: PUSH		{R3-R5,LR}

348. sub_800A034
     地址: 0x800A034
     行号: 18628 - 18696
     指令数: 58
     首指令: PUSH		{R4-R7,LR}

349. sub_800A0B4
     地址: 0x800A0B4
     行号: 18702 - 18770
     指令数: 58
     首指令: PUSH		{R4-R7,LR}

350. sub_800A134
     地址: 0x800A134
     行号: 18776 - 18851
     指令数: 63
     首指令: PUSH		{R4-R6,LR}

351. sub_800A1C8
     地址: 0x800A1C8
     行号: 18861 - 18927
     指令数: 56
     首指令: PUSH.W		{R4-R8,LR}

352. sub_800A246
     地址: 0x800A246
     行号: 18933 - 18955
     指令数: 18
     首指令: PUSH		{R4,LR}

353. sub_800A26C
     地址: 0x800A26C
     行号: 18963 - 19045
     指令数: 70
     首指令: PUSH		{R1,R4-R7,LR}

354. sub_800A30E
     地址: 0x800A30E
     行号: 19051 - 19169
     指令数: 107
     首指令: PUSH.W		{R4-R8,LR}

355. sub_800A412
     地址: 0x800A412
     行号: 19175 - 19301
     指令数: 114
     首指令: PUSH		{R3-R7,LR}

356. sub_800A51A
     地址: 0x800A51A
     行号: 19307 - 19329
     指令数: 18
     首指令: PUSH		{R4,LR}

357. sub_800A560
     地址: 0x800A560
     行号: 19347 - 19351
     指令数: 3
     首指令: PUSH		{R7,LR}

358. sub_800A568
     地址: 0x800A568
     行号: 19357 - 19568
     指令数: 194
     首指令: PUSH.W		{R4-R8,LR}

359. sub_800A70E
     地址: 0x800A70E
     行号: 19574 - 19717
     指令数: 125
     首指令: PUSH		{R3-R5,LR}

360. sub_800A814
     地址: 0x800A814
     行号: 19723 - 19737
     指令数: 10
     首指令: LDR		R0, dword_800A830

361. sub_800A824
     地址: 0x800A824
     行号: 19743 - 19748
     指令数: 4
     首指令: MOVS		R0, #1

362. sub_800A860
     地址: 0x800A860
     行号: 19768 - 19773
     指令数: 4
     首指令: MOVS		R0, #1

363. sub_800A86A
     地址: 0x800A86A
     行号: 19779 - 19784
     指令数: 4
     首指令: MOVS		R0, #5

364. sub_800A874
     地址: 0x800A874
     行号: 19790 - 19823
     指令数: 29
     首指令: PUSH		{R5-R7,LR}

365. sub_800A8BE
     地址: 0x800A8BE
     行号: 19829 - 19902
     指令数: 66
     首指令: PUSH		{R7,LR}

366. sub_800A96E
     地址: 0x800A96E
     行号: 19908 - 20048
     指令数: 125
     首指令: PUSH		{R7,LR}

367. sub_800AA8C
     地址: 0x800AA8C
     行号: 20054 - 20194
     指令数: 123
     首指令: PUSH		{R3-R5,LR}

368. sub_800AB7C
     地址: 0x800AB7C
     行号: 20200 - 20215
     指令数: 13
     首指令: LDR		R0, dword_800AD44

369. sub_800AB94
     地址: 0x800AB94
     行号: 20221 - 20399
     指令数: 158
     首指令: PUSH.W		{R4-R8,LR}

370. sub_800ACDE
     地址: 0x800ACDE
     行号: 20405 - 20442
     指令数: 33
     首指令: PUSH		{R4-R6,LR}

371. sub_800AD6C
     地址: 0x800AD6C
     行号: 20469 - 20606
     指令数: 130
     首指令: PUSH		{R3-R7,LR}

372. sub_800AEDE
     地址: 0x800AEDE
     行号: 20612 - 20700
     指令数: 80
     首指令: PUSH		{R4,LR}

373. sub_800AFBA
     地址: 0x800AFBA
     行号: 20706 - 20769
     指令数: 61
     首指令: PUSH		{R4,LR}

374. sub_800B076
     地址: 0x800B076
     行号: 20775 - 20875
     指令数: 95
     首指令: PUSH		{R4,LR}

375. sub_800B178
     地址: 0x800B178
     行号: 20881 - 20900
     指令数: 17
     首指令: CPSID		I

376. sub_800B1A4
     地址: 0x800B1A4
     行号: 20908 - 20926
     指令数: 17
     首指令: PUSH		{R4}

377. sub_800B1D0
     地址: 0x800B1D0
     行号: 20932 - 20947
     指令数: 14
     首指令: LDRB.W		R1, [R0,#0x40]

378. sub_800B1F8
     地址: 0x800B1F8
     行号: 20955 - 21035
     指令数: 74
     首指令: PUSH		{R4-R6,LR}

379. sub_800B2B4
     地址: 0x800B2B4
     行号: 21041 - 21054
     指令数: 12
     首指令: PUSH		{R4,LR}

380. sub_800B2D6
     地址: 0x800B2D6
     行号: 21060 - 21259
     指令数: 163
     首指令: PUSH.W		{R3-R11,LR}

381. sub_800B478
     地址: 0x800B478
     行号: 21265 - 21336
     指令数: 65
     首指令: PUSH		{R4-R6,LR}

382. sub_800B514
     地址: 0x800B514
     行号: 21342 - 21363
     指令数: 19
     首指令: PUSH		{R3-R5,LR}

383. sub_800B544
     地址: 0x800B544
     行号: 21369 - 21390
     指令数: 17
     首指令: PUSH		{R3-R5,LR}

384. sub_800B566
     地址: 0x800B566
     行号: 21396 - 21431
     指令数: 33
     首指令: PUSH.W		{R4-R8,LR}

385. sub_800B5BA
     地址: 0x800B5BA
     行号: 21437 - 21456
     指令数: 18
     首指令: PUSH		{R4,R5}

386. sub_800B5EA
     地址: 0x800B5EA
     行号: 21462 - 21584
     指令数: 111
     首指令: PUSH.W		{R1-R11,LR}

387. sub_800B70A
     地址: 0x800B70A
     行号: 21590 - 21622
     指令数: 31
     首指令: PUSH.W		{R4-R10,LR}

388. sub_800B756
     地址: 0x800B756
     行号: 21628 - 21753
     指令数: 109
     首指令: PUSH.W		{R4-R8,LR}

389. sub_800B85A
     地址: 0x800B85A
     行号: 21759 - 21803
     指令数: 40
     首指令: PUSH.W		{R4-R8,LR}

390. sub_800B90C
     地址: 0x800B90C
     行号: 21831 - 21925
     指令数: 87
     首指令: PUSH		{R4,LR}

391. sub_800B9F8
     地址: 0x800B9F8
     行号: 21931 - 22013
     指令数: 72
     首指令: PUSH		{R4-R6,LR}

392. sub_800BAAC
     地址: 0x800BAAC
     行号: 22021 - 22634
     指令数: 552
     首指令: PUSH.W		{R4-R10,LR}

393. sub_800C114
     地址: 0x800C114
     行号: 22640 - 22882
     指令数: 225
     首指令: PUSH		{R4-R6,LR}

394. sub_800C3D8
     地址: 0x800C3D8
     行号: 22922 - 22941
     指令数: 15
     首指令: PUSH		{R4,LR}

395. sub_800C404
     地址: 0x800C404
     行号: 22952 - 23022
     指令数: 63
     首指令: MOVS		R1, #0

396. sub_800C496
     地址: 0x800C496
     行号: 23028 - 23209
     指令数: 158
     首指令: PUSH		{R3-R7,LR}

397. sub_800C5F8
     地址: 0x800C5F8
     行号: 23215 - 23401
     指令数: 167
     首指令: PUSH		{R3-R7,LR}

398. sub_800C78A
     地址: 0x800C78A
     行号: 23407 - 23599
     指令数: 167
     首指令: PUSH		{R4-R7}

399. sub_800C912
     地址: 0x800C912
     行号: 23605 - 24047
     指令数: 405
     首指令: PUSH.W		{R4-R10,LR}

400. sub_800CDBC
     地址: 0x800CDBC
     行号: 24080 - 24099
     指令数: 15
     首指令: PUSH		{R4,LR}

401. sub_800CDE8
     地址: 0x800CDE8
     行号: 24110 - 24124
     指令数: 12
     首指令: PUSH		{R7,LR}

402. sub_800CE04
     地址: 0x800CE04
     行号: 24130 - 24138
     指令数: 7
     首指令: MOVS		R2, R0

403. sub_800CE14
     地址: 0x800CE14
     行号: 24144 - 24276
     指令数: 113
     首指令: PUSH.W		{R3-R9,LR}

404. sub_800CF2C
     地址: 0x800CF2C
     行号: 24282 - 24460
     指令数: 154
     首指令: PUSH.W		{R0-R2,R4-R11,LR}

405. sub_800D098
     地址: 0x800D098
     行号: 24466 - 24575
     指令数: 96
     首指令: PUSH.W		{R1,R4-R11,LR}

406. sub_800D176
     地址: 0x800D176
     行号: 24581 - 24645
     指令数: 58
     首指令: PUSH		{R4-R6,LR}

407. sub_800D200
     地址: 0x800D200
     行号: 24651 - 24694
     指令数: 39
     首指令: PUSH		{R4-R6,LR}

408. sub_800D25A
     地址: 0x800D25A
     行号: 24700 - 24784
     指令数: 74
     首指令: PUSH.W		{R4-R10,LR}

409. sub_800D314
     地址: 0x800D314
     行号: 24790 - 24847
     指令数: 51
     首指令: PUSH		{R4-R6,LR}

410. sub_800D38A
     地址: 0x800D38A
     行号: 24853 - 24947
     指令数: 84
     首指令: PUSH		{R3-R7,LR}

411. sub_800D446
     地址: 0x800D446
     行号: 24953 - 25071
     指令数: 104
     首指令: PUSH		{R3-R7,LR}

412. sub_800D53A
     地址: 0x800D53A
     行号: 25077 - 25301
     指令数: 197
     首指令: PUSH.W		{R4-R10,LR}

413. sub_800D738
     地址: 0x800D738
     行号: 25307 - 25365
     指令数: 57
     首指令: PUSH		{R4-R6,LR}

414. sub_800D7E0
     地址: 0x800D7E0
     行号: 25376 - 25785
     指令数: 371
     首指令: PUSH.W		{R4-R8,LR}

415. sub_800DB4C
     地址: 0x800DB4C
     行号: 25799 - 25864
     指令数: 54
     首指令: PUSH		{R3-R5,LR}

416. sub_800DBB0
     地址: 0x800DBB0
     行号: 25870 - 25891
     指令数: 17
     首指令: PUSH		{R3-R5,LR}

417. sub_800DBD0
     地址: 0x800DBD0
     行号: 25897 - 25918
     指令数: 17
     首指令: PUSH		{R3-R5,LR}

418. sub_800DBF0
     地址: 0x800DBF0
     行号: 25924 - 25967
     指令数: 41
     首指令: PUSH.W		{R4-R8,LR}

419. sub_800DC6C
     地址: 0x800DC6C
     行号: 25977 - 26117
     指令数: 125
     首指令: PUSH.W		{R1-R9,LR}

420. sub_800DDAC
     地址: 0x800DDAC
     行号: 26130 - 26297
     指令数: 135
     首指令: PUSH		{R4-R6,LR}

421. sub_800DEFC
     地址: 0x800DEFC
     行号: 26327 - 26385
     指令数: 51
     首指令: MOVS		R1, #0

422. sub_800DF6C
     地址: 0x800DF6C
     行号: 26391 - 26506
     指令数: 107
     首指令: PUSH		{R4,LR}

423. sub_800E070
     地址: 0x800E070
     行号: 26512 - 26835
     指令数: 294
     首指令: PUSH		{R4-R7}

424. sub_800E33C
     地址: 0x800E33C
     行号: 26841 - 26870
     指令数: 25
     首指令: PUSH		{R4,R5}

425. sub_800E372
     地址: 0x800E372
     行号: 26876 - 27042
     指令数: 156
     首指令: PUSH		{R4,R5}

426. sub_800E4EC
     地址: 0x800E4EC
     行号: 27048 - 27371
     指令数: 292
     首指令: PUSH.W		{R3-R9,LR}

427. sub_800E7DE
     地址: 0x800E7DE
     行号: 27377 - 27381
     指令数: 3
     首指令: LDR.W		R1, dword_800E86C

428. sub_800E7E6
     地址: 0x800E7E6
     行号: 27387 - 27391
     指令数: 3
     首指令: LDR.W		R0, dword_800E86C

429. sub_800E870
     地址: 0x800E870
     行号: 27431 - 27502
     指令数: 64
     首指令: PUSH		{R4,LR}

430. sub_800E90C
     地址: 0x800E90C
     行号: 27508 - 27544
     指令数: 29
     首指令: MOVS		R1, #0

431. sub_800E950
     地址: 0x800E950
     行号: 27550 - 27664
     指令数: 103
     首指令: PUSH		{R3-R5,LR}

432. sub_800EA6C
     地址: 0x800EA6C
     行号: 27670 - 27986
     指令数: 291
     首指令: PUSH.W		{R4-R8,LR}

433. sub_800ED4A
     地址: 0x800ED4A
     行号: 27992 - 27996
     指令数: 3
     首指令: LDR		R1, dword_800EDA4

434. sub_800ED50
     地址: 0x800ED50
     行号: 28002 - 28006
     指令数: 3
     首指令: LDR		R0, dword_800EDA4

435. sub_800EDA8
     地址: 0x800EDA8
     行号: 28034 - 28077
     指令数: 42
     首指令: PUSH		{R7,LR}

436. sub_800EE0A
     地址: 0x800EE0A
     行号: 28083 - 28109
     指令数: 22
     首指令: LDR		R0, dword_800EEE0

437. sub_800EE32
     地址: 0x800EE32
     行号: 28115 - 28129
     指令数: 10
     首指令: LDR		R0, dword_800EECC

438. sub_800EE42
     地址: 0x800EE42
     行号: 28135 - 28204
     指令数: 60
     首指令: PUSH		{R4,LR}

439. sub_800EEE4
     地址: 0x800EEE4
     行号: 28224 - 28280
     指令数: 52
     首指令: PUSH		{R4,LR}

440. sub_800EF60
     地址: 0x800EF60
     行号: 28286 - 28318
     指令数: 28
     首指令: PUSH		{R7,LR}

441. sub_800EF9A
     地址: 0x800EF9A
     行号: 28324 - 28357
     指令数: 29
     首指令: PUSH		{R4,LR}

442. sub_800EFD8
     地址: 0x800EFD8
     行号: 28363 - 28403
     指令数: 34
     首指令: PUSH		{R4,LR}

443. sub_800F01E
     地址: 0x800F01E
     行号: 28409 - 28449
     指令数: 34
     首指令: PUSH		{R4-R6,LR}

444. sub_800F062
     地址: 0x800F062
     行号: 28455 - 28539
     指令数: 71
     首指令: PUSH.W		{R4-R10,LR}

445. sub_800F0F6
     地址: 0x800F0F6
     行号: 28545 - 28585
     指令数: 34
     首指令: PUSH		{R4-R6,LR}

446. sub_800F13A
     地址: 0x800F13A
     行号: 28591 - 28626
     指令数: 31
     首指令: PUSH		{R4,LR}

447. sub_800F17C
     地址: 0x800F17C
     行号: 28632 - 28640
     指令数: 7
     首指令: PUSH		{R4,LR}

448. sub_800F18C
     地址: 0x800F18C
     行号: 28646 - 28651
     指令数: 4
     首指令: MOVS		R1, R0

449. sub_800F194
     地址: 0x800F194
     行号: 28657 - 28662
     指令数: 4
     首指令: MOVS		R1, R0

450. sub_800F1C0
     地址: 0x800F1C0
     行号: 28682 - 28714
     指令数: 31
     首指令: PUSH.W		{R3-R11,LR}

451. sub_800F214
     地址: 0x800F214
     行号: 28720 - 28732
     指令数: 10
     首指令: PUSH		{R7,LR}

452. sub_800F22A
     地址: 0x800F22A
     行号: 28738 - 28743
     指令数: 4
     首指令: LDR		R0, dword_800F254

453. sub_800F258
     地址: 0x800F258
     行号: 28759 - 28769
     指令数: 9
     首指令: PUSH		{R4,LR}

454. sub_800F270
     地址: 0x800F270
     行号: 28775 - 28798
     指令数: 19
     首指令: PUSH		{R3-R5,LR}

455. sub_800F29C
     地址: 0x800F29C
     行号: 28806 - 28846
     指令数: 34
     首指令: PUSH		{R4,R5}

456. sub_800F2E0
     地址: 0x800F2E0
     行号: 28852 - 28856
     指令数: 3
     首指令: LDR		R0, dword_800F2F8

457. sub_800F2FC
     地址: 0x800F2FC
     行号: 28869 - 28882
     指令数: 12
     首指令: PUSH		{R7,LR}

458. sub_800F326
     地址: 0x800F326
     行号: 28888 - 28922
     指令数: 32
     首指令: PUSH		{R7,LR}

459. sub_800F390
     地址: 0x800F390
     行号: 28928 - 28939
     指令数: 10
     首指令: PUSH		{R7,LR}

460. sub_800F3B4
     地址: 0x800F3B4
     行号: 28945 - 28951
     指令数: 5
     首指令: PUSH		{R7,LR}

461. sub_800F3C4
     地址: 0x800F3C4
     行号: 28957 - 28973
     指令数: 15
     首指令: PUSH		{R7,LR}

462. sub_800F3FC
     地址: 0x800F3FC
     行号: 28980 - 29004
     指令数: 22
     首指令: PUSH		{R7,LR}

463. sub_800F44C
     地址: 0x800F44C
     行号: 29010 - 29055
     指令数: 41
     首指令: PUSH		{R7,LR}

464. sub_800F4B8
     地址: 0x800F4B8
     行号: 29063 - 29099
     指令数: 32
     首指令: PUSH		{R4,R5}

465. sub_800F4FA
     地址: 0x800F4FA
     行号: 29105 - 29120
     指令数: 11
     首指令: UXTB		R0, R0

466. sub_800F50C
     地址: 0x800F50C
     行号: 29126 - 29141
     指令数: 14
     首指令: PUSH		{R3-R5,LR}

467. sub_800F52E
     地址: 0x800F52E
     行号: 29147 - 29159
     指令数: 11
     首指令: MOVS		R2, R0

468. sub_800F548
     地址: 0x800F548
     行号: 29165 - 29179
     指令数: 13
     首指令: MOVS		R1, R0

469. sub_800F56A
     地址: 0x800F56A
     行号: 29185 - 29216
     指令数: 30
     首指令: PUSH.W		{R4-R8,LR}

470. sub_800F5CC
     地址: 0x800F5CC
     行号: 29228 - 29235
     指令数: 6
     首指令: SUB		SP, SP,	#4

471. sub_800F5DC
     地址: 0x800F5DC
     行号: 29241 - 29256
     指令数: 14
     首指令: PUSH		{R7,LR}

472. sub_800F5FA
     地址: 0x800F5FA
     行号: 29262 - 29270
     指令数: 7
     首指令: MOVS		R0, #0

473. sub_800F608
     地址: 0x800F608
     行号: 29276 - 29307
     指令数: 25
     首指令: LDR		R0, dword_800F638

474. sub_800F640
     地址: 0x800F640
     行号: 29317 - 29334
     指令数: 13
     首指令: MOVS		R1, R0

475. sub_800F656
     地址: 0x800F656
     行号: 29340 - 29375
     指令数: 26
     首指令: PUSH		{R4,LR}

476. sub_800F682
     地址: 0x800F682
     行号: 29381 - 29415
     指令数: 25
     首指令: PUSH		{R4,LR}

477. sub_800F6AC
     地址: 0x800F6AC
     行号: 29421 - 29441
     指令数: 18
     首指令: PUSH		{R3-R5,LR}

478. sub_800F6D6
     地址: 0x800F6D6
     行号: 29447 - 29468
     指令数: 19
     首指令: PUSH		{R3-R5,LR}

479. sub_800F702
     地址: 0x800F702
     行号: 29474 - 29518
     指令数: 39
     首指令: PUSH.W		{R4-R8,LR}

480. sub_800F75C
     地址: 0x800F75C
     行号: 29524 - 29569
     指令数: 40
     首指令: PUSH.W		{R4-R8,LR}

481. sub_800F7B8
     地址: 0x800F7B8
     行号: 29575 - 29600
     指令数: 23
     首指令: PUSH		{R3-R5,LR}

482. sub_800F7EC
     地址: 0x800F7EC
     行号: 29606 - 29621
     指令数: 13
     首指令: PUSH		{R4}

483. sub_800F804
     地址: 0x800F804
     行号: 29627 - 29631
     指令数: 3
     首指令: PUSH		{R1-R3}

484. sub_800F80A
     地址: 0x800F80A
     行号: 29637 - 29641
     指令数: 3
     首指令: PUSH		{R1-R3}

485. sub_800F810
     地址: 0x800F810
     行号: 29647 - 29651
     指令数: 3
     首指令: PUSH		{R1-R3}

486. sub_800F816
     地址: 0x800F816
     行号: 29657 - 29661
     指令数: 3
     首指令: PUSH		{R1-R3}

487. sub_800F81C
     地址: 0x800F81C
     行号: 29667 - 30325
     指令数: 577
     首指令: PUSH.W		{R4-R11,LR}

488. sub_800FD1C
     地址: 0x800FD1C
     行号: 30335 - 31265
     指令数: 823
     首指令: PUSH.W		{R4-R11,LR}

489. sub_8010460
     地址: 0x8010460
     行号: 31300 - 31361
     指令数: 59
     首指令: PUSH		{R4,LR}

490. sub_80104E6
     地址: 0x80104E6
     行号: 31367 - 31595
     指令数: 199
     首指令: PUSH.W		{R3-R11,LR}

491. sub_80106E0
     地址: 0x80106E0
     行号: 31601 - 31622
     指令数: 20
     首指令: MOVS		R2, R0

492. sub_801071A
     地址: 0x801071A
     行号: 31628 - 31648
     指令数: 19
     首指令: MOVS		R2, R0

493. sub_801074E
     地址: 0x801074E
     行号: 31654 - 31755
     指令数: 87
     首指令: PUSH		{R4-R6}

494. sub_80107E8
     地址: 0x80107E8
     行号: 31763 - 31778
     指令数: 11
     首指令: PUSH		{R7,LR}

495. sub_8010804
     地址: 0x8010804
     行号: 31787 - 31802
     指令数: 13
     首指令: PUSH		{R3-R5,LR}

496. sub_801081E
     地址: 0x801081E
     行号: 31808 - 31842
     指令数: 27
     首指令: VPUSH		{S0}

497. sub_8010858
     地址: 0x8010858
     行号: 31850 - 31885
     指令数: 31
     首指令: PUSH		{R4,LR}

498. sub_80108A0
     地址: 0x80108A0
     行号: 31891 - 31925
     指令数: 29
     首指令: PUSH		{R3-R5,LR}

499. sub_80108F0
     地址: 0x80108F0
     行号: 31938 - 31942
     指令数: 3
     首指令: MOVS		R1, R0

500. sub_80108F6
     地址: 0x80108F6
     行号: 31948 - 31952
     指令数: 3
     首指令: MOVS		R2, R0

501. sub_80108FC
     地址: 0x80108FC
     行号: 31958 - 31962
     指令数: 3
     首指令: MOVS		R2, R0

502. sub_8010902
     地址: 0x8010902
     行号: 31968 - 31985
     指令数: 16
     首指令: PUSH		{R2-R4,LR}

503. sub_8010930
     地址: 0x8010930
     行号: 31991 - 32012
     指令数: 19
     首指令: PUSH		{R4}

504. sub_8010968
     地址: 0x8010968
     行号: 32024 - 32059
     指令数: 28
     首指令: MOVS.W		R12, R0

505. sub_80109AC
     地址: 0x80109AC
     行号: 32065 - 32088
     指令数: 22
     首指令: PUSH		{R2,R3}

506. sub_80109E0
     地址: 0x80109E0
     行号: 32096 - 32131
     指令数: 31
     首指令: PUSH		{R4,LR}

507. sub_8010A28
     地址: 0x8010A28
     行号: 32137 - 32174
     指令数: 30
     首指令: PUSH		{R4,LR}

508. sub_8010A62
     地址: 0x8010A62
     行号: 32180 - 32207
     指令数: 23
     首指令: PUSH		{R4,LR}

509. sub_8010AA8
     地址: 0x8010AA8
     行号: 32220 - 32240
     指令数: 18
     首指令: PUSH		{R7,LR}

510. sub_8010ACE
     地址: 0x8010ACE
     行号: 32246 - 32280
     指令数: 33
     首指令: PUSH		{LR}

511. sub_8010B24
     地址: 0x8010B24
     行号: 32286 - 32290
     指令数: 3
     首指令: LDR		R1, dword_8010B48

512. sub_8010B2A
     地址: 0x8010B2A
     行号: 32296 - 32307
     指令数: 10
     首指令: PUSH		{R4,LR}

513. sub_8010B50
     地址: 0x8010B50
     行号: 32318 - 32358
     指令数: 32
     首指令: MOVS		R0, #3

514. sub_8010B92
     地址: 0x8010B92
     行号: 32364 - 32404
     指令数: 32
     首指令: MOVS		R0, #3

515. sub_8010BD4
     地址: 0x8010BD4
     行号: 32410 - 32450
     指令数: 32
     首指令: MOVS		R0, #3

516. sub_8010C16
     地址: 0x8010C16
     行号: 32456 - 32485
     指令数: 24
     首指令: PUSH		{R3-R5,LR}

517. sub_8010C44
     地址: 0x8010C44
     行号: 32491 - 32520
     指令数: 24
     首指令: PUSH		{R3-R5,LR}

518. sub_8010C72
     地址: 0x8010C72
     行号: 32526 - 32555
     指令数: 24
     首指令: PUSH		{R3-R5,LR}

519. sub_8010CA0
     地址: 0x8010CA0
     行号: 32561 - 32575
     指令数: 13
     首指令: LDR.W		R0, dword_8010F0C

520. sub_8010CCA
     地址: 0x8010CCA
     行号: 32581 - 32593
     指令数: 11
     首指令: LDR.W		R0, dword_8010F1C

521. sub_8010CEC
     地址: 0x8010CEC
     行号: 32599 - 32714
     指令数: 106
     首指令: PUSH		{R3-R5,LR}

522. sub_8010E16
     地址: 0x8010E16
     行号: 32720 - 32813
     指令数: 87
     首指令: PUSH		{R4-R6,LR}

523. sub_8010F38
     地址: 0x8010F38
     行号: 32835 - 32897
     指令数: 55
     首指令: MOVS		R1, #0

524. sub_8010FBE
     地址: 0x8010FBE
     行号: 32903 - 32978
     指令数: 68
     首指令: PUSH		{R4,LR}

525. sub_8011068
     地址: 0x8011068
     行号: 32986 - 33379
     指令数: 367
     首指令: PUSH		{R3-R5,LR}

526. sub_80114C0
     地址: 0x80114C0
     行号: 33411 - 33642
     指令数: 214
     首指令: PUSH		{R4-R6,LR}

527. sub_80116E2
     地址: 0x80116E2
     行号: 33648 - 33935
     指令数: 270
     首指令: PUSH		{R4-R7,LR}

528. sub_80119B0
     地址: 0x80119B0
     行号: 33941 - 33989
     指令数: 46
     首指令: PUSH		{R7,LR}

529. sub_8011A26
     地址: 0x8011A26
     行号: 33995 - 34350
     指令数: 320
     首指令: PUSH		{R4-R6}

530. sub_8011D64
     地址: 0x8011D64
     行号: 34374 - 34985
     指令数: 563
     首指令: PUSH		{R4-R7,LR}

531. sub_80122F0
     地址: 0x80122F0
     行号: 35027 - 35039
     指令数: 11
     首指令: PUSH		{R7,LR}

532. sub_8012308
     地址: 0x8012308
     行号: 35045 - 35066
     指令数: 19
     首指令: LDR		R0, dword_8012330

533. sub_8012338
     地址: 0x8012338
     行号: 35076 - 35126
     指令数: 47
     首指令: LDR		R0, dword_8012494

534. sub_801239C
     地址: 0x801239C
     行号: 35132 - 35270
     指令数: 116
     首指令: PUSH.W		{R4-R10,LR}

535. sub_80124D0
     地址: 0x80124D0
     行号: 35292 - 35300
     指令数: 7
     首指令: PUSH		{R4,LR}

536. sub_80124E0
     地址: 0x80124E0
     行号: 35306 - 35314
     指令数: 7
     首指令: PUSH		{R4,LR}

537. sub_80124F0
     地址: 0x80124F0
     行号: 35320 - 35332
     指令数: 11
     首指令: PUSH		{R4,LR}

538. sub_8012520
     地址: 0x8012520
     行号: 35348 - 35363
     指令数: 13
     首指令: MOVS		R2, R0

539. sub_8012538
     地址: 0x8012538
     行号: 35369 - 35426
     指令数: 50
     首指令: PUSH		{R4,LR}

540. sub_80125A4
     地址: 0x80125A4
     行号: 35432 - 35456
     指令数: 20
     首指令: PUSH		{R3-R5,LR}

541. sub_80125CE
     地址: 0x80125CE
     行号: 35462 - 35726
     指令数: 236
     首指令: PUSH		{R3-R5,LR}

542. sub_80127F4
     地址: 0x80127F4
     行号: 35736 - 35771
     指令数: 31
     首指令: PUSH		{R4,LR}

543. sub_8012838
     地址: 0x8012838
     行号: 35777 - 35818
     指令数: 35
     首指令: PUSH		{R4-R6,LR}

544. sub_8012880
     地址: 0x8012880
     行号: 35824 - 35881
     指令数: 53
     首指令: PUSH		{R3-R7,LR}

545. sub_8012914
     地址: 0x8012914
     行号: 35893 - 36400
     指令数: 424
     首指令: PUSH.W		{R4-R11,LR}

546. sub_8012C9A
     地址: 0x8012C9A
     行号: 36406 - 36452
     指令数: 38
     首指令: CMP		R0, #0x62

547. sub_8012CE6
     地址: 0x8012CE6
     行号: 36458 - 36568
     指令数: 100
     首指令: PUSH		{R4-R7}

548. sub_8012DB4
     地址: 0x8012DB4
     行号: 36574 - 36603
     指令数: 24
     首指令: PUSH		{R3-R7,LR}

549. sub_8012DEC
     地址: 0x8012DEC
     行号: 36614 - 36617
     指令数: 2
     首指令: MOVS		R2, #0

550. sub_8012DF4
     地址: 0x8012DF4
     行号: 36625 - 36640
     指令数: 13
     首指令: CMP		R0, R2

551. sub_8012E14
     地址: 0x8012E14
     行号: 36646 - 36680
     指令数: 26
     首指令: PUSH		{R4}

552. sub_8012E40
     地址: 0x8012E40
     行号: 36686 - 36710
     指令数: 20
     首指令: PUSH.W		{R4-R8,LR}

553. sub_8012E70
     地址: 0x8012E70
     行号: 36718 - 36770
     指令数: 50
     首指令: PUSH		{R4,LR}

554. sub_8012F06
     地址: 0x8012F06
     行号: 36776 - 36790
     指令数: 10
     首指令: MOVS		R1, #0

555. sub_8012F16
     地址: 0x8012F16
     行号: 36796 - 36868
     指令数: 71
     首指令: PUSH		{R4,LR}

556. sub_8012FE0
     地址: 0x8012FE0
     行号: 36874 - 36880
     指令数: 5
     首指令: PUSH		{R4,LR}

557. sub_8012FEC
     地址: 0x8012FEC
     行号: 36886 - 36946
     指令数: 59
     首指令: PUSH		{R4,LR}

558. sub_8013086
     地址: 0x8013086
     行号: 36952 - 37013
     指令数: 57
     首指令: PUSH		{R3-R5,LR}

559. sub_8013112
     地址: 0x8013112
     行号: 37019 - 37078
     指令数: 58
     首指令: PUSH		{R3-R5,LR}

560. sub_80131A8
     地址: 0x80131A8
     行号: 37084 - 37128
     指令数: 36
     首指令: PUSH		{R3-R7,LR}

561. sub_80131F2
     地址: 0x80131F2
     行号: 37134 - 37169
     指令数: 28
     首指令: PUSH		{R3-R7,LR}

562. sub_801322A
     地址: 0x801322A
     行号: 37175 - 37212
     指令数: 32
     首指令: PUSH		{R3-R5,LR}

563. sub_8013280
     地址: 0x8013280
     行号: 37224 - 37271
     指令数: 46
     首指令: PUSH		{R4,LR}

564. sub_80132FA
     地址: 0x80132FA
     行号: 37277 - 37290
     指令数: 9
     首指令: MOVS		R1, #0

565. sub_8013308
     地址: 0x8013308
     行号: 37296 - 37357
     指令数: 57
     首指令: PUSH		{R3-R5,LR}

566. sub_8013394
     地址: 0x8013394
     行号: 37363 - 37433
     指令数: 63
     首指令: PUSH.W		{R4-R8,LR}

567. sub_8013448
     地址: 0x8013448
     行号: 37445 - 37460
     指令数: 12
     首指令: LDRB		R2, [R0]

568. sub_8013460
     地址: 0x8013460
     行号: 37468 - 37518
     指令数: 41
     首指令: UXTB		R1, R1

569. sub_80134B8
     地址: 0x80134B8
     行号: 37524 - 37547
     指令数: 21
     首指令: PUSH		{LR}

570. sub_8014614
     地址: 0x8014614
     行号: 37693 - 37773
     指令数: 70
     首指令: PUSH.W		{R3-R9,LR}

571. sub_80146CC
     地址: 0x80146CC
     行号: 37779 - 37835
     指令数: 50
     首指令: PUSH		{R4,LR}

572. sub_8014752
     地址: 0x8014752
     行号: 37841 - 37939
     指令数: 88
     首指令: PUSH		{R3-R5,LR}

573. sub_8014834
     地址: 0x8014834
     行号: 37945 - 38234
     指令数: 254
     首指令: PUSH.W		{R4-R10,LR}

574. sub_8014AA8
     地址: 0x8014AA8
     行号: 38240 - 38254
     指令数: 13
     首指令: PUSH		{R4,R5,LR}

575. sub_8014AC8
     地址: 0x8014AC8
     行号: 38260 - 38415
     指令数: 138
     首指令: PUSH.W		{R4-R10,LR}

576. sub_8014C1A
     地址: 0x8014C1A
     行号: 38421 - 38447
     指令数: 22
     首指令: PUSH		{R4,R5,LR}

577. sub_8014C48
     地址: 0x8014C48
     行号: 38453 - 38520
     指令数: 60
     首指令: PUSH		{R4-R6}

578. sub_8014CCE
     地址: 0x8014CCE
     行号: 38526 - 38541
     指令数: 14
     首指令: PUSH		{R4}

579. sub_8015C48
     地址: 0x8015C48
     行号: 38798 - 38800
     指令数: 1
     首指令: BX		LR

580. sub_8015C4A
     地址: 0x8015C4A
     行号: 38807 - 38809
     指令数: 1
     首指令: B		sub_8015C4A

581. sub_8015C4C
     地址: 0x8015C4C
     行号: 38816 - 38818
     指令数: 1
     首指令: B		sub_8015C4C

582. sub_8015C4E
     地址: 0x8015C4E
     行号: 38825 - 38827
     指令数: 1
     首指令: B		sub_8015C4E

583. sub_8015C50
     地址: 0x8015C50
     行号: 38834 - 38836
     指令数: 1
     首指令: B		sub_8015C50

584. sub_8015C52
     地址: 0x8015C52
     行号: 38842 - 38844
     指令数: 1
     首指令: BX		LR

585. sub_8015C54
     地址: 0x8015C54
     行号: 38850 - 38852
     指令数: 1
     首指令: BX		LR

586. sub_8015C56
     地址: 0x8015C56
     行号: 38858 - 38860
     指令数: 1
     首指令: BX		LR

587. sub_8015C58
     地址: 0x8015C58
     行号: 38866 - 38870
     指令数: 3
     首指令: PUSH		{R7,LR}

588. sub_8015C60
     地址: 0x8015C60
     行号: 38876 - 38880
     指令数: 3
     首指令: PUSH		{R7,LR}

589. sub_8015C68
     地址: 0x8015C68
     行号: 38886 - 38890
     指令数: 3
     首指令: PUSH		{R7,LR}

590. sub_8015C70
     地址: 0x8015C70
     行号: 38896 - 38900
     指令数: 3
     首指令: PUSH		{R7,LR}

591. sub_8015C78
     地址: 0x8015C78
     行号: 38906 - 38910
     指令数: 3
     首指令: PUSH		{R7,LR}

592. sub_8015C80
     地址: 0x8015C80
     行号: 38916 - 38920
     指令数: 3
     首指令: PUSH		{R7,LR}

593. sub_8015C88
     地址: 0x8015C88
     行号: 38926 - 38930
     指令数: 3
     首指令: PUSH		{R7,LR}

594. sub_8015C90
     地址: 0x8015C90
     行号: 38936 - 38940
     指令数: 3
     首指令: PUSH		{R7,LR}

595. sub_8015C98
     地址: 0x8015C98
     行号: 38946 - 38992
     指令数: 41
     首指令: PUSH		{R7,LR}

596. sub_8016384
     地址: 0x8016384
     行号: 39178 - 39200
     指令数: 18
     首指令: PUSH		{R4,LR}

597. sub_8016546
     地址: 0x8016546
     行号: 39283 - 39286
     指令数: 2
     首指令: MOVS		R0, #1

598. sub_801655C
     地址: 0x801655C
     行号: 39311 - 39322
     指令数: 9
     首指令: PUSH		{R7,LR}

599. sub_8016944
     地址: 0x8016944
     行号: 39520 - 39522
     指令数: 1
     首指令: B.W		sub_8016944

600. sub_8016948
     地址: 0x8016948
     行号: 39529 - 39531
     指令数: 1
     首指令: B.W		sub_8016948

601. sub_801694C
     地址: 0x801694C
     行号: 39538 - 39540
     指令数: 1
     首指令: B.W		sub_801694C

602. sub_8016950
     地址: 0x8016950
     行号: 39547 - 39549
     指令数: 1
     首指令: B.W		sub_8016950

603. sub_8016954
     地址: 0x8016954
     行号: 39556 - 39558
     指令数: 1
     首指令: B.W		sub_8016954

604. sub_8016958
     地址: 0x8016958
     行号: 39565 - 39567
     指令数: 1
     首指令: B.W		sub_8016958

605. sub_801695C
     地址: 0x801695C
     行号: 39574 - 39576
     指令数: 1
     首指令: B.W		sub_801695C

606. sub_8016960
     地址: 0x8016960
     行号: 39583 - 39585
     指令数: 1
     首指令: B.W		sub_8016960

607. sub_8016964
     地址: 0x8016964
     行号: 39592 - 39594
     指令数: 1
     首指令: B.W		sub_8016964

608. sub_8016968
     地址: 0x8016968
     行号: 39601 - 39603
     指令数: 1
     首指令: B.W		sub_8016968

609. sub_801696C
     地址: 0x801696C
     行号: 39610 - 39612
     指令数: 1
     首指令: B.W		sub_801696C

610. sub_8016970
     地址: 0x8016970
     行号: 39619 - 39621
     指令数: 1
     首指令: B.W		sub_8016970

611. sub_8016974
     地址: 0x8016974
     行号: 39628 - 39630
     指令数: 1
     首指令: B.W		sub_8016974

612. sub_8016978
     地址: 0x8016978
     行号: 39637 - 39639
     指令数: 1
     首指令: B.W		sub_8016978

613. sub_801697C
     地址: 0x801697C
     行号: 39646 - 39648
     指令数: 1
     首指令: B.W		sub_801697C

614. sub_8016980
     地址: 0x8016980
     行号: 39655 - 39657
     指令数: 1
     首指令: B.W		sub_8016980

615. sub_8016984
     地址: 0x8016984
     行号: 39664 - 39666
     指令数: 1
     首指令: B.W		sub_8016984

616. sub_8016988
     地址: 0x8016988
     行号: 39673 - 39675
     指令数: 1
     首指令: B.W		sub_8016988

617. sub_801698C
     地址: 0x801698C
     行号: 39682 - 39684
     指令数: 1
     首指令: B.W		sub_801698C

618. sub_8016990
     地址: 0x8016990
     行号: 39691 - 39693
     指令数: 1
     首指令: B.W		sub_8016990

619. sub_8016994
     地址: 0x8016994
     行号: 39700 - 39702
     指令数: 1
     首指令: B.W		sub_8016994

620. sub_8016998
     地址: 0x8016998
     行号: 39709 - 39711
     指令数: 1
     首指令: B.W		sub_8016998

621. sub_801699C
     地址: 0x801699C
     行号: 39718 - 39720
     指令数: 1
     首指令: B.W		sub_801699C

622. sub_80169A0
     地址: 0x80169A0
     行号: 39727 - 39729
     指令数: 1
     首指令: B.W		sub_80169A0

623. sub_80169A4
     地址: 0x80169A4
     行号: 39736 - 39738
     指令数: 1
     首指令: B.W		sub_80169A4

624. sub_80169A8
     地址: 0x80169A8
     行号: 39745 - 39747
     指令数: 1
     首指令: B.W		sub_80169A8

625. sub_80169AC
     地址: 0x80169AC
     行号: 39754 - 39756
     指令数: 1
     首指令: B.W		sub_80169AC

626. sub_80169B0
     地址: 0x80169B0
     行号: 39763 - 39765
     指令数: 1
     首指令: B.W		sub_80169B0

627. sub_80169B4
     地址: 0x80169B4
     行号: 39772 - 39774
     指令数: 1
     首指令: B.W		sub_80169B4

628. sub_80169B8
     地址: 0x80169B8
     行号: 39781 - 39783
     指令数: 1
     首指令: B.W		sub_80169B8

629. sub_80169BC
     地址: 0x80169BC
     行号: 39790 - 39792
     指令数: 1
     首指令: B.W		sub_80169BC

630. sub_80169C0
     地址: 0x80169C0
     行号: 39799 - 39801
     指令数: 1
     首指令: B.W		sub_80169C0

631. sub_80169C4
     地址: 0x80169C4
     行号: 39808 - 39810
     指令数: 1
     首指令: B.W		sub_80169C4

632. sub_80169C8
     地址: 0x80169C8
     行号: 39817 - 39819
     指令数: 1
     首指令: B.W		sub_80169C8

633. sub_80169CC
     地址: 0x80169CC
     行号: 39826 - 39828
     指令数: 1
     首指令: B.W		sub_80169CC

634. sub_80169D0
     地址: 0x80169D0
     行号: 39835 - 39837
     指令数: 1
     首指令: B.W		sub_80169D0

635. sub_80169D4
     地址: 0x80169D4
     行号: 39844 - 39846
     指令数: 1
     首指令: B.W		sub_80169D4

636. sub_80169D8
     地址: 0x80169D8
     行号: 39853 - 39855
     指令数: 1
     首指令: B.W		sub_80169D8

637. sub_80169DC
     地址: 0x80169DC
     行号: 39862 - 39864
     指令数: 1
     首指令: B.W		sub_80169DC

638. sub_80169E0
     地址: 0x80169E0
     行号: 39871 - 39873
     指令数: 1
     首指令: B.W		sub_80169E0

639. sub_80169E4
     地址: 0x80169E4
     行号: 39880 - 39882
     指令数: 1
     首指令: B.W		sub_80169E4

640. sub_80169E8
     地址: 0x80169E8
     行号: 39889 - 39891
     指令数: 1
     首指令: B.W		sub_80169E8

641. sub_80169EC
     地址: 0x80169EC
     行号: 39898 - 39900
     指令数: 1
     首指令: B.W		sub_80169EC

642. sub_80169F0
     地址: 0x80169F0
     行号: 39907 - 39909
     指令数: 1
     首指令: B.W		sub_80169F0

643. sub_80169F4
     地址: 0x80169F4
     行号: 39916 - 39918
     指令数: 1
     首指令: B.W		sub_80169F4

644. sub_80169F8
     地址: 0x80169F8
     行号: 39925 - 39927
     指令数: 1
     首指令: B.W		sub_80169F8

645. sub_80169FC
     地址: 0x80169FC
     行号: 39934 - 39936
     指令数: 1
     首指令: B.W		sub_80169FC

646. sub_8016A00
     地址: 0x8016A00
     行号: 39943 - 39945
     指令数: 1
     首指令: B.W		sub_8016A00

647. sub_8016A04
     地址: 0x8016A04
     行号: 39952 - 39954
     指令数: 1
     首指令: B.W		sub_8016A04

648. sub_8016A08
     地址: 0x8016A08
     行号: 39961 - 39963
     指令数: 1
     首指令: B.W		sub_8016A08

649. sub_8016A0C
     地址: 0x8016A0C
     行号: 39970 - 39972
     指令数: 1
     首指令: B.W		sub_8016A0C

650. sub_8016A10
     地址: 0x8016A10
     行号: 39979 - 39981
     指令数: 1
     首指令: B.W		sub_8016A10

651. sub_8016A14
     地址: 0x8016A14
     行号: 39988 - 39990
     指令数: 1
     首指令: B.W		sub_8016A14

652. sub_8016A18
     地址: 0x8016A18
     行号: 39997 - 39999
     指令数: 1
     首指令: B.W		sub_8016A18

653. sub_8016A1C
     地址: 0x8016A1C
     行号: 40006 - 40008
     指令数: 1
     首指令: B.W		sub_8016A1C

654. sub_8016A20
     地址: 0x8016A20
     行号: 40015 - 40017
     指令数: 1
     首指令: B.W		sub_8016A20

655. sub_8016A24
     地址: 0x8016A24
     行号: 40024 - 40026
     指令数: 1
     首指令: B.W		sub_8016A24

656. sub_8016A28
     地址: 0x8016A28
     行号: 40033 - 40035
     指令数: 1
     首指令: B.W		sub_8016A28

657. sub_8016A2C
     地址: 0x8016A2C
     行号: 40042 - 40044
     指令数: 1
     首指令: B.W		sub_8016A2C

658. sub_8016A30
     地址: 0x8016A30
     行号: 40051 - 40053
     指令数: 1
     首指令: B.W		sub_8016A30

659. sub_8016A34
     地址: 0x8016A34
     行号: 40060 - 40062
     指令数: 1
     首指令: B.W		sub_8016A34

660. sub_8016A38
     地址: 0x8016A38
     行号: 40069 - 40071
     指令数: 1
     首指令: B.W		sub_8016A38

661. sub_8016A3C
     地址: 0x8016A3C
     行号: 40078 - 40080
     指令数: 1
     首指令: B.W		sub_8016A3C

662. sub_8016A40
     地址: 0x8016A40
     行号: 40087 - 40089
     指令数: 1
     首指令: B.W		sub_8016A40

663. sub_8016A44
     地址: 0x8016A44
     行号: 40096 - 40098
     指令数: 1
     首指令: B.W		sub_8016A44

664. sub_8016A48
     地址: 0x8016A48
     行号: 40105 - 40107
     指令数: 1
     首指令: B.W		sub_8016A48

665. sub_8016A4C
     地址: 0x8016A4C
     行号: 40114 - 40116
     指令数: 1
     首指令: B.W		sub_8016A4C

666. sub_8016A50
     地址: 0x8016A50
     行号: 40123 - 40125
     指令数: 1
     首指令: B.W		sub_8016A50

667. sub_8016A54
     地址: 0x8016A54
     行号: 40132 - 40134
     指令数: 1
     首指令: B.W		sub_8016A54

