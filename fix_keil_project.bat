@echo off
echo ========================================
echo 修复Keil项目配置
echo ========================================
echo.

echo 诊断结果:
echo ✅ ARM编译器已安装: ARMCC 5.06
echo ✅ AT32器件包已安装: ArteryTek.AT32A403A_DFP
echo ❌ 项目配置需要修复
echo.

echo 🔧 修复方法:
echo.
echo 方法1: 在Keil IDE中手动修复 (推荐)
echo 1. 打开Keil项目
echo 2. 右键项目名称 → "Options for Target"
echo 3. 在"Target"选项卡中选择正确的编译器版本
echo.

echo 方法2: 使用命令行修复
echo.

set /p choice=选择修复方法 (1 或 2): 

if "%choice%"=="1" (
    echo.
    echo 🚀 启动Keil IDE进行手动修复...
    start "" "C:\Keil_v5\UV4\UV4.exe" "keil\at32f403avg_firmware.uvprojx"
    echo.
    echo 📋 详细修复步骤:
    echo.
    echo 1. 等待Keil完全加载项目
    echo 2. 在项目树中右键 "AT32F403AVG_Conversion"
    echo 3. 选择 "Options for Target..."
    echo 4. 在弹出窗口中:
    echo    - 点击 "Target" 选项卡
    echo    - 在 "ARM Compiler" 下拉菜单中选择 "Use default compiler version 5"
    echo    - 或者选择 "V5.06 update 7 (build 960)"
    echo 5. 点击 "OK" 保存设置
    echo 6. 按 F7 重新编译项目
    echo.
    echo ✅ 如果编译成功，项目修复完成！
    
) else if "%choice%"=="2" (
    echo.
    echo 🔧 命令行修复 (实验性)...
    echo.
    echo 备份原项目文件...
    if exist "keil\at32f403avg_firmware.uvprojx" (
        copy "keil\at32f403avg_firmware.uvprojx" "keil\at32f403avg_firmware.uvprojx.backup" >nul
        echo ✅ 已备份项目文件
    )
    echo.
    echo 注意: 命令行修复功能开发中
    echo 建议使用方法1进行手动修复
    
) else (
    echo ❌ 无效选择
)

echo.
echo ========================================
echo 🎯 编译器版本信息
echo ========================================
echo.
echo 已安装的ARM编译器:
echo - ARMCC 5.06 (build 750): C:\Keil_v5\ARM\ARMCC\
echo - ARMCLANG 6.x: C:\Keil_v5\ARM\ARMCLANG\
echo.
echo 推荐设置:
echo - 对于AT32F403AVG项目，建议使用ARMCC 5.06
echo - 这是最稳定和兼容的版本
echo.

echo ========================================
echo 🚀 快速测试编译
echo ========================================
echo.
echo 如果手动修复完成，可以测试编译:
echo.
echo 在Keil中:
echo 1. 按 F7 或点击编译按钮
echo 2. 查看输出窗口的编译信息
echo 3. 检查是否有错误或警告
echo.
echo 预期结果:
echo ✅ 编译成功，无错误
echo ✅ 生成 .axf 和 .hex 文件
echo ✅ 代码大小合理 (约100KB Flash)
echo.

echo ========================================
echo 修复脚本完成
echo ========================================
pause
