// 大规模手工转换批次 15 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_4B996
 * @note 指令数: 17
 */
void func_4b996(uint32_t param0)
{
    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4B9BA
 * @note 指令数: 48
 */
void func_4b9ba(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200000C8 = (volatile uint32_t *)0x200000C8;
    volatile uint32_t *addr_7C = (volatile uint32_t *)0x7C;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_20007896 = (volatile uint32_t *)0x20007896;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4BA34
 * @note 指令数: 38
 */
void func_4ba34(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8015DE4 = (volatile uint32_t *)0x8015DE4;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_3800 = (volatile uint32_t *)0x3800;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4BA90
 * @note 指令数: 54
 */
void func_4ba90(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8014D70 = (volatile uint32_t *)0x8014D70;
    volatile uint32_t *addr_8015DE4 = (volatile uint32_t *)0x8015DE4;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4BB0C
 * @note 指令数: 54
 */
void func_4bb0c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8014D70 = (volatile uint32_t *)0x8014D70;
    volatile uint32_t *addr_8015DE4 = (volatile uint32_t *)0x8015DE4;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4BB86
 * @note 指令数: 57
 */
void func_4bb86(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8014D70 = (volatile uint32_t *)0x8014D70;
    volatile uint32_t *addr_8015DE4 = (volatile uint32_t *)0x8015DE4;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4BC18
 * @note 指令数: 58
 */
void func_4bc18(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_3B10 = (volatile uint32_t *)0x3B10;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4BC90
 * @note 指令数: 18
 */
void func_4bc90(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007590 = (volatile uint32_t *)0x20007590;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4BCB6
 * @note 指令数: 73
 */
void func_4bcb6(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4BD5C
 * @note 指令数: 131
 */
void func_4bd5c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_3D = (volatile uint32_t *)0x3D;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4BE6C
 * @note 指令数: 129
 */
void func_4be6c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_60 = (volatile uint32_t *)0x60;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4BF82
 * @note 指令数: 18
 */
void func_4bf82(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20006E68 = (volatile uint32_t *)0x20006E68;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4BFBC
 * @note 指令数: 3
 */
uint32_t func_4bfbc(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4BFC2
 * @note 指令数: 3
 */
uint32_t func_4bfc2(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4BFC8
 * @note 指令数: 3
 */
uint32_t func_4bfc8(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4BFCE
 * @note 指令数: 20
 */
uint32_t func_4bfce(void)
{
    // 内存地址定义
    volatile uint32_t *addr_8016018 = (volatile uint32_t *)0x8016018;
    volatile uint32_t *addr_41 = (volatile uint32_t *)0x41;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4BFFA
 * @note 指令数: 19
 */
void func_4bffa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200078C8 = (volatile uint32_t *)0x200078C8;
    volatile uint32_t *addr_25F = (volatile uint32_t *)0x25F;
    volatile uint32_t *addr_257 = (volatile uint32_t *)0x257;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C038
 * @note 指令数: 42
 */
void func_4c038(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_8015D04 = (volatile uint32_t *)0x8015D04;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_C0 = (volatile uint32_t *)0xC0;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4C08A
 * @note 指令数: 10
 */
void func_4c08a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000789A = (volatile uint32_t *)0x2000789A;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4C0A0
 * @note 指令数: 6
 */
uint32_t func_4c0a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_2000789A = (volatile uint32_t *)0x2000789A;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4C0D0
 * @note 指令数: 8
 */
uint32_t func_4c0d0(void)
{
    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4C0E0
 * @note 指令数: 30
 */
void func_4c0e0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_4C0F8 = (volatile uint32_t *)0x4C0F8;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4C118
 * @note 指令数: 30
 */
void func_4c118(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;
    volatile uint32_t *addr_8014F70 = (volatile uint32_t *)0x8014F70;
    volatile uint32_t *addr_8014E70 = (volatile uint32_t *)0x8014E70;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C15C
 * @note 指令数: 11
 */
void func_4c15c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;
    volatile uint32_t *addr_37 = (volatile uint32_t *)0x37;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_4C170
 * @note 指令数: 15
 */
void func_4c170(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4C192
 * @note 指令数: 12
 */
uint32_t func_4c192(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4C1AA
 * @note 指令数: 16
 */
void func_4c1aa(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4C1CE
 * @note 指令数: 35
 */
void func_4c1ce(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_8015F30 = (volatile uint32_t *)0x8015F30;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4C21C
 * @note 指令数: 3
 */
uint32_t func_4c21c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_7FFFFFFF = (volatile uint32_t *)0x7FFFFFFF;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4C22C
 * @note 指令数: 4
 */
uint32_t func_4c22c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200078D1 = (volatile uint32_t *)0x200078D1;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4C234
 * @note 指令数: 4
 */
uint32_t func_4c234(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007842 = (volatile uint32_t *)0x20007842;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4C23C
 * @note 指令数: 30
 */
void func_4c23c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_200078BD = (volatile uint32_t *)0x200078BD;
    volatile uint32_t *addr_200078BC = (volatile uint32_t *)0x200078BC;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4C27E
 * @note 指令数: 63
 */
void func_4c27e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200078BE = (volatile uint32_t *)0x200078BE;
    volatile uint32_t *addr_20007840 = (volatile uint32_t *)0x20007840;
    volatile uint32_t *addr_20007844 = (volatile uint32_t *)0x20007844;
    volatile uint32_t *addr_800A68D = (volatile uint32_t *)0x800A68D;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C30C
 * @note 指令数: 118
 */
void func_4c30c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200078D1 = (volatile uint32_t *)0x200078D1;
    volatile uint32_t *addr_200078BE = (volatile uint32_t *)0x200078BE;
    volatile uint32_t *addr_20007840 = (volatile uint32_t *)0x20007840;
    volatile uint32_t *addr_200078BD = (volatile uint32_t *)0x200078BD;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C420
 * @note 指令数: 115
 */
void func_4c420(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200078BE = (volatile uint32_t *)0x200078BE;
    volatile uint32_t *addr_20005F94 = (volatile uint32_t *)0x20005F94;
    volatile uint32_t *addr_20007844 = (volatile uint32_t *)0x20007844;
    volatile uint32_t *addr_200078BA = (volatile uint32_t *)0x200078BA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C520
 * @note 指令数: 14
 */
void func_4c520(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200078BB = (volatile uint32_t *)0x200078BB;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C558
 * @note 指令数: 160
 */
void func_4c558(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2B = (volatile uint32_t *)0x2B;
    volatile uint32_t *addr_200078BE = (volatile uint32_t *)0x200078BE;
    volatile uint32_t *addr_20005F95 = (volatile uint32_t *)0x20005F95;
    volatile uint32_t *addr_20005F94 = (volatile uint32_t *)0x20005F94;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C6AC
 * @note 指令数: 34
 */
void func_4c6ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200077C0 = (volatile uint32_t *)0x200077C0;
    volatile uint32_t *addr_20007840 = (volatile uint32_t *)0x20007840;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C724
 * @note 指令数: 104
 */
void func_4c724(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007560 = (volatile uint32_t *)0x20007560;
    volatile uint32_t *addr_200071AC = (volatile uint32_t *)0x200071AC;
    volatile uint32_t *addr_62 = (volatile uint32_t *)0x62;
    volatile uint32_t *addr_2000788D = (volatile uint32_t *)0x2000788D;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4C7F6
 * @note 指令数: 76
 */
uint32_t func_4c7f6(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_F2177617 = (volatile uint32_t *)0xF2177617;
    volatile uint32_t *addr_20007891 = (volatile uint32_t *)0x20007891;
    volatile uint32_t *addr_20007608 = (volatile uint32_t *)0x20007608;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4C90C
 * @note 指令数: 330
 */
uint16_t func_4c90c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_2000788C = (volatile uint32_t *)0x2000788C;
    volatile uint32_t *addr_200072CC = (volatile uint32_t *)0x200072CC;

    // 局部变量
    uint16_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4CF18
 * @note 指令数: 233
 */
void func_4cf18(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007560 = (volatile uint32_t *)0x20007560;
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;
    volatile uint32_t *addr_2000788D = (volatile uint32_t *)0x2000788D;
    volatile uint32_t *addr_200071AC = (volatile uint32_t *)0x200071AC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4D140
 * @note 指令数: 14
 */
void func_4d140(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4D15C
 * @note 指令数: 67
 */
void func_4d15c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000782A = (volatile uint32_t *)0x2000782A;
    volatile uint32_t *addr_20007478 = (volatile uint32_t *)0x20007478;
    volatile uint32_t *addr_20007820 = (volatile uint32_t *)0x20007820;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4D1E0
 * @note 指令数: 162
 */
void func_4d1e0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000782E = (volatile uint32_t *)0x2000782E;
    volatile uint32_t *addr_2000782C = (volatile uint32_t *)0x2000782C;
    volatile uint32_t *addr_20007668 = (volatile uint32_t *)0x20007668;
    volatile uint32_t *addr_200078AB = (volatile uint32_t *)0x200078AB;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4D360
 * @note 指令数: 182
 */
void func_4d360(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20007820 = (volatile uint32_t *)0x20007820;
    volatile uint32_t *addr_2000782C = (volatile uint32_t *)0x2000782C;
    volatile uint32_t *addr_200078AA = (volatile uint32_t *)0x200078AA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4D530
 * @note 指令数: 207
 */
void func_4d530(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20007058 = (volatile uint32_t *)0x20007058;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_20007826 = (volatile uint32_t *)0x20007826;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4D6F4
 * @note 指令数: 360
 */
void func_4d6f4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_2000782C = (volatile uint32_t *)0x2000782C;
    volatile uint32_t *addr_20007668 = (volatile uint32_t *)0x20007668;
    volatile uint32_t *addr_200078A8 = (volatile uint32_t *)0x200078A8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4DB18
 * @note 指令数: 14
 */
void func_4db18(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4DB34
 * @note 指令数: 56
 */
void func_4db34(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_8015E04 = (volatile uint32_t *)0x8015E04;
    volatile uint32_t *addr_20007794 = (volatile uint32_t *)0x20007794;
    volatile uint32_t *addr_8015DF4 = (volatile uint32_t *)0x8015DF4;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

