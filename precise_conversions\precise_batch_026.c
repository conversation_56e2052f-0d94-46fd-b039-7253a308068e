// 精确转换批次 26 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82316
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_82316(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_82316
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82318
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_82318(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // CPSID   I
    // BL      sub_823D8
    // 调用函数: sub_823D8();
    // BL      sub_82422
    // 调用函数: sub_82422();
    // BL      sub_796F4
    // 调用函数: sub_796F4();
    // BL      sub_79AF4
    // 调用函数: sub_79AF4();
    // BL      sub_79ABC
    // 调用函数: sub_79ABC();
    // BL      sub_775A0
    // 调用函数: sub_775A0();
    // BL      sub_7CD48
    // 调用函数: sub_7CD48();
    // BL      sub_789A0
    // 调用函数: sub_789A0();
    // BL      sub_75CFC
    // 调用函数: sub_75CFC();
    // BL      sub_82474
    // 调用函数: sub_82474();
    // BL      sub_81D90
    // 调用函数: sub_81D90();
    // BL      sub_7B6F0
    // 调用函数: sub_7B6F0();
    // BL      sub_7877C
    // 调用函数: sub_7877C();
    // BL      sub_79ABC
    // 调用函数: sub_79ABC();
    // BL      sub_79754
    // 调用函数: sub_79754();
    // CPSIE   I
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8235C
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_8235c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_79ABC
    // 调用函数: sub_79ABC();
    // BL      sub_75E3C
    // 调用函数: sub_75E3C();
    // BL      sub_7C7B8
    // 调用函数: sub_7C7B8();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8236C
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_8236c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_79ABC
    // 调用函数: sub_79ABC();
    // BL      sub_77688
    // 调用函数: sub_77688();
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_82382
    // 条件跳转
    // BL      sub_7CD86
    // 调用函数: sub_7CD86();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82390
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_82390(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_79ABC
    // 调用函数: sub_79ABC();
    // BL      sub_787E8
    // 调用函数: sub_787E8();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8239C
 * @note 指令数: 16, 标签数: 1
 */
void precise_func_8239c(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_82318
    // 调用函数: sub_82318();
    // BL      sub_8235C
    // 调用函数: sub_8235C();
    // BL      sub_78A46
    // 调用函数: sub_78A46();
    // BL      sub_8236C
    // 调用函数: sub_8236C();
    // BL      sub_78A46
    // 调用函数: sub_78A46();
    // BL      sub_8235C
    // 调用函数: sub_8235C();
    // BL      sub_78A46
    // 调用函数: sub_78A46();
    // BL      sub_8236C
    // 调用函数: sub_8236C();
    // BL      sub_78A46
    // 调用函数: sub_78A46();
    // BL      sub_8235C
    // 调用函数: sub_8235C();
    // BL      sub_78A46
    // 调用函数: sub_78A46();
    // BL      sub_82390
    // 调用函数: sub_82390();
    // BL      sub_78A46
    // 调用函数: sub_78A46();
    // BL      sub_79ABC
    // 调用函数: sub_79ABC();
    // B       loc_823A2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_823D8
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_823d8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x3C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_14= -0x14
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82422
 * @note 指令数: 15, 标签数: 1
 */
void precise_func_82422(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8002040;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // CMP     R0, #0x30 ; '0'
    // 比较操作
    // BCS     loc_82442
    // LSLS    R1, R0, #2
    // LDR     R2, =0x8002040
    // 内存加载操作
    // LDR     R1, [R1,R2]
    // 内存加载操作
    // MOVS    R2, #0x20000000
    // R2 = 0x20000000;
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_8242A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82474
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_82474(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C200;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xBE;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_824B4
    // 条件跳转
    // BL      sub_7E4D8
    // 调用函数: sub_7E4D8();
    // MOVS    R0, #0x3E8
    // R0 = 0x3E8;
    // STR     R0, [SP,#0x10+var_C]
    // 内存存储操作
    // MOVS    R0, #0xBE
    // R0 = 0xBE;
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // MOVS    R3, #2
    // R3 = 2;
    // MOVS    R2, #1
    // R2 = 1;
    // MOVS    R1, #8
    // R1 = 8;
    // MOVS    R0, #0x1C200
    // R0 = 0x1C200;
    // BL      sub_8183C
    // 调用函数: sub_8183C();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x10+var_C]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // MOVS    R3, #1
    // R3 = 1;
    // MOVS    R2, #1
    // R2 = 1;
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_7F318
    // 调用函数: sub_7F318();
    // BL      sub_817DA
    // 调用函数: sub_817DA();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_824B6
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_824b6(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_824C4
    // 条件跳转
    // BL      sub_818FC
    // 调用函数: sub_818FC();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_824C6
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_824c6(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_824C6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82CD4
 * @note 指令数: 11, 标签数: 1
 */
void precise_func_82cd4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R3, #0
    // R3 = 0;
    // B       loc_82D04
    // 无条件跳转
    // LDR     R4, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #4
    // 算术运算
    // TST     R4, R1
    // 比较操作
    // BEQ     loc_82CEA
    // 条件跳转
    // MOV     R5, R9
    // SUBS    R5, R5, #1
    // 算术运算
    // ADDS    R4, R4, R5
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82D10
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_82d10(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // LDR     R1, [R0]
    // 内存加载操作
    // ADDS    R1, R0, R1
    // 算术运算
    // LDR     R4, [R0,#4]
    // 内存加载操作
    // LSRS    R2, R4, #1
    // ADDS    R2, R1, R2
    // 算术运算
    // LDR     R3, [R0,#8]
    // 内存加载操作
    // LSLS    R4, R4, #0x1F
    // BPL     loc_82D26
    // MOV     R4, R9
    // ADDS    R3, R3, R4
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82DDC
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_82ddc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x82DEA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x82DE4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R1, =(loc_82DEC - 0x82DE4)
    // 内存加载操作
    // ADD     R1, PC          ; loc_82DEC
    // 算术运算
    // ADDS    R1, #0x18
    // 算术运算
    // LDR     R4, =(word_82E0E - 0x82DEA)
    // 内存加载操作
    // ADD     R4, PC          ; word_82E0E
    // 算术运算
    // ADDS    R4, #0x16
    // 算术运算
    // B       loc_82DF6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82FD0
 * @note 指令数: 15, 标签数: 0
 */
uint32_t precise_func_82fd0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xD6;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xDC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xD4;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x62;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // ADDS    R6, #0xD4
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // ADDS    R6, #0xD6
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // ADDS    R6, #0x5E ; '^'
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // ADDS    R6, #0x5C ; '\'
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // ADDS    R6, #0x62 ; 'b'
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // ADDS    R6, #0x60 ; '`'
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // ADDS    R6, #0xDC
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82FEE
 * @note 指令数: 2, 标签数: 1
 */
void precise_func_82fee(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // B       loc_82FF0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82FF6
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_82ff6(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_7DFEE
    // 调用函数: sub_7DFEE();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_82FFE
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_82ffe(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_7C8A0
    // 调用函数: sub_7C8A0();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83006
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83006(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83006
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8302A
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_8302a(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_8302A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8302C
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_8302c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R7, R0
    // MOV     R0, R7
    // BL      sub_83038
    // 调用函数: sub_83038();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83036
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83036(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83036
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83038
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_83038(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20026;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xAB;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0x20026
    // R1 = 0x20026;
    // MOVS    R0, #0x18
    // R0 = 0x18;
    // BKPT    0xAB
    // B       sub_83038
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83866
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83866(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83866
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83868
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83868(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83868
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8386A
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_8386a(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_8386A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8386C
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_8386c(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_8386C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8386E
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_8386e(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_8386E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83870
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83870(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83870
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83872
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83872(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83872
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83874
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83874(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83874
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83876
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83876(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83876
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_83878
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_83878(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_83878
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8387A
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_8387a(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_8387A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_84E3C
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_84e3c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #0
    // R0 = 0;
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R2, =byte_C02000
    // 内存加载操作
    // STRB    R0, [R2]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R1, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8B27E
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_8b27e(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // ANDS    R0, R2
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_14BF90
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_14bf90(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5E5E4240;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R4, =0x5E5E4240
    // 内存加载操作
    // MOVS    R7, #1
    // R7 = 1;
    // PUSH    {R1,LR}
    // 栈操作
    // POP     {R0,R3,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_156B84
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_156b84(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x74;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x3140000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R4, #0
    // R4 = 0;
    // LSLS    R7, R1, #0xC
    // PUSH    {R1,R3,LR}
    // 栈操作
    // LDR     R0, [R5,#0x54]
    // 内存加载操作
    // LDRB    R4, [R6,#1]
    // 内存加载操作
    // LSLS    R0, R0, #4
    // ORN.W   R0, R2, #0
    // AND.W   R0, R2, #0x800000
    // AND.W   R9, R10, #0x3140000
    // STRH    R0, [R3,#0x28]
    // 内存存储操作
    // LDR     R4, [R5,#0x74]
    // 内存加载操作
    // STR     R3, [R4,#0x14]
    // 内存存储操作
    // LSLS    R0, R0, #4
    // B       loc_156472
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_161FC8
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_161fc8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R6, [R3,R5]
    // 内存加载操作
    // STM     R1, {R1-R3,R5,R7}
    // PUSH    {R0,R3,R5,LR}
    // 栈操作
    // LDRSB   R7, [R1,R3]
    // IT LE
    // SUBLE   R0, R1, R4
    // B       word_161B46
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16CA98
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_16ca98(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       loc_16C71A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_173D2C
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_173d2c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDMDB.W R4, {R4,R6,R8,R10-R12,PC}
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17CFA0
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_17cfa0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LSRS    R2, R1, #8
    // STRH    R3, [R5,#0x38]
    // 内存存储操作
    // LDRB    R5, [R1,#0x15]
    // 内存加载操作
    // PUSH    {R2,LR}
    // 栈操作
    // STR     R1, [R3,#4]
    // 内存存储操作
    // B       loc_17CF5A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1BBDFA
 * @note 指令数: 9, 标签数: 0
 */
uint32_t precise_func_1bbdfa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x74;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x19;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, [R6,#0x74]
    // 内存加载操作
    // STRB    R5, [R6,#1]
    // 内存存储操作
    // STR     R4, [R0,#0x54]
    // 内存存储操作
    // STR     R4, [R5,#0x54]
    // 内存存储操作
    // STR     R4, [R6,#0x54]
    // 内存存储操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // STRB    R5, [R0,#0x19]
    // 内存存储操作
    // LDR     R5, [R4,#0x64]
    // 内存加载操作
    // BXNS    LR

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C8490
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1c8490(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x294;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2AC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x70;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x154;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2EC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_164= -0x164
    // var_154= -0x154
    // arg_0=  0
    // arg_4=  4
    // arg_70=  0x70
    // arg_280=  0x280
    // arg_294=  0x294
    // arg_2AC=  0x2AC
    // arg_2EC=  0x2EC
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CF9E4
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_1cf9e4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0,R6,LR}
    // 栈操作
    // POP     {R1,R3,R4,R6,R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D0C84
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1d0c84(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFB;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R7, R7
    // SUBS    R7, #0x40 ; '@'
    // 算术运算
    // MOV     R5, R12
    // SUBS    R7, #0x40 ; '@'
    // 算术运算
    // MOV     R2, LR
    // SUBS    R7, #0x40 ; '@'
    // 算术运算
    // STRH    R0, [R1,#0x10]
    // 内存存储操作
    // SUBS    R7, #0xFB
    // 算术运算
    // MOV     R8, SP
    // SUBS    R7, #0x40 ; '@'
    // 算术运算
    // MOV     PC, PC          ; locret_1D0C9C
    // SUBS    R7, #0x40 ; '@'
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D7F98
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_1d7f98(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1D7FD8
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_1d7fd8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xB0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x17C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x178;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // arg_0=  0
    // arg_B0=  0xB0
    // arg_178=  0x178
    // arg_17C=  0x17C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DBA50
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_1dba50(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x66;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x220;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_220=  0x220
    // LSRS    R0, R3, #4
    // CMP     R0, #0x66 ; 'f'
    // 比较操作
    // B       loc_1DB476
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1DBCD8
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_1dbcd8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x178;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x374;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_A4=  0xA4
    // arg_178=  0x178
    // arg_374=  0x374
}

