# Makefile for MH25QH128 converted functions
# 编译所有转换的C函数

CC = arm-none-eabi-gcc
CFLAGS = -mcpu=cortex-m4 -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16
CFLAGS += -Wall -Wextra -O2 -g
CFLAGS += -I. -Isrc

# 源文件
SOURCES = $(wildcard converted_functions/batch_*.c)
OBJECTS = $(SOURCES:.c=.o)

# 目标
TARGET = mh25qh128_functions

.PHONY: all clean

all: $(TARGET).a

$(TARGET).a: $(OBJECTS)
	arm-none-eabi-ar rcs $@ $^
	@echo "库文件已创建: $@"

%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	rm -f $(OBJECTS) $(TARGET).a
	@echo "清理完成"

info:
	@echo "源文件数量: $(words $(SOURCES))"
	@echo "目标文件数量: $(words $(OBJECTS))"
	@echo "编译器: $(CC)"
	@echo "编译选项: $(CFLAGS)"
