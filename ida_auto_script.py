
import idaapi
import idc
import ida_funcs
import ida_hexrays
import ida_kernwin
import ida_auto

def wait_for_analysis():
    """等待IDA完成自动分析"""
    print("等待IDA完成自动分析...")
    ida_auto.auto_wait()
    print("自动分析完成")

def get_all_functions():
    """获取所有函数信息"""
    functions = []
    
    # 遍历所有函数
    for func_ea in idautils.Functions():
        func = ida_funcs.get_func(func_ea)
        if func:
            func_name = idc.get_func_name(func_ea)
            func_start = func.start_ea
            func_end = func.end_ea
            
            functions.append({
                'name': func_name,
                'start': func_start,
                'end': func_end,
                'size': func_end - func_start
            })
    
    return functions

def decompile_function(func_ea, func_name):
    """使用Hex-Rays反编译器反编译函数"""
    try:
        # 检查Hex-Rays是否可用
        if not ida_hexrays.init_hexrays_plugin():
            print(f"Hex-Rays反编译器不可用")
            return None
        
        # 反编译函数
        cfunc = ida_hexrays.decompile(func_ea)
        if cfunc:
            # 获取反编译的C代码
            c_code = str(cfunc)
            return c_code
        else:
            print(f"无法反编译函数 {func_name}")
            return None
            
    except Exception as e:
        print(f"反编译函数 {func_name} 时出错: {e}")
        return None

def export_functions_to_c():
    """导出所有函数到C文件"""
    print("开始导出函数...")
    
    # 等待分析完成
    wait_for_analysis()
    
    # 获取所有函数
    functions = get_all_functions()
    print(f"找到 {len(functions)} 个函数")
    
    # 创建输出目录
    output_dir = "ida_conversions"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 分批处理函数
    batch_size = 50
    batch_num = 1
    
    for i in range(0, len(functions), batch_size):
        batch_functions = functions[i:i + batch_size]
        
        c_content = f"""// IDA Pro反编译批次 {batch_num} - 高质量反编译结果
#include <stdint.h>
#include <stdbool.h>

"""
        
        converted_count = 0
        
        for func_info in batch_functions:
            func_name = func_info['name']
            func_ea = func_info['start']
            
            print(f"反编译函数: {func_name} @ 0x{func_ea:08X}")
            
            # 反编译函数
            c_code = decompile_function(func_ea, func_name)
            
            if c_code:
                # 添加函数注释
                c_content += f"""/**
 * @brief IDA Pro反编译函数
 * @note 原函数: {func_name}
 * @note 地址: 0x{func_ea:08X}
 * @note 大小: {func_info['size']} 字节
 */
{c_code}

"""
                converted_count += 1
            else:
                # 如果反编译失败，添加占位符
                c_content += f"""/**
 * @brief 反编译失败的函数
 * @note 原函数: {func_name}
 * @note 地址: 0x{func_ea:08X}
 */
void {func_name}_placeholder(void)
{{
    // 反编译失败，需要手工转换
}}

"""
        
        # 保存批次文件
        output_file = os.path.join(output_dir, f"ida_batch_{batch_num:03d}.c")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(c_content)
        
        print(f"批次 {batch_num} 完成，成功反编译 {converted_count}/{len(batch_functions)} 个函数")
        print(f"保存到: {output_file}")
        
        batch_num += 1
    
    print("所有函数导出完成！")

def main():
    """主函数"""
    print("IDA Pro自动化反编译脚本启动")
    
    # 导出函数
    export_functions_to_c()
    
    # 退出IDA
    idc.qexit(0)

if __name__ == "__main__":
    main()
