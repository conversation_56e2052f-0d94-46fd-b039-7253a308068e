// 精确转换批次 24 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7ECC4
 * @note 指令数: 36, 标签数: 1
 */
void precise_func_7ecc4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200034AC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R2, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R3, R0
    // CMP     R3, #1
    // 比较操作
    // BGE     loc_7ED14
    // 条件跳转
    // CMP     R3, R1
    // 比较操作
    // BEQ     loc_7ED10
    // 条件跳转
    // LDR     R0, =0x200034AC
    // 内存加载操作
    // MOVS    R4, #0x20 ; ' '
    // R4 = 0x20;
    // MULS    R4, R3
    // ADDS    R0, R0, R4
    // 算术运算
    // LDRB    R0, [R0,#2]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7ED10
    // 条件跳转
    // LDR     R0, =0x200034AC
    // 内存加载操作
    // MOVS    R4, #0x20 ; ' '
    // R4 = 0x20;
    // MULS    R4, R3
    // LDRH    R0, [R0,R4]
    // 内存加载操作
    // UXTH    R2, R2
    // 数据扩展操作
    // CMP     R0, R2
    // 比较操作
    // BNE     loc_7ED10
    // 条件跳转
    // LDR     R0, =0x200034AC
    // 内存加载操作
    // MOVS    R4, #0x20 ; ' '
    // R4 = 0x20;
    // MULS    R4, R3
    // ADDS    R0, R0, R4
    // 算术运算
    // LDRH    R0, [R0,#0xA]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_7ED0C
    // 条件跳转
    // LDR     R0, =0x200034AC
    // 内存加载操作
    // MOVS    R4, #0x20 ; ' '
    // R4 = 0x20;
    // MULS    R4, R3
    // ADDS    R0, R0, R4
    // 算术运算
    // LDRH    R0, [R0,#0xC]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7ED10
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7ED30
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_7ed30(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003757;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R4, R1
    // LDR     R0, =0x20003757
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7ED42
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_7F310
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F318
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_7f318(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
    // arg_4=  4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F40E
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7f40e(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // CMP     R1, #0
    // 比较操作
    // BMI     loc_7F41A
    // CMP     R1, #1
    // 比较操作
    // BLT     loc_7F41E
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F452
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7f452(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R2, R0
    // CMP     R2, #0
    // 比较操作
    // BMI     loc_7F45E
    // CMP     R2, #1
    // 比较操作
    // BLT     loc_7F462
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F4AC
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7f4ac(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R2, R0
    // CMP     R2, #0
    // 比较操作
    // BMI     loc_7F4B8
    // CMP     R2, #1
    // 比较操作
    // BLT     loc_7F4BC
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F4F8
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7f4f8(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // CMP     R1, #0
    // 比较操作
    // BMI     loc_7F504
    // CMP     R1, #1
    // 比较操作
    // BLT     loc_7F508
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F554
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_7f554(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // CMP     R0, #0
    // 比较操作
    // BMI     loc_7F55E
    // CMP     R0, #1
    // 比较操作
    // BLT     loc_7F560
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F5D0
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7f5d0(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R2, R0
    // CMP     R2, #0
    // 比较操作
    // BMI     loc_7F5DC
    // CMP     R2, #1
    // 比较操作
    // BLT     loc_7F5E0
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F62A
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7f62a(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R2, R0
    // CMP     R2, #0
    // 比较操作
    // BMI     loc_7F636
    // CMP     R2, #1
    // 比较操作
    // BLT     loc_7F63A
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F688
 * @note 指令数: 13, 标签数: 0
 */
uint32_t precise_func_7f688(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x7F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // ADDS    R0, R0, R0
    // 算术运算
    // BCS     loc_7F6AA
    // LSRS    R1, R0, #0x18
    // SUBS    R1, #0x7F
    // 算术运算
    // BMI     loc_7F6AA
    // NEGS    R1, R1
    // ADDS    R1, #0x1F
    // 算术运算
    // BMI     loc_7F6A4
    // LSLS    R0, R0, #7
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ORRS    R0, R2
    // LSRS    R0, R1
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F6AE
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_7f6ae(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_7F6AE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F6B0
 * @note 指令数: 7, 标签数: 1
 */
void precise_func_7f6b0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x9D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0x9D
    // R1 = 0x9D;
    // MOVS    R2, R0
    // BEQ     locret_7F6CC
    // 条件跳转
    // BMI     loc_7F6BE
    // SUBS    R1, R1, #1
    // 算术运算
    // ADDS    R2, R2, R2
    // 算术运算
    // BPL     loc_7F6B8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F6CE
 * @note 指令数: 28, 标签数: 0
 */
void precise_func_7f6ce(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R5, R0
    // EORS    R5, R1
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ANDS    R5, R2
    // ADDS    R3, R1, R1
    // 算术运算
    // ADDS    R4, R0, R0
    // 算术运算
    // LSRS    R4, R4, #0x18
    // BEQ     loc_7F734
    // 条件跳转
    // LSRS    R3, R3, #0x18
    // BEQ     loc_7F746
    // 条件跳转
    // CMP     R4, #0xFF
    // 比较操作
    // BEQ     loc_7F754
    // 条件跳转
    // CMP     R3, #0xFF
    // 比较操作
    // BEQ     loc_7F75E
    // 条件跳转
    // SUBS    R4, R4, R3
    // 算术运算
    // LSLS    R0, R0, #8
    // LSLS    R1, R1, #8
    // ORRS    R0, R2
    // ORRS    R1, R2
    // LSRS    R3, R0, #1
    // LSRS    R0, R1, #1
    // LSRS    R1, R2, #0x17
    // SUBS    R3, R3, R0
    // 算术运算
    // BCS     loc_7F708
    // SUBS    R4, R4, #1
    // 算术运算
    // ADDS    R3, R3, R3
    // 算术运算
    // ADDS    R3, R3, R0
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F778
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_7f778(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R2-R6,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R5, R1
    // MOVS    R4, R2
    // MOVS    R0, R4
    // ADDS    R0, R0, #2
    // 算术运算
    // BL      sub_7644C
    // 调用函数: sub_7644C();
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRH    R0, [R1,#0x18+var_14]
    // 内存存储操作
    // LDRB    R0, [R4]
    // 内存加载操作
    // LSLS    R0, R0, #0x1A
    // BPL     loc_7F7A2
    // MOV     R0, SP
    // LDRH    R0, [R0,#0x18+var_14]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // ORRS    R1, R0
    // MOV     R0, SP
    // STRH    R1, [R0,#0x18+var_14]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F87A
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_7f87a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000C0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ASRS    R0, R0, #8
    // LDR     R2, =0x200000C0
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STR     R1, [R2,#0x20]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F888
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_7f888(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_18= -0x18
    // PUSH    {R2,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R7, R0
    // MOVS    R6, R1
    // MOVS    R5, #0
    // R5 = 0;
    // MVNS    R5, R5
    // LDR     R0, [SP,#0x20+var_18]
    // 内存加载操作
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F960
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_7f960(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000C0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R2, R5
    // UXTB    R2, R2
    // 数据扩展操作
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // MOVS    R1, R0
    // LSLS    R1, R1, #0x1F
    // LSRS    R1, R1, #0x1F
    // MOVS    R0, R4
    // BL      sub_7CC74
    // 调用函数: sub_7CC74();
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_7F9A0
    // 条件跳转
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // ORRS    R1, R0
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // STRB    R1, [R0,#0x10]
    // 内存存储操作
    // B       locret_7F9B8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F9BA
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_7f9ba(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000C0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // ADDS    R0, #0xC
    // 算术运算
    // BL      sub_80704
    // 调用函数: sub_80704();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_7F9D6
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_7F9E6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F9E8
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_7f9e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000C0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_806CC
    // 调用函数: sub_806CC();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7F9FC
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_7f9fc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_7F960
    // 调用函数: sub_7F960();
    // MOVS    R1, R5
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_7CBA8
    // 调用函数: sub_7CBA8();
    // MOVS    R0, #0
    // R0 = 0;
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FA18
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_7fa18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000C0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x12;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // LSLS    R0, R0, #0x12
    // BPL     loc_7FA8A
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FB50
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_7fb50(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FC6E
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_7fc6e(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FD1E
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_7fd1e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000C0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R1, R4
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MULS    R2, R5
    // ADDS    R0, R0, R2
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_80698
    // 调用函数: sub_80698();
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FD38
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_7fd38(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R5, R2
    // CMP     R5, #0
    // 比较操作
    // BNE     loc_7FD4A
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // B       locret_7FD58
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FD5A
 * @note 指令数: 34, 标签数: 0
 */
void precise_func_7fd5a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000C0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_7FDA2
    // MOVS    R0, R4
    // BL      sub_7F9BA
    // 调用函数: sub_7F9BA();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_7FDA2
    // 条件跳转
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_7FDA2
    // MOVS    R0, R4
    // BL      sub_7CCA0
    // 调用函数: sub_7CCA0();
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // MOVS    R1, #0xFE
    // R1 = 0xFE;
    // ANDS    R1, R0
    // LDR     R0, =0x200000C0
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // STRB    R1, [R0,#0x10]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FDA8
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7fda8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // ASRS    R5, R5, #8
    // MOVS    R1, R4
    // MOVS    R0, R5
    // BL      sub_7CCB2
    // 调用函数: sub_7CCB2();
    // POP     {R0,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FDC4
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_7fdc4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE000E100;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #1
    // R1 = 1;
    // LSLS    R2, R0, #0x1B
    // LSRS    R2, R2, #0x1B
    // LSLS    R1, R2
    // LDR     R2, =0xE000E100
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FDD2
 * @note 指令数: 38, 标签数: 0
 */
void precise_func_7fdd2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BPL     loc_7FE1E
    // LDR     R4, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R2, R0, #0x1C
    // LSRS    R2, R2, #0x1C
    // SUBS    R2, #8
    // 算术运算
    // LSRS    R5, R2, #2
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R5, R2
    // LDR     R2, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R3, R0, #0x1C
    // LSRS    R3, R3, #0x1C
    // SUBS    R3, #8
    // 算术运算
    // LSRS    R3, R3, #2
    // MOVS    R6, #4
    // R6 = 4;
    // MULS    R3, R6
    // LDR     R2, [R2,R3]
    // 内存加载操作
    // MOVS    R3, #0xFF
    // R3 = 0xFF;
    // LSLS    R6, R0, #0x1E
    // LSRS    R6, R6, #0x1E
    // MOVS    R7, #8
    // R7 = 8;
    // MULS    R6, R7
    // LSLS    R3, R6
    // BICS    R2, R3
    // LSLS    R3, R1, #6
    // UXTB    R3, R3
    // 数据扩展操作
    // LSLS    R6, R0, #0x1E
    // LSRS    R6, R6, #0x1E
    // MOVS    R7, #8
    // R7 = 8;
    // MULS    R6, R7
    // LSLS    R3, R6
    // ORRS    R3, R2
    // STR     R3, [R4,R5]
    // 内存存储操作
    // B       locret_7FE54
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FE56
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7fe56(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // SUBS    R0, R4, #1
    // 算术运算
    // MOVS    R1, #0x1000000
    // R1 = 0x1000000;
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_7FE68
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_7FE86
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FE88
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_7fe88(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R1, R5
    // MOVS    R0, R4
    // SXTB    R0, R0
    // 数据扩展操作
    // BL      sub_7FDD2
    // 调用函数: sub_7FDD2();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FE9C
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7fe9c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // SXTB    R0, R0
    // 数据扩展操作
    // BL      sub_7FDC4
    // 调用函数: sub_7FDC4();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FEAA
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_7feaa(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // BL      sub_7FE56
    // 调用函数: sub_7FE56();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FED0
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_7fed0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000164;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40021004;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x801229C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_80D98
    // 调用函数: sub_80D98();
    // LDR     R1, =0x801229C
    // 内存加载操作
    // LDR     R2, =0x40021004
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // LSRS    R2, R2, #4
    // LSLS    R2, R2, #0x1C
    // LSRS    R2, R2, #0x1C
    // LDRB    R1, [R1,R2]
    // 内存加载操作
    // LSRS    R0, R1
    // LDR     R1, =0x20000164
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20000164
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // POP     {R1,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FEF0
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_7fef0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021004;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x801229C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_7FED0
    // 调用函数: sub_7FED0();
    // LDR     R1, =0x801229C
    // 内存加载操作
    // LDR     R2, =0x40021004
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // LSRS    R2, R2, #8
    // LSLS    R2, R2, #0x1D
    // LSRS    R2, R2, #0x1D
    // LDRB    R1, [R1,R2]
    // 内存加载操作
    // LSRS    R0, R1
    // POP     {R1,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FF14
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_7ff14(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200034E8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // PUSH    {R2,R3}
    // 栈操作
    // MOVS    R7, R1
    // MOVS    R5, #1
    // R5 = 1;
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R0, =0x200034E8
    // 内存加载操作
    // LDRB    R0, [R0,#0x18]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_7FF2C
    // 条件跳转
    // MOVS    R0, #2
    // R0 = 2;
    // B       locret_7FFAC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FFB8
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_7ffb8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xCDEF89AB;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x45670123;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x40022004;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40022010;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x18
    // BPL     loc_7FFD2
    // LDR     R0, =0x45670123
    // 内存加载操作
    // LDR     R1, =0x40022004
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0xCDEF89AB
    // 内存加载操作
    // LDR     R1, =0x40022004
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_7FFD4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FFDC
 * @note 指令数: 8, 标签数: 0
 */
uint32_t precise_func_7ffdc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022010;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80
    // R1 = 0x80;
    // ORRS    R1, R0
    // LDR     R0, =0x40022010
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7FFEC
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_7ffec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40022010;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200034E8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x40022014;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x200034E8
    // 内存加载操作
    // STR     R1, [R2,#0x1C]
    // 内存存储操作
    // LDR     R1, =0x40022010
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #2
    // R2 = 2;
    // ORRS    R2, R1
    // LDR     R1, =0x40022010
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // LDR     R1, =0x40022014
    // 内存加载操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80000
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_80000(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022010;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R1, =0x40022010
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #0x40 ; '@'
    // R2 = 0x40;
    // ORRS    R2, R1
    // LDR     R1, =0x40022010
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80010
 * @note 指令数: 11, 标签数: 0
 */
uint32_t precise_func_80010(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40022010;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200034E8;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x200034E8
    // 内存加载操作
    // STR     R2, [R3,#0x1C]
    // 内存存储操作
    // LDR     R2, =0x40022010
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // MOVS    R3, #1
    // R3 = 1;
    // ORRS    R3, R2
    // LDR     R2, =0x40022010
    // 内存加载操作
    // STR     R3, [R2]
    // 内存存储操作
    // STRH    R1, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_80026
 * @note 指令数: 18, 标签数: 1
 */
void precise_func_80026(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4002200C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // BL      sub_80E78
    // 调用函数: sub_80E78();
    // MOVS    R5, R0
    // LDR     R0, =0x4002200C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_80052
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // CMP     R4, R0
    // 比较操作
    // BEQ     loc_80030
    // 条件跳转
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_8004E
    // 条件跳转
    // BL      sub_80E78
    // 调用函数: sub_80E78();
    // SUBS    R0, R0, R5
    // 算术运算
    // CMP     R4, R0
    // 比较操作
    // BCS     loc_80030
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8007C
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_8007c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x4002200C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200034E8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x4002200C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1B
    // BPL     loc_80092
    // LDR     R0, =0x200034E8
    // 内存加载操作
    // LDR     R0, [R0,#0x1C]
    // 内存加载操作
    // MOVS    R1, #2
    // R1 = 2;
    // ORRS    R1, R0
    // LDR     R0, =0x200034E8
    // 内存加载操作
    // STR     R1, [R0,#0x1C]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_800CC
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_800cc(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // B       loc_800D2
    // 无条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_800E0
 * @note 指令数: 4, 标签数: 1
 */
void precise_func_800e0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // B       loc_800E8
    // 无条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // SUBS    R2, R2, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_800F8
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_800f8(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R1, R0
    // LDR     R3, [R1,#4]
    // 内存加载操作
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ANDS    R2, R3
    // MOVS    R0, #1
    // R0 = 1;
    // LSLS    R3, R3, #0xC
    // LSRS    R3, R3, #0xC
    // STR     R3, [R1,#4]
    // 内存存储操作
    // BNE     loc_80126
    // 条件跳转
    // LDR     R3, [R1]
    // 内存加载操作
    // CMP     R3, #0
    // 比较操作
    // BNE     loc_80126
    // 条件跳转
    // B       loc_80132
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8013C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_8013c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R4, #0x200000
    // R4 = 0x200000;
    // LSLS    R5, R1, #1
    // CMN     R4, R5
    // 比较操作
    // BHI     loc_80160
    // LSLS    R5, R3, #1
    // CMN     R4, R5
    // 比较操作
    // BHI     loc_80160
    // MOVS    R4, R1
    // ORRS    R4, R3
    // LSLS    R4, R4, #1
    // ORRS    R4, R0
    // ORRS    R4, R2
    // BCS     loc_80164
    // CMP     R1, R3
    // 比较操作
    // BNE     loc_80160
    // 条件跳转
    // CMP     R0, R2
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_8016E
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_8016e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R4, #0x200000
    // R4 = 0x200000;
    // LSLS    R5, R1, #1
    // CMN     R4, R5
    // 比较操作
    // BHI     loc_80192
    // LSLS    R5, R3, #1
    // CMN     R4, R5
    // 比较操作
    // BHI     loc_80192
    // MOVS    R4, R1
    // ORRS    R4, R3
    // LSLS    R4, R4, #1
    // ORRS    R4, R0
    // ORRS    R4, R2
    // BCS     loc_80196
    // CMP     R3, R1
    // 比较操作
    // BNE     loc_80192
    // 条件跳转
    // CMP     R2, R0
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_801A0
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_801a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x7FF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // LSLS    R0, R0, #1
    // LSRS    R0, R0, #0x15
    // LDR     R6, =0x7FF
    // 内存加载操作
    // CMP     R0, R6
    // 比较操作
    // BNE     loc_801C2
    // 条件跳转
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // LSLS    R0, R0, #0xC
    // BNE     loc_801BE
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_801EC
    // 条件跳转
}

