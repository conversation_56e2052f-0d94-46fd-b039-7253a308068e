// 大规模手工转换批次 5 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1A962
 * @note 指令数: 5, 类型: simple_function
 */
uint32_t manual_1a962(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1A96C
 * @note 指令数: 3, 类型: simple_function
 */
uint32_t manual_1a96c(uint16_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1A972
 * @note 指令数: 8, 类型: simple_function
 */
uint32_t manual_1a972(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1A982
 * @note 指令数: 3, 类型: simple_function
 */
uint32_t manual_1a982(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1A988
 * @note 指令数: 31, 类型: data_processing
 */
uint32_t manual_1a988(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1A9CC
 * @note 指令数: 17, 类型: simple_function
 */
uint32_t manual_1a9cc(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200000 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1A9FC
 * @note 指令数: 204, 类型: data_processing
 */
void manual_1a9fc(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200000 = (volatile uint32_t *)0x200000;
    volatile uint32_t *addr_7FF = (volatile uint32_t *)0x7FF;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_FF00000 = (volatile uint32_t *)0xFF00000;

    // 局部变量

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1AC54
 * @note 指令数: 38, 类型: control_function
 */
void manual_1ac54(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_3FF00000 = (volatile uint32_t *)0x3FF00000;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1ACB2
 * @note 指令数: 30, 类型: control_function
 */
uint32_t manual_1acb2(void)
{
    // 内存地址定义
    volatile uint32_t *addr_22 = (volatile uint32_t *)0x22;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20008080 = (volatile uint32_t *)0x20008080;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;
    return result;
}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_1ACF6
 * @note 指令数: 64, 类型: lookup_table
 */
void manual_1acf6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_8008266 = (volatile uint32_t *)0x8008266;
    volatile uint32_t *addr_20005B3E = (volatile uint32_t *)0x20005B3E;
    volatile uint32_t *addr_20008080 = (volatile uint32_t *)0x20008080;
    volatile uint32_t *addr_20005B3C = (volatile uint32_t *)0x20005B3C;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1ADBE
 * @note 指令数: 107, 类型: control_function
 */
void manual_1adbe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200079FC = (volatile uint32_t *)0x200079FC;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_200075C4 = (volatile uint32_t *)0x200075C4;
    volatile uint32_t *addr_28 = (volatile uint32_t *)0x28;
    volatile uint32_t *addr_19 = (volatile uint32_t *)0x19;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1AED2
 * @note 指令数: 764, 类型: array_operation
 */
void manual_1aed2(void)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_20008174 = (volatile uint32_t *)0x20008174;
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;
    volatile uint32_t *addr_18E = (volatile uint32_t *)0x18E;
    volatile uint32_t *addr_60 = (volatile uint32_t *)0x60;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1B69E
 * @note 指令数: 139, 类型: array_operation
 */
void manual_1b69e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20008154 = (volatile uint32_t *)0x20008154;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_34 = (volatile uint32_t *)0x34;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;
    volatile uint32_t *addr_20008130 = (volatile uint32_t *)0x20008130;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_1B804
 * @note 指令数: 173, 类型: lookup_table
 */
void manual_1b804(void)
{
    // 内存地址定义
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;
    volatile uint32_t *addr_60 = (volatile uint32_t *)0x60;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_34 = (volatile uint32_t *)0x34;
    volatile uint32_t *addr_20008116 = (volatile uint32_t *)0x20008116;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1B984
 * @note 指令数: 23, 类型: array_operation
 */
void manual_1b984(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007F78 = (volatile uint32_t *)0x20007F78;
    volatile uint32_t *addr_20003B38 = (volatile uint32_t *)0x20003B38;
    volatile uint32_t *addr_20000265 = (volatile uint32_t *)0x20000265;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1B9C8
 * @note 指令数: 27, 类型: array_operation
 */
uint32_t manual_1b9c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200080F4 = (volatile uint32_t *)0x200080F4;
    volatile uint32_t *addr_20007F78 = (volatile uint32_t *)0x20007F78;
    volatile uint32_t *addr_2000816D = (volatile uint32_t *)0x2000816D;
    volatile uint32_t *addr_20007F70 = (volatile uint32_t *)0x20007F70;
    volatile uint32_t *addr_20007F68 = (volatile uint32_t *)0x20007F68;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1BA5C
 * @note 指令数: 305, 类型: array_operation
 */
void manual_1ba5c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200080F6 = (volatile uint32_t *)0x200080F6;
    volatile uint32_t *addr_2000816E = (volatile uint32_t *)0x2000816E;
    volatile uint32_t *addr_20008171 = (volatile uint32_t *)0x20008171;
    volatile uint32_t *addr_20007F78 = (volatile uint32_t *)0x20007F78;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1BD80
 * @note 指令数: 92, 类型: array_operation
 */
uint32_t manual_1bd80(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8001800 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *addr_20000262 = (volatile uint32_t *)0x20000262;
    volatile uint32_t *addr_8001814 = (volatile uint32_t *)0x8001814;
    volatile uint32_t *addr_20008145 = (volatile uint32_t *)0x20008145;
    volatile uint32_t *addr_20008144 = (volatile uint32_t *)0x20008144;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1BE8C
 * @note 指令数: 211, 类型: array_operation
 */
void manual_1be8c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_8001800 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *addr_8001814 = (volatile uint32_t *)0x8001814;
    volatile uint32_t *addr_20008145 = (volatile uint32_t *)0x20008145;
    volatile uint32_t *addr_20008144 = (volatile uint32_t *)0x20008144;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C0EE
 * @note 指令数: 14, 类型: control_function
 */
void manual_1c0ee(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C10E
 * @note 指令数: 9, 类型: control_function
 */
void manual_1c10e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_8002014 = (volatile uint32_t *)0x8002014;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C124
 * @note 指令数: 15, 类型: control_function
 */
void manual_1c124(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_20000008 = (volatile uint32_t *)0x20000008;
    volatile uint32_t *addr_7C = (volatile uint32_t *)0x7C;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C148
 * @note 指令数: 7, 类型: simple_function
 */
uint32_t manual_1c148(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8001800 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *addr_8001804 = (volatile uint32_t *)0x8001804;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C15A
 * @note 指令数: 9, 类型: control_function
 */
void manual_1c15a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;
    volatile uint32_t *addr_200070A6 = (volatile uint32_t *)0x200070A6;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C170
 * @note 指令数: 32, 类型: control_function
 */
void manual_1c170(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_52 = (volatile uint32_t *)0x52;
    volatile uint32_t *addr_20007054 = (volatile uint32_t *)0x20007054;
    volatile uint32_t *addr_2000005A = (volatile uint32_t *)0x2000005A;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C1C4
 * @note 指令数: 9, 类型: control_function
 */
void manual_1c1c4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;
    volatile uint32_t *addr_200070B0 = (volatile uint32_t *)0x200070B0;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C1DA
 * @note 指令数: 32, 类型: control_function
 */
void manual_1c1da(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_20007054 = (volatile uint32_t *)0x20007054;
    volatile uint32_t *addr_20000064 = (volatile uint32_t *)0x20000064;
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C22E
 * @note 指令数: 53, 类型: array_operation
 */
void manual_1c22e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;
    volatile uint32_t *addr_80152C4 = (volatile uint32_t *)0x80152C4;
    volatile uint32_t *addr_B4 = (volatile uint32_t *)0xB4;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_7300 = (volatile uint32_t *)0x7300;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C2AC
 * @note 指令数: 109, 类型: array_operation
 */
void manual_1c2ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_B5 = (volatile uint32_t *)0xB5;
    volatile uint32_t *addr_48 = (volatile uint32_t *)0x48;
    volatile uint32_t *addr_80152C4 = (volatile uint32_t *)0x80152C4;
    volatile uint32_t *addr_B4 = (volatile uint32_t *)0xB4;
    volatile uint32_t *addr_41 = (volatile uint32_t *)0x41;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C3B0
 * @note 指令数: 89, 类型: array_operation
 */
void manual_1c3b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_B5 = (volatile uint32_t *)0xB5;
    volatile uint32_t *addr_80152C4 = (volatile uint32_t *)0x80152C4;
    volatile uint32_t *addr_41 = (volatile uint32_t *)0x41;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_7300 = (volatile uint32_t *)0x7300;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C484
 * @note 指令数: 38, 类型: array_operation
 */
void manual_1c484(void)
{
    // 内存地址定义
    volatile uint32_t *addr_8016760 = (volatile uint32_t *)0x8016760;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C4E0
 * @note 指令数: 89, 类型: array_operation
 */
void manual_1c4e0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_26E = (volatile uint32_t *)0x26E;
    volatile uint32_t *addr_280 = (volatile uint32_t *)0x280;
    volatile uint32_t *addr_41 = (volatile uint32_t *)0x41;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C600
 * @note 指令数: 72, 类型: array_operation
 */
void manual_1c600(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8015598 = (volatile uint32_t *)0x8015598;
    volatile uint32_t *addr_41 = (volatile uint32_t *)0x41;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_6E = (volatile uint32_t *)0x6E;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C6B0
 * @note 指令数: 2, 类型: simple_function
 */
uint32_t manual_1c6b0(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C6B4
 * @note 指令数: 15, 类型: array_operation
 */
void manual_1c6b4(uint32_t param0)
{
    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C6D8
 * @note 指令数: 46, 类型: lookup_table
 */
void manual_1c6d8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_20008142 = (volatile uint32_t *)0x20008142;
    volatile uint32_t *addr_20007054 = (volatile uint32_t *)0x20007054;
    volatile uint32_t *addr_7C = (volatile uint32_t *)0x7C;
    volatile uint32_t *addr_20000008 = (volatile uint32_t *)0x20000008;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C764
 * @note 指令数: 36, 类型: control_function
 */
void manual_1c764(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_3890 = (volatile uint32_t *)0x3890;
    volatile uint32_t *addr_8016650 = (volatile uint32_t *)0x8016650;
    volatile uint32_t *addr_3810 = (volatile uint32_t *)0x3810;
    volatile uint32_t *addr_3800 = (volatile uint32_t *)0x3800;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C7C8
 * @note 指令数: 53, 类型: control_function
 */
void manual_1c7c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_8016650 = (volatile uint32_t *)0x8016650;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_8015888 = (volatile uint32_t *)0x8015888;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C848
 * @note 指令数: 53, 类型: control_function
 */
void manual_1c848(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_8016650 = (volatile uint32_t *)0x8016650;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_8015888 = (volatile uint32_t *)0x8015888;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C8C8
 * @note 指令数: 57, 类型: control_function
 */
void manual_1c8c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_3890 = (volatile uint32_t *)0x3890;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8016650 = (volatile uint32_t *)0x8016650;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C95C
 * @note 指令数: 50, 类型: control_function
 */
void manual_1c95c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_3B10 = (volatile uint32_t *)0x3B10;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1C9DA
 * @note 指令数: 16, 类型: control_function
 */
void manual_1c9da(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007E40 = (volatile uint32_t *)0x20007E40;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1CA00
 * @note 指令数: 64, 类型: control_function
 */
void manual_1ca00(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_3910 = (volatile uint32_t *)0x3910;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_1CAA2
 * @note 指令数: 100, 类型: lookup_table
 */
void manual_1caa2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_39 = (volatile uint32_t *)0x39;
    volatile uint32_t *addr_4B10 = (volatile uint32_t *)0x4B10;
    volatile uint32_t *addr_3A = (volatile uint32_t *)0x3A;
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_1CBA6
 * @note 指令数: 108, 类型: lookup_table
 */
void manual_1cba6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_60 = (volatile uint32_t *)0x60;
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_4C = (volatile uint32_t *)0x4C;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1CCAE
 * @note 指令数: 16, 类型: control_function
 */
void manual_1ccae(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007738 = (volatile uint32_t *)0x20007738;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1CCF4
 * @note 指令数: 3, 类型: control_function
 */
void manual_1ccf4(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1CCFC
 * @note 指令数: 183, 类型: array_operation
 */
void manual_1ccfc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_200080A4 = (volatile uint32_t *)0x200080A4;
    volatile uint32_t *addr_8001800 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;
    volatile uint32_t *addr_2000812C = (volatile uint32_t *)0x2000812C;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_1CEA2
 * @note 指令数: 113, 类型: array_operation
 */
void manual_1cea2(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_2000817D = (volatile uint32_t *)0x2000817D;
    volatile uint32_t *addr_200080A4 = (volatile uint32_t *)0x200080A4;
    volatile uint32_t *addr_803F800 = (volatile uint32_t *)0x803F800;
    volatile uint32_t *addr_803F804 = (volatile uint32_t *)0x803F804;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1CFA8
 * @note 指令数: 8, 类型: simple_function
 */
uint32_t manual_1cfa8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008180 = (volatile uint32_t *)0x20008180;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

