#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动验证分析工具
逐行对比汇编代码和转换后的C代码
"""

def analyze_sub_14B18():
    """手动分析sub_14B18函数"""
    print("=== sub_14B18 手动验证分析 ===\n")
    
    # 汇编代码（从实际文件中获取）
    asm_code = """
sub_14B18
UXTB    R0, R0
CMP     R0, #0x10
BLT     loc_14B24
FLDS    S0, =0.0
B       locret_14B32

loc_14B24
LDR.W   R1, =0x20007584
UXTB    R0, R0
ADDS.W  R0, R1, R0,LSL#2
FLDS    S0, [R0]

locret_14B32
BX      LR
; End of function sub_14B18
"""
    
    # 转换后的C代码
    c_code = """
float precise_func_14b18(uint8_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007584;

    // 局部变量
    float result = 0.0f;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}
"""
    
    print("汇编代码分析:")
    print("=" * 50)
    
    # 逐行分析汇编代码
    asm_analysis = [
        ("UXTB R0, R0", "将R0寄存器扩展为8位无符号整数"),
        ("CMP R0, #0x10", "比较R0与16(0x10)"),
        ("BLT loc_14B24", "如果小于16，跳转到loc_14B24"),
        ("FLDS S0, =0.0", "将0.0加载到浮点寄存器S0"),
        ("B locret_14B32", "无条件跳转到返回点"),
        ("loc_14B24:", "标签：数组访问分支"),
        ("LDR.W R1, =0x20007584", "加载浮点数组基地址到R1"),
        ("UXTB R0, R0", "再次确保R0为8位无符号"),
        ("ADDS.W R0, R1, R0,LSL#2", "计算数组元素地址：base + index*4"),
        ("FLDS S0, [R0]", "从计算的地址加载浮点数到S0"),
        ("locret_14B32:", "返回标签"),
        ("BX LR", "返回到调用者")
    ]
    
    for instr, desc in asm_analysis:
        print(f"  {instr:<20} -> {desc}")
    
    print("\n" + "=" * 80)
    print("❌ 转换问题分析:")
    print("=" * 50)
    
    issues = [
        "🔴 函数签名错误：应该是 float sub_14B18(uint8_t index)",
        "🔴 逻辑完全错误：C代码没有实现汇编的实际逻辑",
        "🔴 条件判断反向：汇编是BLT(小于跳转)，C代码是if(param0 < 0x10)",
        "🔴 缺少数组访问：汇编的核心是浮点数组访问，C代码完全没有",
        "🔴 多余的循环：汇编中没有循环，C代码添加了不存在的for循环",
        "🔴 错误的内存操作：汇编没有读取0x10地址，C代码却有",
        "🔴 返回值错误：应该返回数组中的浮点数，不是param0或0"
    ]
    
    for issue in issues:
        print(f"  {issue}")
    
    print("\n" + "=" * 80)
    print("✅ 正确的C代码应该是:")
    print("=" * 50)
    
    correct_c_code = """
float sub_14B18(uint8_t index)
{
    // UXTB R0, R0 - 确保索引为8位无符号
    index = index & 0xFF;
    
    // CMP R0, #0x10 - 比较索引与16
    // BLT loc_14B24 - 如果小于16，跳转到数组访问
    if (index >= 0x10) {
        // FLDS S0, =0.0 - 返回0.0
        return 0.0f;
    }
    
    // loc_14B24:
    // LDR.W R1, =0x20007584 - 加载浮点数组基地址
    volatile float *float_array = (volatile float *)0x20007584;
    
    // UXTB R0, R0 - 再次确保索引为8位
    // ADDS.W R0, R1, R0,LSL#2 - 计算数组元素地址
    // FLDS S0, [R0] - 加载浮点数
    return float_array[index];
    
    // locret_14B32:
    // BX LR - 函数返回
}
"""
    
    print(correct_c_code)
    
    return {
        'function_name': 'sub_14B18',
        'conversion_accuracy': 5,  # 5/100分
        'major_issues': 7,
        'correct_logic': False,
        'usable': False
    }

def analyze_sub_14B34():
    """手动分析sub_14B34函数"""
    print("\n=== sub_14B34 手动验证分析 ===\n")
    
    # 获取更多汇编代码
    print("需要获取完整的sub_14B34汇编代码...")
    
    # 这里应该是完整的汇编代码，但由于文件读取限制，我们基于已知的部分进行分析
    partial_asm = """
sub_14B34
LDR.W   R1, =0x2000797C
UXTB    R0, R0
LDRH.W  R1, [R1,R0,LSL#1]
CMP     R1, #6
BLT     loc_14B4E
...
"""
    
    print("汇编代码分析（部分）:")
    print("=" * 50)
    
    asm_analysis = [
        ("LDR.W R1, =0x2000797C", "加载16位数组基地址到R1"),
        ("UXTB R0, R0", "确保R0为8位无符号索引"),
        ("LDRH.W R1, [R1,R0,LSL#1]", "读取16位值：array[index]"),
        ("CMP R1, #6", "比较读取的值与6"),
        ("BLT loc_14B4E", "如果小于6，跳转到loc_14B4E")
    ]
    
    for instr, desc in asm_analysis:
        print(f"  {instr:<25} -> {desc}")
    
    print("\n转换后的C代码问题:")
    print("=" * 50)
    
    issues = [
        "🔴 函数签名错误：参数数量和类型不正确",
        "🔴 逻辑不匹配：没有实现16位数组读取和比较",
        "🔴 缺少查表操作：汇编中有复杂的查表逻辑",
        "🔴 多余的循环：汇编中没有循环结构"
    ]
    
    for issue in issues:
        print(f"  {issue}")
    
    return {
        'function_name': 'sub_14B34',
        'conversion_accuracy': 10,  # 10/100分
        'major_issues': 4,
        'correct_logic': False,
        'usable': False
    }

def generate_manual_verification_report():
    """生成手动验证报告"""
    print("\n" + "=" * 80)
    print("🔍 手动验证总结报告")
    print("=" * 80)
    
    # 分析多个函数
    results = []
    results.append(analyze_sub_14B18())
    results.append(analyze_sub_14B34())
    
    print("\n📊 验证结果统计:")
    print("=" * 50)
    
    total_accuracy = sum(r['conversion_accuracy'] for r in results)
    avg_accuracy = total_accuracy / len(results)
    total_issues = sum(r['major_issues'] for r in results)
    usable_count = sum(1 for r in results if r['usable'])
    
    print(f"  检查函数数量: {len(results)}")
    print(f"  平均准确度: {avg_accuracy:.1f}/100")
    print(f"  主要问题总数: {total_issues}")
    print(f"  可用函数数量: {usable_count}")
    print(f"  可用率: {usable_count/len(results)*100:.1f}%")
    
    print("\n🚨 关键发现:")
    print("=" * 50)
    
    findings = [
        "❌ 转换后的C代码与汇编逻辑严重不符",
        "❌ 函数签名（参数类型、返回类型）大部分错误",
        "❌ 核心业务逻辑完全缺失或错误",
        "❌ 添加了汇编中不存在的循环和操作",
        "❌ 内存访问模式不正确",
        "❌ 条件判断逻辑错误或缺失",
        "❌ 当前转换结果无法替代原汇编功能"
    ]
    
    for finding in findings:
        print(f"  {finding}")
    
    print("\n💡 改进建议:")
    print("=" * 50)
    
    suggestions = [
        "✅ 需要完全重新转换，逐行分析汇编指令",
        "✅ 正确映射ARM寄存器到C变量",
        "✅ 准确实现条件分支和跳转逻辑",
        "✅ 正确处理内存访问和数组操作",
        "✅ 确保函数签名与汇编调用约定匹配",
        "✅ 移除不存在的循环和多余操作",
        "✅ 每个函数都需要手动验证"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    print("\n🎯 结论:")
    print("=" * 50)
    print("  当前的转换结果存在严重问题，需要完全重新进行转换。")
    print("  建议使用手工精确转换方法，确保每个函数都完全复刻汇编逻辑。")

if __name__ == "__main__":
    generate_manual_verification_report()
