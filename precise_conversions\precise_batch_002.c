// 精确转换批次 2 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_175CE
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_175ce(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000815E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008163;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20008163
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_175E0
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x2000815E
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17698
 * @note 指令数: 53, 标签数: 0
 */
void precise_func_17698(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200080E6;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007F48;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000815D;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008160;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20008163;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_18C54
    // 调用函数: sub_18C54();
    // LDR     R1, =0x200080EA
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BL      sub_18CCC
    // 调用函数: sub_18CCC();
    // LDR     R1, =0x20008163
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20008164
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200080E6
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200080E6
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x200080E8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000815D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_170B0
    // 调用函数: sub_170B0();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20008070
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_186FE
    // 调用函数: sub_186FE();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20008160
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000815E
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007F40
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007F48
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007F38
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007F48
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007F40
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007F50
    // 内存加载操作
    // BL      sub_164A4
    // 调用函数: sub_164A4();
    // BL      sub_19368
    // 调用函数: sub_19368();
    // LDR     R1, =0x2000815F
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17714
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_17714(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008146;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200080EA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x20008146
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_17732
    // 条件跳转
    // LDR     R0, =0x200080EA
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVW    R1, #0xFFFF
    // R1 = 0xFFFF;
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_17732
    // 条件跳转
    // BL      sub_18CE8
    // 调用函数: sub_18CE8();
    // LDR     R1, =0x200080EA
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17900
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_17900(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC0009;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40012400;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x40012400
    // 内存加载操作
    // CMP     R4, R0
    // 比较操作
    // BNE     loc_17922
    // 条件跳转
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0xC0009
    // 内存加载操作
    // BL      sub_17E36
    // 调用函数: sub_17E36();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R0, =0xC0009
    // 内存加载操作
    // BL      sub_17E36
    // 调用函数: sub_17E36();
    // B       locret_1795C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1795E
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_1795e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0,#8]
    // 内存加载操作
    // BFI.W   R2, R1, #0, #1
    // STR     R2, [R0,#8]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1796A
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_1796a(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40012404;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // LDR.W   R1, =0x40012404
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // BFI.W   R1, R0, #0x10, #4
    // LDR.W   R2, =0x40012404
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1797E
 * @note 指令数: 9, 标签数: 0
 */
uint32_t precise_func_1797e(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#2]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // STRB    R1, [R0,#3]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17990
 * @note 指令数: 18, 标签数: 0
 */
uint32_t precise_func_17990(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDRB    R2, [R1]
    // 内存加载操作
    // LDR     R3, [R0,#4]
    // 内存加载操作
    // BFI.W   R3, R2, #8, #1
    // STR     R3, [R0,#4]
    // 内存存储操作
    // LDRB    R2, [R1,#1]
    // 内存加载操作
    // LDR     R3, [R0,#8]
    // 内存加载操作
    // BFI.W   R3, R2, #1, #1
    // STR     R3, [R0,#8]
    // 内存存储操作
    // LDRB    R2, [R1,#2]
    // 内存加载操作
    // LDR     R3, [R0,#8]
    // 内存加载操作
    // BFI.W   R3, R2, #0xB, #1
    // STR     R3, [R0,#8]
    // 内存存储操作
    // LDRB    R2, [R1,#3]
    // 内存加载操作
    // SUBS    R2, R2, #1
    // 算术运算
    // LDR     R3, [R0,#0x2C]
    // 内存加载操作
    // BFI.W   R3, R2, #0x14, #4
    // STR     R3, [R0,#0x2C]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_179BC
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_179bc(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R2, R2
    // 数据扩展操作
    // CMP     R2, #1
    // 比较操作
    // BNE     loc_179CA
    // 条件跳转
    // LDR     R3, [R0,#4]
    // 内存加载操作
    // ORRS    R3, R1
    // STR     R3, [R0,#4]
    // 内存存储操作
    // B       locret_179D6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_179D8
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_179d8(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, [R0,#8]
    // 内存加载操作
    // ORRS.W  R1, R1, #8
    // STR     R1, [R0,#8]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_179E2
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_179e2(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // LDR     R0, [R1,#8]
    // 内存加载操作
    // UBFX.W  R0, R0, #3, #1
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_179F2
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_179F4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_179F6
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_179f6(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, [R0,#8]
    // 内存加载操作
    // ORRS.W  R1, R1, #4
    // STR     R1, [R0,#8]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17A00
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_17a00(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // LDR     R0, [R1,#8]
    // 内存加载操作
    // UBFX.W  R0, R0, #2, #1
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_17A10
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_17A12
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17A14
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_17a14(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_17A52
    // 条件跳转
    // CMP     R1, #2
    // 比较操作
    // BEQ     loc_17A6A
    // 条件跳转
    // BCC     loc_17A5E
    // CMP     R1, #4
    // 比较操作
    // BEQ     loc_17A82
    // 条件跳转
    // BCC     loc_17A76
    // CMP     R1, #6
    // 比较操作
    // BEQ     loc_17A9A
    // 条件跳转
    // BCC     loc_17A8E
    // CMP     R1, #8
    // 比较操作
    // BEQ     loc_17AB2
    // 条件跳转
    // BCC     loc_17AA6
    // CMP     R1, #0xA
    // 比较操作
    // BEQ     loc_17ACA
    // 条件跳转
    // BCC     loc_17ABE
    // CMP     R1, #0xC
    // 比较操作
    // BEQ     loc_17AE2
    // 条件跳转
    // BCC     loc_17AD6
    // CMP     R1, #0xE
    // 比较操作
    // BEQ     loc_17AFA
    // 条件跳转
    // BCC     loc_17AEE
    // CMP     R1, #0x10
    // 比较操作
    // BEQ     loc_17B12
    // 条件跳转
    // BCC     loc_17B06
    // CMP     R1, #0x11
    // 比较操作
    // BEQ     loc_17B1E
    // 条件跳转
    // B       loc_17B2A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17C26
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_17c26(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #8
    // 比较操作
    // BLT     loc_17C40
    // 条件跳转
    // LDR     R3, [R0,#8]
    // 内存加载操作
    // ORRS.W  R3, R3, #0x2000000
    // STR     R3, [R0,#8]
    // 内存存储操作
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R3, [R0,#8]
    // 内存加载操作
    // BFI.W   R3, R1, #0x11, #3
    // STR     R3, [R0,#8]
    // 内存存储操作
    // B       loc_17C52
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17C5E
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_17c5e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x16;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0,#8]
    // 内存加载操作
    // BFI.W   R2, R1, #0x16, #1
    // STR     R2, [R0,#8]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17C6A
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_17c6a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x4001244C;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x4001244C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17C94
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_17c94(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R3, [R2]
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // TST     R3, R1
    // 比较操作
    // BNE     loc_17CA6
    // 条件跳转
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R0, R3
    // B       loc_17CAA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17CAE
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_17cae(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, [R0]
    // 内存加载操作
    // BICS    R2, R1
    // STR     R2, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17CB8
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_17cb8(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // BFI.W   R2, R2, #8, #8
    // BFI.W   R2, R2, #0x10, #0x10
    // ADDS    R0, R0, R1
    // 算术运算
    // ANDS.W  R3, R0, #3
    // BEQ     loc_17CDA
    // 条件跳转
    // SUBS    R1, R1, R3
    // 算术运算
    // BCC     loc_17D0C
    // LSLS    R3, R3, #0x1F
    // IT MI
    // STRBMI.W R2, [R0,#-1]!
    // IT CS
    // STRHCS.W R2, [R0,#-2]!
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17D20
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_17d20(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R0, =0x40021000
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ORRS.W  R0, R0, #1
    // LDR.W   R1, =0x40021000
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17D8A
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_17d8a(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // MOVS    R1, R0
    // MOVS    R0, #0
    // R0 = 0;
    // LSRS    R2, R1, #0x10
    // LDR.W   R3, =0x40021000
    // 内存加载操作
    // LDR     R2, [R3,R2]
    // 内存加载操作
    // MOVS    R3, #1
    // R3 = 1;
    // ANDS.W  R4, R1, #0x1F
    // LSLS    R3, R4
    // ANDS    R2, R3
    // MOVS    R3, #1
    // R3 = 1;
    // ANDS.W  R4, R1, #0x1F
    // LSLS    R3, R4
    // CMP     R2, R3
    // 比较操作
    // BEQ     loc_17DB4
    // 条件跳转
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R0, R2
    // B       loc_17DB8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17DBE
 * @note 指令数: 12, 标签数: 1
 */
void precise_func_17dbe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R0, #0x11
    // R0 = 0x11;
    // BL      sub_17D8A
    // 调用函数: sub_17D8A();
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_17DDA
    // 条件跳转
    // MOVW    R0, #0xFFFF
    // R0 = 0xFFFF;
    // CMP     R4, R0
    // 比较操作
    // BCS     loc_17DDA
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_17DC4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17DF4
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_17df4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #1
    // 比较操作
    // BNE     loc_17E18
    // 条件跳转
    // ASRS    R2, R0, #0x10
    // LDR.W   R3, =0x40021000
    // 内存加载操作
    // LDR     R2, [R3,R2]
    // 内存加载操作
    // MOVS    R3, #1
    // R3 = 1;
    // ANDS.W  R4, R0, #0x1F
    // LSLS    R3, R4
    // ORRS    R2, R3
    // ASRS    R3, R0, #0x10
    // LDR.W   R4, =0x40021000
    // 内存加载操作
    // STR     R2, [R4,R3]
    // 内存存储操作
    // B       loc_17E32
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17E36
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_17e36(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #1
    // 比较操作
    // BNE     loc_17E5A
    // 条件跳转
    // ASRS    R2, R0, #0x10
    // LDR.W   R3, =0x40021000
    // 内存加载操作
    // LDR     R2, [R3,R2]
    // 内存加载操作
    // MOVS    R3, #1
    // R3 = 1;
    // ANDS.W  R4, R0, #0x1F
    // LSLS    R3, R4
    // ORRS    R2, R3
    // ASRS    R3, R0, #0x10
    // LDR.W   R4, =0x40021000
    // 内存加载操作
    // STR     R2, [R4,R3]
    // 内存存储操作
    // B       loc_17E74
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17E78
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_17e78(uint8_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_17E8C
    // 条件跳转
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_17EB4
    // 条件跳转
    // BCC     loc_17EA0
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_17EDC
    // 条件跳转
    // BCC     loc_17EC8
    // B       locret_17EF0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17EF2
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_17ef2(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // LDR.W   R1, =0x40021004
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // BFI.W   R1, R0, #4, #4
    // LDR.W   R2, =0x40021004
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17F06
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_17f06(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // LDR.W   R1, =0x40021004
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // BFI.W   R1, R0, #8, #3
    // LDR.W   R2, =0x40021004
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17F1A
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_17f1a(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // LDR.W   R1, =0x40021004
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // BFI.W   R1, R0, #0xB, #3
    // LDR.W   R2, =0x40021004
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17F2E
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_17f2e(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // LDR.W   R1, =0x40021004
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // BFI.W   R1, R0, #0xE, #2
    // LDR     R2, =0x40021004
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // LSRS    R1, R1, #2
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, =0x40021004
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BFI.W   R2, R1, #0x1C, #1
    // LDR     R1, =0x40021004
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17F54
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_17f54(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_17F6A
    // 条件跳转
    // LDR     R3, =0x40021004
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // BICS.W  R3, R3, #0x10000
    // LDR     R4, =0x40021004
    // 内存加载操作
    // STR     R3, [R4]
    // 内存存储操作
    // B       loc_17F96
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17FCA
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_17fca(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // LDR     R1, =0x40021004
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // BFI.W   R1, R0, #0, #2
    // LDR     R2, =0x40021004
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17FDA
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_17fda(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x40021004
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // UBFX.W  R0, R0, #2, #2
    // UXTB    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17FE6
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_17fe6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R3-R11,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R7, #0
    // R7 = 0;
    // MOVS.W  R8, #0
    // MOVS.W  R9, #0
    // MOVS.W  R10, #0
    // BL      sub_17FDA
    // 调用函数: sub_17FDA();
    // MOV     R11, R0
    // UXTB.W  R11, R11
    // CMP.W   R11, #0
    // BEQ     loc_18018
    // 条件跳转
    // CMP.W   R11, #2
    // BEQ     loc_18042
    // 条件跳转
    // BCC     loc_1803C
    // B       loc_180B0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1812C
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1812c(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021054;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_18140
    // 条件跳转
    // LDR     R1, =0x40021054
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // ORRS.W  R1, R1, #0x30 ; '0'
    // LDR     R2, =0x40021054
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // B       locret_1814C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1814E
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_1814e(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021054;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // LDR     R1, =0x40021054
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // BFI.W   R1, R0, #0xC, #2
    // LDR     R2, =0x40021054
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_181A0
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_181a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000ED0C;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0xE000ED0C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // UBFX.W  R0, R0, #8, #3
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_181AC
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_181ac(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE000E100;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BMI     locret_181C4
    // MOVS    R1, #1
    // R1 = 1;
    // ANDS.W  R2, R0, #0x1F
    // LSLS    R1, R2
    // LDR     R2, =0xE000E100
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSRS    R3, R0, #5
    // STR.W   R1, [R2,R3,LSL#2]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_181C6
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_181c6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000E180;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BMI     locret_181E6
    // MOVS    R1, #1
    // R1 = 1;
    // ANDS.W  R2, R0, #0x1F
    // LSLS    R1, R2
    // LDR     R2, =0xE000E180
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSRS    R3, R0, #5
    // STR.W   R1, [R2,R3,LSL#2]
    // 内存存储操作
    // DSB.W   SY
    // ISB.W   SY
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_181E8
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_181e8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000E400;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BMI     loc_181FA
    // LSLS    R2, R1, #4
    // LDR     R3, =0xE000E400
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // STRB    R2, [R0,R3]
    // 内存存储操作
    // B       loc_1820A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1820E
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1820e(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6}
    // 栈操作
    // MOVS    R3, R0
    // ANDS.W  R4, R3, #7
    // RSBS.W  R6, R4, #7
    // CMP     R6, #5
    // 比较操作
    // BCC     loc_18222
    // MOVS    R0, #4
    // R0 = 4;
    // B       loc_18226
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1824C
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_1824c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, #0
    // R7 = 0;
    // BL      sub_181A0
    // 调用函数: sub_181A0();
    // MOVS    R2, R6
    // MOVS    R1, R5
    // BL      sub_1820E
    // 调用函数: sub_1820E();
    // MOVS    R7, R0
    // MOVS    R1, R7
    // MOVS    R0, R4
    // SXTB    R0, R0
    // 数据扩展操作
    // BL      sub_181E8
    // 调用函数: sub_181E8();
    // MOVS    R0, R4
    // SXTB    R0, R0
    // 数据扩展操作
    // BL      sub_181AC
    // 调用函数: sub_181AC();
    // POP     {R0,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18278
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_18278(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // SXTB    R0, R0
    // 数据扩展操作
    // BL      sub_181C6
    // 调用函数: sub_181C6();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18286
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_18286(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1FFFFF80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE000ED08;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, =0x1FFFFF80
    // 内存加载操作
    // ANDS    R2, R1
    // ORRS    R2, R0
    // LDR     R3, =0xE000ED08
    // 内存加载操作
    // STR     R2, [R3]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_182B0
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_182b0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000E400;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BMI     loc_182C2
    // LSLS    R2, R1, #4
    // LDR     R3, =0xE000E400
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // STRB    R2, [R0,R3]
    // 内存存储操作
    // B       loc_182D2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_182D6
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_182d6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // SUBS    R0, R4, #1
    // 算术运算
    // CMP.W   R0, #0x1000000
    // BCC     loc_182E6
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_18304
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18306
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_18306(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007FE4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_165BC
    // 调用函数: sub_165BC();
    // LDR     R0, =0x20007FE4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x20007FE4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18318
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_18318(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000254;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2710;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20000254
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVW    R1, #0x2710
    // R1 = 0x2710;
    // UDIV.W  R0, R0, R1
    // BL      sub_182D6
    // 调用函数: sub_182D6();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18348
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_18348(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0]
    // 内存加载操作
    // BFI.W   R2, R1, #0, #1
    // STR     R2, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18354
 * @note 指令数: 6, 标签数: 0
 */
uint32_t precise_func_18354(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // STR     R1, [R0,#0x2C]
    // 内存存储操作
    // STR     R2, [R0,#0x28]
    // 内存存储操作
    // LDR     R3, [R0,#0x14]
    // 内存加载操作
    // ORRS.W  R3, R3, #1
    // STR     R3, [R0,#0x14]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

