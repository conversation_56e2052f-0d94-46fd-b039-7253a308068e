// 精确转换批次 27 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1E586C
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_1e586c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x74;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_11C=  0x11C
    // ADD     R7, SP, #0xC8
    // 算术运算
    // LSLS    R5, R5, #9
    // EORS    R0, R0
    // MOVS    R2, #0x74 ; 't'
    // R2 = 0x74;
    // LSLS    R0, R4, #2
    // ADR     R0, dword_1E5A40
    // STRH    R2, [R2,#(word_83+1 - 0x74)]
    // 内存存储操作
    // SUBS    R3, #5
    // 算术运算
    // LDR     R7, [R0]
    // 内存加载操作
    // STRH    R6, [R5,#0x10]
    // 内存存储操作
    // SUBS    R4, #5
    // 算术运算
    // LDR     R0, [SP,#arg_11C]
    // 内存加载操作
    // LDM     R5, {R3,R5}
    // POP     {R0-R2,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1E6F02
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_1e6f02(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2F0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x27C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_27C=  0x27C
    // arg_28C=  0x28C
    // arg_2F0=  0x2F0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1E74F0
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_1e74f0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // STRB    R2, [R5,#0xA]
    // 内存存储操作
    // B       sub_1E6F02
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1EA2E2
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_1ea2e2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1426220;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, =0x1426220
    // 内存加载操作
    // BXNS    R2
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1FD50C
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1fd50c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x188;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x74;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xB0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_C= -0xC
    // arg_4C=  0x4C
    // arg_70=  0x70
    // arg_74=  0x74
    // arg_B0=  0xB0
    // arg_188=  0x188
    // arg_2EC=  0x2EC
    // arg_368=  0x368
    // arg_3E8=  0x3E8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_203374
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_203374(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x33;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_170=  0x170
    // arg_17C=  0x17C
    // STRH    R5, [R4,#0x2C]
    // 内存存储操作
    // ADD     R2, SP, #arg_17C
    // 算术运算
    // SUBS    R2, #0x33 ; '3'
    // 算术运算
    // STR     R2, [R4,R6]
    // 内存存储操作
    // ADDS    R5, #0xF3
    // 算术运算
    // STM     R5, {R0,R4,R5,R7}
    // STM     R2, {R2-R5}
    // MOVS    R5, R2
    // PUSH    {R2,R3,LR}
    // 栈操作
    // STRH    R5, [R4,#0x24]
    // 内存存储操作
    // ADD     R2, SP, #0xC+arg_170
    // 算术运算
    // SUBS    R2, #0x33 ; '3'
    // 算术运算
    // CMP     R2, R4
    // 比较操作
    // MOVS    R4, R2
    // POP     {R0,R6,R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2096B8
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_2096b8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x90;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x294;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x88;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2C4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // arg_10=  0x10
    // arg_68=  0x68
    // arg_6C=  0x6C
    // arg_84=  0x84
    // arg_88=  0x88
    // arg_90=  0x90
    // arg_E4=  0xE4
    // arg_25C=  0x25C
    // arg_294=  0x294
    // arg_2C4=  0x2C4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_213380
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_213380(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xDC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_DC=  0xDC
    // STR     R6, [SP,#arg_DC]
    // 内存存储操作
    // LDRB    R0, [R7]
    // 内存加载操作
    // SUBS    R4, R4, R5
    // 算术运算
    // STRB    R6, [R2,#0x1D]
    // 内存存储操作
    // LSLS    R5, R6, #4
    // MOVS    R5, #0xC
    // R5 = 0xC;
    // PUSH    {R4,R6,LR}
    // 栈操作
    // ADR     R2, unk_213410
    // MOVS    R2, R4
    // ADR     R0, dword_21355C
    // B       loc_212D9A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2137B8
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_2137b8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC6;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xD;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R2,R5,LR}
    // 栈操作
    // STRH    R5, [R0,R1]
    // 内存存储操作
    // MOVS    R0, #0xC6
    // R0 = 0xC6;
    // SXTH    R4, R1
    // 数据扩展操作
    // LSRS    R4, R5, #0xC
    // LSLS    R5, R5, #0x16
    // LSLS    R5, R1, #0xB
    // LSLS    R5, R4, #0xD
    // POP     {R0-R2,R5,R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_215FC0
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_215fc0(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADR     R0, loc_2160CC
    // BX      R0 ; loc_2160CC
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_216000
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_216000(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x198;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xCC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x188;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x90;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
    // arg_8=  8
    // arg_C=  0xC
    // arg_10=  0x10
    // arg_14=  0x14
    // arg_90=  0x90
    // arg_CC=  0xCC
    // arg_12C=  0x12C
    // arg_154=  0x154
    // arg_164=  0x164
    // arg_188=  0x188
    // arg_198=  0x198
    // arg_204=  0x204
    // arg_214=  0x214
    // arg_21C=  0x21C
    // arg_2B0=  0x2B0
    // arg_304=  0x304
    // arg_308=  0x308
    // arg_388=  0x388
    // arg_3D0=  0x3D0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_222640
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_222640(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADD     SP, SP, #0x24 ; '$'
    // 算术运算
    // LSRS    R1, R0, #7
    // LDR     R0, [R7,#0x18]
    // 内存加载操作
    // STRH    R1, [R0,#8]
    // 内存存储操作
    // PUSH    {R0,R3,R5,R7,LR}
    // 栈操作
    // LSRS    R0, R4, #3
    // POP     {PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2302BA
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_2302ba(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xBB26704;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0xBB26704
    // 内存加载操作
    // BXNS    R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_231780
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_231780(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x52FF3546;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x52FF3546
    // 内存加载操作
    // BXNS    R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_231E54
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_231e54(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xBC7420A0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0xBC7420A0
    // 内存加载操作
    // BX      R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_235F7C
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_235f7c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x6C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R2,R3,LR}
    // 栈操作
    // ASRS    R4, R1, #0x1C
    // PUSH    {R0-R2,R7,LR}
    // 栈操作
    // LDR     R0, [R4,R3]
    // 内存加载操作
    // STR     R6, [R6,#0x6C]
    // 内存存储操作
    // ASRS    R5, R4, #0x1D
    // ASRS    R2, R0, #0x1A
    // BX      R4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23D468
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_23d468(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xEB0892F8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0xEB0892F8
    // 内存加载操作
    // BXNS    R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23F610
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_23f610(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x77;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x14;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_18=  0x18
    // STM     R3!, {R1,R4,R5}
    // SUBS    R0, R3, R4
    // 算术运算
    // ADDS    R7, #0x77 ; 'w'
    // 算术运算
    // LSLS    R5, R2, #8
    // PUSH    {R1,R2,R6,LR}
    // 栈操作
    // ADDS    R2, #0xFF
    // 算术运算
    // LSRS    R2, R0, #0x14
    // STR     R7, [R4,#0x30]
    // 内存存储操作
    // ADD     R5, SP, #0x10+arg_18
    // 算术运算
    // POP     {R3,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23FDC2
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_23fdc2(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x19C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x74;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x70;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // arg_70=  0x70
    // arg_74=  0x74
    // arg_78=  0x78
    // arg_80=  0x80
    // arg_F4=  0xF4
    // arg_F8=  0xF8
    // arg_150=  0x150
    // arg_19C=  0x19C
    // arg_238=  0x238
    // arg_274=  0x274
    // arg_28C=  0x28C
    // arg_290=  0x290
    // arg_2F4=  0x2F4
    // arg_390=  0x390
    // arg_3B4=  0x3B4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24959E
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_24959e(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x118;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_118=  0x118
    // LSLS    R1, R3, #8
    // LDR     R1, [SP,#arg_118]
    // 内存加载操作
    // STRB    R7, [R7,#3]
    // 内存存储操作
    // STM     R0, {R0,R2,R5,R7}
    // SUBS    R4, R1, R4
    // 算术运算
    // PUSH    {R0-R2,R4-R6,LR}
    // 栈操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25D9E4
 * @note 指令数: 23, 标签数: 0
 */
void precise_func_25d9e4(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x278;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x7C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x110;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x3C0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_C= -0xC
    // var_4= -4
    // arg_4=  4
    // arg_44=  0x44
    // arg_58=  0x58
    // arg_6C=  0x6C
    // arg_7C=  0x7C
    // arg_C0=  0xC0
    // arg_110=  0x110
    // arg_150=  0x150
    // arg_1D0=  0x1D0
    // arg_1F8=  0x1F8
    // arg_220=  0x220
    // arg_238=  0x238
    // arg_240=  0x240
    // arg_25C=  0x25C
    // arg_278=  0x278
    // arg_27C=  0x27C
    // arg_2B0=  0x2B0
    // arg_2B8=  0x2B8
    // arg_2E4=  0x2E4
    // arg_3B4=  0x3B4
    // arg_3C0=  0x3C0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25F912
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_25f912(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x4712C813;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x4712C813
    // 内存加载操作
    // BX      R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_26014C
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_26014c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x22014136;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA3;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xA1;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R3, #0xA8
    // R3 = 0xA8;
    // LDR     R5, =0x72C02320
    // 内存加载操作
    // ADDS    R2, #0xF7
    // 算术运算
    // LDR     R5, =0x22014136
    // 内存加载操作
    // STRH    R6, [R2,#0x1A]
    // 内存存储操作
    // SUBS    R5, #0xFC
    // 算术运算
    // STRH    R2, [R0,R0]
    // 内存存储操作
    // SUBS    R0, #0xA1
    // 算术运算
    // PUSH    {R4,R6,LR}
    // 栈操作
    // ADDS    R0, #0x20 ; ' '
    // 算术运算
    // MOVS    R0, #0xA3
    // R0 = 0xA3;
    // MOVS    R3, #0x42 ; 'B'
    // R3 = 0x42;
    // B       loc_25FC02
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2607CA
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_2607ca(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xB12830C1;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R4, =0xB12830C1
    // 内存加载操作
    // BXNS    R4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2629FA
 * @note 指令数: 76, 标签数: 0
 */
void precise_func_2629fa(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x254;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2F8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x210;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x26C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_150= -0x150
    // var_13C= -0x13C
    // var_134= -0x134
    // var_124= -0x124
    // var_F8= -0xF8
    // var_C0= -0xC0
    // var_BC= -0xBC
    // var_8C= -0x8C
    // var_80= -0x80
    // var_74= -0x74
    // var_6C= -0x6C
    // var_64= -0x64
    // var_5C= -0x5C
    // var_58= -0x58
    // var_4C= -0x4C
    // var_44= -0x44
    // var_3C= -0x3C
    // var_38= -0x38
    // var_30= -0x30
    // var_2C= -0x2C
    // var_24= -0x24
    // var_1C= -0x1C
    // var_18= -0x18
    // var_8= -8
    // arg_0=  0
    // arg_1C=  0x1C
    // arg_38=  0x38
    // arg_44=  0x44
    // arg_54=  0x54
    // arg_78=  0x78
    // arg_80=  0x80
    // arg_84=  0x84
    // arg_90=  0x90
    // arg_B0=  0xB0
    // arg_C0=  0xC0
    // arg_C8=  0xC8
    // arg_CC=  0xCC
    // arg_F8=  0xF8
    // arg_174=  0x174
    // arg_184=  0x184
    // arg_18C=  0x18C
    // arg_190=  0x190
    // arg_1AC=  0x1AC
    // arg_1B0=  0x1B0
    // arg_1B4=  0x1B4
    // arg_1B8=  0x1B8
    // arg_1CC=  0x1CC
    // arg_1DC=  0x1DC
    // arg_1F0=  0x1F0
    // arg_1F4=  0x1F4
    // arg_20C=  0x20C
    // arg_210=  0x210
    // arg_214=  0x214
    // arg_22C=  0x22C
    // arg_230=  0x230
    // arg_234=  0x234
    // arg_238=  0x238
    // arg_24C=  0x24C
    // arg_254=  0x254
    // arg_26C=  0x26C
    // arg_274=  0x274
    // arg_278=  0x278
    // arg_28C=  0x28C
    // arg_298=  0x298
    // arg_2A0=  0x2A0
    // arg_2C4=  0x2C4
    // arg_2CC=  0x2CC
    // arg_2EC=  0x2EC
    // arg_2F8=  0x2F8
    // arg_30C=  0x30C
    // arg_338=  0x338
    // arg_33C=  0x33C
    // arg_340=  0x340
    // arg_34C=  0x34C
    // arg_374=  0x374
    // arg_390=  0x390
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_266038
 * @note 指令数: 16, 标签数: 0
 */
float precise_func_266038(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xD0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1EC;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10DC;

    // 局部变量
    float result = 0.0f;
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_1EC=  0x1EC
    // MOVS    R2, #0xD0
    // R2 = 0xD0;
    // ADR     R2, dword_266080
    // ASRS    R1, R0, #3
    // CMP     R2, #0x2A ; '*'
    // 比较操作
    // LSLS    R0, R5, #8
    // STR     R2, [R4,#0x10]
    // 内存存储操作
    // STM     R6!, {R1,R5,R7}
    // MOVS    R2, R6
    // STM     R1, {R1,R6,R7}
    // SXTH    R0, R2
    // 数据扩展操作
    // LDR     R2, [R4,R2]
    // 内存加载操作
    // ADR     R2, (loc_266396+2)
    // FSTS    S23, [R12,#-0x30]
    // 浮点数内存操作
    // MOVW    R1, #0x10DC
    // R1 = 0x10DC;
    // CODE16

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2735D8
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_2735d8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x84;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2B0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_84=  0x84
    // arg_C0=  0xC0
    // arg_2B0=  0x2B0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_275732
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_275732(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1B8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x42;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_1B8=  0x1B8
    // STM     R6!, {R1}
    // MOVS    R2, R6
    // MOVS    R7, #0x42 ; 'B'
    // R7 = 0x42;
    // ADD     R4, SP, #arg_1B8
    // 算术运算
    // BXNS    LR
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_275D1C
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_275d1c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2F0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x190;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xB3;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
    // arg_190=  0x190
    // arg_2F0=  0x2F0
    // LSLS    R0, R1, #8
    // SUBS    R6, #6
    // 算术运算
    // ADD     R5, SP, #arg_0
    // 算术运算
    // ADDS    R0, #2
    // 算术运算
    // MOVS    R0, #0xB3
    // R0 = 0xB3;
    // PUSH    {R0,R2,R5,R6,LR}
    // 栈操作
    // ASRS    R1, R0, #0x18
    // LSRS    R2, R3, #0x1B
    // LDR     R3, [SP,#0x14+arg_2F0]
    // 内存加载操作
    // ADD     R5, SP, #0x14+arg_190
    // 算术运算
    // POP     {R1,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_27E428
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_27e428(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x481601CA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x481601CA
    // 内存加载操作
    // BX      R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_61030A
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_61030a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADDS    R0, #0x38 ; '8'
    // 算术运算
    // ADDS    R4, #0x34 ; '4'
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_610324
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_610324(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x43;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADD     R4, R8
    // 算术运算
    // CMP     R5, #0x43 ; 'C'
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_610328
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_610328(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x56;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // SUBS    R0, #0x56 ; 'V'
    // 算术运算
    // ADDS    R4, #0x30 ; '0'
    // 算术运算
    // MOVS    R4, R6
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_610344
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_610344(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, R0
    // MOVS    R0, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_610348
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_610348(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_61035E
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_61035e(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, R0
    // MOVS    R0, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_6114F0
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_6114f0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, R0
    // STRB    R2, [R2,R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_616A4A
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_616a4a(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ORRS    R0, R0
    // CMP     R0, R0
    // 比较操作
    // BX      R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_616E04
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_616e04(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x64;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LSLS    R0, R7, #1
    // MOVS    R0, R0
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R0
    // CMP     R7, #1
    // 比较操作
    // STR     R2, [R0,#0x14]
    // 内存存储操作
    // LDR     R3, [R4,#0x64]
    // 内存加载操作
    // STRB    R5, [R4,#0x11]
    // 内存存储操作
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_616E1E
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_616e1e(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, R0
    // MOVS    R0, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_616E22
 * @note 指令数: 23, 标签数: 0
 */
void precise_func_616e22(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // MOVS    R0, R0
    // LDRSB   R0, [R0,R4]
    // MOVS    R0, R0
    // CMP     R1, #0
    // 比较操作
    // CMP     R2, #0
    // 比较操作
    // CMP     R3, #0
    // 比较操作
    // CMP     R5, #0
    // 比较操作
    // CMP     R7, #0
    // 比较操作
    // ADDS    R2, #0
    // 算术运算
    // ADDS    R4, #0
    // 算术运算
    // ADDS    R6, #0
    // 算术运算
    // SUBS    R0, #0
    // 算术运算
    // SUBS    R2, #0
    // 算术运算
    // SUBS    R4, #0
    // 算术运算
    // SUBS    R6, #0
    // 算术运算
    // ASRS    R0, R0
    // ORRS    R0, R0
    // CMP     R0, R0
    // 比较操作
    // BX      R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617AB4
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_617ab4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x49;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADD     R2, R6
    // 算术运算
    // MOVS    R0, #0x49 ; 'I'
    // R0 = 0x49;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617AB8
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_617ab8(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R4, R6
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617AC8
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_617ac8(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617ACC
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_617acc(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617AEA
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_617aea(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617C44
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_617c44(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617C54
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_617c54(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617C58
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_617c58(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_617C76
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_617c76(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDM     R6, {R1-R3,R6,R7}
    // LDM     R6, {R1-R3,R6,R7}
}

