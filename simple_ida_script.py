
import idaapi
import idc
import ida_funcs
import idautils
import os

def export_functions_info():
    """导出函数信息"""
    print("开始导出函数信息...")
    
    # 等待自动分析完成
    idaapi.auto_wait()
    
    functions_info = []
    
    # 获取所有函数
    for func_ea in idautils.Functions():
        func_name = idc.get_func_name(func_ea)
        func_start = func_ea
        func_end = idc.get_func_attr(func_ea, idc.FUNCATTR_END)
        
        # 获取函数的汇编代码
        asm_lines = []
        ea = func_start
        while ea < func_end:
            disasm = idc.GetDisasm(ea)
            if disasm:
                asm_lines.append(f"0x{ea:08X}: {disasm}")
            ea = idc.next_head(ea)
        
        functions_info.append({
            'name': func_name,
            'start': func_start,
            'end': func_end,
            'asm': asm_lines
        })
    
    # 保存函数信息到文件
    output_file = "ida_functions_info.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(f"IDA Pro函数分析结果\n")
        f.write(f"总函数数: {len(functions_info)}\n")
        f.write("=" * 80 + "\n\n")
        
        for i, func_info in enumerate(functions_info):
            f.write(f"函数 {i+1}: {func_info['name']}\n")
            f.write(f"地址: 0x{func_info['start']:08X} - 0x{func_info['end']:08X}\n")
            f.write(f"大小: {func_info['end'] - func_info['start']} 字节\n")
            f.write("汇编代码:\n")
            
            for asm_line in func_info['asm'][:20]:  # 只显示前20行
                f.write(f"  {asm_line}\n")
            
            if len(func_info['asm']) > 20:
                f.write(f"  ... (还有 {len(func_info['asm']) - 20} 行)\n")
            
            f.write("\n" + "-" * 60 + "\n\n")
    
    print(f"函数信息已导出到: {output_file}")
    print(f"总共找到 {len(functions_info)} 个函数")

def main():
    """主函数"""
    print("IDA Pro简单分析脚本启动")
    export_functions_info()
    
    # 退出IDA
    idc.qexit(0)

if __name__ == "__main__":
    main()
