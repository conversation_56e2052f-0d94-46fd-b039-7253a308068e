// 精确转换批次 18 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_527B6
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_527b6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007648;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x20007648
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xA
    // 比较操作
    // BLT     loc_527C4
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_527C6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_527C8
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_527c8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007650;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015D4C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x20007650
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_52838
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20007650
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x8015D4C
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015D4C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_474E8
    // 调用函数: sub_474E8();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_527EE
    // 条件跳转
    // MOVS    R4, #0
    // R4 = 0;
    // B       loc_527F0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52860
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_52860(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // B       loc_52866
    // 无条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52874
 * @note 指令数: 4, 标签数: 1
 */
void precise_func_52874(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // B       loc_5287C
    // 无条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // SUBS    R2, R2, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5288C
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_5288c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_54040
    // 调用函数: sub_54040();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_528F6
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_528f6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007638;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20000323;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // LDR     R0, =0x20000323
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BNE     loc_52914
    // 条件跳转
    // LDR     R0, =0x20007638
    // 内存加载操作
    // UXTB    R5, R5
    // 数据扩展操作
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R1, R5
    // STRH    R4, [R0,R1]
    // 内存存储操作
    // BL      sub_52922
    // 调用函数: sub_52922();
    // B       locret_52920
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52922
 * @note 指令数: 38, 标签数: 0
 */
void precise_func_52922(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007638;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_F= -0xF
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, #1
    // R4 = 1;
    // MOVS    R0, #0x40 ; '@'
    // R0 = 0x40;
    // MOVS    R5, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_10]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_F]
    // 内存存储操作
    // LDR     R0, =0x20007638
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_10]
    // 内存存储操作
    // LDR     R0, =0x20007638
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // ASRS    R0, R0, #8
    // LSLS    R0, R0, #0x1C
    // LSRS    R0, R0, #0x1C
    // MOVS    R1, #0x80
    // R1 = 0x80;
    // ORRS    R1, R0
    // MOV     R0, SP
    // STRB    R1, [R0,#0x10+var_F]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_540DC
    // 调用函数: sub_540DC();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #0xC0
    // R1 = 0xC0;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_543A6
    // 调用函数: sub_543A6();
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BNE     loc_5296E
    // 条件跳转
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // B       locret_52B3A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52B48
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_52b48(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015D38;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015D34;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R1, =0x8015D38
    // 内存加载操作
    // LDR     R0, =0x8015D34
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8015D34
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015D34
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_54424
    // 调用函数: sub_54424();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52B94
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_52b94(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R5, #0
    // R5 = 0;
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #2
    // 比较操作
    // BLT     loc_52BB8
    // 条件跳转
    // UXTB    R4, R4
    // 数据扩展操作
    // SUBS    R0, R4, #1
    // 算术运算
    // MOVS    R1, #2
    // R1 = 2;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R5, R0
    // MOVS    R0, #2
    // R0 = 2;
    // MULS    R0, R5
    // SUBS    R4, R4, R0
    // 算术运算
    // SUBS    R4, R4, #1
    // 算术运算
    // B       loc_52BCA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52BDE
 * @note 指令数: 56, 标签数: 0
 */
void precise_func_52bde(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8015D34;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_17= -0x17
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, #1
    // R4 = 1;
    // MOVS    R6, R5
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8015D34
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015D34
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x18+var_18]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x18+var_17]
    // 内存存储操作
    // MOV     R0, SP
    // LDR     R1, =0x20007640
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R2, #2
    // R2 = 2;
    // MULS    R2, R6
    // LDRH    R1, [R1,R2]
    // 内存加载操作
    // STRB    R1, [R0,#0x18+var_18]
    // 内存存储操作
    // LDR     R0, =0x20007640
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R1, R6
    // LDRH    R0, [R0,R1]
    // 内存加载操作
    // ASRS    R0, R0, #8
    // LSLS    R0, R0, #0x1C
    // LSRS    R0, R0, #0x1C
    // LSLS    R1, R5, #6
    // ORRS    R1, R0
    // MOVS    R0, #0x10
    // R0 = 0x10;
    // ORRS    R0, R1
    // MOV     R1, SP
    // STRB    R0, [R1,#0x18+var_17]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // MOV     R0, SP
    // LDRB    R2, [R0,#0x18+var_17]
    // 内存加载操作
    // MOV     R0, SP
    // LDRB    R1, [R0,#0x18+var_18]
    // 内存加载操作
    // MOVS    R0, R7
    // BL      sub_54542
    // 调用函数: sub_54542();
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BNE     loc_52C4E
    // 条件跳转
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // B       locret_52C62
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52C74
 * @note 指令数: 7, 标签数: 0
 */
uint32_t precise_func_52c74(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R2, R2
    // 数据扩展操作
    // SUBS    R2, #0x20 ; ' '
    // 算术运算
    // BCC     loc_52C82
    // MOVS    R0, R1
    // LSRS    R0, R2
    // MOVS    R1, #0
    // R1 = 0;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52C92
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_52c92(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_52CA6
    // 条件跳转
    // LDR     R0, [R1,#0xC]
    // 内存加载操作
    // MOVS    R2, R0
    // LDR     R0, [R1,#0x10]
    // 内存加载操作
    // MOVS    R3, R0
    // SUBS    R0, R2, R3
    // 算术运算
    // B       locret_52CA8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52CAA
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_52caa(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_52CC6
    // 条件跳转
    // MOVS    R0, R4
    // BL      sub_52C92
    // 调用函数: sub_52C92();
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_52CC2
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_52CC8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52CD6
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_52cd6(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_52CF0
    // 条件跳转
    // MOVS    R0, R4
    // BL      sub_52C92
    // 调用函数: sub_52C92();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_52CEC
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_52CF2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52D00
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_52d00(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R0, R4
    // BL      sub_52CD6
    // 调用函数: sub_52CD6();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_52D22
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R4,#0x10]
    // 内存加载操作
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // BL      sub_4637C
    // 调用函数: sub_4637C();
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // MULS    R1, R0
    // ADDS    R5, R5, R1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52D26
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_52d26(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R0, R4
    // BL      sub_52CD6
    // 调用函数: sub_52CD6();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_52D4A
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R4,#0xC]
    // 内存加载操作
    // SUBS    R0, R0, #1
    // 算术运算
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // BL      sub_4637C
    // 调用函数: sub_4637C();
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // MULS    R1, R0
    // ADDS    R5, R5, R1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52D4E
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_52d4e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R7, #0
    // R7 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R0, R5
    // BL      sub_52CD6
    // 调用函数: sub_52CD6();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_52D96
    // 条件跳转
    // LDR     R0, [R5]
    // 内存加载操作
    // MOVS    R6, R0
    // LDR     R0, [R5,#0x10]
    // 内存加载操作
    // LDR     R1, [R5,#8]
    // 内存加载操作
    // BL      sub_4637C
    // 调用函数: sub_4637C();
    // LDR     R0, [R5,#4]
    // 内存加载操作
    // MULS    R1, R0
    // ADDS    R6, R6, R1
    // 算术运算
    // LDR     R0, [SP,#0x18+var_18]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_52D8C
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52D9C
 * @note 指令数: 23, 标签数: 0
 */
void precise_func_52d9c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R7, #0
    // R7 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R0, R5
    // BL      sub_52CD6
    // 调用函数: sub_52CD6();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_52DE6
    // 条件跳转
    // LDR     R0, [R5]
    // 内存加载操作
    // MOVS    R6, R0
    // LDR     R0, [R5,#0xC]
    // 内存加载操作
    // SUBS    R0, R0, #1
    // 算术运算
    // LDR     R1, [R5,#8]
    // 内存加载操作
    // BL      sub_4637C
    // 调用函数: sub_4637C();
    // LDR     R0, [R5,#4]
    // 内存加载操作
    // MULS    R1, R0
    // ADDS    R6, R6, R1
    // 算术运算
    // LDR     R0, [SP,#0x18+var_18]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_52DDC
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52DEC
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_52dec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_52E18
    // 条件跳转
    // MOVS    R0, R4
    // BL      sub_52CAA
    // 调用函数: sub_52CAA();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_52E18
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R4,#0xC]
    // 内存加载操作
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // BL      sub_4637C
    // 调用函数: sub_4637C();
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // MULS    R1, R0
    // ADDS    R5, R5, R1
    // 算术运算
    // LDR     R0, [R4,#0xC]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // STR     R0, [R4,#0xC]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52E1C
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_52e1c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_52E30
    // 条件跳转
    // MOVS    R4, #0
    // R4 = 0;
    // STR     R4, [R0,#0xC]
    // 内存存储操作
    // MOVS    R4, #0
    // R4 = 0;
    // STR     R4, [R0,#0x10]
    // 内存存储操作
    // STR     R1, [R0]
    // 内存存储操作
    // STR     R2, [R0,#4]
    // 内存存储操作
    // STR     R3, [R0,#8]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52E34
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_52e34(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r1= -0xC
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R1-R3}
    // 栈操作
    // ADD     SP, SP, #0xC
    // 算术运算
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52E3A
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_52e3a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r1= -0xC
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R1-R3}
    // 栈操作
    // ADD     SP, SP, #0xC
    // 算术运算
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52E40
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_52e40(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r1= -0xC
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R1-R3}
    // 栈操作
    // ADD     SP, SP, #0xC
    // 算术运算
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52E46
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_52e46(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r1= -0xC
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R1-R3}
    // 栈操作
    // ADD     SP, SP, #0xC
    // 算术运算
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52E4C
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_52e4c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1F= -0x1F
    // var_1E= -0x1E
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5339C
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_5339c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x26;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x34;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_2B= -0x2B
    // var_2A= -0x2A
    // var_28= -0x28
    // var_26= -0x26
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53AD2
 * @note 指令数: 66, 标签数: 0
 */
void precise_func_53ad2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFFFBFFFF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFFFEFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CMP     R4, #0
    // 比较操作
    // BEQ     locret_53B56
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0xFFFEFFFF
    // 内存加载操作
    // ANDS    R1, R0
    // STR     R1, [R4]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0xFFFDFFFF
    // 内存加载操作
    // ANDS    R1, R0
    // STR     R1, [R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#3]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRH    R0, [R4,#4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRH    R0, [R4,#8]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xA]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xB]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xC]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xD]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xE]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [R4,#0x10]
    // 内存存储操作
    // MOVS    R0, #0x30 ; '0'
    // R0 = 0x30;
    // LDRB    R0, [R4,R0]
    // 内存加载操作
    // STRB    R0, [R4,#0x1A]
    // 内存存储操作
    // MOVS    R0, #0x30 ; '0'
    // R0 = 0x30;
    // LDRB    R0, [R4,R0]
    // 内存加载操作
    // STRB    R0, [R4,#0x1B]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0xFFFBFFFF
    // 内存加载操作
    // ANDS    R1, R0
    // STR     R1, [R4]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0xFFF7FFFF
    // 内存加载操作
    // ANDS    R1, R0
    // STR     R1, [R4]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0xFFEFFFFF
    // 内存加载操作
    // ANDS    R1, R0
    // STR     R1, [R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [R4,#0x1C]
    // 内存存储操作
    // MOVS    R0, R4
    // LDR     R1, [R4,#0x24]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0xFFDFFFFF
    // 内存加载操作
    // ANDS    R1, R0
    // STR     R1, [R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R1, #0x28 ; '('
    // R1 = 0x28;
    // STRB    R0, [R4,R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [R4,#0x2C]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53B74
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_53b74(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x26;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_26= -0x26
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53D50
 * @note 指令数: 34, 标签数: 0
 */
uint32_t precise_func_53d50(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // MOVS    R2, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // UXTB    R2, R2
    // 数据扩展操作
    // MOVS    R0, R2
    // EORS    R0, R1
    // MOVS    R3, R0
    // LSLS    R0, R3, #1
    // LSLS    R4, R3, #2
    // EORS    R4, R0
    // LSLS    R0, R3, #3
    // EORS    R0, R4
    // LSLS    R4, R3, #4
    // EORS    R4, R0
    // LSLS    R0, R3, #5
    // EORS    R0, R4
    // LSLS    R4, R3, #6
    // EORS    R4, R0
    // LSLS    R0, R3, #7
    // EORS    R0, R4
    // EORS    R0, R3
    // MOVS    R3, R0
    // MOVS    R0, R3
    // MOVS    R4, #0xFE
    // R4 = 0xFE;
    // ANDS    R4, R0
    // UXTH    R3, R3
    // 数据扩展操作
    // ASRS    R3, R3, #8
    // MOVS    R0, R3
    // LSLS    R0, R0, #0x1F
    // LSRS    R0, R0, #0x1F
    // EORS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // POP     {R4}
    // 栈操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53D94
 * @note 指令数: 26, 标签数: 0
 */
uint32_t precise_func_53d94(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // UXTB    R3, R1
    // 数据扩展操作
    // UXTB    R2, R2
    // 数据扩展操作
    // EORS    R3, R2
    // MOVS    R0, R3
    // UXTH    R1, R1
    // 数据扩展操作
    // LSRS    R1, R1, #8
    // LSLS    R3, R0, #8
    // EORS    R3, R1
    // LSLS    R1, R0, #3
    // EORS    R1, R3
    // LSLS    R3, R0, #0xC
    // EORS    R3, R1
    // MOVS    R1, R0
    // UXTH    R1, R1
    // 数据扩展操作
    // LSRS    R1, R1, #4
    // EORS    R1, R3
    // LSLS    R3, R0, #0x1C
    // LSRS    R3, R3, #0x1C
    // EORS    R3, R1
    // LSLS    R0, R0, #0x1C
    // LSRS    R0, R0, #0x1C
    // LSLS    R0, R0, #7
    // EORS    R0, R3
    // UXTH    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53DC8
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_53dc8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R2, R0
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #1
    // R0 = 1;
    // LDRB    R5, [R2]
    // 内存加载操作
    // LDRB    R6, [R1]
    // 内存加载操作
    // CMP     R5, R6
    // 比较操作
    // BEQ     loc_53DDE
    // 条件跳转
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R0, R5
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53E5E
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_53e5e(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_53E5E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53E60
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_53e60(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x200000
    // R1 = 0x200000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53EBE
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_53ebe(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015130;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #8
    // 比较操作
    // BGE     loc_53EF2
    // 条件跳转
    // LDR     R0, =0x8015130
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x18
    // R1 = 0x18;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015130
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0x18
    // R2 = 0x18;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // BL      sub_474E8
    // 调用函数: sub_474E8();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_53EEC
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_53EEE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53EF6
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_53ef6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015130;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x8015130
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x18
    // R1 = 0x18;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015130
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0x18
    // R2 = 0x18;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // BL      sub_474E8
    // 调用函数: sub_474E8();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_53F1E
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_53F20
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53F2C
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_53f2c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40001410;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x4000140C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000772C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x40001410
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_53F54
    // LDR     R0, =0x4000140C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_53F54
    // MOVS    R0, #1
    // R0 = 1;
    // MVNS    R0, R0
    // LDR     R1, =0x40001410
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000772C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_53F54
    // 条件跳转
    // LDR     R0, =0x2000772C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BLX     R0
    // 调用函数: R0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53F56
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_53f56(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53FB0
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_53fb0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000772C;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x2000772C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53FB6
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_53fb6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40001410;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4000140C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #1
    // R1 = 1;
    // MVNS    R1, R1
    // LDR     R2, =0x40001410
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // LDR     R1, =0x4000140C
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #1
    // R2 = 1;
    // ORRS    R2, R1
    // LDR     R1, =0x4000140C
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_53FE0
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_53fe0(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // CMP     R0, R2
    // 比较操作
    // BNE     locret_54002
    // 条件跳转
    // MOV     R12, R4
    // MOVS    R4, R1
    // ORRS    R4, R3
    // CMP     R0, #1
    // 比较操作
    // ADCS    R4, R4
    // BEQ     loc_54000
    // 条件跳转
    // CMP     R1, R3
    // 比较操作
    // BNE     loc_54000
    // 条件跳转
    // LSLS    R4, R1, #1
    // ASRS    R4, R4, #0x15
    // ADDS    R4, R4, #1
    // 算术运算
    // EORS    R4, R4
    // BCC     loc_54000
    // LSLS    R4, R1, #0xC
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54004
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_54004(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R0, R4
    // BL      sub_46FE8
    // 调用函数: sub_46FE8();
    // MOVS    R1, R4
    // MOVS    R2, #3
    // R2 = 3;
    // MOVS    R3, R5
    // MOVS    R7, #2
    // R7 = 2;
    // MULS    R3, R7
    // LSLS    R2, R3
    // BICS    R0, R2
    // BL      sub_47000
    // 调用函数: sub_47000();
    // MOVS    R0, R4
    // BL      sub_46FE8
    // 调用函数: sub_46FE8();
    // MOVS    R2, R0
    // MOVS    R1, R4
    // MOVS    R3, R5
    // MOVS    R0, #2
    // R0 = 2;
    // MULS    R3, R0
    // MOVS    R0, R6
    // LSLS    R0, R3
    // ORRS    R0, R2
    // BL      sub_47000
    // 调用函数: sub_47000();
    // POP     {R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54040
 * @note 指令数: 56, 标签数: 0
 */
void precise_func_54040(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801546C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R1, R0, R1
    // 算术运算
    // ADDS    R1, R1, #4
    // 算术运算
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R2, #0x48 ; 'H'
    // R2 = 0x48;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R1, R0, R1
    // 算术运算
    // ADDS    R1, #0x1C
    // 算术运算
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R2, #0x48 ; 'H'
    // R2 = 0x48;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0x30]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_540BC
    // 条件跳转
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R1, R0, R1
    // 算术运算
    // ADDS    R1, #0x34 ; '4'
    // 算术运算
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R2, #0x48 ; 'H'
    // R2 = 0x48;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // LDR     R0, [R0,#0x30]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_543DE
    // 调用函数: sub_543DE();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_540CA
 * @note 指令数: 8, 标签数: 1
 */
void precise_func_540ca(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #2
    // 比较操作
    // BGE     locret_540DA
    // 条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_540D0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_540DC
 * @note 指令数: 80, 标签数: 0
 */
void precise_func_540dc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801546C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8016084;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8016084
    // 内存加载操作
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R1, R4
    // LDRH    R1, [R0,R1]
    // 内存加载操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_54004
    // 调用函数: sub_54004();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // LDR     R0, [R0,R3]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // LDR     R0, [R0,R3]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // LDR     R0, [R0,R3]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54190
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_54190(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // BL      sub_540DC
    // 调用函数: sub_540DC();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5419C
 * @note 指令数: 67, 标签数: 0
 */
void precise_func_5419c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801546C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8016084;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8016084
    // 内存加载操作
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R1, R4
    // LDRH    R1, [R0,R1]
    // 内存加载操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_54004
    // 调用函数: sub_54004();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // LDR     R0, [R0,R3]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // LDR     R0, [R0,R3]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54232
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_54232(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801546C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_5425C
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // B       loc_54278
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_542BA
 * @note 指令数: 66, 标签数: 0
 */
void precise_func_542ba(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801546C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8016084;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016084
    // 内存加载操作
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R1, R4
    // LDRH    R1, [R0,R1]
    // 内存加载操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_54004
    // 调用函数: sub_54004();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // LDR     R0, [R0,R3]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R2, #0x48 ; 'H'
    // R2 = 0x48;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_474E8
    // 调用函数: sub_474E8();
    // MOVS    R5, R0
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // LDR     R0, [R0,R3]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8016084
    // 内存加载操作
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R1, R4
    // LDRH    R1, [R0,R1]
    // 内存加载操作
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_54004
    // 调用函数: sub_54004();
    // BL      sub_540CA
    // 调用函数: sub_540CA();
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54358
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_54358(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R7, R1
    // BL      sub_48456
    // 调用函数: sub_48456();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_543A6
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_543a6(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R4, R1
    // MOVS    R7, R2
    // BL      sub_48456
    // 调用函数: sub_48456();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

