// 精确转换批次 22 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78A0A
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_78a0a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000367C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_78A1C
    // 条件跳转
    // LDR     R0, =0x2000367C
    // 内存加载操作
    // MOVS    R6, R0
    // B       loc_78A20
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78A46
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_78a46(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036EC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R1, =0x200036EC
    // 内存加载操作
    // LDRH    R1, [R1]
    // 内存加载操作
    // CMP     R1, #0
    // 比较操作
    // BNE     loc_78A52
    // 条件跳转
    // B       locret_78B92
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78B94
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_78b94(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003680;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20003680
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_78BA4
    // 条件跳转
    // LDR     R0, =0x20003680
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BLX     R0
    // 调用函数: R0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78C1C
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_78c1c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000371B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8001804;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R2, =0x8001800
    // 内存加载操作
    // MOVS    R0, R2
    // LDR     R2, =0x8001804
    // 内存加载操作
    // MOVS    R1, R2
    // LDR     R2, [R0]
    // 内存加载操作
    // MOVS    R3, #0
    // R3 = 0;
    // MVNS    R3, R3
    // CMP     R2, R3
    // 比较操作
    // BNE     loc_78C42
    // 条件跳转
    // LDR     R2, [R1]
    // 内存加载操作
    // MOVS    R3, #0
    // R3 = 0;
    // MVNS    R3, R3
    // CMP     R2, R3
    // 比较操作
    // BNE     loc_78C42
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R3, =0x2000371B
    // 内存加载操作
    // STRB    R2, [R3]
    // 内存存储操作
    // B       locret_78C48
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78C4A
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_78c4a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000371B;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x2000371B
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78C5C
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_78c5c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003719;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x20
    // 算术运算
    // ADD     R0, SP, #0x30+var_2C
    // 算术运算
    // MOVS    R1, #0x1C
    // R1 = 0x1C;
    // BL      sub_76870
    // 调用函数: sub_76870();
    // BL      sub_76874
    // 调用函数: sub_76874();
    // LDR     R0, =0x20003719
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_78C82
    // 条件跳转
    // BCS     loc_78C78
    // B       loc_792E6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79350
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_79350(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003719;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20003719
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_79368
    // 条件跳转
    // BCS     loc_7935E
    // B       locret_796CC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_796F4
 * @note 指令数: 43, 标签数: 0
 */
void precise_func_796f4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036D4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000371A;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200036DA;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003719;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036DC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x8001800
    // 内存加载操作
    // MOVS    R4, R0
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0x2000365C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x8001804
    // 内存加载操作
    // MOVS    R4, R0
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0x20003660
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036D4
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036D6
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036D8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036DA
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000371D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000371C
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20003719
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000371A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000371B
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_78C1C
    // 调用函数: sub_78C1C();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036DC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79754
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_79754(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x58;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x60;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_60= -0x60
    // var_5C= -0x5C
    // var_58= -0x58
    // var_38= -0x38
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7988A
 * @note 指令数: 6, 标签数: 0
 */
uint32_t precise_func_7988a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036DC;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x200036DC
    // 内存加载操作
    // LDRB    R1, [R0]
    // 内存加载操作
    // MOVS    R0, R1
    // LSLS    R0, R0, #0x1F
    // LSRS    R0, R0, #0x1F
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_798C8
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_798c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x24
    // 算术运算
    // MOV     R0, SP
    // MOVS    R1, #0x20 ; ' '
    // R1 = 0x20;
    // BL      sub_76870
    // 调用函数: sub_76870();
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_798DE
    // 条件跳转
    // B       loc_79A0A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79A44
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_79a44(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011548;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x8011548
    // 内存加载操作
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x18
    // R1 = 0x18;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011548
    // 内存加载操作
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0x18
    // R2 = 0x18;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // BL      sub_7E3AE
    // 调用函数: sub_7E3AE();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_79A6C
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_79A6E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79A72
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_79a72(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79ABC
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_79abc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011CC8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x8011CC8
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011CC8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7E3AE
    // 调用函数: sub_7E3AE();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_79AE2
    // 条件跳转
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8011CC8
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011CC8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // B       locret_79AF2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79AF4
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_79af4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011CCC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011CC8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R1, =0x8011CCC
    // 内存加载操作
    // LDR     R0, =0x8011CC8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7E160
    // 调用函数: sub_7E160();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79B10
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_79b10(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79DC0
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_79dc0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80113E0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80113DC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_79DDC
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x80113E0
    // 内存加载操作
    // LDR     R1, [R0]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x80113DC
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // B       locret_79DEC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79DFC
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_79dfc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xAF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, #0x30 ; '0'
    // R0 = 0x30;
    // BL      sub_79F54
    // 调用函数: sub_79F54();
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_79E14
    // 条件跳转
    // MOVS    R0, #0xAF
    // R0 = 0xAF;
    // BL      sub_79F54
    // 调用函数: sub_79F54();
    // B       locret_79E1A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79E1C
 * @note 指令数: 33, 标签数: 1
 */
void precise_func_79e1c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011730;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #8
    // 比较操作
    // BGE     locret_79E64
    // 条件跳转
    // MOVS    R2, R4
    // UXTB    R2, R2
    // 数据扩展操作
    // LSRS    R2, R2, #7
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R1, [R0,#0x34]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R0, [R0,#0x30]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // LSLS    R4, R4, #1
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // ADDS    R5, R5, #1
    // 算术运算
    // B       loc_79E24
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79E70
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_79e70(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011730;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79ED8
 * @note 指令数: 35, 标签数: 0
 */
void precise_func_79ed8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011730;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
    // PUSH    {R0,R3-R7,LR}
    // 栈操作
    // SUB     SP, SP, #4
    // 算术运算
    // MOVS    R7, R1
    // MOVS    R6, R2
    // LDR     R5, [SP,#0x20+arg_0]
    // 内存加载操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8011730
    // 内存加载操作
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_79E1C
    // 调用函数: sub_79E1C();
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x20+var_1C]
    // 内存加载操作
    // BL      sub_79E1C
    // 调用函数: sub_79E1C();
    // MOVS    R0, R7
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_79E1C
    // 调用函数: sub_79E1C();
    // MOVS    R0, R6
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_79E1C
    // 调用函数: sub_79E1C();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79F54
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_79f54(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x48000014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF7F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48000414;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // LDR     R0, =0x48000414
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R3, =unk_FFFC
    // 内存加载操作
    // ANDS    R3, R0
    // LDR     R0, =0x48000414
    // 内存加载操作
    // STR     R3, [R0]
    // 内存存储操作
    // LDR     R0, =0x48000014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R3, =0xFF7F
    // 内存加载操作
    // ANDS    R3, R0
    // LDR     R0, =0x48000014
    // 内存加载操作
    // STR     R3, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R2, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_79FE2
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_79fe2(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x48000014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF7F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48000414;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // LDR     R0, =0x48000414
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R3, =unk_FFFD
    // 内存加载操作
    // ANDS    R3, R0
    // LDR     R0, =0x48000414
    // 内存加载操作
    // STR     R3, [R0]
    // 内存存储操作
    // LDR     R0, =0x48000414
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R3, #1
    // R3 = 1;
    // ORRS    R3, R0
    // LDR     R0, =0x48000414
    // 内存加载操作
    // STR     R3, [R0]
    // 内存存储操作
    // LDR     R0, =0x48000014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R3, =0xFF7F
    // 内存加载操作
    // ANDS    R3, R0
    // LDR     R0, =0x48000014
    // 内存加载操作
    // STR     R3, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R2, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A090
 * @note 指令数: 32, 标签数: 0
 */
void precise_func_7a090(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x75;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R7, R2
    // MOVS    R6, R3
    // MOVS    R0, #0x15
    // R0 = 0x15;
    // BL      sub_79F54
    // 调用函数: sub_79F54();
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_79FE2
    // 调用函数: sub_79FE2();
    // MOVS    R0, R4
    // MOVS    R1, R7
    // ADDS    R0, R0, R1
    // 算术运算
    // SUBS    R0, R0, #1
    // 算术运算
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_79FE2
    // 调用函数: sub_79FE2();
    // MOVS    R0, #0x75 ; 'u'
    // R0 = 0x75;
    // BL      sub_79F54
    // 调用函数: sub_79F54();
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_79FE2
    // 调用函数: sub_79FE2();
    // MOVS    R0, R5
    // MOVS    R1, R6
    // ADDS    R0, R0, R1
    // 算术运算
    // SUBS    R0, R0, #1
    // 算术运算
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_79FE2
    // 调用函数: sub_79FE2();
    // MOVS    R0, #0x30 ; '0'
    // R0 = 0x30;
    // BL      sub_79F54
    // 调用函数: sub_79F54();
    // MOVS    R0, #0x5C ; '\'
    // R0 = 0x5C;
    // BL      sub_79F54
    // 调用函数: sub_79F54();
    // POP     {R0,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A0E0
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_7a0e0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R2,R3}
    // 栈操作
    // MOVS    R2, #0x1000000
    // R2 = 0x1000000;
    // ADDS    R3, R0, R0
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BHI     loc_7A0FC
    // ADDS    R3, R1, R1
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BHI     loc_7A0FC
    // MOVS    R2, R0
    // ORRS    R2, R1
    // ADDS    R2, R2, R2
    // 算术运算
    // BCS     loc_7A100
    // CMP     R0, R1
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A106
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_7a106(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_7A106
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A108
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_7a108(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0x1000000
    // R2 = 0x1000000;
    // ADDS    R1, R0, R0
    // 算术运算
    // CMN     R2, R1
    // 比较操作
    // BCS     loc_7A128
    // CMP     R2, R1
    // 比较操作
    // BHI     loc_7A132
    // MOVS    R2, #0x38000000
    // R2 = 0x38000000;
    // LSRS    R1, R1, #4
    // ADDS    R1, R1, R2
    // 算术运算
    // LSRS    R2, R0, #0x1F
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A120
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_7a120(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LSLS    R2, R2, #0x1F
    // ORRS    R1, R2
    // LSLS    R0, R0, #0x1D
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A13A
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7a13a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R6, R1
    // EORS    R6, R3
    // MOVS    R5, #0x80000000
    // R5 = 0x80000000;
    // ANDS    R6, R5
    // MOV     R12, R6
    // LSRS    R6, R5, #0xA
    // ADDS    R7, R3, R3
    // 算术运算
    // ADDS    R4, R1, R1
    // 算术运算
    // CMN     R4, R6
    // 比较操作
    // BCS     loc_7A164
    // CMN     R7, R6
    // 比较操作
    // BCS     loc_7A174
    // LSRS    R4, R4, #0x15
    // BEQ     loc_7A15E
    // 条件跳转
    // LSRS    R7, R7, #0x15
    // BNE     loc_7A184
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A2BE
 * @note 指令数: 6, 标签数: 0
 */
uint32_t precise_func_7a2be(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0x200000
    // R2 = 0x200000;
    // ADDS    R3, R1, R1
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BLS     loc_7A2CC
    // ASRS    R0, R3, #0x15
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A320
 * @note 指令数: 39, 标签数: 0
 */
void precise_func_7a320(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R5, R0
    // EORS    R5, R1
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ANDS    R5, R2
    // ADDS    R3, R1, R1
    // 算术运算
    // ADDS    R4, R0, R0
    // 算术运算
    // LSRS    R4, R4, #0x18
    // BEQ     loc_7A392
    // 条件跳转
    // LSRS    R3, R3, #0x18
    // BEQ     loc_7A39E
    // 条件跳转
    // CMP     R4, #0xFF
    // 比较操作
    // BEQ     loc_7A3A6
    // 条件跳转
    // CMP     R3, #0xFF
    // 比较操作
    // BEQ     loc_7A3B6
    // 条件跳转
    // ADDS    R4, R4, R3
    // 算术运算
    // LSLS    R0, R0, #8
    // LSLS    R1, R1, #8
    // ORRS    R0, R2
    // ORRS    R1, R2
    // LSRS    R0, R0, #8
    // LSRS    R1, R1, #8
    // MOV     R12, R2
    // UXTB    R2, R0
    // 数据扩展操作
    // MULS    R2, R1
    // LSRS    R0, R0, #8
    // UXTB    R3, R0
    // 数据扩展操作
    // MULS    R3, R1
    // LSRS    R0, R0, #8
    // MULS    R0, R1
    // LSRS    R1, R2, #8
    // ADDS    R3, R3, R1
    // 算术运算
    // ORRS    R2, R3
    // UXTB    R2, R2
    // 数据扩展操作
    // LSRS    R1, R3, #8
    // ADDS    R1, R1, R0
    // 算术运算
    // BMI     loc_7A370
    // ADDS    R1, R1, R1
    // 算术运算
    // SUBS    R4, R4, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A3D0
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_7a3d0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x7F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADDS    R2, R0, R0
    // 算术运算
    // BCS     loc_7A408
    // LSRS    R2, R2, #0x18
    // SUBS    R2, #0x7F
    // 算术运算
    // BMI     loc_7A408
    // LSLS    R1, R0, #8
    // MOVS    R0, #0x80000000
    // R0 = 0x80000000;
    // ORRS    R1, R0
    // SUBS    R2, #0x1F
    // 算术运算
    // BGT     loc_7A3F0
    // 条件跳转
    // NEGS    R2, R2
    // MOVS    R0, R1
    // LSRS    R0, R2
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A40C
 * @note 指令数: 4, 标签数: 1
 */
void precise_func_7a40c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // TST     R2, R2
    // 比较操作
    // BEQ     loc_7A45A
    // 条件跳转
    // CMP     R1, R3
    // 比较操作
    // BLS     loc_7A466
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A476
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_7a476(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_7A476
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A478
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_7a478(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R1, R0
    // MOVS    R0, R1
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R2, R3
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A4A4
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_7a4a4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x801227C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x1C
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R0, #5
    // R0 = 5;
    // BL      sub_768D8
    // 调用函数: sub_768D8();
    // MOVS    R5, #0x18
    // R5 = 0x18;
    // MOVS    R6, #0
    // R6 = 0;
    // ADD     R7, SP, #0x30+var_2C
    // 算术运算
    // MOVS    R2, R6
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_7A4D0
    // 条件跳转
    // LDR     R1, =0x801227C
    // 内存加载操作
    // ADD     R0, SP, #0x30+var_2C
    // 算术运算
    // BL      sub_78944
    // 调用函数: sub_78944();
    // B       loc_7A4D8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A510
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_7a510(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_42= -0x42
    // var_40= -0x40
    // var_3C= -0x3C
    // var_24= -0x24
    // var_20= -0x20
    // var_8= -8
    // var_s0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7A960
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7a960(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x52;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x58;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_58= -0x58
    // var_54= -0x54
    // var_52= -0x52
    // var_50= -0x50
    // var_4C= -0x4C
    // var_48= -0x48
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B2F8
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_7b2f8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801228C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x14;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
    // arg_14=  0x14
    // LDR     R0, =0x2000374B
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_7B33C
    // 条件跳转
    // MOVS    R6, #0x18
    // R6 = 0x18;
    // MOVS    R5, #0x20 ; ' '
    // R5 = 0x20;
    // ADD     R7, SP, #arg_14
    // 算术运算
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // LDR     R1, =0x80123C0
    // 内存加载操作
    // ADD     R0, SP, #arg_14
    // 算术运算
    // BL      sub_78944
    // 调用函数: sub_78944();
    // ADD     R0, SP, #arg_14
    // 算术运算
    // BL      sub_77834
    // 调用函数: sub_77834();
    // ADD     R1, SP, #arg_14
    // 算术运算
    // MOVS    R2, #0x20 ; ' '
    // R2 = 0x20;
    // STRB    R2, [R1,R0]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#arg_0]
    // 内存存储操作
    // MOVS    R3, #1
    // R3 = 1;
    // ADD     R2, SP, #arg_14
    // 算术运算
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, #4
    // R0 = 4;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // B       sub_7B370
    // 无条件跳转
    // ALIGN 4
    // dword_7B338 DCD 0x801228C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B370
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_7b370(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x44;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADD     SP, SP, #0x44 ; 'D'
    // 算术运算
    // POP     {R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B378
 * @note 指令数: 60, 标签数: 0
 */
void precise_func_7b378(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200033A4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003702;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xE1;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003704;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003634;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003700
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003702
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003704
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000374E
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003753
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003750
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003751
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003752
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036B4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003706
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036B8
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x1F4
    // R0 = 0x1F4;
    // LDR     R1, =0x20003634
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000374F
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0xE1
    // R0 = 0xE1;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R4, =0x200033A4
    // 内存加载操作
    // MOVS    R2, R6
    // LDR     R1, [SP,#0x18+var_18]
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R5, #0x18
    // R5 = 0x18;
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R7, =0x20003538
    // 内存加载操作
    // MOVS    R2, R4
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // LDR     R1, =0x20000130
    // 内存加载操作
    // LDR     R0, =0x20003538
    // 内存加载操作
    // BL      sub_78944
    // 调用函数: sub_78944();
    // POP     {R0,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B440
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_7b440(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000374E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R4, =0x2000374E
    // 内存加载操作
    // LDRB    R4, [R4]
    // 内存加载操作
    // CMP     R4, #0x78 ; 'x'
    // 比较操作
    // BLT     loc_7B44E
    // 条件跳转
    // B       locret_7B6CE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B6D4
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_7b6d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000130;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003538;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R1, =0x20000130
    // 内存加载操作
    // LDR     R0, =0x20003538
    // 内存加载操作
    // BL      sub_78944
    // 调用函数: sub_78944();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B6F0
 * @note 指令数: 63, 标签数: 0
 */
void precise_func_7b6f0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200033A4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003702;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xE1;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000374D;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003704;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003700
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003702
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003704
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000374E
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003753
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000374D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003706
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003750
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003751
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003752
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036B4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036B8
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0xE1
    // R0 = 0xE1;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R4, =0x200033A4
    // 内存加载操作
    // MOVS    R2, R6
    // LDR     R1, [SP,#0x18+var_18]
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R5, #0x18
    // R5 = 0x18;
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R7, =0x20003538
    // 内存加载操作
    // MOVS    R2, R4
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // LDR     R1, =0x20000130
    // 内存加载操作
    // LDR     R0, =0x20003538
    // 内存加载操作
    // BL      sub_78944
    // 调用函数: sub_78944();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20003634
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000374F
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // POP     {R0,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B774
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_7b774(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003702;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000978;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036B4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003702
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003700
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20000978
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7B798
    // 条件跳转
    // LDR     R0, =0x20000978
    // 内存加载操作
    // LDR     R1, =0x200036B4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20003706
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // B       loc_7B7A4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B7BA
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_7b7ba(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036B4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R2, =0x200036B4
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // CMP     R2, #0
    // 比较操作
    // BEQ     loc_7B7EC
    // 条件跳转
    // LDR     R2, =0x200036B4
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // MOVS    R1, R2
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B7FE
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_7b7fe(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200033A4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE1;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, #0xE1
    // R4 = 0xE1;
    // MOVS    R5, #0
    // R5 = 0;
    // LDR     R6, =0x200033A4
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R4
    // MOVS    R0, R6
    // BL      sub_76820
    // 调用函数: sub_76820();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B812
 * @note 指令数: 63, 标签数: 1
 */
void precise_func_7b812(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTH    R0, R0
    // 数据扩展操作
    // CMP     R0, #0x78 ; 'x'
    // 比较操作
    // BGE     loc_7B890
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20000978
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20000978
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STRH    R1, [R2,#4]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20000978
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STRB    R1, [R2,#6]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20000978
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STRB    R1, [R2,#8]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20000978
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STR     R1, [R2,#0x20]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20000978
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STR     R1, [R2,#0x24]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20000978
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STR     R1, [R2,#0x28]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20000978
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STR     R1, [R2,#0x2C]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_7B818
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B8CC
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_7b8cc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003634;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000374F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R0, =0x20003634
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_7B98E
    // 条件跳转
    // LDR     R0, =0x2000374F
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     locret_7B98E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000374F
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_7F4F8
    // 调用函数: sub_7F4F8();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7B8F6
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_7F554
    // 调用函数: sub_7F554();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7B9B4
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7b9b4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036B4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R5, #0
    // R5 = 0;
    // BL      sub_7B7BA
    // 调用函数: sub_7B7BA();
    // MOVS    R4, R0
    // LDR     R0, =0x20003751
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7BA84
    // 条件跳转
    // LDR     R0, =0x200036B4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7A478
    // 调用函数: sub_7A478();
    // MOVS    R5, R0
    // LDRB    R0, [R5,#7]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_7B9DA
    // 条件跳转
    // B       locret_7BB04
    // 无条件跳转
}

