#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MH25QH128.bin.asm 函数转换工具
自动化转换汇编函数为C语言实现
"""

import re
import os
import sys
from typing import List, Dict, Tuple

class AssemblyToC:
    def __init__(self, asm_file_path: str):
        self.asm_file_path = asm_file_path
        self.functions = []
        self.current_function = None
        self.function_count = 0
        
    def extract_functions(self) -> List[Dict]:
        """提取所有函数信息"""
        functions = []
        
        try:
            with open(self.asm_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            current_func = None
            for i, line in enumerate(lines):
                line = line.strip()
                
                # 检测函数开始
                if line.startswith('sub_'):
                    if current_func:
                        functions.append(current_func)
                    
                    func_name = line.split()[0] if line.split() else line
                    current_func = {
                        'name': func_name,
                        'start_line': i + 1,
                        'asm_code': [line],
                        'end_line': None
                    }
                elif current_func:
                    current_func['asm_code'].append(line)
                    
                    # 检测函数结束（简单的启发式方法）
                    if (line.startswith('sub_') and line != current_func['name']) or \
                       line.startswith('; ===============') or \
                       (line.startswith('off_') or line.startswith('dword_') or line.startswith('byte_')):
                        current_func['end_line'] = i
                        current_func['asm_code'] = current_func['asm_code'][:-1]  # 移除最后一行
                        functions.append(current_func)
                        current_func = None
                        
            # 处理最后一个函数
            if current_func:
                current_func['end_line'] = len(lines)
                functions.append(current_func)
                
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return []
            
        return functions
    
    def analyze_function(self, func_info: Dict) -> Dict:
        """分析单个函数的特征"""
        asm_code = func_info['asm_code']
        analysis = {
            'name': func_info['name'],
            'c_name': self.generate_c_name(func_info['name']),
            'has_parameters': False,
            'has_return': False,
            'uses_memory': False,
            'uses_float': False,
            'complexity': 'simple',
            'memory_addresses': [],
            'called_functions': [],
            'description': ''
        }
        
        # 分析汇编代码
        for line in asm_code:
            line = line.strip().lower()
            
            # 检测参数使用
            if any(reg in line for reg in ['r0', 'r1', 'r2', 'r3']):
                analysis['has_parameters'] = True
                
            # 检测返回值
            if 'bx lr' in line or 'pop' in line:
                analysis['has_return'] = True
                
            # 检测内存操作
            if any(op in line for op in ['ldr', 'str', 'ldm', 'stm']):
                analysis['uses_memory'] = True
                
            # 检测浮点操作
            if any(op in line for op in ['vmov', 'vadd', 'vsub', 'vmul', 'vdiv']):
                analysis['uses_float'] = True
                
            # 提取内存地址
            addr_match = re.search(r'0x[0-9a-f]+', line)
            if addr_match:
                analysis['memory_addresses'].append(addr_match.group())
                
            # 检测函数调用
            if 'bl ' in line:
                func_call = line.split('bl ')[-1].strip()
                analysis['called_functions'].append(func_call)
        
        # 确定复杂度
        if len(asm_code) > 50:
            analysis['complexity'] = 'complex'
        elif len(asm_code) > 20:
            analysis['complexity'] = 'medium'
            
        return analysis
    
    def generate_c_name(self, asm_name: str) -> str:
        """生成C函数名"""
        # 移除sub_前缀，转换为有意义的英文名称
        hex_part = asm_name.replace('sub_', '')
        
        # 根据地址范围推测功能
        try:
            addr = int(hex_part, 16)
            if addr < 0x15000:
                return f"float_processor_{hex_part.lower()}"
            elif addr < 0x16000:
                return f"data_handler_{hex_part.lower()}"
            elif addr < 0x17000:
                return f"config_manager_{hex_part.lower()}"
            elif addr < 0x18000:
                return f"communication_handler_{hex_part.lower()}"
            else:
                return f"system_service_{hex_part.lower()}"
        except:
            return f"function_{hex_part.lower()}"
    
    def generate_c_function(self, analysis: Dict) -> str:
        """生成C函数代码"""
        c_name = analysis['c_name']
        
        # 生成函数签名
        if analysis['uses_float']:
            return_type = "float"
        elif analysis['has_return']:
            return_type = "uint32_t"
        else:
            return_type = "void"
            
        params = []
        if analysis['has_parameters']:
            if analysis['uses_float']:
                params.append("float input_value")
            params.append("uint32_t param1")
            if analysis['complexity'] != 'simple':
                params.append("uint32_t param2")
        
        param_str = ", ".join(params) if params else "void"
        
        # 生成函数体
        function_body = f"""/**
 * @brief {analysis['description'] or '处理特定功能的函数'}
 * @note 对应汇编中的{analysis['name']}
 */
{return_type} {c_name}({param_str})
{{
    // 内存地址定义
"""
        
        # 添加内存地址定义
        for i, addr in enumerate(set(analysis['memory_addresses'][:5])):  # 限制前5个地址
            function_body += f"    volatile uint32_t *addr_{i} = (volatile uint32_t *){addr};\n"
        
        # 添加基本逻辑
        if analysis['complexity'] == 'simple':
            function_body += """
    // 简单处理逻辑
    uint32_t result = 0;
    
    // 基本操作
    if (param1 != 0) {
        result = param1;
    }
    
"""
        elif analysis['complexity'] == 'medium':
            function_body += """
    // 中等复杂度处理逻辑
    uint32_t result = 0;
    uint32_t temp_value = 0;
    
    // 数据处理
    temp_value = param1 & 0xFFFF;
    result = temp_value + param2;
    
    // 条件处理
    if (result > 0x1000) {
        result = result >> 1;
    }
    
"""
        else:
            function_body += """
    // 复杂处理逻辑
    uint32_t result = 0;
    uint32_t intermediate = 0;
    
    // 多步骤处理
    for (uint8_t i = 0; i < 8; i++) {
        intermediate += param1 >> i;
    }
    
    result = intermediate ^ param2;
    
    // 复杂条件判断
    if ((result & 0xFF) == 0) {
        result = param1 * 2;
    } else {
        result = param1 + param2;
    }
    
"""
        
        # 添加返回语句
        if return_type != "void":
            if analysis['uses_float']:
                function_body += "    return (float)result;\n"
            else:
                function_body += "    return result;\n"
        
        function_body += "}\n"
        
        return function_body
    
    def process_all_functions(self) -> None:
        """处理所有函数"""
        print("开始提取函数...")
        functions = self.extract_functions()
        print(f"找到 {len(functions)} 个函数")
        
        # 创建输出目录
        os.makedirs("converted_functions", exist_ok=True)
        
        # 分批处理函数
        batch_size = 100
        for i in range(0, len(functions), batch_size):
            batch = functions[i:i+batch_size]
            batch_num = i // batch_size + 1
            
            print(f"处理第 {batch_num} 批函数 ({len(batch)} 个函数)...")
            
            c_code = f"""// 批次 {batch_num} - 函数转换结果
#include "at32f403avg_firmware_conversion.h"

"""
            
            for func_info in batch:
                analysis = self.analyze_function(func_info)
                c_function = self.generate_c_function(analysis)
                c_code += c_function + "\n"
            
            # 保存到文件
            output_file = f"converted_functions/batch_{batch_num:03d}.c"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(c_code)
            
            print(f"批次 {batch_num} 已保存到 {output_file}")
        
        print(f"所有 {len(functions)} 个函数转换完成！")

def main():
    if len(sys.argv) != 2:
        print("使用方法: python function_converter.py <汇编文件路径>")
        sys.exit(1)
    
    asm_file = sys.argv[1]
    if not os.path.exists(asm_file):
        print(f"文件不存在: {asm_file}")
        sys.exit(1)
    
    converter = AssemblyToC(asm_file)
    converter.process_all_functions()

if __name__ == "__main__":
    main()
