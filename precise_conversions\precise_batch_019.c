// 精确转换批次 19 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_543DE
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_543de(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801546C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // CMP     R4, #2
    // 比较操作
    // BGE     locret_5441E
    // 条件跳转
    // LDR     R0, =0x801546C
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0x30]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_5441E
    // 条件跳转
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_54400
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // B       loc_54402
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54424
 * @note 指令数: 60, 标签数: 0
 */
void precise_func_54424(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x100000
    // R1 = 0x100000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x8015948
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R1, R0, R1
    // 算术运算
    // ADDS    R1, R1, #4
    // 算术运算
    // LDR     R0, =0x8015948
    // 内存加载操作
    // MOVS    R2, #0x48 ; 'H'
    // R2 = 0x48;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // LDR     R0, =0x8015948
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R1, R0, R1
    // 算术运算
    // ADDS    R1, #0x1C
    // 算术运算
    // LDR     R0, =0x8015948
    // 内存加载操作
    // MOVS    R2, #0x48 ; 'H'
    // R2 = 0x48;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // LDR     R0, =0x8015948
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R1, R0, R1
    // 算术运算
    // ADDS    R1, #0x34 ; '4'
    // 算术运算
    // LDR     R0, =0x8015948
    // 内存加载操作
    // MOVS    R2, #0x48 ; 'H'
    // R2 = 0x48;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // LDR     R0, [R0,#0x30]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8015948
    // 内存加载操作
    // LDR     R1, [R0,#0x34]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015948
    // 内存加载操作
    // LDR     R0, [R0,#0x30]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_544AA
 * @note 指令数: 7, 标签数: 1
 */
void precise_func_544aa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // CMP     R0, #0xFF
    // 比较操作
    // BGT     locret_544B8
    // 条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_544B0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_544BA
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_544ba(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015948;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_544E4
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8015948
    // 内存加载操作
    // MOVS    R1, #0x48 ; 'H'
    // R1 = 0x48;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015948
    // 内存加载操作
    // MOVS    R3, #0x48 ; 'H'
    // R3 = 0x48;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // B       loc_54500
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54542
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_54542(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015948;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R7, R0
    // MOVS    R6, R1
    // MOVS    R5, R2
    // BL      sub_48456
    // 调用函数: sub_48456();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8015948
    // 内存加载操作
    // LDR     R1, [R0,#0x34]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015948
    // 内存加载操作
    // LDR     R0, [R0,#0x30]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // BL      sub_544AA
    // 调用函数: sub_544AA();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_545D8
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_545d8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016068;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000254;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R2, =0x8016068
    // 内存加载操作
    // MOVS    R1, R4
    // LDR     R0, =0x20000254
    // 内存加载操作
    // BL      sub_54642
    // 调用函数: sub_54642();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_545E8
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_545e8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016068;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000027C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R2, =0x8016068
    // 内存加载操作
    // MOVS    R1, R4
    // LDR     R0, =0x2000027C
    // 内存加载操作
    // BL      sub_54642
    // 调用函数: sub_54642();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_545F8
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_545f8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016070;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015F9C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200002CC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // LDR     R0, =0x8015F9C
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // LDR     R3, =0x8016070
    // 内存加载操作
    // MOVS    R2, #0x80
    // R2 = 0x80;
    // MOVS    R1, R4
    // LDR     R0, =0x200002CC
    // 内存加载操作
    // BL      sub_5466E
    // 调用函数: sub_5466E();
    // POP     {R1,R2,R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54628
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_54628(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R2, R0
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_5463E
    // 条件跳转
    // LDRB    R0, [R2]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // STRH    R0, [R1]
    // 内存存储操作
    // LDRH    R0, [R1]
    // 内存加载操作
    // LDRB    R3, [R2,#1]
    // 内存加载操作
    // ORRS    R0, R3
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_54642
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_54642(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R3, R0
    // MOVS    R0, R2
    // MOVS    R2, #0
    // R2 = 0;
    // CMP     R3, #0
    // 比较操作
    // BEQ     loc_54664
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5466E
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_5466e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // arg_0=  0
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R7, R1
    // MOVS    R6, R2
    // LDR     R5, [SP,#0x18+arg_0]
    // 内存加载操作
    // CMP     R7, R6
    // 比较操作
    // BCS     loc_54688
    // LDR     R2, [SP,#0x18+var_18]
    // 内存加载操作
    // MOVS    R1, R7
    // MOVS    R0, R4
    // BL      sub_54642
    // 调用函数: sub_54642();
    // B       locret_54692
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_55866
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_55866(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20000150;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x20000150
    // 内存加载操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // LSLS    R0, R0, #0x1E
    // BMI     loc_5587C
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_558DC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_558DE
 * @note 指令数: 33, 标签数: 0
 */
void precise_func_558de(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000150;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x20000150
    // 内存加载操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_540DC
    // 调用函数: sub_540DC();
    // LDR     R0, =0x20000150
    // 内存加载操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_55946
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x20000150
    // 内存加载操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#4]
    // 内存加载操作
    // MOVS    R1, #0xFE
    // R1 = 0xFE;
    // ANDS    R1, R0
    // LDR     R0, =0x20000150
    // 内存加载操作
    // MOVS    R3, #0x14
    // R3 = 0x14;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_543A6
    // 调用函数: sub_543A6();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_55924
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_559AA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_559B4
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_559b4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20000150;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
    // PUSH    {R1-R7,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R7, R1
    // CMP     R5, #0
    // 比较操作
    // BNE     loc_559CE
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20000150
    // 内存加载操作
    // MOVS    R2, #0x14
    // R2 = 0x14;
    // MULS    R2, R5
    // ADDS    R0, R0, R2
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_543DE
    // 调用函数: sub_543DE();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_55BF0
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_55bf0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R4,R5,LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOV     R0, SP
    // STRB    R4, [R0,#0x18+var_14]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R2, #1
    // R2 = 1;
    // ADD     R1, SP, #0x18+var_14
    // 算术运算
    // MOVS    R0, R5
    // BL      sub_559B4
    // 调用函数: sub_559B4();
    // POP     {R1-R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_55C10
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_55c10(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_55D60
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_55d60(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R4,R5,LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R2, #1
    // R2 = 1;
    // ADD     R1, SP, #0x18+var_14
    // 算术运算
    // MOVS    R0, R4
    // BL      sub_55C10
    // 调用函数: sub_55C10();
    // MOVS    R5, R0
    // SXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BPL     loc_55D86
    // MOVS    R0, R5
    // SXTH    R0, R0
    // 数据扩展操作
    // B       locret_55D8C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_55D8E
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_55d8e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R3, R0
    // MOVS    R4, R2
    // CMP     R2, #0
    // 比较操作
    // BNE     loc_55D9E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // B       locret_55E14
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57046
 * @note 指令数: 2, 标签数: 1
 */
void precise_func_57046(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // B       loc_57048
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5704E
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_5704e(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_46FA2
    // 调用函数: sub_46FA2();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57056
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_57056(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      loc_48798
    // 调用函数: loc_48798();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5705E
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_5705e(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_488BA
    // 调用函数: sub_488BA();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57066
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_57066(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_48A1C
    // 调用函数: sub_48A1C();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5706E
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_5706e(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_45AD8
    // 调用函数: sub_45AD8();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57076
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_57076(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_53F2C
    // 调用函数: sub_53F2C();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5707E
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_5707e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40001010;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4000100C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200077F8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x65;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x48000C00;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #1
    // R0 = 1;
    // MVNS    R0, R0
    // LDR     R1, =0x40001010
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200077F8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x200077F8
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200077F8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x65 ; 'e'
    // 比较操作
    // BLT     locret_570BE
    // 条件跳转
    // MOVS    R1, #8
    // R1 = 8;
    // LDR     R0, =0x48000C00
    // 内存加载操作
    // BL      sub_47536
    // 调用函数: sub_47536();
    // LDR     R0, =0x200077F8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R1, =(a031meDSImageAt_0+0x29) ; "ount %d exceeds max %d\x1B[0m\n"
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BLT     locret_570BE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200077F8
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x4000100C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // BICS    R0, R1
    // LDR     R1, =0x4000100C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_570D4
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_570d4(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_45828
    // 调用函数: sub_45828();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_577B4
 * @note 指令数: 11, 标签数: 1
 */
void precise_func_577b4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R3, #0
    // R3 = 0;
    // B       loc_577E4
    // 无条件跳转
    // LDR     R4, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #4
    // 算术运算
    // TST     R4, R1
    // 比较操作
    // BEQ     loc_577CA
    // 条件跳转
    // MOV     R5, R9
    // SUBS    R5, R5, #1
    // 算术运算
    // ADDS    R4, R4, R5
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_578D4
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_578d4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x578DC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x578E2;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R1, =(aMstpRxHeaderFr_0+0xC - 0x578DC) ; "der: FrameTooLong %u\n"
    // 内存加载操作
    // ADD     R1, PC          ; "der: FrameTooLong %u\n"
    // 算术运算
    // ADDS    R1, #0x18
    // 算术运算
    // LDR     R4, =(word_5792A - 0x578E2)
    // 内存加载操作
    // ADD     R4, PC          ; word_5792A
    // 算术运算
    // ADDS    R4, #0x16
    // 算术运算
    // B       loc_578EE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57A6A
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_57a6a(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57A6E
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_57a6e(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_57A78
    // 调用函数: sub_57A78();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57A76
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_57a76(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_57A76
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57A78
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_57a78(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R7, R0
    // MOV     R0, R7
    // BL      sub_57A84
    // 调用函数: sub_57A84();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57A82
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_57a82(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_57A82
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_57A84
 * @note 指令数: 8, 标签数: 1
 */
void precise_func_57a84(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20026;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xAB;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // NOP
    // NOP
    // LDR     R2, =0x20026
    // 内存加载操作
    // MOVS    R1, R2
    // MOVS    R0, #0x18
    // R0 = 0x18;
    // BKPT    0xAB
    // B       loc_57A8C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_73494
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_73494(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80120AC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200036B0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R4,LR}
    // 栈操作
    // MOV     R0, SP
    // LDR     R1, =0x80120AC
    // 内存加载操作
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // BL      sub_7630C
    // 调用函数: sub_7630C();
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // MOV     R1, SP
    // LDR     R0, =0x200036B0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_76340
    // 调用函数: sub_76340();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_734F0
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_734F2
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_734f2(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80120BC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200036B0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R4,LR}
    // 栈操作
    // MOV     R0, SP
    // LDR     R1, =0x80120BC
    // 内存加载操作
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // BL      sub_7630C
    // 调用函数: sub_7630C();
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // MOV     R1, SP
    // LDR     R0, =0x200036B0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_76340
    // 调用函数: sub_76340();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_73550
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_73552
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_73552(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80120CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200036B0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R4,LR}
    // 栈操作
    // MOV     R0, SP
    // LDR     R1, =0x80120CC
    // 内存加载操作
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // BL      sub_7630C
    // 调用函数: sub_7630C();
    // MOVS    R2, #0xE
    // R2 = 0xE;
    // MOV     R1, SP
    // LDR     R0, =0x200036B0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_76340
    // 调用函数: sub_76340();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_735AA
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_735AC
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_735ac(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036B0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80120DC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R4,LR}
    // 栈操作
    // MOV     R0, SP
    // LDR     R1, =0x80120DC
    // 内存加载操作
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // BL      sub_7630C
    // 调用函数: sub_7630C();
    // MOVS    R2, #0xF
    // R2 = 0xF;
    // MOV     R1, SP
    // LDR     R0, =0x200036B0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_76340
    // 调用函数: sub_76340();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_73604
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_73638
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_73638(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7374A
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7374a(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x1C
    // 算术运算
    // ADD     R0, SP, #0x1C+var_18
    // 算术运算
    // MOVS    R1, #0
    // R1 = 0;
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R5, #0x10
    // R5 = 0x10;
    // MOVS    R6, #0
    // R6 = 0;
    // ADD     R7, SP, #8
    // 算术运算
    // MOVS    R2, R6
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_738D0
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_738d0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003690;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {R2-R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, byte_73A60
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDRSH   R0, [R0,R1]
    // CMP     R0, #0xC
    // 比较操作
    // BLT     loc_738F2
    // 条件跳转
    // LDR     R0, =0x20003690
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // LDR     R0, byte_73A60
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // SUBS    R0, #0xC
    // 算术运算
    // MOVS    R4, R0
    // B       loc_73932
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_73A64
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_73a64(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x1C
    // 算术运算
    // ADD     R0, SP, #0x1C+var_18
    // 算术运算
    // MOVS    R1, #0
    // R1 = 0;
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R5, #0x10
    // R5 = 0x10;
    // MOVS    R6, #0
    // R6 = 0;
    // ADD     R7, SP, #8
    // 算术运算
    // MOVS    R2, R6
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_73BEC
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_73bec(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003694;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {R2-R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDRSH   R0, [R0,R1]
    // CMP     R0, #0xC
    // 比较操作
    // BLT     loc_73C0E
    // 条件跳转
    // LDR     R0, =0x20003694
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // SUBS    R0, #0xC
    // 算术运算
    // MOVS    R4, R0
    // B       loc_73C4E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_73D80
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_73d80(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x1C
    // 算术运算
    // ADD     R0, SP, #0x1C+var_18
    // 算术运算
    // MOVS    R1, #0
    // R1 = 0;
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R5, #0x10
    // R5 = 0x10;
    // MOVS    R6, #0
    // R6 = 0;
    // ADD     R7, SP, #8
    // 算术运算
    // MOVS    R2, R6
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_73F08
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_73f08(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003698;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {R2-R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDRSH   R0, [R0,R1]
    // CMP     R0, #0xC
    // 比较操作
    // BLT     loc_73F2A
    // 条件跳转
    // LDR     R0, =0x20003698
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // SUBS    R0, #0xC
    // 算术运算
    // MOVS    R4, R0
    // B       loc_73F6A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74098
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_74098(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_24= -0x24
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_741A4
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_741a4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFFFF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x200036F0
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // ADD     R3, SP, #0x10+var_C
    // 算术运算
    // LDR     R2, =0x200036FA
    // 内存加载操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // MOV     R0, SP
    // BL      sub_73638
    // 调用函数: sub_73638();
    // LDR     R0, [SP,#0x10+var_10]
    // 内存加载操作
    // MOVS    R1, #0x10000
    // R1 = 0x10000;
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_741D4
    // LDR     R0, =0xFFFF
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74260
 * @note 指令数: 35, 标签数: 0
 */
void precise_func_74260(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003734;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011F18;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011F18
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x1C
    // R0 = 0x1C;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_768D8
    // 调用函数: sub_768D8();
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_768D8
    // 调用函数: sub_768D8();
    // LDR     R0, =0x20003734
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_742B2
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #1
    // R3 = 1;
    // LDR     R2, =0x801245C
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0x44 ; 'D'
    // R0 = 0x44;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x801236C
    // 内存加载操作
    // MOVS    R1, #4
    // R1 = 4;
    // MOVS    R0, #0x40 ; '@'
    // R0 = 0x40;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // B       locret_742DA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_742E0
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_742e0(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_76502
    // 调用函数: sub_76502();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_742F2
    // 条件跳转
    // BL      sub_7646C
    // 调用函数: sub_7646C();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_742FE
    // 条件跳转
}

