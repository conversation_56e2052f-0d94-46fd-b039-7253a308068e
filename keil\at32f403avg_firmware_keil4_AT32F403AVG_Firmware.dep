Dependencies for Project 'at32f403avg_firmware_keil4', Target 'AT32F403AVG_Firmware': (DO NOT MODIFY !)
F (../src/at32f403avg_firmware.c)(0x68417996)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/at32f403avg_firmware.o)
F (..\src\logo_data.c)(0x6841750A)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/logo_data.o)
F (..\src\startup_at32f403avg.c)(0x68417360)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -<PERSON> -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/startup_at32f403avg.o)
F (..\src\web_server.c)(0x684176E9)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/web_server.o)
F (..\src\web_server_utils.c)(0x68417719)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/web_server_utils.o)
F (..\src\web_page_generator.c)(0x684178B2)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/web_page_generator.o)
F (..\src\crc_utils.c)(0x684178E0)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/crc_utils.o)
F (..\src\string_utils.c)(0x68417907)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/string_utils.o)
F (..\src\communication_protocol.c)(0x6841793A)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/communication_protocol.o)
F (..\src\math_utils.c)(0x68417A3C)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/math_utils.o)
F (..\src\hardware_drivers.c)(0x68417A74)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/hardware_drivers.o)
F (..\src\system_management.c)(0x68417AB3)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/system_management.o)
F (..\src\peripheral_management.c)(0x68417BE6)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/peripheral_management.o)
F (..\src\data_processing.c)(0x68417C1D)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/data_processing.o)
F (..\src\network_communication.c)(0x68417C5A)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/network_communication.o)
F (..\src\device_management.c)(0x68417D69)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/device_management.o)
F (..\src\advanced_communication.c)(0x68417DAE)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/advanced_communication.o)
F (..\src\flash_storage.c)(0x68417F18)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/flash_storage.o)
F (..\src\interrupt_service.c)(0x68417F57)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/interrupt_service.o)
F (..\src\application_tasks.c)(0x684190F0)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/application_tasks.o)
F (..\src\rtc_management.c)(0x684180BD)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/rtc_management.o)
F (..\src\config_management.c)(0x684180FC)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/config_management.o)
F (..\src\debug_diagnostics.c)(0x68419099)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/debug_diagnostics.o)
F (..\src\low_level_hardware.c)(0x68418E12)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/low_level_hardware.o)
F (..\src\advanced_string_utils.c)(0x68419069)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/advanced_string_utils.o)
F (..\src\final_system_functions.c)(0x68419124)(-c -mcpu=cortex-m3 -gdwarf-2 -MD -Wall -O -mapcs-frame -mthumb-interwork -IC:/Keil_v4/ARM/INC/AT32F403AVG7/ -o ./Objects/final_system_functions.o)
