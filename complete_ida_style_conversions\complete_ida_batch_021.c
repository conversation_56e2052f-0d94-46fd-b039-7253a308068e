// 完整IDA风格转换批次 21 - 专业级转换
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_76870
 * @note 指令数: 2
 * @note 类型: simple_function
 */
void ida_76870(void)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_76874
 * @note 指令数: 22
 * @note 类型: control_function
 */
void ida_76874(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_100 = (volatile uint32_t *)0x100;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_768A8
 * @note 指令数: 21
 * @note 类型: control_function
 */
void ida_768a8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_100 = (volatile uint32_t *)0x100;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_768D8
 * @note 指令数: 47
 * @note 类型: control_function
 */
void ida_768d8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_7693E
 * @note 指令数: 42
 * @note 类型: lookup_table
 */
void ida_7693e(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_76998
 * @note 指令数: 185
 * @note 类型: lookup_table
 */
void ida_76998(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_2D = (volatile uint32_t *)0x2D;
    volatile uint32_t *addr_42C80000 = (volatile uint32_t *)0x42C80000;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_76B34
 * @note 指令数: 25
 * @note 类型: lookup_table
 */
void ida_76b34(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_C0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_76B68
 * @note 指令数: 33
 * @note 类型: lookup_table
 */
void ida_76b68(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_76BAA
 * @note 指令数: 33
 * @note 类型: lookup_table
 */
void ida_76baa(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_76BEC
 * @note 指令数: 30
 * @note 类型: control_function
 */
void ida_76bec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_C0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_76C2E
 * @note 指令数: 157
 * @note 类型: lookup_table
 */
void ida_76c2e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_B0 = (volatile uint32_t *)0xB0;
    volatile uint32_t *addr_1A = (volatile uint32_t *)0x1A;
    volatile uint32_t *addr_A1 = (volatile uint32_t *)0xA1;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_76D64
 * @note 指令数: 200
 * @note 类型: lookup_table
 */
void ida_76d64(void)
{
    // 内存地址定义
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_76EF0
 * @note 指令数: 78
 * @note 类型: lookup_table
 */
void ida_76ef0(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003745 = (volatile uint32_t *)0x20003745;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_76F94
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint8_t ida_76f94(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003743 = (volatile uint32_t *)0x20003743;

    // 局部变量
    uint8_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_76FA8
 * @note 指令数: 169
 * @note 类型: lookup_table
 */
uint32_t ida_76fa8(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003745 = (volatile uint32_t *)0x20003745;
    volatile uint32_t *addr_20003614 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *addr_80124AC = (volatile uint32_t *)0x80124AC;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t result = 0;

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7711C
 * @note 指令数: 30
 * @note 类型: array_access
 */
void ida_7711c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;
    volatile uint32_t *addr_20003624 = (volatile uint32_t *)0x20003624;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_77178
 * @note 指令数: 136
 * @note 类型: array_access
 */
void ida_77178(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;
    volatile uint32_t *addr_800FAF0 = (volatile uint32_t *)0x800FAF0;
    volatile uint32_t *addr_C0 = (volatile uint32_t *)0xC0;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数据访问函数
 * @note 原函数: sub_772B0
 * @note 指令数: 28
 * @note 类型: data_access
 */
void ida_772b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_FFFF = (volatile uint32_t *)0xFFFF;

    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_772E8
 * @note 指令数: 29
 * @note 类型: array_access
 */
void ida_772e8(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003604 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_2000362C = (volatile uint32_t *)0x2000362C;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7732A
 * @note 指令数: 16
 * @note 类型: control_function
 */
void ida_7732a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003746 = (volatile uint32_t *)0x20003746;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_77350
 * @note 指令数: 9
 * @note 类型: simple_function
 */
void ida_77350(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003746 = (volatile uint32_t *)0x20003746;

    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_77362
 * @note 指令数: 4
 * @note 类型: simple_function
 */
uint32_t ida_77362(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003746 = (volatile uint32_t *)0x20003746;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7737C
 * @note 指令数: 67
 * @note 类型: array_access
 */
void ida_7737c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003746 = (volatile uint32_t *)0x20003746;
    volatile uint32_t *addr_2000374A = (volatile uint32_t *)0x2000374A;
    volatile uint32_t *addr_2000362C = (volatile uint32_t *)0x2000362C;
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_77416
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint8_t ida_77416(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint8_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_77430
 * @note 指令数: 96
 * @note 类型: array_access
 */
void ida_77430(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_2000358C = (volatile uint32_t *)0x2000358C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_8039010 = (volatile uint32_t *)0x8039010;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_77508
 * @note 指令数: 70
 * @note 类型: array_access
 */
uint32_t ida_77508(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_803F010 = (volatile uint32_t *)0x803F010;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;
    volatile uint32_t *addr_2000374A = (volatile uint32_t *)0x2000374A;
    volatile uint32_t *addr_20003614 = (volatile uint32_t *)0x20003614;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_775A0
 * @note 指令数: 57
 * @note 类型: array_access
 */
void ida_775a0(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003745 = (volatile uint32_t *)0x20003745;
    volatile uint32_t *addr_20003604 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *addr_20003749 = (volatile uint32_t *)0x20003749;
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_7762C
 * @note 指令数: 20
 * @note 类型: lookup_table
 */
void ida_7762c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_8011700 = (volatile uint32_t *)0x8011700;
    volatile uint32_t *addr_B8 = (volatile uint32_t *)0xB8;
    volatile uint32_t *addr_20003743 = (volatile uint32_t *)0x20003743;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_77688
 * @note 指令数: 157
 * @note 类型: array_access
 */
void ida_77688(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_3E8 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *addr_20003604 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *addr_20003749 = (volatile uint32_t *)0x20003749;
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_77834
 * @note 指令数: 8
 * @note 类型: computation
 */
uint32_t ida_77834(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_77844
 * @note 指令数: 7
 * @note 类型: simple_function
 */
uint32_t ida_77844(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_E000E180 = (volatile uint32_t *)0xE000E180;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_77852
 * @note 指令数: 27
 * @note 类型: control_function
 */
uint32_t ida_77852(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_778A4
 * @note 指令数: 981
 * @note 类型: array_access
 */
void ida_778a4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_70 = (volatile uint32_t *)0x70;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;
    volatile uint32_t *addr_20002E48 = (volatile uint32_t *)0x20002E48;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_780E4
 * @note 指令数: 73
 * @note 类型: array_access
 */
void ida_780e4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_803F010 = (volatile uint32_t *)0x803F010;
    volatile uint32_t *addr_100 = (volatile uint32_t *)0x100;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;
    volatile uint32_t *addr_2000374A = (volatile uint32_t *)0x2000374A;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_78198
 * @note 指令数: 29
 * @note 类型: array_access
 */
uint32_t ida_78198(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_20003741 = (volatile uint32_t *)0x20003741;
    volatile uint32_t *addr_200035EC = (volatile uint32_t *)0x200035EC;
    volatile uint32_t *addr_200035FC = (volatile uint32_t *)0x200035FC;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_781E4
 * @note 指令数: 49
 * @note 类型: lookup_table
 */
void ida_781e4(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_84 = (volatile uint32_t *)0x84;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20001FF8 = (volatile uint32_t *)0x20001FF8;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_7824C
 * @note 指令数: 30
 * @note 类型: lookup_table
 */
void ida_7824c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003739 = (volatile uint32_t *)0x20003739;
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;
    volatile uint32_t *addr_72 = (volatile uint32_t *)0x72;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_78298
 * @note 指令数: 46
 * @note 类型: lookup_table
 */
void ida_78298(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_84 = (volatile uint32_t *)0x84;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_20001FF8 = (volatile uint32_t *)0x20001FF8;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7832C
 * @note 指令数: 533
 * @note 类型: array_access
 */
void ida_7832c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003660 = (volatile uint32_t *)0x20003660;
    volatile uint32_t *addr_20003736 = (volatile uint32_t *)0x20003736;
    volatile uint32_t *addr_71 = (volatile uint32_t *)0x71;
    volatile uint32_t *addr_200036F0 = (volatile uint32_t *)0x200036F0;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7877C
 * @note 指令数: 23
 * @note 类型: array_access
 */
uint32_t ida_7877c(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200035EC = (volatile uint32_t *)0x200035EC;
    volatile uint32_t *addr_200035FC = (volatile uint32_t *)0x200035FC;
    volatile uint32_t *addr_3E8 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *addr_200035F4 = (volatile uint32_t *)0x200035F4;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_787E8
 * @note 指令数: 127
 * @note 类型: array_access
 */
void ida_787e8(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_23 = (volatile uint32_t *)0x23;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20003741 = (volatile uint32_t *)0x20003741;
    volatile uint32_t *addr_1A = (volatile uint32_t *)0x1A;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_788F0
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint8_t ida_788f0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003741 = (volatile uint32_t *)0x20003741;

    // 局部变量
    uint8_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_78930
 * @note 指令数: 10
 * @note 类型: simple_function
 */
uint32_t ida_78930(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_78944
 * @note 指令数: 30
 * @note 类型: control_function
 */
void ida_78944(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7897C
 * @note 指令数: 17
 * @note 类型: computation
 */
uint32_t ida_7897c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_7899E
 * @note 指令数: 1
 * @note 类型: simple_function
 */
void ida_7899e(void)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_789A0
 * @note 指令数: 15
 * @note 类型: array_access
 */
uint32_t ida_789a0(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200036EC = (volatile uint32_t *)0x200036EC;
    volatile uint32_t *addr_200035B4 = (volatile uint32_t *)0x200035B4;
    volatile uint32_t *addr_2000368C = (volatile uint32_t *)0x2000368C;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_789C2
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint32_t ida_789c2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003680 = (volatile uint32_t *)0x20003680;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_789C8
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint32_t ida_789c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003688 = (volatile uint32_t *)0x20003688;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_789CE
 * @note 指令数: 28
 * @note 类型: control_function
 */
void ida_789ce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003670 = (volatile uint32_t *)0x20003670;
    volatile uint32_t *addr_20003674 = (volatile uint32_t *)0x20003674;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

