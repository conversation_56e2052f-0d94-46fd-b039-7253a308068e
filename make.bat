@echo off
REM AT32F403AVG项目Make包装脚本
REM 使用MinGW64的mingw32-make编译项目

echo ========================================
echo AT32F403AVG 100%精确汇编转换项目
echo 使用MinGW64编译
echo ========================================
echo.

set MINGW_PATH=C:\mingw64\bin
set MAKE_EXE=%MINGW_PATH%\mingw32-make.exe

REM 检查mingw32-make是否存在
if not exist "%MAKE_EXE%" (
    echo ❌ 错误: 未找到mingw32-make.exe
    echo 请确保MinGW64已正确安装到: C:\mingw64
    pause
    exit /b 1
)

REM 临时添加MinGW64到PATH
set PATH=%MINGW_PATH%;%PATH%

echo 🔧 编译工具信息:
echo Make版本:
"%MAKE_EXE%" --version | findstr "GNU Make"
echo.
echo GCC版本:
"%MINGW_PATH%\gcc.exe" --version | findstr "gcc"
echo.

echo 🚀 开始编译...
echo 命令: "%MAKE_EXE%" %*
echo.

REM 执行make命令，传递所有参数
"%MAKE_EXE%" %*

set MAKE_RESULT=%errorlevel%

echo.
if %MAKE_RESULT% equ 0 (
    echo ✅ 编译成功完成！
) else (
    echo ❌ 编译失败，错误代码: %MAKE_RESULT%
)

echo.
echo ========================================
echo 编译完成
echo ========================================
