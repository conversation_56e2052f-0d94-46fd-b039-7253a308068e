// 精确转换批次 14 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49580
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_49580(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_495E8
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_495e8(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CMP     R4, #0
    // 比较操作
    // BNE     loc_495F4
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_49630
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49634
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_49634(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015EB4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000314;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_49EFC
    // 调用函数: sub_49EFC();
    // LDR     R1, =0x8015EB4
    // 内存加载操作
    // LDR     R2, =0x40021004
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // LSRS    R2, R2, #4
    // LSLS    R2, R2, #0x1C
    // LSRS    R2, R2, #0x1C
    // LDRB    R1, [R1,R2]
    // 内存加载操作
    // LSRS    R0, R1
    // LDR     R1, =0x20000314
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20000314
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // POP     {R1,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49654
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_49654(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015EB4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_49634
    // 调用函数: sub_49634();
    // LDR     R1, =0x8015EB4
    // 内存加载操作
    // LDR     R2, =0x40021004
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // LSRS    R2, R2, #8
    // LSLS    R2, R2, #0x1D
    // LSRS    R2, R2, #0x1D
    // LDRB    R1, [R1,R2]
    // 内存加载操作
    // LSRS    R0, R1
    // POP     {R1,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49678
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_49678(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R1, R0
    // LDR     R3, [R1,#4]
    // 内存加载操作
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ANDS    R2, R3
    // MOVS    R0, #1
    // R0 = 1;
    // LSLS    R3, R3, #0xC
    // LSRS    R3, R3, #0xC
    // STR     R3, [R1,#4]
    // 内存存储操作
    // BNE     loc_496A6
    // 条件跳转
    // LDR     R3, [R1]
    // 内存加载操作
    // CMP     R3, #0
    // 比较操作
    // BNE     loc_496A6
    // 条件跳转
    // B       loc_496B2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_496BC
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_496bc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R4, #0x200000
    // R4 = 0x200000;
    // LSLS    R5, R1, #1
    // CMN     R4, R5
    // 比较操作
    // BHI     loc_496E0
    // LSLS    R5, R3, #1
    // CMN     R4, R5
    // 比较操作
    // BHI     loc_496E0
    // MOVS    R4, R1
    // ORRS    R4, R3
    // LSLS    R4, R4, #1
    // ORRS    R4, R0
    // ORRS    R4, R2
    // BCS     loc_496E4
    // CMP     R1, R3
    // 比较操作
    // BNE     loc_496E0
    // 条件跳转
    // CMP     R0, R2
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_496EE
 * @note 指令数: 33, 标签数: 0
 */
void precise_func_496ee(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // MOVS    R6, R1
    // EORS    R6, R3
    // MOVS    R5, #0x80000000
    // R5 = 0x80000000;
    // ANDS    R6, R5
    // MOV     R12, R6
    // LSRS    R6, R5, #0xA
    // ADDS    R7, R3, R3
    // 算术运算
    // ADDS    R4, R1, R1
    // 算术运算
    // CMN     R4, R6
    // 比较操作
    // BCS     loc_4977E
    // CMN     R7, R6
    // 比较操作
    // BCS     loc_4978E
    // LSRS    R4, R4, #0x15
    // BEQ     loc_49796
    // 条件跳转
    // LSRS    R7, R7, #0x15
    // BEQ     loc_49784
    // 条件跳转
    // SUBS    R4, R4, R7
    // 算术运算
    // LSLS    R1, R1, #0xB
    // LSLS    R3, R3, #0xB
    // ORRS    R1, R5
    // ORRS    R3, R5
    // LSRS    R7, R1, #0xB
    // LSRS    R3, R3, #0xB
    // MOVS    R6, R0
    // SUBS    R6, R6, R2
    // 算术运算
    // SBCS    R7, R3
    // BCS     loc_49732
    // SUBS    R4, R4, #1
    // 算术运算
    // ADDS    R6, R6, R6
    // 算术运算
    // ADCS    R7, R7
    // ADDS    R6, R6, R2
    // 算术运算
    // ADCS    R7, R3
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_497B0
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_497b0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // LDRB    R0, [R4]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_49864
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49C84
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_49c84(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R0, =0x40022000
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // LSLS    R1, R1, #0x1F
    // LSRS    R1, R1, #0x1F
    // CMP     R1, R4
    // 比较操作
    // BCC     loc_49C9C
    // B       loc_49DBC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49EFC
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_49efc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40021004;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R7, #0
    // R7 = 0;
    // LDR     R0, =0x40021004
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R4, R0
    // MOVS    R0, #0xC
    // R0 = 0xC;
    // ANDS    R0, R4
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_49F22
    // 条件跳转
    // CMP     R0, #8
    // 比较操作
    // BEQ     loc_49F28
    // 条件跳转
    // CMP     R0, #0xC
    // 比较操作
    // BEQ     loc_49F8A
    // 条件跳转
    // B       loc_49F90
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49FC0
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_49fc0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3E8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // BL      sub_49634
    // 调用函数: sub_49634();
    // MOVS    R1, #0x3E8
    // R1 = 0x3E8;
    // BL      sub_4637C
    // 调用函数: sub_4637C();
    // BL      sub_46EB8
    // 调用函数: sub_46EB8();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, R4
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // BL      sub_46E96
    // 调用函数: sub_46E96();
    // MOVS    R0, #0
    // R0 = 0;
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49FE4
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_49fe4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077F4;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x200077F4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49FF0
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_49ff0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000E180;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1B;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #1
    // R1 = 1;
    // LSLS    R2, R0, #0x1B
    // LSRS    R2, R2, #0x1B
    // LSLS    R1, R2
    // LDR     R2, =0xE000E180
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49FFE
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_49ffe(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078C0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_4B3B4
    // 调用函数: sub_4B3B4();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4A00E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078C0
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A016
 * @note 指令数: 8, 标签数: 0
 */
uint32_t precise_func_4a016(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // LDRB    R0, [R2]
    // 内存加载操作
    // MOVS    R3, #0x80
    // R3 = 0x80;
    // ORRS    R3, R0
    // STRB    R3, [R2]
    // 内存存储操作
    // STRB    R1, [R2,#1]
    // 内存存储操作
    // MOVS    R0, #2
    // R0 = 2;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A026
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_4a026(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R2,R4-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R7, #0
    // R7 = 0;
    // MOVS    R5, #0
    // R5 = 0;
    // LDRB    R0, [R6,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R4, R0
    // LDRB    R0, [R6,#2]
    // 内存加载操作
    // ADDS    R4, R4, R0
    // 算术运算
    // LDRB    R0, [R6,#3]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     loc_4A044
    // 条件跳转
    // LDRB    R0, [R6,#3]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4A04A
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A112
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_4a112(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_20= -0x20
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A2BC
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4a2bc(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A3A6
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_4a3a6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFA00;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x800DC3E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8016088;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R5, R0
    // MOVS    R6, R1
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R4, R0
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // ADDS    R4, R4, R0
    // 算术运算
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R0, #0xFA00
    // R0 = 0xFA00;
    // CMP     R4, R0
    // 比较操作
    // BLT     loc_4A3E8
    // 条件跳转
    // MOVS    R0, #0x600
    // R0 = 0x600;
    // ADDS    R4, R4, R0
    // 算术运算
    // MOVS    R0, R4
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R5,#1]
    // 内存存储操作
    // MOVS    R0, R4
    // STRB    R0, [R5,#2]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // LDR     R3, =0x8016088
    // 内存加载操作
    // LDR     R2, =0x800DC3E
    // 内存加载操作
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R5
    // BL      sub_4A2BC
    // 调用函数: sub_4A2BC();
    // B       locret_4A428
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A42A
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_4a42a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015F48;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015F54;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x260;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x15A0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R5, R0
    // MOVS    R6, R1
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R4, R0
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // ADDS    R4, R4, R0
    // 算术运算
    // UXTH    R4, R4
    // 数据扩展操作
    // LDR     R0, =(dword_E800+0x260)
    // 内存加载操作
    // CMP     R4, R0
    // 比较操作
    // BLT     loc_4A46A
    // 条件跳转
    // MOVS    R0, #0x15A0
    // R0 = 0x15A0;
    // ADDS    R4, R4, R0
    // 算术运算
    // MOVS    R0, R4
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R5,#1]
    // 内存存储操作
    // MOVS    R0, R4
    // STRB    R0, [R5,#2]
    // 内存存储操作
    // MOVS    R0, #5
    // R0 = 5;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // LDR     R3, =0x8015F54
    // 内存加载操作
    // LDR     R2, =0x8015F48
    // 内存加载操作
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R5
    // BL      sub_4A2BC
    // 调用函数: sub_4A2BC();
    // B       locret_4A47C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A47E
 * @note 指令数: 25, 标签数: 0
 */
void precise_func_4a47e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R1-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R5, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R5, R5, R0
    // 算术运算
    // LDRB    R0, [R4,#3]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R7, R0
    // LDRB    R0, [R4,#4]
    // 内存加载操作
    // ADDS    R7, R7, R0
    // 算术运算
    // MOV     R0, SP
    // LDRH    R0, [R0,#0x20+var_18]
    // 内存加载操作
    // UXTH    R5, R5
    // 数据扩展操作
    // UXTH    R7, R7
    // 数据扩展操作
    // ADDS    R1, R5, R7
    // 算术运算
    // CMP     R0, R1
    // 比较操作
    // BGE     loc_4A4AE
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R4
    // BL      sub_4A016
    // 调用函数: sub_4A016();
    // B       locret_4A526
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A528
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_4a528(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFA00;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80154FC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x21;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R6, R1
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R4, R0
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // ADDS    R4, R4, R0
    // 算术运算
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R0, #0xFA00
    // R0 = 0xFA00;
    // CMP     R4, R0
    // 比较操作
    // BLT     loc_4A564
    // 条件跳转
    // MOVS    R0, #0x600
    // R0 = 0x600;
    // ADDS    R4, R4, R0
    // 算术运算
    // MOVS    R0, R4
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R5,#1]
    // 内存存储操作
    // MOVS    R0, R4
    // STRB    R0, [R5,#2]
    // 内存存储操作
    // MOVS    R3, #0x21 ; '!'
    // R3 = 0x21;
    // LDR     R2, =0x80154FC
    // 内存加载操作
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R5
    // BL      sub_4A47E
    // 调用函数: sub_4A47E();
    // B       locret_4A59E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A5A0
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_4a5a0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801466C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xB5;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xFA00;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R7, R1
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R4, R0
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // ADDS    R4, R4, R0
    // 算术运算
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R0, #0xFA00
    // R0 = 0xFA00;
    // CMP     R4, R0
    // 比较操作
    // BLT     loc_4A5DC
    // 条件跳转
    // MOVS    R0, #0x600
    // R0 = 0x600;
    // ADDS    R4, R4, R0
    // 算术运算
    // MOVS    R0, R4
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R5,#1]
    // 内存存储操作
    // MOVS    R0, R4
    // STRB    R0, [R5,#2]
    // 内存存储操作
    // MOVS    R3, #0xB5
    // R3 = 0xB5;
    // LDR     R2, =0x801466C
    // 内存加载操作
    // MOVS    R1, R7
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R5
    // BL      sub_4A47E
    // 调用函数: sub_4A47E();
    // B       locret_4A658
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A688
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_4a688(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFA00;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xB6;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R7, R2
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R5, R0
    // LDRB    R0, [R4,#2]
    // 内存加载操作
    // ADDS    R5, R5, R0
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // MOVS    R0, #0xFA00
    // R0 = 0xFA00;
    // CMP     R5, R0
    // 比较操作
    // BLT     loc_4A70E
    // 条件跳转
    // MOVS    R0, #0x600
    // R0 = 0x600;
    // ADDS    R5, R5, R0
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #0xB6
    // 比较操作
    // BLT     loc_4A6BA
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R4
    // BL      sub_4A016
    // 调用函数: sub_4A016();
    // B       locret_4A778
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A7A0
 * @note 指令数: 30, 标签数: 0
 */
void precise_func_4a7a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xB6;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x600;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFA00;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH    {R0-R2,R4-R7,LR}
    // 栈操作
    // MOVS    R7, R0
    // LDRB    R0, [R7,#1]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R4, R0
    // LDRB    R0, [R7,#2]
    // 内存加载操作
    // ADDS    R4, R4, R0
    // 算术运算
    // LDRB    R0, [R7,#3]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // MOVS    R6, R0
    // LDRB    R0, [R7,#4]
    // 内存加载操作
    // ADDS    R6, R6, R0
    // 算术运算
    // MOV     R0, SP
    // STRH    R4, [R0,#0x20+var_20]
    // 内存存储操作
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R0, #0xFA00
    // R0 = 0xFA00;
    // CMP     R4, R0
    // 比较操作
    // BLT     loc_4A886
    // 条件跳转
    // MOVS    R0, #0x600
    // R0 = 0x600;
    // ADDS    R4, R4, R0
    // 算术运算
    // UXTH    R4, R4
    // 数据扩展操作
    // UXTH    R6, R6
    // 数据扩展操作
    // ADDS    R0, R4, R6
    // 算术运算
    // CMP     R0, #0xB6
    // 比较操作
    // BLT     loc_4A7E0
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, R7
    // BL      sub_4A016
    // 调用函数: sub_4A016();
    // B       locret_4A964
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4A96C
 * @note 指令数: 61, 标签数: 0
 */
void precise_func_4a96c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000031C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x82;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x3EB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_D8= -0xD8
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0xE8
    // 算术运算
    // MOVS    R5, R0
    // MOVS    R6, R1
    // MOVS    R4, R5
    // ADDS    R4, R4, #1
    // 算术运算
    // LDR     R1, =0x8015E24
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #0x40 ; '@'
    // 算术运算
    // LDR     R0, =0x2000031C
    // 内存加载操作
    // LDRH    R2, [R0]
    // 内存加载操作
    // ADR     R1, dword_4AA28
    // MOV     R0, SP
    // BL      sub_4C0E0
    // 调用函数: sub_4C0E0();
    // MOV     R1, SP
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #8
    // 算术运算
    // ADD     R0, SP, #0xF8+var_D8
    // 算术运算
    // BL      sub_4BFCE
    // 调用函数: sub_4BFCE();
    // ADD     R1, SP, #0xF8+var_D8
    // 算术运算
    // ADDS    R1, #0x82
    // 算术运算
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #8
    // 算术运算
    // LDR     R1, =0x8015F3C
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #0x20 ; ' '
    // 算术运算
    // LDR     R1, =0x8016020
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #0x20 ; ' '
    // 算术运算
    // LDR     R1, =0x20000144
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #0x20 ; ' '
    // 算术运算
    // MOV     R1, SP
    // MOVS    R0, #0x3E8
    // R0 = 0x3E8;
    // BL      sub_4C1CE
    // 调用函数: sub_4C1CE();
    // MOV     R1, SP
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #8
    // 算术运算
    // MOV     R1, SP
    // LDR     R0, =0x3EB
    // 内存加载操作
    // BL      sub_4C1CE
    // 调用函数: sub_4C1CE();
    // MOV     R1, SP
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #8
    // 算术运算
    // ADD     R1, SP, #0xF8+var_D8
    // 算术运算
    // ADDS    R1, #0x41 ; 'A'
    // 算术运算
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // ADDS    R4, #0x10
    // 算术运算
    // MOVS    R0, #0xD1
    // R0 = 0xD1;
    // ADD     SP, SP, #0xE8
    // 算术运算
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4AA3C
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_4aa3c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xE8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_E0= -0xE0
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0xE8
    // 算术运算
    // MOVS    R5, R0
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // CMP     R0, #0xE
    // 比较操作
    // BNE     loc_4AA60
    // 条件跳转
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_4AA74
    // 条件跳转
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_4AA74
    // 条件跳转
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BEQ     loc_4AA74
    // 条件跳转
    // LDRB    R0, [R5,#2]
    // 内存加载操作
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_4AA74
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4AD54
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_4ad54(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_4AD80
    // 条件跳转
    // LDRB    R1, [R4,#3]
    // 内存加载操作
    // MOVS    R0, R4
    // ADDS    R0, R0, #4
    // 算术运算
    // BL      sub_4BA34
    // 调用函数: sub_4BA34();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4AD74
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#3]
    // 内存存储操作
    // B       loc_4AD78
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4ADBC
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4adbc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R1, [R4,#1]
    // 内存加载操作
    // MOVS    R0, R4
    // ADDS    R0, R0, #2
    // 算术运算
    // BL      sub_4BA34
    // 调用函数: sub_4BA34();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4ADD6
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // B       loc_4ADDA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4ADDE
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4adde(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDRB    R1, [R4,#1]
    // 内存加载操作
    // MOVS    R0, R4
    // ADDS    R0, R0, #2
    // 算术运算
    // BL      sub_4BB86
    // 调用函数: sub_4BB86();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4ADF8
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // B       loc_4ADFC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4AE00
 * @note 指令数: 34, 标签数: 0
 */
void precise_func_4ae00(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20001F00;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // CPSID   I
    // MOVS    R0, #0x1B
    // R0 = 0x1B;
    // BL      sub_49FF0
    // 调用函数: sub_49FF0();
    // MOVS    R0, #0x1C
    // R0 = 0x1C;
    // BL      sub_49FF0
    // 调用函数: sub_49FF0();
    // MOVS    R0, #0x1D
    // R0 = 0x1D;
    // BL      sub_49FF0
    // 调用函数: sub_49FF0();
    // MOVS    R0, #0xC
    // R0 = 0xC;
    // BL      sub_49FF0
    // 调用函数: sub_49FF0();
    // MOVS    R0, #0x10
    // R0 = 0x10;
    // BL      sub_49FF0
    // 调用函数: sub_49FF0();
    // MOVS    R0, #0x12
    // R0 = 0x12;
    // BL      sub_49FF0
    // 调用函数: sub_49FF0();
    // MOVS    R5, #4
    // R5 = 4;
    // LDR     R6, =0x8016028
    // 内存加载操作
    // LDR     R7, =0x20001F00
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x18+var_18]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4AE52
    // 条件跳转
    // MOVS    R5, #4
    // R5 = 4;
    // LDR     R6, =0x8016030
    // 内存加载操作
    // LDR     R7, =0x20001F50
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4AE5E
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_4ae5e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000787C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x21;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_21= -0x21
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x10
    // 算术运算
    // MOVS    R5, R0
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_4AED8
    // 条件跳转
    // LDR     R0, =0x2000787C
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // STRB    R0, [R5,#2]
    // 内存存储操作
    // MOV     R0, SP
    // BL      sub_4B42C
    // 调用函数: sub_4B42C();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4AF8A
 * @note 指令数: 34, 标签数: 0
 */
void precise_func_4af8a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x45;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x6E;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x70;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2B;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x41;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDRB    R0, [R4]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_4AFEE
    // 条件跳转
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_4AFFA
    // 条件跳转
    // CMP     R0, #3
    // 比较操作
    // BEQ     loc_4B032
    // 条件跳转
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_4B006
    // 条件跳转
    // CMP     R0, #5
    // 比较操作
    // BEQ     loc_4AFCE
    // 条件跳转
    // CMP     R0, #6
    // 比较操作
    // BEQ     loc_4B012
    // 条件跳转
    // CMP     R0, #0xF
    // 比较操作
    // BEQ     loc_4AFDE
    // 条件跳转
    // CMP     R0, #0x10
    // 比较操作
    // BEQ     loc_4B022
    // 条件跳转
    // CMP     R0, #0x2B ; '+'
    // 比较操作
    // BEQ     loc_4B03E
    // 条件跳转
    // CMP     R0, #0x41 ; 'A'
    // 比较操作
    // BEQ     loc_4B07A
    // 条件跳转
    // CMP     R0, #0x43 ; 'C'
    // 比较操作
    // BEQ     loc_4B056
    // 条件跳转
    // CMP     R0, #0x45 ; 'E'
    // 比较操作
    // BEQ     loc_4B062
    // 条件跳转
    // CMP     R0, #0x6E ; 'n'
    // 比较操作
    // BEQ     loc_4B04A
    // 条件跳转
    // CMP     R0, #0x70 ; 'p'
    // 比较操作
    // BEQ     loc_4B06E
    // 条件跳转
    // B       loc_4B086
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B0AC
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_4b0ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80017FC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xAA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000031E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // LDR     R0, =0x80017FC
    // 内存加载操作
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // LDR     R0, [SP,#0x18+var_18]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000031E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000031E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20000322
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20000322
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xAA
    // 比较操作
    // BEQ     loc_4B0D0
    // 条件跳转
    // MOVS    R0, #0xFF
    // R0 = 0xFF;
    // LDR     R1, =0x20000322
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B1A4
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4b1a4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007896;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_4DDF8
    // 调用函数: sub_4DDF8();
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_4B1BE
    // 条件跳转
    // LDR     R0, =0x20007896
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0xFE
    // R1 = 0xFE;
    // ANDS    R1, R0
    // LDR     R0, =0x20007896
    // 内存加载操作
    // STRB    R1, [R0]
    // 内存存储操作
    // B       locret_4B3A0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B3B4
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_4b3b4(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_4DDF8
    // 调用函数: sub_4DDF8();
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_4B3CA
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_4DDF8
    // 调用函数: sub_4DDF8();
    // CMP     R0, #2
    // 比较操作
    // BNE     loc_4B3CE
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B3D4
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_4b3d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8002014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0x10
    // R5 = 0x10;
    // LDR     R6, =0x8002014
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R4
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B3F8
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_4b3f8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000C8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x7C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0x7C ; '|'
    // R2 = 0x7C;
    // MOVS    R1, R4
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // BL      sub_4DC70
    // 调用函数: sub_4DC70();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_4B418
    // 条件跳转
    // MOVS    R5, #0x7C ; '|'
    // R5 = 0x7C;
    // LDR     R6, =0x200000C8
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R4
    // BL      sub_47F58
    // 调用函数: sub_47F58();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B41A
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4b41a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8001804;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, =0x8001800
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // STR     R2, [R0]
    // 内存存储操作
    // LDR     R2, =0x8001804
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B42C
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_4b42c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20006766;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0xA
    // R5 = 0xA;
    // LDR     R6, =0x20006766
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R4
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B44C
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_4b44c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x52;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20006714;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // LDR     R0, =0x20006714
    // 内存加载操作
    // MOVS    R4, R0
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // MOVS    R5, R0
    // MOVS    R0, R4
    // ADDS    R0, #0x52 ; 'R'
    // 算术运算
    // ADDS    R0, R5, R0
    // 算术运算
    // SUBS    R5, R0, R4
    // 算术运算
    // MOVS    R6, #0xA
    // R6 = 0xA;
    // LDR     R0, [SP,#0x20+var_18]
    // 内存加载操作
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R7, R4
    // ADDS    R7, #0x52 ; 'R'
    // 算术运算
    // MOVS    R2, R6
    // LDR     R1, [SP,#0x20+var_20]
    // 内存加载操作
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // MOVS    R2, #0xA
    // R2 = 0xA;
    // MOVS    R1, R4
    // ADDS    R1, #0x52 ; 'R'
    // 算术运算
    // MOVS    R0, R5
    // UXTH    R0, R0
    // 数据扩展操作
    // BL      sub_4DD4C
    // 调用函数: sub_4DD4C();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4B48A
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_4B49E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B4A0
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_4b4a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006770;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0xA
    // R5 = 0xA;
    // LDR     R6, =0x20006770
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R4
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B4B4
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_4b4b4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20006714;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // LDR     R0, =0x20006714
    // 内存加载操作
    // MOVS    R4, R0
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // MOVS    R5, R0
    // MOVS    R0, R4
    // ADDS    R0, #0x5C ; '\'
    // 算术运算
    // ADDS    R0, R5, R0
    // 算术运算
    // SUBS    R5, R0, R4
    // 算术运算
    // MOVS    R6, #0xA
    // R6 = 0xA;
    // LDR     R0, [SP,#0x20+var_18]
    // 内存加载操作
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R7, R4
    // ADDS    R7, #0x5C ; '\'
    // 算术运算
    // MOVS    R2, R6
    // LDR     R1, [SP,#0x20+var_20]
    // 内存加载操作
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // MOVS    R2, #0xA
    // R2 = 0xA;
    // MOVS    R1, R4
    // ADDS    R1, #0x5C ; '\'
    // 算术运算
    // MOVS    R0, R5
    // UXTH    R0, R0
    // 数据扩展操作
    // BL      sub_4DD4C
    // 调用函数: sub_4DD4C();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4B4F2
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_4B506
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B524
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_4b524(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x60;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x7300;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xB4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_60= -0x60
    // var_5C= -0x5C
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x4C
    // 算术运算
    // MOVS    R0, #0xB4
    // R0 = 0xB4;
    // MOV     R1, SP
    // STRH    R0, [R1,#0x60+var_60]
    // 内存存储操作
    // MOVS    R0, #0x7300
    // R0 = 0x7300;
    // MOVS    R7, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B5BC
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_4b5bc(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x60;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x68;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x66;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_68= -0x68
    // var_66= -0x66
    // var_64= -0x64
    // var_60= -0x60
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B6D0
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_4b6d0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xB5;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x58;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x56;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_58= -0x58
    // var_56= -0x56
    // var_54= -0x54
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x44
    // 算术运算
    // MOVS    R6, R0
    // MOVS    R0, #0xB5
    // R0 = 0xB5;
    // MOV     R1, SP
    // STRH    R0, [R1,#0x58+var_56]
    // 内存存储操作
    // LDRH    R0, [R6]
    // 内存加载操作
    // MOV     R1, SP
    // LDRH    R1, [R1,#0x58+var_56]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_4B6EA
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_4B7A2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B7A8
 * @note 指令数: 30, 标签数: 0
 */
void precise_func_4b7a8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x168;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8015EF4;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4,R5,LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_4B804
    // 条件跳转
    // MOVS    R0, #0x168
    // R0 = 0x168;
    // MOVS    R5, R0
    // MOVS    R2, #0xA
    // R2 = 0xA;
    // ADD     R1, SP, #0x20+var_1C
    // 算术运算
    // MOVS    R0, R5
    // MOVS    R3, #0x7300
    // R3 = 0x7300;
    // ADDS    R0, R0, R3
    // 算术运算
    // UXTH    R0, R0
    // 数据扩展操作
    // BL      sub_4DC70
    // 调用函数: sub_4DC70();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4B804
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // ADD     R1, SP, #0x20+var_1C
    // 算术运算
    // STRB    R0, [R1,#0xA]
    // 内存存储操作
    // LDR     R1, =0x8015EF4
    // 内存加载操作
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // BL      sub_48774
    // 调用函数: sub_48774();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4B804
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRH    R0, [R1,#0x20+var_20]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B80C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_4b80c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x58;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x41;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_58= -0x58
    // var_56= -0x56
    // var_54= -0x54
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x44
    // 算术运算
    // MOVS    R6, R0
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_4DDF0
    // 调用函数: sub_4DDF0();
    // MOV     R1, SP
    // STRB    R0, [R1,#0x58+var_58]
    // 内存存储操作
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x58+var_58]
    // 内存加载操作
    // CMP     R0, #0x41 ; 'A'
    // 比较操作
    // BLT     loc_4B82A
    // 条件跳转
    // MOVS    R0, #0x40 ; '@'
    // R0 = 0x40;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x58+var_58]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B8F0
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_4b8f0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x41;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x44
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_4DDF0
    // 调用函数: sub_4DDF0();
    // MOVS    R6, R0
    // UXTB    R6, R6
    // 数据扩展操作
    // CMP     R6, #0x41 ; 'A'
    // 比较操作
    // BLT     loc_4B908
    // 条件跳转
    // MOVS    R0, #0x40 ; '@'
    // R0 = 0x40;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B992
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_4b992(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

