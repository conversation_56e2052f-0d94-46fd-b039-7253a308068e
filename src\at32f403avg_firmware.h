/**
 * @file at32f403avg_firmware.h
 * @brief AT32F403AVG固件头文件 - 从汇编代码转换而来
 * @date 2024
 *
 * 完整转换AT32F403AVG-FLASH-J201.asm到C语言
 * 保持原始内存布局和功能
 */

#ifndef AT32F403AVG_FIRMWARE_H
#define AT32F403AVG_FIRMWARE_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdarg.h>
#include <stddef.h>

// =============================================================================
// 内存映射定义 (从汇编代码分析得出)
// =============================================================================

// 内存区域
#define FLASH_BASE              0x08000000
#define SRAM_BASE               0x20000000
#define BOOT_BASE               0x08000000  // 引导加载程序
#define APP_BASE                0x08002000  // 应用程序
#define MAC_ADDR_BASE           0x08001810  // MAC地址位置

// 栈指针初始值
#define INITIAL_SP              0x20000618

// =============================================================================
// 硬件寄存器定义 (从汇编代码dword_定义中提取)
// =============================================================================

// NVIC和系统控制
#define NVIC_ISER_BASE          0xE000E400
#define NVIC_ICER_BASE          0xE000E180
#define SCB_AIRCR               0xE000ED0C
#define SYSTICK_CTRL            0xE000E010
#define SYSTICK_LOAD            0xE000E014
#define SYSTICK_VAL             0xE000E018

// RCC (复位和时钟控制)
#define RCC_BASE                0x40021000
#define RCC_CR                  (RCC_BASE + 0x00)
#define RCC_CFGR                (RCC_BASE + 0x04)
#define RCC_CIR                 (RCC_BASE + 0x08)
#define RCC_APB2RSTR            (RCC_BASE + 0x0C)
#define RCC_APB1RSTR            (RCC_BASE + 0x10)
#define RCC_AHBENR              (RCC_BASE + 0x14)
#define RCC_APB2ENR             (RCC_BASE + 0x18)
#define RCC_APB1ENR             (RCC_BASE + 0x1C)

// GPIO端口
#define GPIOA_BASE              0x40010800
#define GPIOB_BASE              0x40010C00
#define GPIOC_BASE              0x40011000
#define GPIOD_BASE              0x40011400
#define GPIOE_BASE              0x40011800

// GPIO寄存器偏移
#define GPIO_CRL_OFFSET         0x00
#define GPIO_CRH_OFFSET         0x04
#define GPIO_IDR_OFFSET         0x08
#define GPIO_ODR_OFFSET         0x0C
#define GPIO_BSRR_OFFSET        0x10
#define GPIO_BRR_OFFSET         0x14
#define GPIO_LCKR_OFFSET        0x18

// 串口
#define USART1_BASE             0x40013800
#define USART2_BASE             0x40004400
#define USART3_BASE             0x40004800

// 串口寄存器偏移
#define USART_SR_OFFSET         0x00
#define USART_DR_OFFSET         0x04
#define USART_BRR_OFFSET        0x08
#define USART_CR1_OFFSET        0x0C
#define USART_CR2_OFFSET        0x10
#define USART_CR3_OFFSET        0x14
#define USART_GTPR_OFFSET       0x18

// 定时器
#define TIM1_BASE               0x40012C00
#define TIM2_BASE               0x40000000
#define TIM3_BASE               0x40000400
#define TIM4_BASE               0x40000800

// I2C
#define I2C1_BASE               0x40005400
#define I2C2_BASE               0x40005800

// SPI
#define SPI1_BASE               0x40013000
#define SPI2_BASE               0x40003800

// =============================================================================
// 全局变量地址 (从汇编代码数据段分析得出)
// =============================================================================

// 系统定时变量
#define SYSTEM_TICK_ADDR        0x2000000A  // uint16_t
#define SYSTEM_TIME_MS_ADDR     0x20000004  // uint32_t
#define DELAY_COUNTER_ADDR      0x2000000C  // uint16_t

// 通信变量
#define UART_MODE_FLAG_ADDR     0x20000011  // uint8_t
#define BUFFER_INDEX_ADDR       0x20000008  // uint16_t
#define CRC_VALUE_ADDR          0x2000000E  // uint16_t
#define TEMP_BUFFER_ADDR        0x20000010  // uint8_t[16]

// 缓冲区指针
#define RX_BUFFER_ADDR          0x20001000  // 主接收缓冲区
#define TX_BUFFER_ADDR          0x20001C00  // 主发送缓冲区
#define WORK_BUFFER_ADDR        0x20001F00  // 工作缓冲区

// =============================================================================
// 常量和字符串 (从汇编代码DCB段提取)
// =============================================================================

// 版本信息
#define BOOTLOADER_ID           "KXM-16P BL"
#define BUILD_DATE              "Jun  2 2022"
#define BUILD_TIME              "16:16:17"
#define SIGNATURE               0xAA55AA55

// 协议常量
#define BOOT_COMMAND            "bOoT"
#define ECHO_COMMAND            "EcHo"
#define G0B1_COMMAND            "G0B1"

// 缓冲区大小
#define RX_BUFFER_SIZE          0x0C00
#define TX_BUFFER_SIZE          0x0300
#define TEMP_BUFFER_SIZE        16

// RTC相关常量
#define RTC_INT_SECOND                  0x01
#define RTC_INT_ALARM                   0x02
#define RTC_INT_OVERFLOW                0x04
#define RTC_STATUS_ENABLED              0x01
#define RTC_STATUS_LSE_READY            0x02
#define RTC_STATUS_SYNCHRONIZED         0x04
#define RTC_STATUS_CONFIG_MODE          0x08

// 配置管理常量
#define MAX_CONFIG_PARAMETERS           64
#define MAX_PARAM_SIZE                  64
#define CONFIG_MAGIC                    0x43464700  // "CFG\0"
#define CONFIG_VERSION                  0x0100      // v1.0
#define CONFIG_MAX_SIZE                 (8 * 1024)  // 8KB
#define CONFIG_FLASH_BASE               0x0807E000  // Flash最后8KB
#define CONFIG_BACKUP_BASE              0x0807C000  // 备份区域
#define CONFIG_AUTO_SAVE_INTERVAL       60000       // 60秒

// 配置参数ID定义
#define CONFIG_PARAM_DEVICE_ID          0
#define CONFIG_PARAM_IP_ADDRESS         1
#define CONFIG_PARAM_SUBNET_MASK        2
#define CONFIG_PARAM_GATEWAY            3
#define CONFIG_PARAM_HTTP_PORT          4
#define CONFIG_PARAM_DHCP_ENABLE        5
#define CONFIG_PARAM_DEVICE_NAME        6
#define CONFIG_PARAM_FIRMWARE_VERSION   7

// 配置状态定义
#define CONFIG_STATUS_LOADED            0x01
#define CONFIG_STATUS_MODIFIED          0x02
#define CONFIG_STATUS_SAVED             0x04
#define CONFIG_STATUS_DEFAULT           0x08

// 配置错误代码
#define CONFIG_SUCCESS                  0
#define CONFIG_ERROR_INVALID_PARAM      1
#define CONFIG_ERROR_INVALID_ID         2
#define CONFIG_ERROR_SIZE_EXCEEDED      3
#define CONFIG_ERROR_INVALID_SIZE       4
#define CONFIG_ERROR_INVALID_VALUE      5
#define CONFIG_ERROR_CACHE_INVALID      6
#define CONFIG_ERROR_FLASH_ERASE        7
#define CONFIG_ERROR_FLASH_WRITE        8
#define CONFIG_ERROR_CHECKSUM_MISMATCH  9
#define CONFIG_ERROR_BACKUP_FAILED      10
#define CONFIG_ERROR_BACKUP_INVALID     11

// 默认配置值
#define DEFAULT_DEVICE_ID               0x12345678
#define DEFAULT_HTTP_PORT               80

// 调试相关常量
#define DEBUG_BUFFER_SIZE               256
#define MAX_ERROR_LOGS                  16
#define PERFORMANCE_UPDATE_INTERVAL     1000  // 1秒

// 调试级别定义
#define DEBUG_LEVEL_ERROR               0
#define DEBUG_LEVEL_WARNING             1
#define DEBUG_LEVEL_INFO                2
#define DEBUG_LEVEL_DEBUG               3

// 诊断组件定义
#define DIAG_COMPONENT_CPU              0
#define DIAG_COMPONENT_MEMORY           1
#define DIAG_COMPONENT_FLASH            2
#define DIAG_COMPONENT_UART             3
#define DIAG_COMPONENT_TIMER            4
#define DIAG_COMPONENT_GPIO             5
#define DIAG_COMPONENT_ADC              6
#define DIAG_COMPONENT_RTC              7

// 诊断结果定义
#define DIAG_RESULT_PASS                0
#define DIAG_RESULT_FAIL                1
#define DIAG_RESULT_WARNING             2
#define DIAG_RESULT_UNSUPPORTED         3

// 系统阈值定义
#define MIN_CPU_FREQUENCY               8000000   // 8MHz
#define MAX_CPU_FREQUENCY               120000000 // 120MHz
#define CPU_TEMP_WARNING_THRESHOLD      70        // 70°C
#define FLASH_USAGE_WARNING_THRESHOLD   90        // 90%

// I2C/SPI相关常量
#define MAX_I2C_CHANNELS                2
#define MAX_SPI_CHANNELS                3
#define MAX_GPIO_PORTS                  7
#define I2C_TIMEOUT_VALUE               50000
#define SPI_TIMEOUT_VALUE               50000
#define I2C1_BASE                       0x40005400
#define I2C2_BASE                       0x40005800
#define SPI1_BASE                       0x40013000
#define SPI2_BASE                       0x40003800
#define SPI3_BASE                       0x40003C00

// I2C状态定义
#define I2C_STATUS_READY                0
#define I2C_STATUS_START                1
#define I2C_STATUS_ADDRESS_SENT         2
#define I2C_STATUS_TIMEOUT              3

// SPI状态定义
#define SPI_STATUS_READY                0
#define SPI_STATUS_BUSY                 1
#define SPI_STATUS_TIMEOUT              2

// 格式化输出常量
#define FORMAT_BUFFER_SIZE              512
#define FLAG_LEFT_ALIGN                 0x01
#define FLAG_SHOW_SIGN                  0x02
#define FLAG_SPACE_SIGN                 0x04
#define FLAG_ALTERNATE                  0x08
#define FLAG_ZERO_PAD                   0x10

// 长度修饰符定义
#define LENGTH_HH                       1
#define LENGTH_H                        2
#define LENGTH_L                        3
#define LENGTH_LL                       4
#define LENGTH_Z                        5
#define LENGTH_T                        6

// 系统启动常量
#define HSE_STARTUP_TIMEOUT             50000
#define PLL_STARTUP_TIMEOUT             50000
#define CLOCK_SWITCH_TIMEOUT            50000
#define IWDG_TIMEOUT_MS                 5000
#define LOW_POWER_CHECK_INTERVAL        1000

// 系统标志定义
#define SYSTEM_FLAG_CORE_INIT           0x00000001
#define SYSTEM_FLAG_STARTUP_COMPLETE    0x00000002
#define SYSTEM_FLAG_TASKS_STARTED       0x00000004
#define SYSTEM_FLAG_LOW_POWER_ENABLE    0x00000008

// 系统错误标志定义
#define SYSTEM_ERROR_CLOCK_INIT         0x00000001
#define SYSTEM_ERROR_MPU_INIT           0x00000002
#define SYSTEM_ERROR_HARD_FAULT         0x00000004
#define SYSTEM_ERROR_MEM_MANAGE         0x00000008
#define SYSTEM_ERROR_BUS_FAULT          0x00000010
#define SYSTEM_ERROR_USAGE_FAULT        0x00000020
#define SYSTEM_ERROR_RECOVERABLE_MASK   0x00000003

// 低功耗模式定义
#define LOW_POWER_SLEEP                 0
#define LOW_POWER_STOP                  1
#define LOW_POWER_STANDBY               2

// 错误代码定义
#define ERROR_CODE_TEMPERATURE          0x0001
#define ERROR_CODE_VOLTAGE              0x0002
#define ERROR_CODE_COMMUNICATION_TIMEOUT 0x0003
#define ERROR_CODE_SELF_TEST            0x0004
#define ERROR_CODE_HARD_FAULT           0x0005
#define ERROR_CODE_CFSR                 0x0006
#define ERROR_CODE_BFAR                 0x0007
#define ERROR_CODE_MMFAR                0x0008
#define ERROR_CODE_MEM_MANAGE           0x0009
#define ERROR_CODE_BUS_FAULT            0x000A
#define ERROR_CODE_USAGE_FAULT          0x000B
#define ERROR_CODE_SYSTEM_ERROR         0x000C

// =============================================================================
// 函数类型定义
// =============================================================================

// 中断处理函数类型
typedef void (*interrupt_handler_t)(void);

// 应用程序入口点类型
typedef void (*app_entry_t)(void);

// =============================================================================
// 结构体定义
// =============================================================================

// 中断向量表结构 (从汇编代码中断向量表转换)
typedef struct {
    uint32_t initial_sp;
    interrupt_handler_t reset_handler;
    interrupt_handler_t nmi_handler;
    interrupt_handler_t hardfault_handler;
    interrupt_handler_t memmanage_handler;
    interrupt_handler_t busfault_handler;
    interrupt_handler_t usagefault_handler;
    uint32_t reserved1[4];
    interrupt_handler_t svc_handler;
    interrupt_handler_t debugmon_handler;
    uint32_t reserved2;
    interrupt_handler_t pendsv_handler;
    interrupt_handler_t systick_handler;
    // 外部中断 (AT32F403总共68个)
    interrupt_handler_t external_irq[68];
} vector_table_t;

// GPIO配置结构体
typedef struct {
    volatile uint32_t CRL;      // 端口配置寄存器低位
    volatile uint32_t CRH;      // 端口配置寄存器高位
    volatile uint32_t IDR;      // 端口输入数据寄存器
    volatile uint32_t ODR;      // 端口输出数据寄存器
    volatile uint32_t BSRR;     // 端口位设置/复位寄存器
    volatile uint32_t BRR;      // 端口位复位寄存器
    volatile uint32_t LCKR;     // 端口配置锁定寄存器
} gpio_regs_t;

// 串口配置结构体
typedef struct {
    volatile uint32_t SR;       // 状态寄存器
    volatile uint32_t DR;       // 数据寄存器
    volatile uint32_t BRR;      // 波特率寄存器
    volatile uint32_t CR1;      // 控制寄存器1
    volatile uint32_t CR2;      // 控制寄存器2
    volatile uint32_t CR3;      // 控制寄存器3
    volatile uint32_t GTPR;     // 保护时间和预分频寄存器
} usart_regs_t;

// 系统状态结构体
typedef struct {
    uint32_t system_time_ms;
    uint16_t system_tick_counter;
    uint16_t delay_counter;
    uint8_t uart_mode_flag;
    uint16_t buffer_index;
    uint16_t crc_value;
    bool application_valid;
} system_state_t;

// 设备信息结构体
typedef struct {
    char company_name[32];
    char product_series[16];
    char product_model[16];
    char website_url[64];
    char firmware_version[16];
    char build_date[16];
    char build_time[16];
    uint32_t device_id;
    uint32_t serial_number;
} device_info_t;

// 网络配置结构体
typedef struct {
    uint8_t mac_address[6];
    uint8_t ip_address[4];
    uint8_t subnet_mask[4];
    uint8_t gateway[4];
    uint16_t http_port;
    bool dhcp_enabled;
} network_config_t;

// 设备运行时间结构体
typedef struct {
    uint32_t days;
    uint8_t hours;
    uint8_t minutes;
    uint8_t seconds;
    uint16_t milliseconds;
} device_uptime_t;

// 设备内存信息结构体
typedef struct {
    uint32_t flash_total;
    uint32_t flash_used;
    uint32_t flash_free;
    uint32_t ram_total;
    uint32_t ram_used;
    uint32_t ram_free;
} device_memory_info_t;

// 设备错误日志结构体
typedef struct {
    uint32_t timestamp;
    uint16_t error_code;
    uint32_t error_data;
    uint8_t system_state;
} device_error_log_t;

// 设备性能统计结构体
typedef struct {
    uint8_t cpu_usage;
    uint8_t memory_usage;
    uint32_t uptime_seconds;
    uint32_t network_packets_sent;
    uint32_t network_packets_received;
    uint32_t network_errors;
} device_performance_stats_t;

// 环形缓冲区结构体
typedef struct {
    uint8_t* buffer;
    uint16_t size;
    uint16_t head;
    uint16_t tail;
    uint16_t count;
} ring_buffer_t;

// 协议状态结构体
typedef struct {
    uint8_t current_state;
    uint32_t packet_count;
    uint32_t error_count;
    uint32_t last_activity;
} protocol_state_t;

// Flash保护状态结构体
typedef struct {
    uint8_t read_protection;
    uint32_t write_protection;
} flash_protection_status_t;

// Flash信息结构体
typedef struct {
    uint32_t total_size;
    uint32_t page_size;
    uint32_t page_count;
    uint32_t base_address;
    uint32_t device_id;
} flash_info_t;

// RTC时间结构体
typedef struct {
    uint8_t year;       // 年 (0-99, 相对于2000年)
    uint8_t month;      // 月 (1-12)
    uint8_t day;        // 日 (1-31)
    uint8_t hour;       // 时 (0-23)
    uint8_t minute;     // 分 (0-59)
    uint8_t second;     // 秒 (0-59)
} rtc_time_t;

// 配置参数结构体
typedef struct {
    bool valid;
    uint8_t size;
    uint16_t checksum;
    uint8_t data[MAX_PARAM_SIZE];
} config_parameter_t;

// 配置头结构体
typedef struct {
    uint32_t magic;
    uint16_t version;
    uint32_t size;
    uint16_t param_count;
    uint32_t checksum;
} config_header_t;

// 配置参数头结构体
typedef struct {
    uint16_t id;
    uint8_t size;
    uint16_t checksum;
} config_param_header_t;

// 调试错误统计结构体
typedef struct {
    uint32_t total_errors;
    uint32_t critical_errors;
    uint32_t warning_errors;
    uint32_t last_error_time;
} debug_error_stats_t;

// 调试错误日志结构体
typedef struct {
    uint32_t timestamp;
    uint16_t error_code;
    uint32_t error_data;
    uint32_t line;
    char file[32];
} debug_error_log_t;

// 调试性能数据结构体
typedef struct {
    uint8_t cpu_usage;
    uint8_t memory_usage;
    uint32_t uptime_seconds;
    uint32_t network_packets_sent;
    uint32_t network_packets_received;
} debug_performance_t;

// SPI配置结构体
typedef struct {
    uint8_t master_mode;           // 主从模式 (1:主机, 0:从机)
    uint8_t clock_polarity;        // 时钟极性 (1:高电平空闲, 0:低电平空闲)
    uint8_t clock_phase;           // 时钟相位 (1:第二个边沿采样, 0:第一个边沿采样)
    uint8_t data_size;             // 数据位数 (8或16)
    uint8_t lsb_first;             // 位序 (1:LSB先发, 0:MSB先发)
    uint8_t baud_rate_prescaler;   // 波特率预分频 (0-7)
} spi_config_t;

// GPIO操作结构体
typedef struct {
    uint16_t pin_mask;             // 引脚掩码
    uint8_t value;                 // 输出值 (0或1)
} gpio_operation_t;

// 格式说明符结构体
typedef struct {
    uint8_t flags;                 // 格式标志
    int width;                     // 字段宽度
    int precision;                 // 精度
    uint8_t length;                // 长度修饰符
    char specifier;                // 格式说明符
} format_spec_t;

// =============================================================================
// 全局变量声明
// =============================================================================

// 设备信息全局变量
extern device_info_t g_device_info;
extern network_config_t g_network_config;
extern protocol_state_t g_protocol_state;
extern ring_buffer_t g_protocol_rx_buffer;
extern ring_buffer_t g_protocol_tx_buffer;

// =============================================================================
// Macro Definitions
// =============================================================================

// Register access macros
#define REG32(addr)             (*(volatile uint32_t*)(addr))
#define REG16(addr)             (*(volatile uint16_t*)(addr))
#define REG8(addr)              (*(volatile uint8_t*)(addr))

// GPIO port access
#define GPIOA                   ((gpio_regs_t*)GPIOA_BASE)
#define GPIOB                   ((gpio_regs_t*)GPIOB_BASE)
#define GPIOC                   ((gpio_regs_t*)GPIOC_BASE)
#define GPIOD                   ((gpio_regs_t*)GPIOD_BASE)
#define GPIOE                   ((gpio_regs_t*)GPIOE_BASE)

// USART access
#define USART1                  ((usart_regs_t*)USART1_BASE)
#define USART2                  ((usart_regs_t*)USART2_BASE)
#define USART3                  ((usart_regs_t*)USART3_BASE)

// System variables access
#define SYSTEM_TICK             REG16(SYSTEM_TICK_ADDR)
#define SYSTEM_TIME_MS          REG32(SYSTEM_TIME_MS_ADDR)
#define DELAY_COUNTER           REG16(DELAY_COUNTER_ADDR)
#define UART_MODE_FLAG          REG8(UART_MODE_FLAG_ADDR)
#define BUFFER_INDEX            REG16(BUFFER_INDEX_ADDR)
#define CRC_VALUE               REG16(CRC_VALUE_ADDR)

// Buffer access
#define RX_BUFFER               ((uint8_t*)RX_BUFFER_ADDR)
#define TX_BUFFER               ((uint8_t*)TX_BUFFER_ADDR)
#define TEMP_BUFFER             ((uint8_t*)TEMP_BUFFER_ADDR)

// =============================================================================
// 函数原型声明
// =============================================================================

// 系统初始化和控制
void system_init(void);
void clock_init(void);
uint8_t gpio_init_all(void);
uint8_t uart_init(uint32_t uart_base, uint32_t baudrate, uint8_t config);
uint8_t timer_init_all(void);

// 核心系统函数 (从汇编代码sub_8000xxx函数转换)
uint32_t delay_ms(uint32_t ms);
uint16_t calculate_crc(const uint8_t* data, uint16_t length);
void gpio_config(int8_t pin, uint8_t mode);
bool check_signature(void);
void jump_to_application(uint32_t app_address);

// 通信函数
void uart_send_byte(uint8_t data);
uint8_t uart_receive_byte(void);
void uart_send_string(const char* str);
bool uart_data_available(void);

// 协议处理
uint8_t handle_boot_command(uint8_t* data, uint16_t length);
uint8_t handle_echo_command(uint8_t* data, uint16_t length);
void process_received_data(void);

// 中断处理函数
void reset_handler(void);
void systick_handler(void);
void uart1_irq_handler(void);
void uart2_irq_handler(void);

// 主应用程序
void bootloader_main(void);

// Logo数据分析函数
void analyze_logo_data(void);
void get_logo_dimensions(uint32_t* width, uint32_t* height, const char** format);
void print_logo_info(void);
const uint32_t* get_logo_data(void);
uint32_t get_logo_data_size(void);
uint32_t get_logo_data_words(void);
uint32_t copy_logo_data(uint8_t* buffer, uint32_t buffer_size);

// Web服务器函数 (从汇编代码sub_800D7E0等函数转换)
void web_server_init(void);
void web_server_process(void);
bool web_server_handle_request(const uint8_t* request_data, uint16_t request_length);
uint16_t generate_web_page(uint8_t page_type, uint8_t param_id, uint8_t* buffer, uint16_t buffer_size);
uint16_t generate_parameter_page(uint8_t page_type, uint8_t param_id, uint8_t* buffer, uint16_t buffer_size);

// Web页面生成器函数 (从汇编sub_800D7E0转换)
uint16_t web_page_generator_main(uint8_t* request_buffer, uint8_t* response_buffer);
uint16_t generate_page_content(uint8_t* req_buf, uint8_t* resp_buf, uint8_t page_type, uint8_t page_param, uint16_t resp_index);
void format_mac_address_string(char* buffer, const uint8_t* mac_addr);
void get_parameter_string(uint16_t param_id, char* buffer);
void format_special_parameter(char* buffer);

// CRC计算函数 (从汇编sub_80002BA转换)
uint16_t calculate_crc16(const uint8_t* data, uint16_t length);
uint16_t calculate_crc16_ccitt(const uint8_t* data, uint16_t length);
uint16_t calculate_crc16_table(const uint8_t* data, uint16_t length);
bool verify_crc16(const uint8_t* data, uint16_t length, uint16_t expected_crc);
uint16_t append_crc16(uint8_t* data, uint16_t length);
bool check_data_with_crc16(const uint8_t* data, uint16_t total_length);

// 字符串处理函数 (从汇编sub_8007B28, sub_8010968, sub_80109AC转换)
size_t string_length(const char* str);
void* memory_copy(void* dest, const void* src, size_t count);
char* string_copy(char* dest, const char* src);
char* string_copy_n(char* dest, const char* src, size_t max_len);
int simple_sprintf(char* buffer, const char* format, ...);
int format_decimal(char* buffer, int value);
int format_hex(char* buffer, unsigned int value, bool uppercase);
int string_compare(const char* str1, const char* str2);
int string_compare_n(const char* str1, const char* str2, size_t max_len);

// 通信协议函数 (从汇编sub_8002674, sub_80028BC转换)
uint16_t uart_communication_handler(uint8_t uart_channel, uint8_t* data, uint16_t length);
uint16_t uart1_process_data(uint8_t* data, uint16_t length);
uint16_t uart2_process_data(uint8_t* data, uint16_t length);
uint8_t protocol_parse_packet(uint8_t* buffer, uint16_t length);
bool protocol_receive_byte(uint8_t byte);
void protocol_reset_receiver(void);
// 函数声明已在上面定义
uint8_t handle_g0b1_command(uint8_t* data, uint16_t length);
uint8_t handle_inco_command(uint8_t* data, uint16_t length);
void send_protocol_response(uint32_t command, uint8_t* data, uint16_t length);

// 数学运算函数 (从汇编sub_8004284, sub_8004348, sub_80045B4等转换)
double float_to_double(float f);
float double_to_float(double d);
float int_to_float(int32_t value);
float uint_to_float(uint32_t value);
float sint_to_float(int32_t value);
double double_add(double a, double b);
double double_sub(double a, double b);
double double_mul(double a, double b);
double double_div(double a, double b);
double math_sin(double x);
double math_cos(double x);
double math_tan(double x);
double math_sqrt(double x);
double math_pow(double base, double exponent);
bool math_isnan(double x);
bool math_isinf(double x);
double math_max(double a, double b);
double math_min(double a, double b);

// 硬件驱动函数 (从汇编sub_8005492, sub_80054CA, sub_8005660等转换)
uint8_t spi_configure(uint32_t spi_base, uint8_t config, uint8_t enable);
uint16_t spi_transfer(uint32_t spi_base, uint16_t data);
uint8_t spi_get_flag_status(uint32_t spi_base, uint8_t flag);
void spi_clear_flag(uint32_t spi_base, uint8_t flag);
uint8_t i2c_init(uint32_t i2c_base, uint32_t clock_speed);
void i2c_generate_start(uint32_t i2c_base);
void i2c_generate_stop(uint32_t i2c_base);
uint8_t i2c_check_event(uint32_t i2c_base, uint32_t event);
void i2c_send_data(uint32_t i2c_base, uint8_t data);
uint8_t i2c_receive_data(uint32_t i2c_base);
uint8_t adc_init(uint32_t adc_base, uint8_t enable);
void adc_start_conversion(uint32_t adc_base, uint8_t channel, uint8_t mode, uint8_t trigger);
uint16_t adc_get_conversion_value(uint32_t adc_base);
uint8_t adc_get_flag_status(uint32_t adc_base);
void dac_init(uint32_t dac_base, uint8_t channel, uint8_t enable);
void dac_set_value(uint32_t dac_base, uint8_t channel, uint16_t value);
void dac_software_trigger(uint32_t dac_base, uint8_t channel, uint8_t trigger);
void gpio_configure(uint32_t gpio_base, uint16_t pin_mask, uint8_t mode, uint8_t speed);
void gpio_write(uint32_t gpio_base, uint16_t pin_mask, uint8_t value);
uint8_t gpio_read(uint32_t gpio_base, uint16_t pin_mask);

// 系统管理函数 (从汇编sub_800558C, sub_8004208, sub_8004966等转换)
uint8_t rcc_clock_config(void);
uint8_t rcc_peripheral_clock_enable(uint32_t peripheral);
uint32_t rcc_get_system_clock_freq(void);
uint8_t power_management_init(void);
void power_enter_sleep_mode(uint8_t mode);
void power_enter_stop_mode(void);
void power_enter_standby_mode(void);
void system_software_reset(void);
void peripheral_reset(uint32_t peripheral);
uint32_t get_reset_flags(void);
void clear_reset_flags(void);
uint8_t iwdg_init(uint8_t prescaler, uint16_t reload);
void iwdg_reload(void);
uint8_t wwdg_init(uint8_t counter, uint8_t window);
void wwdg_reload(uint8_t counter);
uint32_t get_chip_id(void);
void get_unique_id(uint8_t* uid_buffer);
uint16_t get_flash_size(void);

// 外设管理函数 (从汇编sub_8006778, sub_80067F0, sub_8006942等转换)
uint8_t timer_configure(uint32_t timer_base, uint16_t prescaler, uint16_t period);
uint8_t timer_interrupt_config(uint32_t timer_base, uint8_t interrupt_enable);
uint8_t timer_get_flag_status(uint32_t timer_base, uint8_t flag);
void timer_clear_flag(uint32_t timer_base, uint8_t flag);
uint8_t timer_pwm_config(uint32_t timer_base, uint8_t channel, uint8_t duty_cycle);
uint8_t uart_init(uint32_t uart_base, uint32_t baudrate, uint8_t config);
uint8_t uart_send_data(uint32_t uart_base, uint8_t data);
uint8_t uart_receive_data(uint32_t uart_base, uint8_t* data);
uint8_t uart_get_flag_status(uint32_t uart_base, uint8_t flag);
uint8_t uart_interrupt_config(uint32_t uart_base, uint8_t interrupt_type, uint8_t enable);
uint8_t nvic_interrupt_config(uint8_t irq_number, uint8_t priority, uint8_t enable);
uint8_t nvic_priority_group_config(uint8_t priority_group);
uint8_t nvic_software_interrupt(uint8_t irq_number);
uint8_t peripheral_clock_control(uint8_t peripheral_id, uint8_t enable);
uint8_t peripheral_reset_control(uint8_t peripheral_id, uint8_t reset);

// 数据处理函数 (从汇编sub_8006B8A, sub_8006BD4, sub_8006EB0等转换)
uint16_t format_data_to_hex_string(const uint8_t* data, char* buffer, uint16_t length);
uint16_t parse_hex_string_to_data(const char* hex_string, uint8_t* data, uint16_t max_length);
bool is_hex_char(char ch);
uint8_t char_to_hex_value(char ch);
uint8_t convert_endianness(uint8_t* data, uint16_t length);
uint8_t bcd_to_binary(uint8_t bcd_value);
uint8_t binary_to_bcd(uint8_t binary_value);
uint8_t calculate_checksum(const uint8_t* data, uint16_t length, uint8_t checksum_type);
uint16_t compress_data_rle(const uint8_t* input, uint16_t input_length, uint8_t* output, uint16_t output_size);
uint16_t decompress_data_rle(const uint8_t* input, uint16_t input_length, uint8_t* output, uint16_t output_size);

// 网络通信函数 (从汇编sub_8007E48, sub_8008038, sub_8008062等转换)
uint8_t ethernet_init(const uint8_t* mac_addr);
uint8_t phy_init(void);
uint8_t phy_read_register(uint8_t phy_addr, uint8_t reg_addr, uint16_t* data);
uint8_t phy_write_register(uint8_t phy_addr, uint8_t reg_addr, uint16_t data);
uint8_t ip_config(const uint8_t* ip_addr, const uint8_t* subnet_mask, const uint8_t* gateway);
uint8_t arp_add_entry(const uint8_t* ip_addr, const uint8_t* mac_addr);
uint8_t icmp_ping_reply(uint8_t* packet, uint16_t length);
uint16_t calculate_ip_checksum(const uint8_t* data, uint16_t length);
uint8_t tcp_listen(uint16_t port);
uint8_t set_dhcp_enabled(uint8_t enable);
uint8_t dhcp_start(void);
uint8_t dhcp_stop(void);
uint8_t check_link_status(void);

// 设备管理函数 (从汇编sub_8009070, sub_80091F0, sub_80092C8等转换)
uint8_t device_config_init(void);
uint8_t device_status_check(void);
uint8_t device_reset(uint8_t reset_type);
uint8_t device_read_parameter(uint16_t param_id, uint8_t* value);
uint8_t device_write_parameter(uint16_t param_id, uint32_t value);
uint8_t save_device_parameters(void);
uint8_t load_device_parameters(void);
int16_t device_get_temperature(void);
uint16_t device_get_voltage(void);
uint8_t device_get_uptime(device_uptime_t* uptime);
uint8_t device_get_memory_info(device_memory_info_t* memory_info);
uint16_t device_self_test(void);
uint8_t device_log_error(uint16_t error_code, uint32_t error_data);
uint8_t device_get_performance_stats(device_performance_stats_t* stats);

// 高级通信函数 (从汇编sub_8009A9A, sub_8009B18, sub_8009C1C等转换)
uint8_t protocol_stack_init(void);
uint8_t protocol_packet_handler(uint8_t channel);
uint8_t process_protocol_command(uint8_t command_type, uint8_t* payload, uint16_t payload_length);
uint16_t handle_device_info_command(uint8_t* payload, uint16_t payload_length, uint8_t* response);
uint16_t handle_parameter_read_command(uint8_t* payload, uint16_t payload_length, uint8_t* response);
uint16_t handle_parameter_write_command(uint8_t* payload, uint16_t payload_length, uint8_t* response);
uint16_t handle_system_control_command(uint8_t* payload, uint16_t payload_length, uint8_t* response);
uint16_t handle_network_config_command(uint8_t* payload, uint16_t payload_length, uint8_t* response);
uint16_t handle_diagnostic_command(uint8_t* payload, uint16_t payload_length, uint8_t* response);
uint8_t get_parameter_size(uint16_t param_id);
uint8_t protocol_timeout_check(void);

// Flash存储函数 (从汇编sub_800C114, sub_800C5F8, sub_800C912等转换)
uint8_t flash_unlock(void);
uint8_t flash_lock(void);
uint8_t flash_get_status(void);
uint8_t flash_wait_for_operation(uint32_t timeout);
uint8_t flash_erase_page(uint32_t page_address);
uint8_t flash_erase_all(void);
uint8_t flash_program_halfword(uint32_t address, uint16_t data);
uint8_t flash_program_word(uint32_t address, uint32_t data);
uint8_t flash_program_buffer(uint32_t address, const uint8_t* buffer, uint32_t length);
uint8_t flash_read_buffer(uint32_t address, uint8_t* buffer, uint32_t length);
uint16_t flash_read_halfword(uint32_t address);
uint32_t flash_read_word(uint32_t address);
uint8_t flash_set_write_protection(uint32_t sectors);
uint8_t flash_set_read_protection(uint8_t level);
uint8_t flash_get_protection_status(flash_protection_status_t* protection_status);
uint8_t flash_get_info(flash_info_t* flash_info);
uint8_t flash_calculate_checksum(uint32_t start_address, uint32_t length, uint32_t* checksum);
uint8_t flash_check_blank(uint32_t start_address, uint32_t length);

// 中断服务函数 (从汇编sub_800E7DE, sub_800E90C, sub_800EA6C等转换)
void SysTick_Handler(void);
void TIM2_IRQHandler(void);
void TIM3_IRQHandler(void);
void USART1_IRQHandler(void);
void USART2_IRQHandler(void);
void EXTI0_IRQHandler(void);
void EXTI1_IRQHandler(void);
void EXTI15_10_IRQHandler(void);
void timer_interrupt_handler(uint8_t timer_id);
void pwm_interrupt_handler(uint8_t timer_id, uint8_t channel);
void uart_receive_interrupt_handler(uint8_t uart_id, uint8_t data);
void uart_transmit_interrupt_handler(uint8_t uart_id);
void uart_transmit_complete_handler(uint8_t uart_id);
void uart_error_handler(uint8_t uart_id, uint8_t error_flags);
void external_interrupt_handler(uint8_t exti_line);
void task_scheduler_10ms(void);
void task_scheduler_100ms(void);
void task_scheduler_1000ms(void);
void interrupt_priority_init(void);

// 应用任务函数 (从汇编sub_800DF6C, sub_800E4EC等转换)
void key_scan_task(void);
void key_press_handler(uint8_t key_id);
void key_release_handler(uint8_t key_id, uint32_t press_duration);
void key_short_press_handler(uint8_t key_id);
void key_long_press_handler(uint8_t key_id);
void led_update_task(void);
void led_set_state(uint8_t led_id, uint8_t state);
void led_set_blink(uint8_t led_id, uint16_t period);
void led_status_indication(uint8_t status);
void system_status_check_task(void);
void temperature_monitor_task(void);
void voltage_monitor_task(void);
void communication_timeout_check(void);
void system_self_test_task(void);
void parameter_save_task(void);
void performance_statistics_task(void);

// RTC管理函数 (从汇编sub_800F548, sub_80114C0, sub_8011A26等转换)
uint8_t rtc_bcd_to_binary(uint8_t bcd_value);
uint8_t rtc_binary_to_bcd(uint8_t binary_value);
uint8_t rtc_write_bcd_register(volatile uint32_t* rtc_reg, uint8_t value);
uint8_t rtc_init(void);
uint8_t rtc_set_time(const rtc_time_t* time_info);
uint8_t rtc_get_time(rtc_time_t* time_info);
uint32_t rtc_calculate_timestamp(const rtc_time_t* time_info);
void rtc_timestamp_to_time(uint32_t timestamp, rtc_time_t* time_info);
bool rtc_is_leap_year(uint16_t year);
uint8_t rtc_set_alarm(const rtc_time_t* alarm_time);
uint8_t rtc_disable_alarm(void);
uint8_t rtc_get_interrupt_status(void);
uint8_t rtc_clear_interrupt_flags(uint8_t flags);
uint16_t rtc_format_time_string(const rtc_time_t* time_info, char* buffer, uint8_t format);
uint8_t rtc_get_status(void);
uint8_t rtc_set_default_time(void);

// 配置管理函数 (从汇编sub_800F56A等转换)
uint8_t config_read_parameter(uint16_t param_id, uint8_t* buffer);
uint8_t config_write_parameter(uint16_t param_id, const uint8_t* buffer, uint8_t size);
uint8_t config_validate_parameter(uint16_t param_id, const uint8_t* buffer, uint8_t size);
uint8_t config_get_default_value(uint16_t param_id, uint8_t* buffer);
uint8_t config_load_from_flash(void);
uint8_t config_save_to_flash(void);
uint8_t config_load_defaults(void);
uint8_t config_get_parameter_size(uint16_t param_id);
uint8_t config_integrity_check(void);
uint8_t config_backup(void);
uint8_t config_restore(void);
uint8_t config_get_status(void);
uint8_t config_auto_save_check(void);

// 调试诊断函数 (从汇编sub_800F804, sub_8010858等转换)
void debug_printf(uint8_t level, const char* format, ...);
void debug_print_data(const uint8_t* data, uint16_t length, uint8_t format);
void debug_print_registers(uint32_t reg_base, uint8_t reg_count, const char** reg_names);
uint8_t debug_diagnostics_init(void);
uint8_t debug_system_diagnosis(uint8_t component);
uint8_t debug_diagnose_cpu(void);
uint8_t debug_diagnose_memory(void);
uint8_t debug_diagnose_flash(void);
void debug_log_error(uint16_t error_code, uint32_t error_data, const char* file, uint32_t line);
uint8_t debug_get_error_stats(debug_error_stats_t* stats);
uint8_t debug_get_error_logs(debug_error_log_t* logs, uint8_t max_count);
void debug_update_performance(void);
uint8_t debug_get_performance_data(debug_performance_t* perf_data);
const char* debug_get_level_string(uint8_t level);
const char* debug_get_diag_result_string(uint8_t result);
void debug_set_level(uint8_t level);
void debug_set_enabled(bool enabled);

// 调试宏定义
#define DEBUG_LOG_ERROR(code, data) debug_log_error(code, data, __FILE__, __LINE__)

// 底层硬件控制函数 (从汇编sub_80122F0, sub_80124D0等转换)
uint8_t i2c_low_level_init(uint8_t i2c_channel);
uint8_t i2c_start(uint8_t i2c_channel);
uint8_t i2c_stop(uint8_t i2c_channel);
uint8_t i2c_send_address(uint8_t i2c_channel, uint8_t address, uint8_t direction);
uint8_t i2c_send_data_low(uint8_t i2c_channel, uint8_t data);
uint8_t i2c_receive_data_low(uint8_t i2c_channel, uint8_t* data);
uint8_t spi_low_level_init(uint8_t spi_channel, const spi_config_t* config);
uint8_t spi_transfer_low(uint8_t spi_channel, uint16_t tx_data, uint16_t* rx_data);
uint8_t gpio_fast_write(uint8_t gpio_port, uint16_t pin_mask, uint8_t value);
uint16_t gpio_fast_read(uint8_t gpio_port, uint16_t pin_mask);
uint8_t gpio_batch_operations(uint8_t gpio_port, const gpio_operation_t* operations, uint8_t count);

// 高级字符串处理函数 (从汇编sub_8012914, sub_8013448等转换)
uint16_t advanced_sprintf(char* buffer, const char* format, ...);
uint16_t advanced_vsnprintf(char* buffer, uint16_t size, const char* format, va_list args);
const char* parse_format_specifier(const char* format, format_spec_t* spec);
uint16_t process_format_specifier(char* buffer, uint16_t size, const format_spec_t* spec, va_list* args);
uint16_t format_integer(char* buffer, uint16_t size, const format_spec_t* spec, va_list* args, uint8_t base, uint8_t is_signed);
uint16_t format_character(char* buffer, uint16_t size, const format_spec_t* spec, va_list* args);
uint16_t format_string(char* buffer, uint16_t size, const format_spec_t* spec, va_list* args);
const char* string_find_char(const char* str, char ch);
const char* string_find_substring(const char* haystack, const char* needle);
void* advanced_memset(void* dest, int value, size_t count);
void* advanced_memcpy(void* dest, const void* src, size_t count);

// 最终系统函数 (从汇编sub_8012338, Reset_Handler等转换)
uint8_t system_core_init(void);
uint8_t system_clock_config(void);
uint8_t mpu_config(void);
void cache_config(void);
void HardFault_Handler(void);
void MemManage_Handler(void);
void BusFault_Handler(void);
void UsageFault_Handler(void);
void system_reset(void);
void enter_low_power_mode(uint8_t mode);
uint32_t get_system_status(void);
uint32_t get_system_error_status(void);
void clear_system_error_flags(uint32_t error_mask);
uint8_t system_startup_init(void);
void start_system_tasks(void);
void main_loop(void);
void system_error_handler(void);

// 系统变量声明
extern uint32_t SystemCoreClock;

// 补充的函数声明
uint16_t format_pointer(char* buffer, uint16_t size, const format_spec_t* spec, va_list* args);
uint16_t format_float(char* buffer, uint16_t size, const format_spec_t* spec, va_list* args, uint8_t format_type);
uint16_t debug_vsnprintf(char* buffer, uint16_t size, const char* format, va_list args);
uint8_t debug_diagnose_uart(void);
uint8_t debug_diagnose_timer(void);
uint8_t debug_diagnose_gpio(void);
uint8_t debug_diagnose_adc(void);
uint8_t debug_diagnose_rtc(void);
uint8_t debug_memory_test(void);
uint8_t debug_flash_test(void);
void debug_uart_send(const char* buffer, uint16_t length);
void debug_uart_init(void);

// 补充的应用任务函数
void menu_display_update(void);
void menu_item_handler(uint8_t item_id);
void external_event_handler(uint8_t event_id);
void system_reset_request(void);
void parameter_reset_request(void);
void enter_setting_mode(void);
void enter_test_mode(void);
void pwm_duty_cycle_update(void);
void encoder_read_task(void);
void input_capture_process(void);
void frequency_measurement_task(void);
void process_capture_value(uint8_t channel, uint32_t capture_value);
void uart_overrun_error_handler(uint8_t uart_id);
void uart_frame_error_handler(uint8_t uart_id);
void uart_parity_error_handler(uint8_t uart_id);

// 补充的系统函数
uint32_t get_cpu_frequency(void);
uint32_t get_pclk1_frequency(void);
uint8_t get_cpu_usage(void);
uint32_t get_network_packets_sent(void);
uint32_t get_network_packets_received(void);
uint8_t peripheral_init_all(void);
uint8_t application_init(void);
uint8_t network_init(void);
uint8_t systick_init(uint32_t tick_hz);
// iwdg_init已在上面定义
void iwdg_reload(void);
void network_process(void);
void web_server_process(void);
void protocol_process(void);

// 内联函数和宏定义
#define __NOP()                     __asm volatile ("nop")
#define __disable_irq()             __asm volatile ("cpsid i" : : : "memory")
#define __enable_irq()              __asm volatile ("cpsie i" : : : "memory")
#define __WFI()                     __asm volatile ("wfi")

// 系统时间访问宏
#define SYSTEM_TIME_MS              g_system_time_ms

// 缺失的全局变量声明
extern volatile uint32_t g_system_time_ms;
extern volatile uint8_t g_parameter_changed_flag;
extern volatile uint8_t g_uart1_tx_complete_flag;
extern volatile uint8_t g_uart2_tx_complete_flag;
extern volatile uint8_t g_confirm_flag;
extern volatile uint8_t g_cancel_flag;
extern volatile uint8_t g_current_mode;
extern volatile uint8_t g_current_menu_item;
extern volatile uint8_t g_menu_item_count;
extern volatile uint8_t g_menu_level;

// 常量定义
#define MAX_MODE_COUNT              4

#endif // AT32F403AVG_FIRMWARE_H
