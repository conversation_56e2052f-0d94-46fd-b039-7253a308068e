// 大规模手工转换批次 37 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_659534
 * @note 指令数: 5
 */
void func_659534(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_6598E2
 * @note 指令数: 2
 */
void func_6598e2(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_659918
 * @note 指令数: 579
 */
void func_659918(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_659D9E
 * @note 指令数: 2
 */
void func_659d9e(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_659DB8
 * @note 指令数: 2
 */
void func_659db8(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_659E3C
 * @note 指令数: 2
 */
void func_659e3c(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_659E40
 * @note 指令数: 10
 */
void func_659e40(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_659E54
 * @note 指令数: 2
 */
void func_659e54(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B174
 * @note 指令数: 2
 */
void func_65b174(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B178
 * @note 指令数: 441
 */
void func_65b178(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B4EA
 * @note 指令数: 2
 */
void func_65b4ea(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B4EE
 * @note 指令数: 186
 */
void func_65b4ee(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B662
 * @note 指令数: 2
 */
void func_65b662(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B666
 * @note 指令数: 74
 */
void func_65b666(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B6FA
 * @note 指令数: 75
 */
void func_65b6fa(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B790
 * @note 指令数: 11
 */
void func_65b790(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B7A6
 * @note 指令数: 2
 */
void func_65b7a6(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B7AA
 * @note 指令数: 8
 */
void func_65b7aa(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B7BA
 * @note 指令数: 2
 */
void func_65b7ba(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65B7BE
 * @note 指令数: 197
 */
void func_65b7be(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_D402D302 = (volatile uint32_t *)0xD402D302;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_CF02CE02 = (volatile uint32_t *)0xCF02CE02;
    volatile uint32_t *addr_D602D502 = (volatile uint32_t *)0xD602D502;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_65BA10
 * @note 指令数: 2
 */
void func_65ba10(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;
    volatile uint32_t *addr_13 = (volatile uint32_t *)0x13;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65BA14
 * @note 指令数: 46
 */
void func_65ba14(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_13 = (volatile uint32_t *)0x13;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65BA70
 * @note 指令数: 191
 */
void func_65ba70(void)
{
    // 内存地址定义
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_13 = (volatile uint32_t *)0x13;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65BBEE
 * @note 指令数: 2
 */
void func_65bbee(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65BBF2
 * @note 指令数: 350
 */
void func_65bbf2(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65BEAE
 * @note 指令数: 2
 */
void func_65beae(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65C2A8
 * @note 指令数: 2
 */
void func_65c2a8(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65C2AC
 * @note 指令数: 68
 */
void func_65c2ac(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65C334
 * @note 指令数: 15
 */
void func_65c334(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65C352
 * @note 指令数: 7
 */
void func_65c352(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65C360
 * @note 指令数: 127
 */
void func_65c360(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_D7 = (volatile uint32_t *)0xD7;
    volatile uint32_t *addr_85 = (volatile uint32_t *)0x85;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65C45E
 * @note 指令数: 2
 */
void func_65c45e(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65C52A
 * @note 指令数: 268
 */
void func_65c52a(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65C742
 * @note 指令数: 2
 */
void func_65c742(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65CAB4
 * @note 指令数: 645
 */
void func_65cab4(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65CFBE
 * @note 指令数: 2
 */
void func_65cfbe(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65D0FC
 * @note 指令数: 2
 */
void func_65d0fc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65D100
 * @note 指令数: 508
 */
void func_65d100(void)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65D4F8
 * @note 指令数: 1346
 */
void func_65d4f8(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65E40A
 * @note 指令数: 2
 */
void func_65e40a(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65E40E
 * @note 指令数: 548
 */
void func_65e40e(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65E856
 * @note 指令数: 2
 */
void func_65e856(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65E85A
 * @note 指令数: 600
 */
void func_65e85a(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65ED0A
 * @note 指令数: 2
 */
void func_65ed0a(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65ED0E
 * @note 指令数: 67
 */
void func_65ed0e(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65ED94
 * @note 指令数: 2
 */
void func_65ed94(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65F612
 * @note 指令数: 2
 */
void func_65f612(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_65F616
 * @note 指令数: 1028
 */
void func_65f616(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_660466
 * @note 指令数: 2
 */
void func_660466(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_66046A
 * @note 指令数: 5
 */
void func_66046a(uint32_t param0)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

