// 精确转换批次 10 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_259BE
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_259be(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // CMP     R4, #2
    // 比较操作
    // BGE     locret_259FE
    // 条件跳转
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_259FE
    // 条件跳转
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_259E0
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // B       loc_259E2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25A14
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_25a14(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x180003;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80163AC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x180005;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =dword_180004
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180005
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // LDR     R0, =0x80163AC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // ADDS    R1, R0, #4
    // 算术运算
    // LDR     R0, =0x80163AC
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // LDR     R0, =0x80163AC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // ADDS.W  R1, R0, #0x10
    // 算术运算
    // LDR     R0, =0x80163AC
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R0, R2, R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25A8E
 * @note 指令数: 6, 标签数: 1
 */
void precise_func_25a8e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // CMP     R0, #0xFF
    // 比较操作
    // BGT     locret_25A9A
    // 条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_25A92
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25A9C
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_25a9c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80163AC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_25AC6
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x80163AC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x80163AC
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // B       loc_25AE2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25B28
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_25b28(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80163AC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R7, R2
    // MOVS    R6, R3
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x80163AC
    // 内存加载操作
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x80163AC
    // 内存加载操作
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_25A8E
    // 调用函数: sub_25A8E();
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R8, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25BDC
 * @note 指令数: 7, 标签数: 1
 */
void precise_func_25bdc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDRB    R2, [R0]
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R2, R1
    // 比较操作
    // CBZ     R2, loc_25BEC
    // ITT NE
    // LDRBNE.W R2, [R0,#1]!
    // BNE     loc_25BE0
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25BF4
 * @note 指令数: 9, 标签数: 1
 */
void precise_func_25bf4(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LSLS    R3, R0, #0x1E
    // BEQ     loc_25C08
    // 条件跳转
    // SUBS    R2, R2, #1
    // 算术运算
    // BCC     loc_25C44
    // LDRB.W  R3, [R0],#1
    // CMP     R1, R3
    // 比较操作
    // BNE     loc_25BF6
    // 条件跳转
    // B       loc_25C48
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25C4C
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_25c4c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // NOP
    // MOVS    R3, R2
    // MOV     LR, R2
    // MOV     R12, R2
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_26E60
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_26e60(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20000090;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x20000090
    // 内存加载操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R4, R0
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // LSLS    R0, R0, #0x1E
    // BMI     loc_26E78
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_26EE4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_26EE6
 * @note 指令数: 28, 标签数: 0
 */
void precise_func_26ee6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000090;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x20000090
    // 内存加载操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_256AA
    // 调用函数: sub_256AA();
    // LDR.W   R0, =0x20000090
    // 内存加载操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R4, R0
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_26F5A
    // MOVS    R2, #1
    // R2 = 1;
    // LDR.W   R0, =0x20000090
    // 内存加载操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MLA.W   R0, R1, R4, R0
    // LDRB    R0, [R0,#4]
    // 内存加载操作
    // ANDS.W  R1, R0, #0xFE
    // LDR.W   R0, =0x20000090
    // 内存加载操作
    // MOVS    R3, #0x14
    // R3 = 0x14;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_25986
    // 调用函数: sub_25986();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_26F34
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_26FC6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_26FC8
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_26fc8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000090;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // LDRSH.W R8, [SP,#0x20+arg_0]
    // CMP     R4, #0
    // 比较操作
    // BNE     loc_26FEE
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R0, =0x20000090
    // 内存加载操作
    // MOVS    R2, #0x14
    // R2 = 0x14;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_259BE
    // 调用函数: sub_259BE();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2723C
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_2723c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFFFFFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R4,R5,LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // STRB.W  R5, [SP,#0x18+var_14]
    // MOVS.W  R0, #0xFFFFFFFF
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R2, #1
    // R2 = 1;
    // ADD     R1, SP, #0x18+var_14
    // 算术运算
    // MOVS    R0, R4
    // BL      sub_26FC8
    // 调用函数: sub_26FC8();
    // POP     {R1-R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2725C
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_2725c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007F20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // LDRSH.W R8, [SP,#0x20+arg_0]
    // LDR     R0, =0x20007F20
    // 内存加载操作
    // STRH.W  R8, [R0]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_273AE
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_273ae(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFFFFFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R4,R5,LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R4, R0
    // MOVS.W  R0, #0xFFFFFFFF
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R2, #1
    // R2 = 1;
    // ADD     R1, SP, #0x18+var_14
    // 算术运算
    // MOVS    R0, R4
    // BL      sub_2725C
    // 调用函数: sub_2725C();
    // MOVS    R5, R0
    // SXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BPL     loc_273D4
    // MOVS    R0, R5
    // SXTH    R0, R0
    // 数据扩展操作
    // B       locret_273DA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_283DE
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_283de(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_283DE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_283E0
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_283e0(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_283E0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_283E2
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_283e2(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_283E2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_283E4
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_283e4(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_283E4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_283EC
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_283ec(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_18306
    // 调用函数: sub_18306();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_283F4
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_283f4(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_19A38
    // 调用函数: sub_19A38();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_283FC
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_283fc(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_16892
    // 调用函数: sub_16892();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_28404
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_28404(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      loc_196F8
    // 调用函数: loc_196F8();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2840C
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_2840c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_19894
    // 调用函数: sub_19894();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_28414
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_28414(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_19BF0
    // 调用函数: sub_19BF0();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2841C
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_2841c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_16640
    // 调用函数: sub_16640();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_28424
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_28424(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_2323C
    // 调用函数: sub_2323C();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2842C
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_2842c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x65;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40001000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40011400;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007FD0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x40001000
    // 内存加载操作
    // BL      sub_183A2
    // 调用函数: sub_183A2();
    // LDR     R0, =0x20007FD0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x20007FD0
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007FD0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x65 ; 'e'
    // 比较操作
    // BLT     locret_28480
    // 条件跳转
    // MOVS    R1, #8
    // R1 = 8;
    // LDR     R0, =0x40011400
    // 内存加载操作
    // BL      sub_184B4
    // 调用函数: sub_184B4();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_28458
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // B       loc_2845A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_289B4
 * @note 指令数: 6, 标签数: 1
 */
void precise_func_289b4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       loc_289EA
    // 无条件跳转
    // LDR.W   R1, [R0],#4
    // 内存加载操作
    // LSLS    R3, R1, #0x1F
    // ITT MI
    // SUBMI.W R3, R9, #1
    // ADDMI   R1, R3, R1
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_28B18
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_28b18(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x28B26;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28B20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x16;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R1, =(loc_28B92+2 - 0x28B20)
    // 内存加载操作
    // ADD     R1, PC          ; loc_28B92
    // 算术运算
    // ADDS    R1, #0x18
    // 算术运算
    // LDR     R4, =(word_28BB6 - 0x28B26)
    // 内存加载操作
    // ADD     R4, PC          ; word_28BB6
    // 算术运算
    // ADDS    R4, #0x16
    // 算术运算
    // B       loc_28B32
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_28CDA
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_28cda(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_28CDE
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_28cde(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B.W     loc_28CE4
    // ALIGN 4
    // MOV     R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_28CF0
 * @note 指令数: 8, 标签数: 1
 */
void precise_func_28cf0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20026;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xAB;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // NOP
    // NOP
    // LDR     R2, =0x20026
    // 内存加载操作
    // MOVS    R1, R2
    // MOVS    R0, #0x18
    // R0 = 0x18;
    // BKPT    0xAB
    // B       loc_28CF8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_43E90
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_43e90(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #0x10
    // 比较操作
    // BLT     loc_43E9E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_43EA8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_43EAA
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_43eaa(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200070AC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R1, =0x200070AC
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R2, #2
    // R2 = 2;
    // MULS    R2, R0
    // LDRH    R1, [R1,R2]
    // 内存加载操作
    // CMP     R1, #6
    // 比较操作
    // BLT     loc_43EC6
    // 条件跳转
    // MOVS    R1, #5
    // R1 = 5;
    // LDR     R2, =0x200070AC
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_43EEA
 * @note 指令数: 111, 标签数: 1
 */
void precise_func_43eea(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006C74;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200070AC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200069F4;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20006BF4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0x10
    // 比较操作
    // BGE     loc_43FCA
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20006BB4
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // LDR     R1, =0x42C80000
    // 内存加载操作
    // LDR     R2, =0x20006BF4
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0x3F800000
    // R1 = 0x3F800000;
    // LDR     R2, =0x20006C34
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #5
    // R1 = 5;
    // LDR     R2, =0x2000710C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #2
    // R1 = 2;
    // LDR     R2, =0x200070EC
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20006A34
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // LDR     R1, =0x42C80000
    // 内存加载操作
    // LDR     R2, =0x20006A74
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20006AF4
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #5
    // R1 = 5;
    // LDR     R2, =0x200070AC
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // LDR     R1, =0x425C0000
    // 内存加载操作
    // LDR     R2, =0x20006B34
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // LDR     R1, =0xC2340000
    // 内存加载操作
    // LDR     R2, =0x20006B74
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R2, =0x200070CC
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20006AB4
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20006C74
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // LDR     R1, =0x3DCCCCCD
    // 内存加载操作
    // LDR     R2, =0x200069F4
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0x3E ; '>'
    // R1 = 0x3E;
    // LDR     R2, =0x200064D4
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0xC
    // R3 = 0xC;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x200064D4
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #0xC
    // R3 = 0xC;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STRB    R1, [R2,#8]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_43EF0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_44038
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_44038(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007812;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007885;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // BL      sub_45420
    // 调用函数: sub_45420();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007812
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BL      sub_43EEA
    // 调用函数: sub_43EEA();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007885
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_441EC
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_441ec(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_44430
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_44430(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1F= -0x1F
    // var_1E= -0x1E
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_44864
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_44864(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_1B= -0x1B
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_44AE0
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_44ae0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200070CC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x200070CC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R1, R4
    // LDRH    R0, [R0,R1]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_44B00
    // 条件跳转
    // LDR     R0, =0x200070CC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R1, R4
    // LDRH    R0, [R0,R1]
    // 内存加载操作
    // CMP     R0, #0x11
    // 比较操作
    // BLT     loc_44B0C
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_44B74
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_44b74(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200075F8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // LDR     R0, =0x200075F8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x3E8
    // R1 = 0x3E8;
    // CMP     R0, R1
    // 比较操作
    // BLT     loc_44BC2
    // 条件跳转
    // LDR     R0, =0x200075F8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x3E8
    // R1 = 0x3E8;
    // SUBS    R0, R0, R1
    // 算术运算
    // LDR     R1, =0x200075F8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_44EE4
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_44ee4(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_453B0
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_453b0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007889;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20007889
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_453BC
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_453bc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007889;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20007889
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45420
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_45420(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_454CE
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_454ce(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xFFFE0201;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, [R0,#0x10]
    // 内存加载操作
    // LSLS    R3, R3, #2
    // LSRS    R3, R3, #2
    // STR     R3, [R0,#0x10]
    // 内存存储操作
    // LDR     R3, [R0,#0x10]
    // 内存加载操作
    // LDR     R4, [R1]
    // 内存加载操作
    // ORRS    R4, R3
    // STR     R4, [R0,#0x10]
    // 内存存储操作
    // LDR     R3, [R0,#0xC]
    // 内存加载操作
    // LDR     R4, =0xFFFE0201
    // 内存加载操作
    // ANDS    R4, R3
    // STR     R4, [R0,#0xC]
    // 内存存储操作
    // LDR     R3, [R1,#0x30]
    // 内存加载操作
    // CMP     R3, #1
    // 比较操作
    // BEQ     loc_454F6
    // 条件跳转
    // MOVS    R3, #0x1000
    // R3 = 0x1000;
    // B       loc_454F8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45556
 * @note 指令数: 14, 标签数: 0
 */
uint32_t precise_func_45556(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R4, #1
    // R4 = 1;
    // LSLS    R4, R1
    // STR     R4, [R0,#0x28]
    // 内存存储操作
    // LDR     R4, [R0,#0x14]
    // 内存加载操作
    // MOVS    R5, #7
    // R5 = 7;
    // BICS    R4, R5
    // STR     R4, [R0,#0x14]
    // 内存存储操作
    // LDR     R4, [R0,#0x14]
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // ORRS    R4, R3
    // STR     R4, [R0,#0x14]
    // 内存存储操作
    // POP     {R4,R5}
    // 栈操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45572
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_45572(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40012408;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R1, =0x40012408
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #1
    // R2 = 1;
    // BICS    R1, R2
    // LDR     R2, =0x40012408
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // LDR     R1, [R0,#8]
    // 内存加载操作
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ORRS    R2, R1
    // STR     R2, [R0,#8]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_455AC
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_455ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007680;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007838;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200077B8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_46FB4
    // 调用函数: sub_46FB4();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007838
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200077B8
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007680
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007680
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_455CE
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_455ce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077AC;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x200077AC
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

