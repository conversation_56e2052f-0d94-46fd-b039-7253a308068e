// 精确转换批次 20 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74360
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_74360(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_24= -0x24
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74470
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_74470(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFFFF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x200036F2
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // ADD     R3, SP, #0x10+var_C
    // 算术运算
    // LDR     R2, =0x200036FA
    // 内存加载操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // MOV     R0, SP
    // BL      sub_73638
    // 调用函数: sub_73638();
    // LDR     R0, [SP,#0x10+var_10]
    // 内存加载操作
    // MOVS    R1, #0x10000
    // R1 = 0x10000;
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_744A0
    // LDR     R0, =0xFFFF
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74534
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_74534(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8012464;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80120FC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011D88;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011D88
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x10
    // R0 = 0x10;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x80120FC
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // LDR     R0, =0x2000373C
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_7457A
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8012464
    // 内存加载操作
    // MOVS    R1, #4
    // R1 = 4;
    // MOVS    R0, #0x48 ; 'H'
    // R0 = 0x48;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // B       locret_7458A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74598
 * @note 指令数: 66, 标签数: 0
 */
void precise_func_74598(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011F40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x90;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003576;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011F2C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036F6;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x10
    // 算术运算
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R2, #0
    // R2 = 0;
    // STM     R0!, {R1,R2}
    // SUBS    R0, #8
    // 算术运算
    // LDR     R1, =0x20003576
    // 内存加载操作
    // LDR     R0, =0x20000118
    // 内存加载操作
    // LDR     R2, =0x200036F8
    // 内存加载操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDRSH   R2, [R2,R3]
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R2
    // LDR     R2, =0x200036F6
    // 内存加载操作
    // MOVS    R4, #0
    // R4 = 0;
    // LDRSH   R2, [R2,R4]
    // ADDS    R2, R3, R2
    // 算术运算
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R2, R3
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // BL      sub_78930
    // 调用函数: sub_78930();
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011F2C
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x10
    // R0 = 0x10;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011F40
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R5, #6
    // R5 = 6;
    // MOVS    R4, #0
    // R4 = 0;
    // ADD     R6, SP, #0x20+var_1C
    // 算术运算
    // MOVS    R2, R4
    // MOVS    R1, R5
    // MOVS    R0, R6
    // BL      sub_76820
    // 调用函数: sub_76820();
    // LDR     R0, =0x20003576
    // 内存加载操作
    // BL      sub_7644C
    // 调用函数: sub_7644C();
    // MOVS    R1, R0
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // BL      sub_7693E
    // 调用函数: sub_7693E();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADD     R2, SP, #0x20+var_1C
    // 算术运算
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0x90
    // R0 = 0x90;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #6
    // R0 = 6;
    // BL      sub_7832C
    // 调用函数: sub_7832C();
    // MOVS    R0, #2
    // R0 = 2;
    // LDR     R1, =0x2000373B
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // POP     {R0-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74638
 * @note 指令数: 47, 标签数: 0
 */
void precise_func_74638(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011F68;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011F54;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000012C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x10
    // 算术运算
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R2, #0
    // R2 = 0;
    // STM     R0!, {R1,R2}
    // SUBS    R0, #8
    // 算术运算
    // LDR     R0, =0x2000012C
    // 内存加载操作
    // LDR     R1, =0x200036F8
    // 内存加载操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDRSH   R1, [R1,R2]
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R1
    // LDR     R1, =0x200036F6
    // 内存加载操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDRSH   R1, [R1,R3]
    // ADDS    R1, R2, R1
    // 算术运算
    // LDRB    R0, [R0,R1]
    // 内存加载操作
    // LDR     R1, =0x20003574
    // 内存加载操作
    // STRB    R0, [R1,#7]
    // 内存存储操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011F54
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x1C
    // R0 = 0x1C;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011F68
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // LDR     R0, =0x20003574
    // 内存加载操作
    // LDRB    R0, [R0,#7]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7469A
    // 条件跳转
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_746AC
    // 条件跳转
    // BCC     loc_746BE
    // B       loc_746CE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_746F8
 * @note 指令数: 46, 标签数: 0
 */
void precise_func_746f8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011F90;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011F7C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x10
    // 算术运算
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R2, #0
    // R2 = 0;
    // STM     R0!, {R1,R2}
    // SUBS    R0, #8
    // 算术运算
    // LDR     R0, =0x20000168
    // 内存加载操作
    // LDR     R1, =0x200036F8
    // 内存加载操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDRSH   R1, [R1,R2]
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R1
    // LDR     R1, =0x200036F6
    // 内存加载操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDRSH   R1, [R1,R3]
    // ADDS    R1, R2, R1
    // 算术运算
    // LDRB    R0, [R0,R1]
    // 内存加载操作
    // LDR     R1, =0x20003574
    // 内存加载操作
    // STRB    R0, [R1,#8]
    // 内存存储操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011F7C
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x18
    // R0 = 0x18;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011F90
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // LDR     R0, =0x20003574
    // 内存加载操作
    // LDRB    R0, [R0,#8]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_74758
    // 条件跳转
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_7476A
    // 条件跳转
    // B       loc_7477A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74798
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_74798(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x20003738
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_747AE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003738
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000373A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_747B0
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_747b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x20003738
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_747C6
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003738
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000373A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_747D0
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_747d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x20003738
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_747E6
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003738
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000373A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74810
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_74810(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_24= -0x24
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74938
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_74938(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200036EE;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
    // PUSH    {R5-R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x200036EE
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_8]
    // 内存存储操作
    // ADD     R3, SP, #0x10+var_C
    // 算术运算
    // LDR     R2, =0x200036FA
    // 内存加载操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // ADD     R0, SP, #0x10+var_8
    // 算术运算
    // BL      sub_73638
    // 调用函数: sub_73638();
    // LDR     R0, [SP,#0x10+var_8]
    // 内存加载操作
    // MOVS    R1, #0x10000
    // R1 = 0x10000;
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_74966
    // LDR     R0, =0xFFFF
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_8]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74A14
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_74a14(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_24= -0x24
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74B24
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_74b24(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200031A8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x3FFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
    // PUSH    {R5-R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x200031A8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_8]
    // 内存存储操作
    // ADD     R3, SP, #0x10+var_C
    // 算术运算
    // LDR     R2, =0x200036FA
    // 内存加载操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // ADD     R0, SP, #0x10+var_8
    // 算术运算
    // BL      sub_73638
    // 调用函数: sub_73638();
    // LDR     R0, [SP,#0x10+var_8]
    // 内存加载操作
    // MOVS    R1, #0x400000
    // R1 = 0x400000;
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_74B52
    // LDR     R0, =0x3FFFFF
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_8]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74BF8
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_74bf8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74D3C
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_74d3c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200031B2;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {R2-R4,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x200031B2
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDRSH   R0, [R0,R1]
    // CMP     R0, #0
    // 比较操作
    // BMI     loc_74D64
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // LDR     R0, =0x200031B2
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDRSH   R0, [R0,R1]
    // MOVS    R1, #0x3E8
    // R1 = 0x3E8;
    // ADDS    R0, R0, R1
    // 算术运算
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // B       loc_74D76
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74E60
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_74e60(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_74F58
 * @note 指令数: 23, 标签数: 0
 */
void precise_func_74f58(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200031B5;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x200031B5
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // ADD     R3, SP, #0x10+var_C
    // 算术运算
    // LDR     R2, =0x200036FA
    // 内存加载操作
    // LDR     R0, =0x200036FA
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // MOV     R0, SP
    // BL      sub_73638
    // 调用函数: sub_73638();
    // LDR     R0, [SP,#0x10+var_10]
    // 内存加载操作
    // CMP     R0, #0x80
    // 比较操作
    // BCC     loc_74F84
    // MOVS    R0, #0x7F
    // R0 = 0x7F;
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75018
 * @note 指令数: 60, 标签数: 0
 */
void precise_func_75018(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011F40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000369C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20000108;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x10
    // 算术运算
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R2, #0
    // R2 = 0;
    // STM     R0!, {R1,R2}
    // SUBS    R0, #8
    // 算术运算
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003739
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20000108
    // 内存加载操作
    // LDR     R1, =0x200036F8
    // 内存加载操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDRSH   R1, [R1,R2]
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R1
    // LDR     R1, =0x200036F6
    // 内存加载操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDRSH   R1, [R1,R3]
    // ADDS    R1, R2, R1
    // 算术运算
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R1, R2
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // LDR     R1, =0x2000369C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011FA4
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x1C
    // R0 = 0x1C;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011F40
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R4, #6
    // R4 = 6;
    // MOVS    R5, #0
    // R5 = 0;
    // ADD     R6, SP, #0x20+var_1C
    // 算术运算
    // MOVS    R2, R5
    // MOVS    R1, R4
    // MOVS    R0, R6
    // BL      sub_76820
    // 调用函数: sub_76820();
    // LDR     R0, =0x2000369C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_75098
    // 条件跳转
    // LDR     R0, =0x2000369C
    // 内存加载操作
    // LDR     R1, [R0]
    // 内存加载操作
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // BL      sub_7693E
    // 调用函数: sub_7693E();
    // B       loc_750A8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_750C6
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_750c6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x20003738
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_750DC
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003738
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000373A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_750EC
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_750ec(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_24= -0x24
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75210
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_75210(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003614;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
    // PUSH    {R5-R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [SP,#0x10+var_8]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRB    R0, [R1,#0x10+var_C]
    // 内存存储操作
    // LDR     R0, =0x20003614
    // 内存加载操作
    // LDR     R1, =0x200036FA
    // 内存加载操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDRSH   R1, [R1,R2]
    // SUBS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_8]
    // 内存存储操作
    // LDR     R0, [SP,#0x10+var_8]
    // 内存加载操作
    // CMP     R0, #0xA
    // 比较操作
    // BCC     loc_75234
    // MOVS    R0, #9
    // R0 = 9;
    // STR     R0, [SP,#0x10+var_8]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75304
 * @note 指令数: 38, 标签数: 0
 */
void precise_func_75304(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011E78;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011FE0;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011E78
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x20 ; ' '
    // R0 = 0x20;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011FE0
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // LDR     R0, =0x20003735
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_7535A
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #1
    // R3 = 1;
    // ADR     R2, dword_75550
    // MOVS    R1, #4
    // R1 = 4;
    // MOVS    R0, #0x38 ; '8'
    // R0 = 0x38;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADR     R2, dword_75554
    // MOVS    R1, #4
    // R1 = 4;
    // MOVS    R0, #0x78 ; 'x'
    // R0 = 0x78;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // B       loc_7537A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75394
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_75394(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_7646C
    // 调用函数: sub_7646C();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_753A6
    // 条件跳转
    // BL      sub_764D0
    // 调用函数: sub_764D0();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_753B2
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75428
 * @note 指令数: 38, 标签数: 0
 */
void precise_func_75428(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011C04;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011CE0;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003737;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011CE0
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #8
    // R0 = 8;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011C04
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // LDR     R0, =0x20003737
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_7547E
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #1
    // R3 = 1;
    // ADR     R2, dword_75550
    // MOVS    R1, #4
    // R1 = 4;
    // MOVS    R0, #0x38 ; '8'
    // R0 = 0x38;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#8+var_8]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADR     R2, dword_75554
    // MOVS    R1, #4
    // R1 = 4;
    // MOVS    R0, #0x78 ; 'x'
    // R0 = 0x78;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // B       loc_7549E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_754B8
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_754b8(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_7646C
    // 调用函数: sub_7646C();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_754CA
    // 条件跳转
    // BL      sub_764D0
    // 调用函数: sub_764D0();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_754D6
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7557C
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_7557c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8012300;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x80116F0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x1C
    // 算术运算
    // ADD     R0, SP, #0x30+var_2C
    // 算术运算
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // BL      sub_76870
    // 调用函数: sub_76870();
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x30+var_30]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8012300
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x34 ; '4'
    // R0 = 0x34;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // LDR     R0, =0x20003740
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_755B8
    // 条件跳转
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x80116F0
    // 内存加载操作
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76B68
    // 调用函数: sub_76B68();
    // B       loc_755E2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_758C8
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_758c8(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_76502
    // 调用函数: sub_76502();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_758DA
    // 条件跳转
    // BL      sub_7649E
    // 调用函数: sub_7649E();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_758F0
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75920
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_75920(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // STR     R1, [R1,R1]
    // 内存存储操作
    // MOVS    R2, R7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_759C0
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_759c0(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_7646C
    // 调用函数: sub_7646C();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_759D2
    // 条件跳转
    // BL      sub_764D0
    // 调用函数: sub_764D0();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_759DE
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75A40
 * @note 指令数: 47, 标签数: 0
 */
void precise_func_75a40(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE1;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000373D;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003580;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036B0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R1-R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036F6
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036F8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000373D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0xA
    // R0 = 0xA;
    // STR     R0, [SP,#0x20+var_18]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [SP,#0x20+var_1C]
    // 内存存储操作
    // LDR     R4, =0x20003580
    // 内存加载操作
    // LDR     R2, [SP,#0x20+var_1C]
    // 内存加载操作
    // LDR     R1, [SP,#0x20+var_18]
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000373F
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x80121AC
    // 内存加载操作
    // LDR     R1, =0x200036B0
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0xE1
    // R0 = 0xE1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R4, =0x200032C0
    // 内存加载操作
    // MOVS    R2, R6
    // LDR     R1, [SP,#0x20+var_20]
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R5, #0x18
    // R5 = 0x18;
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R7, =0x20003508
    // 内存加载操作
    // MOVS    R2, R4
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // LDR     R1, =0x8012390
    // 内存加载操作
    // LDR     R0, =0x20003508
    // 内存加载操作
    // BL      sub_78944
    // 调用函数: sub_78944();
    // POP     {R0-R2,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75AE0
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_75ae0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80116F0;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036B0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x24
    // 算术运算
    // LDR     R0, =0x200036B0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // CMP     R0, #5
    // 比较操作
    // BLT     loc_75B3A
    // 条件跳转
    // LDR     R0, =0x200036F8
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDRSH   R0, [R0,R1]
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_75B06
    // 条件跳转
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x80116F0
    // 内存加载操作
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76B68
    // 调用函数: sub_76B68();
    // B       loc_75B40
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75CFC
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_75cfc(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_75E3C
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_75e3c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // BL      sub_765D0
    // 调用函数: sub_765D0();
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_75E4C
    // 条件跳转
    // B       locret_762B0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_762E4
 * @note 指令数: 10, 标签数: 1
 */
void precise_func_762e4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // TST     R2, R2
    // 比较操作
    // BEQ     locret_762F8
    // 条件跳转
    // LSLS    R3, R1, #0x1E
    // BEQ     loc_762FA
    // 条件跳转
    // LDRB    R3, [R1]
    // 内存加载操作
    // ADDS    R1, R1, #1
    // 算术运算
    // STRB    R3, [R0]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // SUBS    R2, R2, #1
    // 算术运算
    // BHI     loc_762E8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7630C
 * @note 指令数: 7, 标签数: 1
 */
void precise_func_7630c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6}
    // 栈操作
    // SUBS    R2, #0x10
    // 算术运算
    // BCC     loc_7631A
    // LDM     R1!, {R3-R6}
    // SUBS    R2, #0x10
    // 算术运算
    // STM     R0!, {R3-R6}
    // BCS     loc_76312
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76340
 * @note 指令数: 5, 标签数: 1
 */
void precise_func_76340(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // B       loc_7634A
    // 无条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // ADDS    R1, R1, #1
    // 算术运算
    // SUBS    R2, R2, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76398
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_76398(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R3, R0
    // ORRS    R3, R1
    // BMI     loc_7636C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7639E
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_7639e(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0
    // R2 = 0;
    // LSRS    R3, R0, #8
    // CMP     R3, R1
    // 比较操作
    // BCS     loc_763BE
    // LSRS    R3, R0, #4
    // CMP     R3, R1
    // 比较操作
    // BCS     loc_763E0
    // LSRS    R3, R0, #1
    // CMP     R3, R1
    // 比较操作
    // BCS     loc_76410
    // SUBS    R1, R0, R1
    // 算术运算
    // BCS     loc_763B8
    // MOV     R1, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7644C
 * @note 指令数: 11, 标签数: 0
 */
uint32_t precise_func_7644c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDRB    R3, [R0,#3]
    // 内存加载操作
    // LDRB    R2, [R0,#2]
    // 内存加载操作
    // LDRB    R1, [R0,#1]
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LSLS    R3, R3, #8
    // ORRS    R2, R3
    // LSLS    R1, R1, #8
    // LSLS    R2, R2, #0x10
    // ORRS    R0, R1
    // ORRS    R0, R2
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76462
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_76462(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, [R0]
    // 内存加载操作
    // ADDS    R3, R2, #1
    // 算术运算
    // STR     R3, [R0]
    // 内存存储操作
    // STRB    R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7646C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7646c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000371F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000371E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x2000371E
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_76492
    // 条件跳转
    // LDR     R0, =0x2000371F
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_76492
    // 条件跳转
    // BL      sub_772E8
    // 调用函数: sub_772E8();
    // BL      sub_7732A
    // 调用函数: sub_7732A();
    // LDR     R0, =0x2000371E
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000371F
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_7649C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7649E
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7649e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003722;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003721;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20003721
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_764C4
    // 条件跳转
    // LDR     R0, =0x20003722
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_764C4
    // 条件跳转
    // BL      sub_772E8
    // 调用函数: sub_772E8();
    // BL      sub_7732A
    // 调用函数: sub_7732A();
    // LDR     R0, =0x20003721
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20003722
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_764CE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_764D0
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_764d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003725;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003724;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20003724
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_764F6
    // 条件跳转
    // LDR     R0, =0x20003725
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_764F6
    // 条件跳转
    // BL      sub_772E8
    // 调用函数: sub_772E8();
    // BL      sub_7732A
    // 调用函数: sub_7732A();
    // LDR     R0, =0x20003724
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20003725
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_76500
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76502
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_76502(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003727;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003728;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20003727
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_76528
    // 条件跳转
    // LDR     R0, =0x20003728
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_76528
    // 条件跳转
    // BL      sub_772E8
    // 调用函数: sub_772E8();
    // BL      sub_7732A
    // 调用函数: sub_7732A();
    // LDR     R0, =0x20003727
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20003728
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_76532
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76534
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_76534(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000372E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000372D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x2000372D
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_7655A
    // 条件跳转
    // LDR     R0, =0x2000372E
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7655A
    // 条件跳转
    // BL      sub_772E8
    // 调用函数: sub_772E8();
    // BL      sub_7732A
    // 调用函数: sub_7732A();
    // LDR     R0, =0x2000372D
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000372E
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_76564
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76566
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_76566(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003730;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20003730
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7656C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7656c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000372A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000372B;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x2000372A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_76592
    // 条件跳转
    // LDR     R0, =0x2000372B
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_76592
    // 条件跳转
    // BL      sub_772E8
    // 调用函数: sub_772E8();
    // BL      sub_7732A
    // 调用函数: sub_7732A();
    // LDR     R0, =0x2000372A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000372B
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_7659C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7659E
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7659e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200035A4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_79A72
    // 调用函数: sub_79A72();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x200035A4
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_765D0
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_765d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200035A4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x200035A4
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_765DC
    // 条件跳转
    // B       locret_767B8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76820
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_76820(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LSLS    R2, R2, #0x18
    // LSRS    R3, R2, #8
    // ORRS    R2, R3
    // LSRS    R3, R2, #0x10
    // ORRS    R2, R3
    // NOP
    // TST     R1, R1
    // 比较操作
    // BEQ     locret_7683C
    // 条件跳转
}

