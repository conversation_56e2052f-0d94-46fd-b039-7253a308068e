// 精确转换批次 8 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21B58
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_21b58(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_17714
    // 调用函数: sub_17714();
    // BL      sub_244F8
    // 调用函数: sub_244F8();
    // BL      sub_1E8A8
    // 调用函数: sub_1E8A8();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_21200
    // 调用函数: sub_21200();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_237FC
    // 调用函数: sub_237FC();
    // BL      sub_215D6
    // 调用函数: sub_215D6();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // BL      sub_21D9C
    // 调用函数: sub_21D9C();
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21B90
 * @note 指令数: 21, 标签数: 1
 */
void precise_func_21b90(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_21ABA
    // 调用函数: sub_21ABA();
    // BL      sub_21B24
    // 调用函数: sub_21B24();
    // BL      sub_1CEA2
    // 调用函数: sub_1CEA2();
    // BL      sub_164D6
    // 调用函数: sub_164D6();
    // BL      sub_21B48
    // 调用函数: sub_21B48();
    // BL      sub_1CEA2
    // 调用函数: sub_1CEA2();
    // BL      sub_164D6
    // 调用函数: sub_164D6();
    // BL      sub_21B24
    // 调用函数: sub_21B24();
    // BL      sub_1CEA2
    // 调用函数: sub_1CEA2();
    // BL      sub_164D6
    // 调用函数: sub_164D6();
    // BL      sub_21B48
    // 调用函数: sub_21B48();
    // BL      sub_1CEA2
    // 调用函数: sub_1CEA2();
    // BL      sub_164D6
    // 调用函数: sub_164D6();
    // BL      sub_21B24
    // 调用函数: sub_21B24();
    // BL      sub_1CEA2
    // 调用函数: sub_1CEA2();
    // BL      sub_164D6
    // 调用函数: sub_164D6();
    // BL      sub_21B58
    // 调用函数: sub_21B58();
    // BL      sub_1CEA2
    // 调用函数: sub_1CEA2();
    // BL      sub_164D6
    // 调用函数: sub_164D6();
    // B       loc_21B96
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21BE0
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_21be0(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_17D20
    // 调用函数: sub_17D20();
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_17E78
    // 调用函数: sub_17E78();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21C4C
 * @note 指令数: 24, 标签数: 1
 */
void precise_func_21c4c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015988;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015A88;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R2, R0
    // MOVS    R0, #0xFF
    // R0 = 0xFF;
    // MOVS    R3, #0xFF
    // R3 = 0xFF;
    // MOVS    R5, R1
    // SUBS    R1, R5, #1
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_21C80
    // 条件跳转
    // UXTB    R3, R3
    // 数据扩展操作
    // LDRB    R5, [R2]
    // 内存加载操作
    // EORS    R5, R3
    // MOVS    R4, R5
    // ADDS    R2, R2, #1
    // 算术运算
    // LDR.W   R5, =0x8015988
    // 内存加载操作
    // UXTH    R4, R4
    // 数据扩展操作
    // LDRB    R5, [R4,R5]
    // 内存加载操作
    // EORS    R5, R0
    // MOVS    R3, R5
    // LDR.W   R5, =0x8015A88
    // 内存加载操作
    // UXTH    R4, R4
    // 数据扩展操作
    // LDRB    R5, [R4,R5]
    // 内存加载操作
    // MOVS    R0, R5
    // B       loc_21C54
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21C8E
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_21c8e(uint8_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x37;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0xA
    // 比较操作
    // BLT     loc_21C9A
    // 条件跳转
    // ADDS    R0, #0x37 ; '7'
    // 算术运算
    // UXTB    R0, R0
    // 数据扩展操作
    // B       locret_21C9E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21CA0
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_21ca0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #4
    // BL      sub_21C8E
    // 调用函数: sub_21C8E();
    // STRB    R0, [R4]
    // 内存存储操作
    // ANDS.W  R0, R5, #0xF
    // BL      sub_21C8E
    // 调用函数: sub_21C8E();
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#2]
    // 内存存储操作
    // POP     {R0,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21CC2
 * @note 指令数: 11, 标签数: 0
 */
uint32_t precise_func_21cc2(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R1, R2
    // UXTB    R1, R1
    // 数据扩展操作
    // LSRS    R1, R1, #4
    // MOVS    R3, #0xA
    // R3 = 0xA;
    // SMULBB.W R1, R1, R3
    // MOVS    R0, R1
    // ANDS.W  R1, R2, #0xF
    // ADDS    R0, R1, R0
    // 算术运算
    // UXTB    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21CDC
 * @note 指令数: 13, 标签数: 0
 */
uint32_t precise_func_21cdc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R2, #0xA
    // R2 = 0xA;
    // SDIV.W  R2, R1, R2
    // LSLS    R2, R2, #4
    // MOVS    R0, R2
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R2, #0xA
    // R2 = 0xA;
    // UXTAB.W R0, R1, R0
    // SDIV.W  R3, R1, R2
    // MLS.W   R0, R2, R3, R0
    // UXTB    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21CFE
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_21cfe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80167CC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTH    R4, R4
    // 数据扩展操作
    // MOV.W   R0, #0x3E8
    // SDIV.W  R0, R4, R0
    // MOVS    R6, R0
    // UXTH    R4, R4
    // 数据扩展操作
    // UXTH    R6, R6
    // 数据扩展操作
    // MOV.W   R0, #0x3E8
    // MLS.W   R0, R0, R6, R4
    // MOVS    R1, #0x64 ; 'd'
    // R1 = 0x64;
    // SDIV.W  R0, R0, R1
    // MOVS    R7, R0
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R0, #0x64 ; 'd'
    // R0 = 0x64;
    // SDIV.W  R1, R4, R0
    // MLS.W   R0, R0, R1, R4
    // MOV     R8, R0
    // UXTH.W  R8, R8
    // STR.W   R8, [SP,#0x20+var_20]
    // 内存存储操作
    // UXTH    R7, R7
    // 数据扩展操作
    // MOVS    R3, R7
    // UXTH    R6, R6
    // 数据扩展操作
    // MOVS    R2, R6
    // LDR     R1, =0x80167CC
    // 内存加载操作
    // MOVS    R0, R5
    // BL      sub_23140
    // 调用函数: sub_23140();
    // POP.W   {R0,R1,R4-R8,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21D60
 * @note 指令数: 7, 标签数: 0
 */
float precise_func_21d60(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x7FFFFFFF;

    // 局部变量
    float result = 0.0f;
    uint32_t temp = 0;

    // 汇编指令转换
    // var_4= -4
    // SUB     SP, SP, #4
    // 算术运算
    // MOVS    R0, #0x7FFFFFFF
    // R0 = 0x7FFFFFFF;
    // STR     R0, [SP,#4+var_4]
    // 内存存储操作
    // FLDS    S0, [SP,#4+var_4]
    // 浮点数内存操作
    // ADD     SP, SP, #4
    // 算术运算
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21D70
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_21d70(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080C4;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008147;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007EC8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0x1E
    // R0 = 0x1E;
    // LDR     R1, =0x200080C4
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008147
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007EC8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007EC8
    // 内存加载操作
    // BL      sub_164A4
    // 调用函数: sub_164A4();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21D8E
 * @note 指令数: 7, 标签数: 0
 */
uint32_t precise_func_21d8e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008147;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007EC8;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007EC8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008147
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21D9C
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_21d9c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008147;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080C4;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007EC8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20008147
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_21DC0
    // 条件跳转
    // LDR     R0, =0x200080C4
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20007EC8
    // 内存加载操作
    // LDRH    R1, [R1]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BCS     loc_21DB8
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20008147
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_21DC6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21DD4
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_21dd4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_21DE6
    // 条件跳转
    // LDR     R0, [R1,#0xC]
    // 内存加载操作
    // MOVS    R2, R0
    // LDR     R0, [R1,#0x10]
    // 内存加载操作
    // MOVS    R3, R0
    // SUBS    R0, R2, R3
    // 算术运算
    // B       locret_21DE8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21DEA
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_21dea(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_21E06
    // 条件跳转
    // MOVS    R0, R4
    // BL      sub_21DD4
    // 调用函数: sub_21DD4();
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_21E02
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_21E08
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21E16
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_21e16(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_21E30
    // 条件跳转
    // MOVS    R0, R4
    // BL      sub_21DD4
    // 调用函数: sub_21DD4();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_21E2C
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_21E32
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21E40
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_21e40(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R0, R4
    // BL      sub_21E16
    // 调用函数: sub_21E16();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_21E66
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R4,#0x10]
    // 内存加载操作
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // UDIV.W  R2, R0, R1
    // MLS.W   R0, R1, R2, R0
    // LDR     R1, [R4,#4]
    // 内存加载操作
    // MLA.W   R5, R1, R0, R5
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21E6A
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_21e6a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R0, R4
    // BL      sub_21E16
    // 调用函数: sub_21E16();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_21E92
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R4,#0xC]
    // 内存加载操作
    // SUBS    R0, R0, #1
    // 算术运算
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // UDIV.W  R2, R0, R1
    // MLS.W   R0, R1, R2, R0
    // LDR     R1, [R4,#4]
    // 内存加载操作
    // MLA.W   R5, R1, R0, R5
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21E96
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_21e96(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R7, #0
    // R7 = 0;
    // MOVS    R0, R4
    // BL      sub_21E16
    // 调用函数: sub_21E16();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_21EE8
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R7, R0
    // LDR     R0, [R4,#0x10]
    // 内存加载操作
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // UDIV.W  R2, R0, R1
    // MLS.W   R0, R1, R2, R0
    // LDR     R1, [R4,#4]
    // 内存加载操作
    // MLA.W   R7, R1, R0, R7
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_21EDE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R8, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21EF0
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_21ef0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R7, #0
    // R7 = 0;
    // MOVS    R0, R4
    // BL      sub_21E16
    // 调用函数: sub_21E16();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_21F44
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R7, R0
    // LDR     R0, [R4,#0xC]
    // 内存加载操作
    // SUBS    R0, R0, #1
    // 算术运算
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // UDIV.W  R2, R0, R1
    // MLS.W   R0, R1, R2, R0
    // LDR     R1, [R4,#4]
    // 内存加载操作
    // MLA.W   R7, R1, R0, R7
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_21F3A
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R8, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21F4C
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_21f4c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_21F7C
    // 条件跳转
    // MOVS    R0, R4
    // BL      sub_21DEA
    // 调用函数: sub_21DEA();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_21F7C
    // 条件跳转
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R4,#0xC]
    // 内存加载操作
    // LDR     R1, [R4,#8]
    // 内存加载操作
    // UDIV.W  R2, R0, R1
    // MLS.W   R0, R1, R2, R0
    // LDR     R1, [R4,#4]
    // 内存加载操作
    // MLA.W   R5, R1, R0, R5
    // LDR     R0, [R4,#0xC]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // STR     R0, [R4,#0xC]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21F80
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_21f80(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_21F94
    // 条件跳转
    // MOVS    R4, #0
    // R4 = 0;
    // STR     R4, [R0,#0xC]
    // 内存存储操作
    // MOVS    R4, #0
    // R4 = 0;
    // STR     R4, [R0,#0x10]
    // 内存存储操作
    // STR     R1, [R0]
    // 内存存储操作
    // STR     R2, [R0,#4]
    // 内存存储操作
    // STR     R3, [R0,#8]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21F98
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_21f98(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r1= -0xC
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R1-R3}
    // 栈操作
    // ADD     SP, SP, #0xC
    // 算术运算
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21F9E
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_21f9e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r1= -0xC
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R1-R3}
    // 栈操作
    // ADD     SP, SP, #0xC
    // 算术运算
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21FA4
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_21fa4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r1= -0xC
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R1-R3}
    // 栈操作
    // ADD     SP, SP, #0xC
    // 算术运算
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21FAA
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_21faa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r1= -0xC
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R1-R3}
    // 栈操作
    // ADD     SP, SP, #0xC
    // 算术运算
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_21FB0
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_21fb0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_224B0
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_224b0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_2A= -0x2A
    // var_28= -0x28
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22BF4
 * @note 指令数: 57, 标签数: 0
 */
void precise_func_22bf4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x80000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CMP     R4, #0
    // 比较操作
    // BEQ     locret_22C78
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#1]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // BICS.W  R0, R0, #0x10000
    // STR     R0, [R4]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // BICS.W  R0, R0, #0x20000
    // STR     R0, [R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#3]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRH    R0, [R4,#4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRH    R0, [R4,#8]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xA]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xB]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xC]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xD]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R4,#0xE]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [R4,#0x10]
    // 内存存储操作
    // LDRB.W  R0, [R4,#0x30]
    // STRB    R0, [R4,#0x1A]
    // 内存存储操作
    // LDRB.W  R0, [R4,#0x30]
    // STRB    R0, [R4,#0x1B]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // BICS.W  R0, R0, #0x40000
    // STR     R0, [R4]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // BICS.W  R0, R0, #0x80000
    // STR     R0, [R4]
    // 内存存储操作
    // LDR     R0, [R4]
    // 内存加载操作
    // BICS.W  R0, R0, #0x100000
    // STR     R0, [R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [R4,#0x1C]
    // 内存存储操作
    // MOVS    R0, R4
    // LDR     R1, [R4,#0x24]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
    // LDR     R0, [R4]
    // 内存加载操作
    // BICS.W  R0, R0, #0x200000
    // STR     R0, [R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB.W  R0, [R4,#0x28]
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [R4,#0x2C]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22C7A
 * @note 指令数: 51, 标签数: 0
 */
void precise_func_22c7a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x26;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_26= -0x26
    // PUSH.W  {R3-R11,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // MOVS.W  R8, #0
    // MOVS.W  R9, #0
    // MOVS    R0, #0
    // R0 = 0;
    // STRH.W  R0, [SP,#0x28+var_26]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRH.W  R0, [SP,#0x28+var_28]
    // 内存存储操作
    // MOVS.W  R10, #0
    // MOVS.W  R11, #0
    // CMP     R4, #0
    // 比较操作
    // BEQ.W   loc_22E6E
    // CMP     R7, #0
    // 比较操作
    // BEQ.W   loc_22E6E
    // LDRB    R0, [R4]
    // 内存加载操作
    // STRB    R0, [R7]
    // 内存存储操作
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #7
    // STRB    R0, [R7,#2]
    // 内存存储操作
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #2
    // ANDS.W  R0, R0, #1
    // STRB    R0, [R7,#1]
    // 内存存储操作
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // ANDS.W  R0, R0, #3
    // STRB    R0, [R7,#3]
    // 内存存储操作
    // MOVS    R0, #2
    // R0 = 2;
    // MOV     R8, R0
    // LDRB    R0, [R4,#1]
    // 内存加载操作
    // LSLS    R0, R0, #0x1A
    // BPL     loc_22D4A
    // MOV     R1, SP
    // ADDS.W  R0, R8, R4
    // 算术运算
    // BL      sub_24CB4
    // 调用函数: sub_24CB4();
    // ADDS.W  R0, R0, R8
    // 算术运算
    // MOV     R8, R0
    // LDRB.W  R0, [R8,R4]
    // MOV     R10, R0
    // ADDS.W  R8, R8, #1
    // 算术运算
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_22D00
    // 条件跳转
    // LDRH.W  R0, [SP,#0x28+var_28]
    // 内存加载操作
    // STRH    R0, [R5,#8]
    // 内存存储操作
    // STRB.W  R10, [R5,#0xA]
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22E74
 * @note 指令数: 20, 标签数: 0
 */
uint32_t precise_func_22e74(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // UXTB    R1, R1
    // 数据扩展操作
    // UXTB    R2, R2
    // 数据扩展操作
    // EORS.W  R3, R2, R1
    // MOVS    R0, R3
    // LSLS    R3, R0, #2
    // EORS.W  R3, R3, R0,LSL#1
    // EORS.W  R3, R3, R0,LSL#3
    // EORS.W  R3, R3, R0,LSL#4
    // EORS.W  R3, R3, R0,LSL#5
    // EORS.W  R3, R3, R0,LSL#6
    // EORS.W  R3, R3, R0,LSL#7
    // EORS    R0, R3
    // ANDS.W  R3, R0, #0xFE
    // UXTH    R0, R0
    // 数据扩展操作
    // ASRS    R0, R0, #8
    // ANDS.W  R0, R0, #1
    // EORS    R0, R3
    // UXTB    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22EAE
 * @note 指令数: 19, 标签数: 0
 */
uint32_t precise_func_22eae(uint8_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // UXTB    R0, R1
    // 数据扩展操作
    // UXTB    R2, R2
    // 数据扩展操作
    // EORS    R0, R2
    // MOVS    R3, R0
    // UXTH    R1, R1
    // 数据扩展操作
    // LSLS    R0, R3, #8
    // EORS.W  R0, R0, R1,LSR#8
    // EORS.W  R0, R0, R3,LSL#3
    // EORS.W  R1, R0, R3,LSL#12
    // MOVS    R0, R3
    // UXTH    R0, R0
    // 数据扩展操作
    // EORS.W  R0, R1, R0,LSR#4
    // ANDS.W  R1, R3, #0xF
    // EORS    R0, R1
    // ANDS.W  R1, R3, #0xF
    // EORS.W  R0, R0, R1,LSL#7
    // UXTH    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22EE2
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_22ee2(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6}
    // 栈操作
    // MOVS    R2, R0
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #1
    // R0 = 1;
    // LDRB    R5, [R2]
    // 内存加载操作
    // LDRB    R6, [R1]
    // 内存加载操作
    // CMP     R5, R6
    // 比较操作
    // BEQ     loc_22EF8
    // 条件跳转
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R0, R5
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22F7C
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_22f7c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000263;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20000263
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BNE     loc_22F8C
    // 条件跳转
    // BL      sub_24CCC
    // 调用函数: sub_24CCC();
    // B       locret_22F90
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22F98
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_22f98(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     locret_22FB0
    // 条件跳转
    // MOVS    R1, R5
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_24D38
    // 调用函数: sub_24D38();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22FB2
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_22fb2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x17;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_4= -4
    // VPUSH   {S0}
    // LDR     R0, [SP,#4+var_4]
    // 内存加载操作
    // UBFX.W  R0, R0, #0x17, #8
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     loc_22FD4
    // 条件跳转
    // CBZ     R0, loc_22FC4
    // B       loc_22FE2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_22FEC
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_22fec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x180005;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x180003;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x180002;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =dword_180004
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180005
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23034
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_23034(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     locret_2306C
    // 条件跳转
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_2304A
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // B       loc_2304C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23084
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_23084(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2308A
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_2308a(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23090
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_23090(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23096
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_23096(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x82;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80168CC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x834;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x41;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {R2-R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R1, =0x80168CC
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_230FC
    // 调用函数: sub_230FC();
    // MOV     R1, SP
    // ADD     R0, SP, #0x10+var_C
    // 算术运算
    // BL      sub_1C148
    // 调用函数: sub_1C148();
    // LDR     R2, [SP,#0x10+var_10]
    // 内存加载操作
    // ADR     R1, dword_230F0
    // ADDS.W  R0, R4, #0x41 ; 'A'
    // 算术运算
    // BL      sub_23140
    // 调用函数: sub_23140();
    // ADDS.W  R1, R4, #0x82
    // 算术运算
    // MOVW    R0, #0x834
    // R0 = 0x834;
    // BL      sub_21CFE
    // 调用函数: sub_21CFE();
    // POP     {R0,R1,R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_230C4
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_230c4(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008174;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x257;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x25F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // ADDS    R3, R1, R0
    // 算术运算
    // MOVS    R2, R3
    // UXTH    R0, R0
    // 数据扩展操作
    // MOVW    R3, #0x257
    // R3 = 0x257;
    // CMP     R0, R3
    // 比较操作
    // BLT     loc_230EA
    // 条件跳转
    // UXTH    R2, R2
    // 数据扩展操作
    // MOVW    R3, #0x25F
    // R3 = 0x25F;
    // CMP     R2, R3
    // 比较操作
    // BGE     loc_230EA
    // 条件跳转
    // LDR     R3, =0x20008174
    // 内存加载操作
    // LDRB    R3, [R3]
    // 内存加载操作
    // ORRS.W  R3, R3, #4
    // LDR     R4, =0x20008174
    // 内存加载操作
    // STRB    R3, [R4]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_230FC
 * @note 指令数: 8, 标签数: 1
 */
void precise_func_230fc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS.W  R12, R0
    // LSLS    R3, R0, #0x1E
    // BEQ     loc_23112
    // 条件跳转
    // LDRB.W  R3, [R1],#1
    // STRB.W  R3, [R0],#1
    // CBZ     R3, loc_2313C
    // LSLS    R3, R0, #0x1E
    // BNE     loc_23104
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23140
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_23140(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // var_C= -0xC
    // varg_r2= -8
    // varg_r3= -4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23174
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_23174(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x180003;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x180002;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x180006;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180006
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =dword_180004
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_231BC
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_231bc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015E90;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #8
    // 比较操作
    // BGE     loc_231F2
    // 条件跳转
    // LDR     R0, =0x8015E90
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0xC
    // R1 = 0xC;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015E90
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0xC
    // R2 = 0xC;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_18496
    // 调用函数: sub_18496();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_231EC
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_231EE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_231F6
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_231f6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015E90;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x8015E90
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0xC
    // R1 = 0xC;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015E90
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0xC
    // R2 = 0xC;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_18496
    // 调用函数: sub_18496();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_23220
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_23222
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2323C
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_2323c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40001400;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007FE0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x40001400
    // 内存加载操作
    // BL      sub_1838A
    // 调用函数: sub_1838A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_23260
    // 条件跳转
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x40001400
    // 内存加载操作
    // BL      sub_183A2
    // 调用函数: sub_183A2();
    // LDR     R0, =0x20007FE0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_23260
    // 条件跳转
    // LDR     R0, =0x20007FE0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BLX     R0
    // 调用函数: R0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23262
 * @note 指令数: 34, 标签数: 0
 */
void precise_func_23262(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40001400;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2710;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C0005;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x37;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_14= -0x14
    // PUSH    {LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOV     R0, SP
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // BL      sub_25580
    // 调用函数: sub_25580();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x1C0005
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOV     R0, SP
    // BL      sub_17FE6
    // 调用函数: sub_17FE6();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #2
    // R1 = 2;
    // MOVS    R0, #0x37 ; '7'
    // R0 = 0x37;
    // BL      sub_1824C
    // 调用函数: sub_1824C();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x40001400
    // 内存加载操作
    // BL      sub_18362
    // 调用函数: sub_18362();
    // MOVS    R2, #5
    // R2 = 5;
    // LDR     R0, [SP,#0x18+var_14]
    // 内存加载操作
    // MOVW    R1, #0x2710
    // R1 = 0x2710;
    // UDIV.W  R0, R0, R1
    // SUBS    R1, R0, #1
    // 算术运算
    // LDR     R0, =0x40001400
    // 内存加载操作
    // BL      sub_18354
    // 调用函数: sub_18354();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x40001400
    // 内存加载操作
    // BL      sub_18348
    // 调用函数: sub_18348();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x40001400
    // 内存加载操作
    // BL      sub_1836E
    // 调用函数: sub_1836E();
    // ADD     SP, SP, #0x14
    // 算术运算
    // POP     {PC}
    // 栈操作
}

