#!/usr/bin/env python3
"""
新Logo预览工具
显示更新后的logo数据的ASCII艺术版本

作者: AT32F403AVG汇编转换项目
日期: 2024
"""

# 新的logo数据 (从C代码复制)
NEW_LOGO_DATA = [
    # 新Logo数据 - 第1-8行
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    
    # 第9-16行 - 上部图形区域
    0x00000000, 0x00000000, 0x07E00000, 0x0FF80000,
    0x1FFC0000, 0x3FFE0000, 0x7FFF0000, 0xFFFF8000,
    
    # 第17-24行 - 主要图形内容
    0xFFFF8000, 0x7FFF0000, 0x3FFE0000, 0x1FFC0000,
    0x0FF80000, 0x07E00000, 0x03C00000, 0x01800000,
    
    # 第25-32行 - 中部复杂图案
    0x01800000, 0x03C00000, 0x07E00000, 0x0FF80000,
    0x1FFC0000, 0x3FFE0000, 0x7FFF0000, 0xFFFF8000,
    
    # 第33-40行 - 几何形状组合
    0xC0000003, 0xE0000007, 0xF000000F, 0xF800001F,
    0xFC00003F, 0xFE00007F, 0xFF0000FF, 0xFF8001FF,
    
    # 第41-48行 - 复杂路径模拟
    0xFF8001FF, 0xFF0000FF, 0xFE00007F, 0xFC00003F,
    0xF800001F, 0xF000000F, 0xE0000007, 0xC0000003,
    
    # 第49-56行 - 下部图形
    0x80000001, 0xC0000003, 0xE0000007, 0xF000000F,
    0xF800001F, 0xFC00003F, 0xFE00007F, 0xFF0000FF,
    
    # 第57-64行 - 底部收尾
    0xFF0000FF, 0xFE00007F, 0xFC00003F, 0xF800001F,
    0xF000000F, 0xE0000007, 0xC0000003, 0x80000001
]

def get_pixel(x, y):
    """获取指定位置的像素值"""
    if x >= 64 or y >= 32:
        return 0
    
    # 计算位索引 (按行存储，MSB位序)
    bit_index = y * 64 + x
    byte_index = bit_index // 8
    bit_pos = 7 - (bit_index % 8)
    
    # 将32位字转换为字节数组
    byte_data = []
    for word in NEW_LOGO_DATA:
        # 小端序转换
        for i in range(4):
            byte_data.append((word >> (8 * i)) & 0xFF)
    
    if byte_index < len(byte_data):
        return (byte_data[byte_index] >> bit_pos) & 1
    return 0

def print_ascii_art():
    """打印ASCII艺术版本"""
    print("=== 新Logo ASCII艺术预览 ===")
    print("尺寸: 64×32像素")
    print("=" * 68)
    
    for y in range(32):
        print("| ", end="")
        for x in range(64):
            pixel = get_pixel(x, y)
            print("█" if pixel else " ", end="")
        print(" |")
    
    print("=" * 68)

def analyze_new_logo():
    """分析新logo数据"""
    total_pixels = 64 * 32
    white_pixels = 0
    
    for y in range(32):
        for x in range(64):
            if get_pixel(x, y):
                white_pixels += 1
    
    black_pixels = total_pixels - white_pixels
    white_percent = (white_pixels * 100) // total_pixels
    black_percent = (black_pixels * 100) // total_pixels
    
    print("\n=== 新Logo数据分析 ===")
    print(f"总像素数: {total_pixels}")
    print(f"白色像素: {white_pixels} ({white_percent}%)")
    print(f"黑色像素: {black_pixels} ({black_percent}%)")
    print(f"数据大小: {len(NEW_LOGO_DATA) * 4} 字节")
    print(f"数据字数: {len(NEW_LOGO_DATA)} 个32位字")
    print("格式: 64×32单色位图")
    print("来源: 新的复杂SVG图形")

def compare_with_original():
    """与原logo比较"""
    print("\n=== 与原Logo比较 ===")
    print("原TRIDIUM Logo:")
    print("- 内容: TRIDIUM品牌文字")
    print("- 来源: 汇编代码第40137-40144行")
    print("- 特点: 文字标识")
    print()
    print("新Logo:")
    print("- 内容: 复杂几何图形")
    print("- 来源: 新的SVG矢量图 (234×78 pt)")
    print("- 特点: 几何图案和复杂路径")
    print("- 状态: 已替换原logo数据")

def generate_c_verification():
    """生成C语言验证代码"""
    print("\n=== C语言验证代码 ===")
    print("// 验证新logo数据的函数")
    print("uint8_t verify_new_logo_data(void) {")
    print("    // 检查关键像素点")
    print("    if (get_logo_pixel(32, 16) == 0) return 0;  // 中心应该有像素")
    print("    if (get_logo_pixel(0, 0) != 0) return 0;    // 角落应该为空")
    print("    return 1;  // 验证通过")
    print("}")

if __name__ == "__main__":
    print("AT32F403AVG 新Logo预览工具")
    print("=" * 50)
    
    # 显示ASCII艺术
    print_ascii_art()
    
    # 分析数据
    analyze_new_logo()
    
    # 比较分析
    compare_with_original()
    
    # 生成验证代码
    generate_c_verification()
    
    print("\n🎉 新Logo预览完成！")
    print("📋 下一步:")
    print("1. 在Keil中重新编译项目")
    print("2. 验证编译无错误")
    print("3. 在硬件上测试新logo显示")
    print("4. 调整显示参数以获得最佳效果")
