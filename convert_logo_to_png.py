#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AT32F403AVG Logo数据转PNG工具
将汇编代码中的logo数据转换为PNG图像文件

作者: AI Assistant
日期: 2024
"""

import struct
import numpy as np
from PIL import Image
import os

def extract_logo_data():
    """
    提取logo数据 (从汇编代码第40137-40144行)
    返回256字节的原始数据
    """
    # Logo数据 (从汇编代码转换而来)
    logo_data = [
        # 汇编代码第40137行
        0x646F0E60, 0x3733366A, 0x584B0000, 0x36312D4D, 0xE3010050, 0xF1120834, 0x2212011F, 0x45055,
        
        # 汇编代码第40138行
        0x1020800, 0x6100A0B, 0x611B0100, 0x6E696D64, 0x31025052, 0x59343332, 0x807C4BC2, 0x167A808,
        
        # 汇编代码第40139行
        0x4001A408, 0x79800000, 0x5214B412, 0x15214A0, 0x14C01217, 0xA20390, 0xA0100010, 0xAC0A07F0,
        
        # 汇编代码第40140行
        0x80080168, 0x2000025, 0xB412F2B4, 0xBC12F224, 0xC412F224, 0xF4022024, 0x1F20006A, 0x8305C812,
        
        # 汇编代码第40141行
        0x210C6BBC, 0x433C4301, 0x80171510, 0x4B3C0001, 0x1201610C, 0xEC2160FC, 0x21E8FC12, 0x4031064,
        
        # 汇编代码第40142行
        0x3080169, 0x80C1208, 0x86273A1, 0x28684228, 0x28681443, 0x66D00310, 0x8040801, 0x1208E012,
        
        # 汇编代码第40143行
        0x20120805, 0x8061218, 0x12082C12, 0xF0120807, 0xC3492118, 0x43506614, 0x21506914, 0x43502120,
        
        # 汇编代码第40144行
        0x435064F4, 0x43506700, 0x43506710, 0x43506510, 0x935064B8, 0x33486720, 0xB7A12, 0xFF010808
    ]
    
    # 将32位字转换为字节数组
    byte_data = []
    for word in logo_data:
        # 小端序转换 (ARM处理器通常使用小端序)
        bytes_le = struct.pack('<I', word)
        byte_data.extend(bytes_le)
    
    return bytes(byte_data)

def convert_to_bitmap(data, width, height, bit_order='lsb', invert=False):
    """
    将字节数据转换为位图
    
    参数:
    - data: 原始字节数据
    - width: 图像宽度
    - height: 图像高度  
    - bit_order: 位序 ('lsb' 或 'msb')
    - invert: 是否反转颜色
    
    返回:
    - numpy数组表示的位图
    """
    if len(data) * 8 < width * height:
        print(f"警告: 数据不足，需要{width * height}位，但只有{len(data) * 8}位")
        return None
    
    bitmap = np.zeros((height, width), dtype=np.uint8)
    
    bit_index = 0
    for y in range(height):
        for x in range(width):
            if bit_index >= len(data) * 8:
                break
                
            byte_index = bit_index // 8
            bit_pos = bit_index % 8
            
            if bit_order == 'lsb':
                # LSB优先 (最低位在前)
                pixel = (data[byte_index] >> bit_pos) & 1
            else:
                # MSB优先 (最高位在前)
                pixel = (data[byte_index] >> (7 - bit_pos)) & 1
            
            if invert:
                pixel = 1 - pixel
                
            bitmap[y, x] = pixel * 255  # 0->黑色, 1->白色
            bit_index += 1
    
    return bitmap

def try_different_formats(data):
    """
    尝试不同的图像格式和参数组合
    """
    formats = [
        # (宽度, 高度, 位序, 反转, 描述)
        (64, 32, 'lsb', False, '64x32_LSB_正常'),
        (64, 32, 'lsb', True, '64x32_LSB_反转'),
        (64, 32, 'msb', False, '64x32_MSB_正常'),
        (64, 32, 'msb', True, '64x32_MSB_反转'),
        (32, 64, 'lsb', False, '32x64_LSB_正常'),
        (32, 64, 'lsb', True, '32x64_LSB_反转'),
        (32, 64, 'msb', False, '32x64_MSB_正常'),
        (32, 64, 'msb', True, '32x64_MSB_反转'),
        (128, 16, 'lsb', False, '128x16_LSB_正常'),
        (128, 16, 'lsb', True, '128x16_LSB_反转'),
        (16, 128, 'lsb', False, '16x128_LSB_正常'),
        (16, 128, 'lsb', True, '16x128_LSB_反转'),
    ]
    
    results = []
    
    for width, height, bit_order, invert, desc in formats:
        print(f"尝试格式: {desc} ({width}x{height})")
        
        bitmap = convert_to_bitmap(data, width, height, bit_order, invert)
        if bitmap is not None:
            results.append((bitmap, desc, width, height))
    
    return results

def save_images(results, base_filename="logo"):
    """
    保存所有生成的图像
    """
    saved_files = []
    
    for bitmap, desc, width, height in results:
        # 创建PIL图像
        img = Image.fromarray(bitmap, mode='L')  # 'L' = 灰度模式
        
        # 放大图像以便查看 (每个像素放大4倍)
        scale_factor = max(1, 512 // max(width, height))  # 确保图像至少512像素
        new_width = width * scale_factor
        new_height = height * scale_factor
        img_scaled = img.resize((new_width, new_height), Image.NEAREST)
        
        # 保存文件
        filename = f"{base_filename}_{desc}.png"
        img_scaled.save(filename)
        saved_files.append(filename)
        
        print(f"已保存: {filename} ({new_width}x{new_height}, 原始{width}x{height})")
    
    return saved_files

def analyze_data(data):
    """
    分析数据特征
    """
    print("=== 数据分析 ===")
    print(f"数据长度: {len(data)} 字节")
    print(f"总位数: {len(data) * 8} 位")
    
    # 统计0和1的数量
    total_bits = len(data) * 8
    ones = 0
    zeros = 0
    
    for byte in data:
        for i in range(8):
            if (byte >> i) & 1:
                ones += 1
            else:
                zeros += 1
    
    print(f"1的数量: {ones} ({ones/total_bits*100:.1f}%)")
    print(f"0的数量: {zeros} ({zeros/total_bits*100:.1f}%)")
    
    # 显示前几个字节的二进制表示
    print("\n前8个字节的二进制表示:")
    for i in range(min(8, len(data))):
        binary = format(data[i], '08b')
        print(f"字节{i:2d}: 0x{data[i]:02X} = {binary}")

def main():
    """
    主函数
    """
    print("AT32F403AVG Logo数据转PNG工具")
    print("=" * 50)
    
    # 提取logo数据
    print("提取logo数据...")
    data = extract_logo_data()
    
    # 分析数据
    analyze_data(data)
    
    # 尝试不同格式
    print("\n尝试不同的图像格式...")
    results = try_different_formats(data)
    
    # 保存图像
    print(f"\n保存{len(results)}个图像变体...")
    saved_files = save_images(results)
    
    print(f"\n完成! 共生成{len(saved_files)}个PNG文件:")
    for filename in saved_files:
        print(f"  - {filename}")
    
    print("\n建议:")
    print("1. 查看所有生成的PNG文件")
    print("2. 找到最清晰、最有意义的图像")
    print("3. 64x32格式通常是最可能的正确格式")
    print("4. 如果图像看起来颠倒，尝试反转版本")

if __name__ == "__main__":
    try:
        main()
    except ImportError as e:
        print("错误: 缺少必要的Python库")
        print("请安装所需库: pip install pillow numpy")
        print(f"具体错误: {e}")
    except Exception as e:
        print(f"转换过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
