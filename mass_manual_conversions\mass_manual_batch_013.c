// 大规模手工转换批次 13 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_47F28
 * @note 指令数: 21
 */
uint32_t func_47f28(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_400 = (volatile uint32_t *)0x400;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_47F58
 * @note 指令数: 47
 */
uint32_t func_47f58(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_47FB4
 * @note 指令数: 47
 */
void func_47fb4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8015798 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4800E
 * @note 指令数: 23
 */
void func_4800e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_8015798 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_4803A
 * @note 指令数: 38
 */
void func_4803a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8015798 = (volatile uint32_t *)0x8015798;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_4807C
 * @note 指令数: 38
 */
void func_4807c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8015798 = (volatile uint32_t *)0x8015798;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_480BE
 * @note 指令数: 25
 */
void func_480be(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_8015798 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_480EE
 * @note 指令数: 24
 */
void func_480ee(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_8015798 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_4811C
 * @note 指令数: 20
 */
void func_4811c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_8015798 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_48148
 * @note 指令数: 22
 */
void func_48148(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200078AE = (volatile uint32_t *)0x200078AE;
    volatile uint32_t *addr_20007088 = (volatile uint32_t *)0x20007088;
    volatile uint32_t *addr_20007798 = (volatile uint32_t *)0x20007798;
    volatile uint32_t *addr_23 = (volatile uint32_t *)0x23;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4817A
 * @note 指令数: 202
 */
void func_4817a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_200078AE = (volatile uint32_t *)0x200078AE;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20007798 = (volatile uint32_t *)0x20007798;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4830E
 * @note 指令数: 46
 */
void func_4830e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20007798 = (volatile uint32_t *)0x20007798;
    volatile uint32_t *addr_200078AE = (volatile uint32_t *)0x200078AE;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_48368
 * @note 指令数: 16
 */
void func_48368(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20007798 = (volatile uint32_t *)0x20007798;
    volatile uint32_t *addr_200078AE = (volatile uint32_t *)0x200078AE;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_48386
 * @note 指令数: 16
 */
void func_48386(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20007798 = (volatile uint32_t *)0x20007798;
    volatile uint32_t *addr_200078AE = (volatile uint32_t *)0x200078AE;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_483B4
 * @note 指令数: 75
 */
void func_483b4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_80151F0 = (volatile uint32_t *)0x80151F0;
    volatile uint32_t *addr_200000 = (volatile uint32_t *)0x200000;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_48456
 * @note 指令数: 26
 */
void func_48456(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8015D64 = (volatile uint32_t *)0x8015D64;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4848E
 * @note 指令数: 18
 */
uint32_t func_4848e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_8015D68 = (volatile uint32_t *)0x8015D68;
    volatile uint32_t *addr_8015D64 = (volatile uint32_t *)0x8015D64;
    volatile uint32_t *addr_20000 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_484B8
 * @note 指令数: 7
 */
uint32_t func_484b8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8015D64 = (volatile uint32_t *)0x8015D64;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_484D8
 * @note 指令数: 31
 */
void func_484d8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8015674 = (volatile uint32_t *)0x8015674;
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_48516
 * @note 指令数: 170
 */
void func_48516(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_4002101C = (volatile uint32_t *)0x4002101C;
    volatile uint32_t *addr_8015674 = (volatile uint32_t *)0x8015674;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4866A
 * @note 指令数: 29
 */
uint32_t func_4866a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_8015674 = (volatile uint32_t *)0x8015674;
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_486AA
 * @note 指令数: 76
 */
void func_486aa(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_700 = (volatile uint32_t *)0x700;
    volatile uint32_t *addr_1000 = (volatile uint32_t *)0x1000;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_48764
 * @note 指令数: 4
 */
uint32_t func_48764(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20000318 = (volatile uint32_t *)0x20000318;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_48774
 * @note 指令数: 17
 */
uint32_t func_48774(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_48796
 * @note 指令数: 145
 */
void func_48796(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_488BA
 * @note 指令数: 143
 */
void func_488ba(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40004408 = (volatile uint32_t *)0x40004408;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_40004424 = (volatile uint32_t *)0x40004424;
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_48A1C
 * @note 指令数: 278
 */
void func_48a1c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40011C08 = (volatile uint32_t *)0x40011C08;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_40011C18 = (volatile uint32_t *)0x40011C18;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_48C50
 * @note 指令数: 9
 */
uint32_t func_48c50(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_48C9C
 * @note 指令数: 318
 */
void func_48c9c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_4002101C = (volatile uint32_t *)0x4002101C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_48F44
 * @note 指令数: 3
 */
uint32_t func_48f44(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_48F4A
 * @note 指令数: 43
 */
void func_48f4a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_19 = (volatile uint32_t *)0x19;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_48FA0
 * @note 指令数: 21
 */
void func_48fa0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_19 = (volatile uint32_t *)0x19;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_48FCC
 * @note 指令数: 17
 */
void func_48fcc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_48FF8
 * @note 指令数: 25
 */
uint32_t func_48ff8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_49034
 * @note 指令数: 20
 */
void func_49034(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_49060
 * @note 指令数: 17
 */
void func_49060(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_20006810 = (volatile uint32_t *)0x20006810;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_49082
 * @note 指令数: 8
 */
void func_49082(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_49094
 * @note 指令数: 16
 */
uint32_t func_49094(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_38 = (volatile uint32_t *)0x38;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_490B6
 * @note 指令数: 16
 */
uint32_t func_490b6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_38 = (volatile uint32_t *)0x38;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_490D8
 * @note 指令数: 7
 */
uint32_t func_490d8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8014AF8 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_490E8
 * @note 指令数: 57
 */
void func_490e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_FFFFB7FF = (volatile uint32_t *)0xFFFFB7FF;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_2A = (volatile uint32_t *)0x2A;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4917C
 * @note 指令数: 26
 */
void func_4917c(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_491B0
 * @note 指令数: 28
 */
void func_491b0(uint32_t param0)
{
    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_491E8
 * @note 指令数: 11
 */
void func_491e8(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_491FC
 * @note 指令数: 20
 */
void func_491fc(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_49222
 * @note 指令数: 18
 */
void func_49222(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_49244
 * @note 指令数: 8
 */
uint32_t func_49244(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_49254
 * @note 指令数: 258
 */
void func_49254(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_7FF = (volatile uint32_t *)0x7FF;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_49540
 * @note 指令数: 2
 */
uint32_t func_49540(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_49544
 * @note 指令数: 30
 */
void func_49544(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_80155FC = (volatile uint32_t *)0x80155FC;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

