// 精确转换批次 13 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47F28
 * @note 指令数: 17, 标签数: 0
 */
uint32_t precise_func_47f28(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x400;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // ADDS    R2, R1, R1
    // 算术运算
    // BCS     loc_47F52
    // LSRS    R0, R0, #0x15
    // LSLS    R1, R1, #0xB
    // ORRS    R0, R1
    // MOVS    R1, #0x80000000
    // R1 = 0x80000000;
    // ORRS    R0, R1
    // LSRS    R2, R2, #0x15
    // MOVS    R1, #0x400
    // R1 = 0x400;
    // SUBS    R2, R2, R1
    // 算术运算
    // ADDS    R2, R2, #1
    // 算术运算
    // BMI     loc_47F52
    // NEGS    R2, R2
    // ADDS    R2, #0x1F
    // 算术运算
    // BMI     loc_47F4E
    // LSRS    R0, R2
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47F58
 * @note 指令数: 10, 标签数: 1
 */
void precise_func_47f58(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // TST     R2, R2
    // 比较操作
    // BEQ     locret_47F6C
    // 条件跳转
    // LSLS    R3, R1, #0x1E
    // BEQ     loc_47F6E
    // 条件跳转
    // LDRB    R3, [R1]
    // 内存加载操作
    // ADDS    R1, R1, #1
    // 算术运算
    // STRB    R3, [R0]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // SUBS    R2, R2, #1
    // 算术运算
    // BHI     loc_47F5C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47FB4
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_47fb4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R6, R0
    // MOVS    R7, R2
    // BL      sub_49540
    // 调用函数: sub_49540();
    // MOV     R1, SP
    // STRH    R0, [R1,#0x20+var_20]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4800E
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_4800e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LSLS    R5, R4, #0x1C
    // LSRS    R5, R5, #0x1C
    // LDR     R0, =0x8015798
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MULS    R1, R5
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0x1C]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_48034
    // 条件跳转
    // ASRS    R0, R4, #8
    // LDR     R1, =0x8015798
    // 内存加载操作
    // MOVS    R2, #0x30 ; '0'
    // R2 = 0x30;
    // MULS    R2, R5
    // ADDS    R1, R1, R2
    // 算术运算
    // LDR     R1, [R1,#0x1C]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
    // B       locret_48038
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4803A
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_4803a(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4807C
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_4807c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_480BE
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_480be(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // LSLS    R5, R4, #0x1C
    // LSRS    R5, R5, #0x1C
    // LDR     R0, =0x8015798
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MULS    R1, R5
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0x14]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_480E8
    // 条件跳转
    // MOVS    R1, R6
    // ASRS    R0, R4, #8
    // LDR     R2, =0x8015798
    // 内存加载操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MULS    R3, R5
    // ADDS    R2, R2, R3
    // 算术运算
    // LDR     R2, [R2,#0x14]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
    // B       locret_480EC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_480EE
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_480ee(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LSLS    R5, R4, #0x1C
    // LSRS    R5, R5, #0x1C
    // LDR     R0, =0x8015798
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MULS    R1, R5
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0x24]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_48116
    // 条件跳转
    // ASRS    R0, R4, #8
    // LDR     R1, =0x8015798
    // 内存加载操作
    // MOVS    R2, #0x30 ; '0'
    // R2 = 0x30;
    // MULS    R2, R5
    // ADDS    R1, R1, R2
    // 算术运算
    // LDR     R1, [R1,#0x24]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
    // SXTH    R0, R0
    // 数据扩展操作
    // B       locret_4811A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4811C
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_4811c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015798;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LSLS    R5, R4, #0x1C
    // LSRS    R5, R5, #0x1C
    // LDR     R0, =0x8015798
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MULS    R1, R5
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0x2C]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_48140
    // 条件跳转
    // ASRS    R0, R4, #8
    // LDR     R1, =0x8015798
    // 内存加载操作
    // MOVS    R2, #0x30 ; '0'
    // R2 = 0x30;
    // MULS    R2, R5
    // ADDS    R1, R1, R2
    // 算术运算
    // LDR     R1, [R1,#0x2C]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48148
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_48148(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007088;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007678;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007798;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200078AE;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x23;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R0, #5
    // R0 = 5;
    // LDR     R1, =0x200078AE
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R5, #0x23 ; '#'
    // R5 = 0x23;
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R6, =0x20007088
    // 内存加载操作
    // MOVS    R2, R4
    // MOVS    R1, R5
    // MOVS    R0, R6
    // BL      sub_46D38
    // 调用函数: sub_46D38();
    // LDR     R0, =0x20007088
    // 内存加载操作
    // LDR     R1, =0x20007798
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007678
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007678
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // BL      sub_49580
    // 调用函数: sub_49580();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4817A
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_4817a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007678;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // LDR     R0, =0x20007678
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xA
    // 比较操作
    // BGE     loc_48186
    // 条件跳转
    // B       locret_4830C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4830E
 * @note 指令数: 44, 标签数: 0
 */
void precise_func_4830e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078AE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007798;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // LDR     R3, =0x200078AE
    // 内存加载操作
    // LDRB    R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R3
    // 比较操作
    // BCS     locret_48366
    // MOVS    R3, #3
    // R3 = 3;
    // LDR     R4, =0x20007798
    // 内存加载操作
    // LDR     R4, [R4]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R5, #7
    // R5 = 7;
    // MULS    R5, R0
    // ADDS    R4, R4, R5
    // 算术运算
    // STRB    R3, [R4,#4]
    // 内存存储操作
    // LDR     R3, =0x20007798
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #7
    // R4 = 7;
    // MULS    R4, R0
    // ADDS    R3, R3, R4
    // 算术运算
    // MOVS    R4, R1
    // STRB    R4, [R3]
    // 内存存储操作
    // LSRS    R4, R4, #8
    // STRB    R4, [R3,#1]
    // 内存存储操作
    // LDR     R3, =0x20007798
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #7
    // R4 = 7;
    // MULS    R4, R0
    // ADDS    R3, R3, R4
    // 算术运算
    // MOVS    R4, R2
    // STRB    R4, [R3,#2]
    // 内存存储操作
    // LSRS    R4, R4, #8
    // STRB    R4, [R3,#3]
    // 内存存储操作
    // LDR     R3, =0x20007798
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #7
    // R4 = 7;
    // MULS    R4, R0
    // ADDS    R3, R3, R4
    // 算术运算
    // MOVS    R4, #0
    // R4 = 0;
    // STRB    R4, [R3,#5]
    // 内存存储操作
    // LSRS    R4, R4, #8
    // STRB    R4, [R3,#6]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48368
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_48368(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078AE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007798;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R1, =0x200078AE
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R1
    // 比较操作
    // BCS     locret_48384
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R2, =0x20007798
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #7
    // R3 = 7;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STRB    R1, [R2,#4]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48386
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_48386(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078AE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007798;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R1, =0x200078AE
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R1
    // 比较操作
    // BCS     locret_483A2
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007798
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #7
    // R3 = 7;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STRB    R1, [R2,#4]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_483B4
 * @note 指令数: 28, 标签数: 0
 */
void precise_func_483b4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, #0
    // R5 = 0;
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x200000
    // R1 = 0x200000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48456
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_48456(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015D64;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x8015D64
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015D64
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_474E8
    // 调用函数: sub_474E8();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4847C
    // 条件跳转
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8015D64
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015D64
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // B       locret_4848C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4848E
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_4848e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015D68;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8015D64;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R1, =0x8015D68
    // 内存加载操作
    // LDR     R0, =0x8015D64
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_484B8
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_484b8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015D64;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x8015D64
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // LDR     R0, =0x8015D64
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_47352
    // 调用函数: sub_47352();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_484D8
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_484d8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015674;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDR     R0, =0x8015674
    // 内存加载操作
    // MOVS    R1, #0x64 ; 'd'
    // R1 = 0x64;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#4]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_48514
    // 条件跳转
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_484F6
    // 条件跳转
    // MOVS    R2, #0
    // R2 = 0;
    // B       loc_484F8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48516
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_48516(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4866A
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_4866a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015674;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // BL      sub_48456
    // 调用函数: sub_48456();
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_484D8
    // 调用函数: sub_484D8();
    // UXTH    R5, R5
    // 数据扩展操作
    // LDR     R0, =0x8015674
    // 内存加载操作
    // MOVS    R1, #0x64 ; 'd'
    // R1 = 0x64;
    // MULS    R1, R4
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // STR     R5, [R0,#0xC]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_486AA
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_486aa(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // LDR     R3, [R1,#8]
    // 内存加载操作
    // LDR     R4, =(dword_700+1)
    // 内存加载操作
    // CMP     R3, R4
    // 比较操作
    // BCC     loc_486BA
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R2, R3
    // B       loc_486C0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48764
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_48764(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000318;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20000318
    // 内存加载操作
    // BL      nullsub_27
    // 调用函数: nullsub_27();
    // POP     {R1,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48774
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_48774(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       loc_4877A
    // 无条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // ADDS    R1, R1, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48796
 * @note 指令数: 22, 标签数: 1
 */
void precise_func_48796(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40013820;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4001381C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007734;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LSLS    R0, R5, #0xF
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x4001381C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_487CA
    // LDR     R0, =0x40013800
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x17
    // BPL     loc_487CA
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x40013820
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007734
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_487C2
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR     R2, =0x20007734
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_488BA
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_488ba(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007738;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x4000441C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x4000441C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_488EC
    // LDR     R0, =0x40004400
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x17
    // BPL     loc_488EC
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x40004420
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007738
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_488E4
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR     R2, =0x20007738
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48A1C
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_48a1c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40004800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4000481C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40004820;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x4000481C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_48A46
    // LDR     R0, =0x40004800
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x17
    // BPL     loc_48A46
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x40004820
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000773C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_48A46
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR     R2, =0x2000773C
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48C50
 * @note 指令数: 9, 标签数: 0
 */
uint32_t precise_func_48c50(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8014AF8;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // LDR     R0, [R0,#0x50]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // BICS    R0, R1
    // LDR     R1, =0x8014AF8
    // 内存加载操作
    // LDR     R1, [R1,#0x50]
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48C9C
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_48c9c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // var_14= -0x14
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48F44
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_48f44(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48F4A
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_48f4a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x19;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R1, #0x50 ; 'P'
    // R1 = 0x50;
    // MULS    R1, R5
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x19
    // BMI     loc_48F6C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48FA0
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_48fa0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x19;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R1
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // LDR     R0, [R0,#0x1C]
    // 内存加载操作
    // LSLS    R0, R0, #0x18
    // BPL     loc_48FC4
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R1
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // LDR     R0, [R0,#0x1C]
    // 内存加载操作
    // LSLS    R0, R0, #0x19
    // BPL     loc_48FC4
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_48FC6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48FCC
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_48fcc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8014AF8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R1, #0x50 ; 'P'
    // R1 = 0x50;
    // MULS    R1, R4
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40 ; '@'
    // R1 = 0x40;
    // ORRS    R1, R0
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, R4
    // BL      sub_478D8
    // 调用函数: sub_478D8();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_48FF8
 * @note 指令数: 25, 标签数: 0
 */
void precise_func_48ff8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8014AF8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x8014AF8
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R0
    // LDR     R1, [R1,R2]
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #0x40 ; '@'
    // R2 = 0x40;
    // BICS    R1, R2
    // LDR     R2, =0x8014AF8
    // 内存加载操作
    // MOVS    R3, #0x50 ; 'P'
    // R3 = 0x50;
    // MULS    R3, R0
    // LDR     R2, [R2,R3]
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // LDR     R1, =0x8014AF8
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R0
    // LDR     R1, [R1,R2]
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #0x80
    // R2 = 0x80;
    // BICS    R1, R2
    // LDR     R2, =0x8014AF8
    // 内存加载操作
    // MOVS    R3, #0x50 ; 'P'
    // R3 = 0x50;
    // MULS    R3, R0
    // LDR     R2, [R2,R3]
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49034
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_49034(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_49058
    // 条件跳转
    // UXTB    R6, R6
    // 数据扩展操作
    // CMP     R6, #0
    // 比较操作
    // BEQ     loc_49050
    // 条件跳转
    // MOVS    R0, R4
    // BL      sub_490B6
    // 调用函数: sub_490B6();
    // B       locret_4905E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49060
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_49060(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20006810;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R1, #0x50 ; 'P'
    // R1 = 0x50;
    // MULS    R1, R4
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // LDR     R1, =0x20006810
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20006810
    // 内存加载操作
    // BL      sub_495E8
    // 调用函数: sub_495E8();
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_4907E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_49080
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49082
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_49082(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_49034
    // 调用函数: sub_49034();
    // MOVS    R0, #1
    // R0 = 1;
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49094
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_49094(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R1, #0x50 ; 'P'
    // R1 = 0x50;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x3C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R3, #0x50 ; 'P'
    // R3 = 0x50;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x38]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_490B6
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_490b6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8014AF8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R1, #0x50 ; 'P'
    // R1 = 0x50;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#0x3C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8014AF8
    // 内存加载操作
    // MOVS    R3, #0x50 ; 'P'
    // R3 = 0x50;
    // MULS    R3, R4
    // ADDS    R0, R0, R3
    // 算术运算
    // LDR     R0, [R0,#0x38]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_490D8
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_490d8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8014AF8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, =0x8014AF8
    // 内存加载操作
    // MOVS    R3, #0x50 ; 'P'
    // R3 = 0x50;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // LDR     R2, [R2,#4]
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_490E8
 * @note 指令数: 57, 标签数: 0
 */
void precise_func_490e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xEFFF69F3;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R0, [R5,#4]
    // 内存加载操作
    // LDR     R1, [R5,#0xC]
    // 内存加载操作
    // ORRS    R1, R0
    // LDR     R0, [R5,#0x10]
    // 内存加载操作
    // ORRS    R0, R1
    // LDR     R1, [R5,#0x18]
    // 内存加载操作
    // ORRS    R1, R0
    // MOVS    R6, R1
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0xEFFF69F3
    // 内存加载操作
    // ANDS    R1, R0
    // ORRS    R1, R6
    // STR     R1, [R4]
    // 内存存储操作
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // LDR     R1, =0xFFFFCFFF
    // 内存加载操作
    // ANDS    R1, R0
    // LDR     R0, [R5,#8]
    // 内存加载操作
    // ORRS    R0, R1
    // STR     R0, [R4,#4]
    // 内存存储操作
    // LDR     R0, [R5,#0x14]
    // 内存加载操作
    // LDR     R1, [R5,#0x1C]
    // 内存加载操作
    // ORRS    R1, R0
    // MOVS    R6, R1
    // LDR     R0, [R4,#8]
    // 内存加载操作
    // LDR     R1, =0xFFFFF4FF
    // 内存加载操作
    // ANDS    R1, R0
    // ORRS    R1, R6
    // STR     R1, [R4,#8]
    // 内存存储操作
    // BL      sub_49654
    // 调用函数: sub_49654();
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R0, R1
    // LDR     R1, [R5]
    // 内存加载操作
    // BL      sub_4637C
    // 调用函数: sub_4637C();
    // LDR     R1, =dword_FFF0
    // 内存加载操作
    // ANDS    R1, R0
    // MOVS    R2, R1
    // MOVS    R1, R0
    // UXTH    R1, R1
    // 数据扩展操作
    // LSRS    R1, R1, #1
    // LSLS    R1, R1, #0x1D
    // LSRS    R1, R1, #0x1D
    // ORRS    R1, R2
    // UXTH    R1, R1
    // 数据扩展操作
    // STR     R1, [R4,#0xC]
    // 内存存储操作
    // LDR     R2, [R4,#4]
    // 内存加载操作
    // LDR     R3, =0xFFFFB7FF
    // 内存加载操作
    // ANDS    R3, R2
    // STR     R3, [R4,#4]
    // 内存存储操作
    // LDR     R2, [R4,#8]
    // 内存加载操作
    // MOVS    R3, #0x2A ; '*'
    // R3 = 0x2A;
    // BICS    R2, R3
    // STR     R2, [R4,#8]
    // 内存存储操作
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4917C
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_4917c(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R0, R5
    // BL      sub_491FC
    // 调用函数: sub_491FC();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_49192
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // B       locret_491AE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_491B0
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_491b0(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // BL      sub_491E8
    // 调用函数: sub_491E8();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_491C4
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // B       locret_491E6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_491E8
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_491e8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // LDRH    R0, [R1,#4]
    // 内存加载操作
    // LDRH    R2, [R1,#6]
    // 内存加载操作
    // CMP     R0, R2
    // 比较操作
    // BNE     loc_491F8
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_491FA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_491FC
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_491fc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // LDRH    R0, [R1,#4]
    // 内存加载操作
    // MOVS    R2, R0
    // ADDS    R2, R2, #1
    // 算术运算
    // LDRH    R0, [R1,#8]
    // 内存加载操作
    // UXTH    R2, R2
    // 数据扩展操作
    // CMP     R2, R0
    // 比较操作
    // BNE     loc_49212
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R2, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49222
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_49222(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDRH    R1, [R0,#4]
    // 内存加载操作
    // LDRH    R2, [R0,#6]
    // 内存加载操作
    // CMP     R1, R2
    // 比较操作
    // BCC     loc_49236
    // LDRH    R1, [R0,#4]
    // 内存加载操作
    // LDRH    R0, [R0,#6]
    // 内存加载操作
    // SUBS    R0, R1, R0
    // 算术运算
    // UXTH    R0, R0
    // 数据扩展操作
    // B       locret_49242
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49244
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_49244(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // B       loc_4924A
    // 无条件跳转
    // ADDS    R1, R1, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49254
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_49254(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49540
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_49540(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #2
    // R0 = 2;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_49544
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_49544(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // MOVS    R0, #1
    // R0 = 1;
    // MOVS    R5, R0
}

