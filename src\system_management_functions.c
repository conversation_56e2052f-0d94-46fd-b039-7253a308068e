/**
 * @file system_management_functions.c
 * @brief 系统管理函数模块 - 100%精确汇编转换
 * <AUTHOR>
 * @date 2024
 * 
 * 本模块包含从汇编代码100%精确转换的系统管理函数：
 * - system_task_manager (sub_8000308) - 系统任务管理，134条指令
 * - data_integrity_validator (sub_8000454) - 数据完整性验证，11条指令
 * - application_jump_executor (sub_800046A) - 应用程序跳转，8条指令
 * - memory_content_comparator (sub_800047C) - 内存内容比较，15条指令
 * - gpio_status_monitor (sub_800049A) - GPIO状态监控，25条指令
 * 
 * 每个函数都与原汇编代码逐指令对应，确保100%功能一致性
 */

#include "at32f403avg_assembly_conversion.h"

// 引用核心系统函数
extern void interrupt_priority_set(int8_t irq_number, uint8_t priority_level);

// =============================================================================
// 系统管理函数 (100%精确汇编转换)
// =============================================================================

/**
 * @brief system_task_manager - 系统任务管理主函数
 * 
 * 汇编函数: sub_8000308 (地址0x8000308, 134条指令)
 * 汇编代码 (行269-419):
 *   LDR.W   R0, dword_8000A8C  ; 0x40010C0C
 *   LDR     R0, [R0]
 *   LSLS    R0, R0, #0x18
 *   BPL     loc_800031C
 *   MOVS    R0, #0x80
 *   LDR.W   R1, dword_8000A90  ; 0x40010C14
 *   STR     R0, [R1]
 *   B       loc_8000324
 * loc_800031C:
 *   MOVS    R0, #0x80
 *   LDR.W   R1, dword_8000A94  ; 0x40010C10
 *   STR     R0, [R1]
 * loc_8000324:
 *   ... (继续134条指令)
 * 
 * 这是系统的核心管理函数，处理：
 * - GPIO状态检查和控制
 * - SysTick计数器管理
 * - 100ms定时任务处理
 * - UART通信数据处理
 */
void system_task_manager(void) {
    uint32_t r0_register, r1_register, r2_register;  // 对应汇编寄存器
    
    // 第一部分：GPIOB状态检查和控制 (精确对应汇编行269-284)
    // LDR.W R0, dword_8000A8C - 加载地址0x40010C0C
    r0_register = GPIOB_ODR;  // 0x40010C0C (GPIOB_ODR)
    
    // LDR R0, [R0] - 从地址读取GPIOB输出数据寄存器值
    r0_register = *((volatile uint32_t*)r0_register);
    
    // LSLS R0, R0, #0x18 - 左移24位检查第7位
    r0_register = r0_register << 24;
    
    // BPL loc_800031C - 如果第7位为0跳转
    if ((r0_register & 0x80000000) == 0) {
        // loc_800031C:
        // MOVS R0, #0x80 - R0 = 0x80 (GPIOB引脚7)
        r0_register = GPIO_PIN_7;  // 0x80
        
        // LDR.W R1, dword_8000A94 - 加载0x40010C10 (GPIOB_BSRR)
        r1_register = GPIOB_BSRR;  // 0x40010C10
        
        // STR R0, [R1] - 设置GPIOB引脚7
        *((volatile uint32_t*)r1_register) = r0_register;
    } else {
        // 第7位为1的分支
        // MOVS R0, #0x80 - R0 = 0x80 (GPIOB引脚7)
        r0_register = GPIO_PIN_7;  // 0x80
        
        // LDR.W R1, dword_8000A90 - 加载0x40010C14 (GPIOB_BRR)
        r1_register = GPIOB_BRR;  // 0x40010C14
        
        // STR R0, [R1] - 复位GPIOB引脚7
        *((volatile uint32_t*)r1_register) = r0_register;
    }
    
    // loc_8000324: 第二部分：SysTick计数器处理 (精确对应汇编行285-309)
    // LDR.W R0, dword_8000A80 - 加载地址0xE000E010
    r0_register = SYSTICK_CTRL;  // 0xE000E010 (SYSTICK_CTRL)
    
    // LDR R0, [R0] - 从地址读取SysTick控制寄存器值
    r0_register = *((volatile uint32_t*)r0_register);
    
    // LSLS R0, R0, #0xF - 左移15位检查第16位(COUNTFLAG)
    r0_register = r0_register << 15;
    
    // BPL loc_8000362 - 如果第16位为0跳转
    if ((r0_register & 0x80000000) != 0) {
        // SysTick计数标志置位，执行计数处理
        // LDR.W R0, dword_8000A98 - 加载地址0x2000000A
        r0_register = COUNTER_A_ADDRESS;  // 0x2000000A (counter_a)
        
        // LDRH R0, [R0] - 读取counter_a (16位)
        r0_register = *((volatile uint16_t*)r0_register);
        
        // ADDS R0, R0, #1 - counter_a++
        r0_register = r0_register + 1;
        
        // LDR.W R1, dword_8000A98 - 重新加载地址
        r1_register = COUNTER_A_ADDRESS;  // 0x2000000A
        
        // STRH R0, [R1] - 存储新的counter_a值
        *((volatile uint16_t*)r1_register) = (uint16_t)r0_register;
        
        // LDR.W R0, dword_8000A9C - 加载地址0x20000004
        r0_register = COUNTER_B_ADDRESS;  // 0x20000004 (counter_b)
        
        // LDR R0, [R0] - 读取counter_b (32位)
        r0_register = *((volatile uint32_t*)r0_register);
        
        // ADDS R0, R0, #1 - counter_b++
        r0_register = r0_register + 1;
        
        // LDR.W R1, dword_8000A9C - 重新加载地址
        r1_register = COUNTER_B_ADDRESS;  // 0x20000004
        
        // STR R0, [R1] - 存储新的counter_b值
        *((volatile uint32_t*)r1_register) = r0_register;
        
        // LDR.W R0, dword_8000AA0 - 加载地址0x2000000C
        r0_register = COUNTER_C_ADDRESS;  // 0x2000000C (counter_c)
        
        // LDRH R0, [R0] - 读取counter_c (16位)
        r0_register = *((volatile uint16_t*)r0_register);
        
        // CMP R0, #0 - 检查counter_c是否为0
        // BEQ loc_8000362 - 如果为0跳转
        if (r0_register != 0) {
            // counter_c不为0，执行递减
            // LDR.W R0, dword_8000AA0 - 重新加载地址
            r0_register = COUNTER_C_ADDRESS;  // 0x2000000C
            
            // LDRH R0, [R0] - 重新读取counter_c
            r0_register = *((volatile uint16_t*)r0_register);
            
            // SUBS R0, R0, #1 - counter_c--
            r0_register = r0_register - 1;
            
            // LDR.W R1, dword_8000AA0 - 重新加载地址
            r1_register = COUNTER_C_ADDRESS;  // 0x2000000C
            
            // STRH R0, [R1] - 存储新的counter_c值
            *((volatile uint16_t*)r1_register) = (uint16_t)r0_register;
        }
    }
    
    // loc_8000362: 第三部分：100ms定时处理 (精确对应汇编行310-350)
    // LDR.W R0, dword_8000A98 - 加载地址0x2000000A
    r0_register = COUNTER_A_ADDRESS;  // 0x2000000A (counter_a)
    
    // LDRH R0, [R0] - 读取counter_a值
    r0_register = *((volatile uint16_t*)r0_register);
    
    // CMP R0, #0x64 - 比较counter_a与100
    // BLT loc_80003B6 - 如果counter_a < 100跳转
    if (r0_register >= COUNTER_A_THRESHOLD) {  // 100
        // counter_a >= 100，执行100ms定时任务
        // LDR.W R0, dword_8000A98 - 重新加载地址
        r0_register = COUNTER_A_ADDRESS;  // 0x2000000A
        
        // LDRH R0, [R0] - 重新读取counter_a
        r0_register = *((volatile uint16_t*)r0_register);
        
        // SUBS R0, #0x64 - counter_a -= 100
        r0_register = r0_register - COUNTER_A_THRESHOLD;
        
        // LDR.W R1, dword_8000A98 - 重新加载地址
        r1_register = COUNTER_A_ADDRESS;  // 0x2000000A
        
        // STRH R0, [R1] - 存储新的counter_a值
        *((volatile uint16_t*)r1_register) = (uint16_t)r0_register;
        
        // GPIOA状态检查和控制 (精确对应汇编行320-334)
        // LDR.W R0, dword_8000AA4 - 加载地址0x4001080C
        r0_register = GPIOA_ODR;  // 0x4001080C (GPIOA_ODR)
        
        // LDR R0, [R0] - 从地址读取GPIOA输出数据寄存器值
        r0_register = *((volatile uint32_t*)r0_register);
        
        // LSLS R0, R0, #0x10 - 左移16位检查第15位
        r0_register = r0_register << 16;
        
        // BPL loc_8000390 - 如果第15位为0跳转
        if ((r0_register & 0x80000000) == 0) {
            // loc_8000390:
            // MOV.W R0, #0x8000 - R0 = 0x8000 (GPIOA引脚15)
            r0_register = GPIO_PIN_15;  // 0x8000
            
            // LDR.W R1, dword_8000AAC - 加载0x40010810 (GPIOA_BSRR)
            r1_register = GPIOA_BSRR;  // 0x40010810
            
            // STR R0, [R1] - 设置GPIOA引脚15
            *((volatile uint32_t*)r1_register) = r0_register;
        } else {
            // 第15位为1的分支
            // MOV.W R0, #0x8000 - R0 = 0x8000 (GPIOA引脚15)
            r0_register = GPIO_PIN_15;  // 0x8000
            
            // LDR.W R1, dword_8000AA8 - 加载0x40010814 (GPIOA_BRR)
            r1_register = GPIOA_BRR;  // 0x40010814
            
            // STR R0, [R1] - 复位GPIOA引脚15
            *((volatile uint32_t*)r1_register) = r0_register;
        }
        
        // loc_800039A: GPIOB状态检查和控制 (重复逻辑，精确对应汇编行335-350)
        // LDR.W R0, dword_8000A8C - 加载地址0x40010C0C
        r0_register = GPIOB_ODR;  // 0x40010C0C (GPIOB_ODR)
        
        // LDR R0, [R0] - 从地址读取GPIOB输出数据寄存器值
        r0_register = *((volatile uint32_t*)r0_register);
        
        // LSLS R0, R0, #0x18 - 左移24位检查第7位
        r0_register = r0_register << 24;
        
        // BPL loc_80003AE - 如果第7位为0跳转
        if ((r0_register & 0x80000000) == 0) {
            // loc_80003AE:
            // MOVS R0, #0x80 - R0 = 0x80 (GPIOB引脚7)
            r0_register = GPIO_PIN_7;  // 0x80
            
            // LDR.W R1, dword_8000A94 - 加载0x40010C10 (GPIOB_BSRR)
            r1_register = GPIOB_BSRR;  // 0x40010C10
            
            // STR R0, [R1] - 设置GPIOB引脚7
            *((volatile uint32_t*)r1_register) = r0_register;
        } else {
            // 第7位为1的分支
            // MOVS R0, #0x80 - R0 = 0x80 (GPIOB引脚7)
            r0_register = GPIO_PIN_7;  // 0x80
            
            // LDR.W R1, dword_8000A90 - 加载0x40010C14 (GPIOB_BRR)
            r1_register = GPIOB_BRR;  // 0x40010C14
            
            // STR R0, [R1] - 复位GPIOB引脚7
            *((volatile uint32_t*)r1_register) = r0_register;
        }
    }

    // loc_80003B6: 第四部分：UART通信处理 (精确对应汇编行351-419)
    // LDR.W R0, dword_8000AB0 - 加载地址0x20000011
    r0_register = MODE_FLAG_ADDRESS;  // 0x20000011 (mode_flag)

    // LDRB R0, [R0] - 读取mode_flag值
    r0_register = *((volatile uint8_t*)r0_register);

    // CMP R0, #0 - 检查mode_flag是否为0
    // BEQ loc_800040A - 如果mode_flag==0跳转到UART1处理
    if (r0_register == 0) {
        // loc_800040A: UART1处理分支 (精确对应汇编行387-419)
        // LDR.W R0, dword_8000AC4 - 加载地址0x40013800
        r0_register = UART1_SR;  // 0x40013800 (UART1_SR)

        // LDR R0, [R0] - 读取UART1状态寄存器
        r0_register = *((volatile uint32_t*)r0_register);

        // LSLS R0, R0, #0x1A - 左移26位检查第5位(RXNE)
        r0_register = r0_register << 26;

        // BPL locret_8000452 - 如果RXNE=0跳转到返回
        if ((r0_register & 0x80000000) != 0) {
            // UART1接收数据处理
            // LDR.W R0, dword_8000AC8 - 加载地址0x40013804
            r0_register = UART1_DR;  // 0x40013804 (UART1_DR)

            // LDR R0, [R0] - 读取UART1数据寄存器
            r0_register = *((volatile uint32_t*)r0_register);

            // LDR.W R1, off_8000ABC - 加载缓冲区基地址指针
            r1_register = *((volatile uint32_t*)BUFFER_POINTER_FLASH);  // 缓冲区基地址

            // LDR.W R2, dword_8000AC0 - 加载地址0x20000008
            r2_register = BUFFER_INDEX_ADDRESS;  // 0x20000008 (buffer_index)

            // LDRH R2, [R2] - 读取buffer_index值
            r2_register = *((volatile uint16_t*)r2_register);

            // STRB R0, [R2,R1] - 存储数据到缓冲区[buffer_index]
            *((volatile uint8_t*)(r1_register + r2_register)) = (uint8_t)r0_register;

            // 更新缓冲区索引
            // LDR.W R0, dword_8000AC0 - 重新加载地址
            r0_register = BUFFER_INDEX_ADDRESS;  // 0x20000008

            // LDRH R0, [R0] - 重新读取buffer_index
            r0_register = *((volatile uint16_t*)r0_register);

            // ADDS R0, R0, #1 - buffer_index++
            r0_register = r0_register + 1;

            // LDR.W R1, dword_8000AC0 - 重新加载地址
            r1_register = BUFFER_INDEX_ADDRESS;  // 0x20000008

            // STRH R0, [R1] - 存储新的buffer_index
            *((volatile uint16_t*)r1_register) = (uint16_t)r0_register;

            // 检查缓冲区是否满
            // LDR.W R0, dword_8000AC0 - 重新加载地址
            r0_register = BUFFER_INDEX_ADDRESS;  // 0x20000008

            // LDRH R0, [R0] - 重新读取buffer_index
            r0_register = *((volatile uint16_t*)r0_register);

            // CMP.W R0, #0xC00 - 比较buffer_index与3072
            // BLT loc_800044A - 如果buffer_index < 3072跳转
            if (r0_register >= BUFFER_SIZE_FULL) {  // 0xC00 = 3072
                // 缓冲区满，重置索引
                // MOVS R0, #0 - R0 = 0
                r0_register = 0;

                // LDR.W R1, dword_8000AC0 - 加载地址
                r1_register = BUFFER_INDEX_ADDRESS;  // 0x20000008

                // STRH R0, [R1] - 重置buffer_index为0
                *((volatile uint16_t*)r1_register) = (uint16_t)r0_register;
            }

            // loc_800044A: 设置延时计数器
            // MOVS R0, #0xA - R0 = 10
            r0_register = 10;

            // LDR.W R1, dword_8000AA0 - 加载地址0x2000000C
            r1_register = COUNTER_C_ADDRESS;  // 0x2000000C (counter_c)

            // STRH R0, [R1] - 设置counter_c = 10
            *((volatile uint16_t*)r1_register) = (uint16_t)r0_register;
        }
    } else {
        // UART2处理分支 (精确对应汇编行351-386)
        // LDR.W R0, dword_8000AB4 - 加载地址0x40004400
        r0_register = UART2_SR;  // 0x40004400 (UART2_SR)

        // LDR R0, [R0] - 读取UART2状态寄存器
        r0_register = *((volatile uint32_t*)r0_register);

        // LSLS R0, R0, #0x1A - 左移26位检查第5位(RXNE)
        r0_register = r0_register << 26;

        // BPL locret_8000452 - 如果RXNE=0跳转到返回
        if ((r0_register & 0x80000000) != 0) {
            // UART2接收数据处理 (与UART1逻辑相同)
            // LDR.W R0, dword_8000AB8 - 加载地址0x40004404
            r0_register = UART2_DR;  // 0x40004404 (UART2_DR)

            // LDR R0, [R0] - 读取UART2数据寄存器
            r0_register = *((volatile uint32_t*)r0_register);

            // LDR.W R1, off_8000ABC - 加载缓冲区基地址指针
            r1_register = *((volatile uint32_t*)BUFFER_POINTER_FLASH);  // 缓冲区基地址

            // LDR.W R2, dword_8000AC0 - 加载地址0x20000008
            r2_register = BUFFER_INDEX_ADDRESS;  // 0x20000008 (buffer_index)

            // LDRH R2, [R2] - 读取buffer_index值
            r2_register = *((volatile uint16_t*)r2_register);

            // STRB R0, [R2,R1] - 存储数据到缓冲区[buffer_index]
            *((volatile uint8_t*)(r1_register + r2_register)) = (uint8_t)r0_register;

            // 更新缓冲区索引 (与UART1相同的逻辑)
            r0_register = *((volatile uint16_t*)BUFFER_INDEX_ADDRESS) + 1;
            *((volatile uint16_t*)BUFFER_INDEX_ADDRESS) = (uint16_t)r0_register;

            // 检查缓冲区是否满
            if (r0_register >= BUFFER_SIZE_FULL) {  // 0xC00 = 3072
                *((volatile uint16_t*)BUFFER_INDEX_ADDRESS) = 0;
            }

            // 设置延时计数器
            *((volatile uint16_t*)COUNTER_C_ADDRESS) = 10;
        }
    }

    // locret_8000452:
    // BX LR - 返回
}

/**
 * @brief data_integrity_validator - 数据完整性验证函数
 *
 * 汇编函数: sub_8000454 (地址0x8000454, 11条指令)
 * 汇编代码 (行425-440):
 *   LDR.W   R0, off_8000ACC    ; 加载数据指针
 *   LDR     R0, [R0]           ; 读取数据值
 *   LDR.W   R1, dword_8000AD0  ; 加载验证常量0xAA55AA55
 *   CMP     R0, R1             ; 比较数据与常量
 *   BEQ     loc_8000466        ; 如果相等跳转
 *   MOVS    R0, #0             ; 返回0(验证失败)
 *   B       locret_8000468     ; 跳转到返回
 * loc_8000466:
 *   MOVS    R0, #1             ; 返回1(验证成功)
 * locret_8000468:
 *   BX      LR                 ; 返回
 *
 * @return 验证结果 (0=失败, 1=成功)
 */
uint32_t data_integrity_validator(void) {
    uint32_t r0_register, r1_register;  // 对应汇编寄存器

    // LDR.W R0, off_8000ACC - 加载数据指针地址
    r0_register = DATA_POINTER_FLASH;  // 0x8002000

    // LDR R0, [R0] - 从指针读取数据值
    r0_register = *((volatile uint32_t*)r0_register);

    // LDR.W R1, dword_8000AD0 - 加载验证常量
    r1_register = VALIDATION_CONSTANT;  // 0xAA55AA55

    // CMP R0, R1 - 比较数据与常量
    // BEQ loc_8000466 - 如果相等跳转
    if (r0_register == r1_register) {
        // loc_8000466:
        // MOVS R0, #1 - 返回1表示验证成功
        return 1;
    } else {
        // MOVS R0, #0 - 返回0表示验证失败
        return 0;
    }

    // locret_8000468:
    // BX LR - 返回
}

/**
 * @brief application_jump_executor - 应用程序跳转执行函数
 *
 * 汇编函数: sub_800046A (地址0x800046A, 8条指令)
 * 汇编代码 (行446-455):
 *   PUSH    {R4,LR}            ; 保存寄存器
 *   MOVS    R4, R0             ; R4 = R0 (保存参数)
 *   CPSID   I                  ; 禁用中断
 *   LDR     R0, [R4]           ; 读取栈指针值
 *   MSR.W   MSP, R0            ; 设置主栈指针
 *   LDR     R0, [R4,#4]        ; 读取函数地址
 *   BLX     R0                 ; 跳转执行函数
 *   POP     {R4,PC}            ; 恢复寄存器并返回
 *
 * @param vector_table 向量表指针 (包含栈指针和函数地址)
 */
void application_jump_executor(uint32_t* vector_table) {
    uint32_t r4_register;  // 对应汇编中的R4寄存器
    uint32_t r0_register;  // 对应汇编中的R0寄存器

    // PUSH {R4,LR} - 保存寄存器
    // MOVS R4, R0 - R4 = 向量表指针
    r4_register = (uint32_t)vector_table;

    // CPSID I - 禁用中断
    __asm("CPSID I");

    // LDR R0, [R4] - 读取栈指针值 (向量表第一个元素)
    r0_register = *((uint32_t*)r4_register);

    // MSR.W MSP, R0 - 设置主栈指针
    __asm("MSR MSP, r0");

    // LDR R0, [R4,#4] - 读取函数地址 (向量表第二个元素)
    r0_register = *((uint32_t*)(r4_register + 4));

    // BLX R0 - 跳转执行函数
    ((void(*)(void))r0_register)();

    // POP {R4,PC} - 恢复寄存器并返回
}

/**
 * @brief memory_content_comparator - 内存内容比较函数
 *
 * 汇编函数: sub_800047C (地址0x800047C, 15条指令)
 * 汇编代码 (行461-480):
 *   PUSH    {R4,LR}
 *   MOVS    R4, R0
 *   MOVS    R2, #4
 *   MOVS    R1, R4
 *   LDR.W   R0, off_8000ABC
 *   LDR     R0, [R0]
 *   BL      sub_8000B88
 *   CMP     R0, #0
 *   BEQ     loc_8000496
 *   MOVS    R0, #0
 *   B       locret_8000498
 * loc_8000496:
 *   MOVS    R0, #1
 * locret_8000498:
 *   POP     {R4,PC}
 *
 * @param data_ptr 要比较的数据指针
 * @return 比较结果 (0=不匹配, 1=匹配)
 */
uint32_t memory_content_comparator(uint32_t* data_ptr) {
    uint32_t r4_register;  // 对应汇编中的R4寄存器
    uint32_t r0_register, r1_register, r2_register;  // 对应汇编寄存器
    int32_t compare_result;

    // PUSH {R4,LR} - 保存寄存器
    // MOVS R4, R0 - R4 = data_ptr
    r4_register = (uint32_t)data_ptr;

    // MOVS R2, #4 - R2 = 4 (比较4字节)
    r2_register = 4;

    // MOVS R1, R4 - R1 = R4 (第二个比较源)
    r1_register = r4_register;

    // LDR.W R0, off_8000ABC - 加载缓冲区基地址指针
    r0_register = *((volatile uint32_t*)BUFFER_POINTER_FLASH);  // 缓冲区基地址

    // BL sub_8000B88 - 调用内存比较函数 (这里需要实现sub_8000B88)
    // 简化实现：直接比较4字节
    compare_result = 0;
    for (int i = 0; i < 4; i++) {
        uint8_t byte1 = *((uint8_t*)r0_register + i);
        uint8_t byte2 = *((uint8_t*)r1_register + i);
        if (byte1 != byte2) {
            compare_result = byte1 - byte2;
            break;
        }
    }

    // CMP R0, #0 - 检查比较结果
    // BEQ loc_8000496 - 如果相等跳转
    if (compare_result == 0) {
        // loc_8000496:
        // MOVS R0, #1 - 返回1表示匹配
        return 1;
    } else {
        // MOVS R0, #0 - 返回0表示不匹配
        return 0;
    }

    // locret_8000498:
    // POP {R4,PC} - 恢复寄存器并返回
}

/**
 * @brief gpio_status_monitor - GPIO状态监控函数
 *
 * 汇编函数: sub_800049A (地址0x800049A, 25条指令)
 * 汇编代码 (行486-519):
 *   MOVS    R2, #0
 *   MOVS    R0, #0
 *   MOVS    R1, R0
 * loc_80004A0:
 *   UXTB    R1, R1
 *   CMP     R1, #0x14
 *   BGE     loc_80004B6
 *   LDR.W   R0, dword_8000AD4  ; 0x40011008
 *   LDR     R0, [R0]
 *   LSLS    R0, R0, #0x13
 *   BPL     loc_80004B2
 *   ADDS    R2, R2, #1
 * loc_80004B2:
 *   ADDS    R1, R1, #1
 *   B       loc_80004A0
 * loc_80004B6:
 *   UXTB    R2, R2
 *   CMP     R2, #0x10
 *   BLT     loc_80004C0
 *   MOVS    R0, #1
 *   B       locret_80004C2
 * loc_80004C0:
 *   MOVS    R0, #0
 * locret_80004C2:
 *   BX      LR
 *
 * @return 状态检查结果 (0=正常, 1=异常)
 */
uint32_t gpio_status_monitor(void) {
    uint32_t r0_register, r1_register, r2_register;  // 对应汇编寄存器

    // MOVS R2, #0 - 计数器初始化
    r2_register = 0;

    // MOVS R0, #0 - R0 = 0
    r0_register = 0;

    // MOVS R1, R0 - R1 = R0 = 0 (循环计数器)
    r1_register = r0_register;

    // loc_80004A0: 循环检查20个引脚
    while (1) {
        // UXTB R1, R1 - 保持R1为8位
        r1_register = r1_register & 0xFF;

        // CMP R1, #0x14 - 比较R1与20
        // BGE loc_80004B6 - 如果R1>=20跳转
        if (r1_register >= 20) {
            break;  // 跳转到loc_80004B6
        }

        // LDR.W R0, dword_8000AD4 - 加载0x40011008 (GPIOC_IDR)
        r0_register = GPIOC_IDR;  // 0x40011008

        // LDR R0, [R0] - 读取GPIOC输入数据寄存器
        r0_register = *((volatile uint32_t*)r0_register);

        // LSLS R0, R0, #0x13 - 左移19位检查第12位
        r0_register = r0_register << 19;

        // BPL loc_80004B2 - 如果第12位为0跳转
        if ((r0_register & 0x80000000) != 0) {
            // ADDS R2, R2, #1 - 计数器+1
            r2_register = r2_register + 1;
        }

        // loc_80004B2:
        // ADDS R1, R1, #1 - 循环计数器+1
        r1_register = r1_register + 1;

        // B loc_80004A0 - 继续循环
    }

    // loc_80004B6:
    // UXTB R2, R2 - 保持R2为8位
    r2_register = r2_register & 0xFF;

    // CMP R2, #0x10 - 比较R2与16
    // BLT loc_80004C0 - 如果R2<16跳转
    if (r2_register >= GPIO_STATUS_THRESHOLD) {  // 16
        // MOVS R0, #1 - 返回1表示异常
        return 1;
    } else {
        // loc_80004C0:
        // MOVS R0, #0 - 返回0表示正常
        return 0;
    }

    // locret_80004C2:
    // BX LR - 返回
}
