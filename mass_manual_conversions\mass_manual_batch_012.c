// 大规模手工转换批次 12 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_46AC4
 * @note 指令数: 53
 */
uint32_t func_46ac4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200076A0 = (volatile uint32_t *)0x200076A0;
    volatile uint32_t *addr_200077BC = (volatile uint32_t *)0x200077BC;
    volatile uint32_t *addr_200078B8 = (volatile uint32_t *)0x200078B8;
    volatile uint32_t *addr_20007698 = (volatile uint32_t *)0x20007698;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_46B74
 * @note 指令数: 174
 */
void func_46b74(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_C9 = (volatile uint32_t *)0xC9;
    volatile uint32_t *addr_200077BC = (volatile uint32_t *)0x200077BC;
    volatile uint32_t *addr_65 = (volatile uint32_t *)0x65;
    volatile uint32_t *addr_200078B5 = (volatile uint32_t *)0x200078B5;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_46D18
 * @note 指令数: 15
 */
uint32_t func_46d18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_46D38
 * @note 指令数: 40
 */
uint32_t func_46d38(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_46D88
 * @note 指令数: 7
 */
uint32_t func_46d88(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_E000E100 = (volatile uint32_t *)0xE000E100;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_46D96
 * @note 指令数: 67
 */
void func_46d96(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_E000ED1C = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_E000E400 = (volatile uint32_t *)0xE000E400;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_46E1A
 * @note 指令数: 38
 */
void func_46e1a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_E000ED1C = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *addr_E000E400 = (volatile uint32_t *)0xE000E400;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_46E64
 * @note 指令数: 24
 */
void func_46e64(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_E000E018 = (volatile uint32_t *)0xE000E018;
    volatile uint32_t *addr_E000E014 = (volatile uint32_t *)0xE000E014;
    volatile uint32_t *addr_E000E010 = (volatile uint32_t *)0xE000E010;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_46E96
 * @note 指令数: 9
 */
void func_46e96(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_46EAA
 * @note 指令数: 6
 */
void func_46eaa(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_46EB8
 * @note 指令数: 5
 */
void func_46eb8(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_46EC4
 * @note 指令数: 6
 */
void func_46ec4(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_46EEC
 * @note 指令数: 67
 */
void func_46eec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_E000ED1C = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_E000E400 = (volatile uint32_t *)0xE000E400;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_46F70
 * @note 指令数: 24
 */
void func_46f70(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_E000E018 = (volatile uint32_t *)0xE000E018;
    volatile uint32_t *addr_E000E014 = (volatile uint32_t *)0xE000E014;
    volatile uint32_t *addr_E000E010 = (volatile uint32_t *)0xE000E010;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_46FA2
 * @note 指令数: 8
 */
uint32_t func_46fa2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007730 = (volatile uint32_t *)0x20007730;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_46FB4
 * @note 指令数: 7
 */
void func_46fb4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20000314 = (volatile uint32_t *)0x20000314;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_46FE8
 * @note 指令数: 11
 */
uint32_t func_46fe8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_47000
 * @note 指令数: 10
 */
uint32_t func_47000(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_47014
 * @note 指令数: 67
 */
void func_47014(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_40002000 = (volatile uint32_t *)0x40002000;
    volatile uint32_t *addr_40000400 = (volatile uint32_t *)0x40000400;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_470BC
 * @note 指令数: 304
 */
void func_470bc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_40010404 = (volatile uint32_t *)0x40010404;
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47352
 * @note 指令数: 182
 */
void func_47352(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_48000400 = (volatile uint32_t *)0x48000400;
    volatile uint32_t *addr_40010400 = (volatile uint32_t *)0x40010400;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_474E8
 * @note 指令数: 18
 */
void func_474e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4750E
 * @note 指令数: 19
 */
void func_4750e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47536
 * @note 指令数: 12
 */
void func_47536(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47564
 * @note 指令数: 4
 */
void func_47564(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4756E
 * @note 指令数: 26
 */
void func_4756e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_4100 = (volatile uint32_t *)0x4100;
    volatile uint32_t *addr_8015FE0 = (volatile uint32_t *)0x8015FE0;
    volatile uint32_t *addr_4000 = (volatile uint32_t *)0x4000;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_475AC
 * @note 指令数: 4
 */
void func_475ac(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_475B6
 * @note 指令数: 16
 */
void func_475b6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2A00 = (volatile uint32_t *)0x2A00;
    volatile uint32_t *addr_8015FE8 = (volatile uint32_t *)0x8015FE8;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_475E4
 * @note 指令数: 5
 */
void func_475e4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_21 = (volatile uint32_t *)0x21;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_475F0
 * @note 指令数: 157
 */
void func_475f0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;
    volatile uint32_t *addr_8016008 = (volatile uint32_t *)0x8016008;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_47736
 * @note 指令数: 7
 */
uint32_t func_47736(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_47744
 * @note 指令数: 105
 */
void func_47744(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4781C
 * @note 指令数: 45
 */
void func_4781c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_FE = (volatile uint32_t *)0xFE;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_47876
 * @note 指令数: 22
 */
void func_47876(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_478A4
 * @note 指令数: 9
 */
uint32_t func_478a4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_478BC
 * @note 指令数: 12
 */
void func_478bc(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_478D8
 * @note 指令数: 142
 */
void func_478d8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_FE = (volatile uint32_t *)0xFE;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_47A18
 * @note 指令数: 141
 */
void func_47a18(void)
{
    // 内存地址定义
    volatile uint32_t *addr_FFFFA002 = (volatile uint32_t *)0xFFFFA002;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_47B36
 * @note 指令数: 91
 */
void func_47b36(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_47BE6
 * @note 指令数: 12
 */
uint32_t func_47be6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_47C00
 * @note 指令数: 17
 */
void func_47c00(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47C22
 * @note 指令数: 36
 */
void func_47c22(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_FE = (volatile uint32_t *)0xFE;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_47C70
 * @note 指令数: 8
 */
void func_47c70(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47C8C
 * @note 指令数: 37
 */
void func_47c8c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_100000 = (volatile uint32_t *)0x100000;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_8015A8C = (volatile uint32_t *)0x8015A8C;
    volatile uint32_t *addr_20007630 = (volatile uint32_t *)0x20007630;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47CE0
 * @note 指令数: 22
 */
void func_47ce0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8015A8C = (volatile uint32_t *)0x8015A8C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47D0E
 * @note 指令数: 14
 */
void func_47d0e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8015A8C = (volatile uint32_t *)0x8015A8C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47D2A
 * @note 指令数: 193
 */
void func_47d2a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_100000 = (volatile uint32_t *)0x100000;
    volatile uint32_t *addr_64 = (volatile uint32_t *)0x64;
    volatile uint32_t *addr_80153F8 = (volatile uint32_t *)0x80153F8;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47EC4
 * @note 指令数: 23
 */
void func_47ec4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_8015D20 = (volatile uint32_t *)0x8015D20;
    volatile uint32_t *addr_20000 = (volatile uint32_t *)0x20000;
    volatile uint32_t *addr_8015D1C = (volatile uint32_t *)0x8015D1C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_47F18
 * @note 指令数: 2
 */
uint32_t func_47f18(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_47F1C
 * @note 指令数: 5
 */
void func_47f1c(void)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

