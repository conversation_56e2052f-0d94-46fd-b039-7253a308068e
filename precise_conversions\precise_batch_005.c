// 精确转换批次 5 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A962
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_1a962(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x17;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTH    R1, R1
    // 数据扩展操作
    // LSLS    R2, R1, #0x17
    // LSRS    R2, R2, #0x17
    // STR     R2, [R0,#4]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A96C
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_1a96c(uint16_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, [R0,#4]
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A972
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_1a972(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // LDR     R0, [R2]
    // 内存加载操作
    // TST     R0, R1
    // 比较操作
    // BEQ     loc_1A97E
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_1A980
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A982
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_1a982(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MVNS    R2, R1
    // STR     R2, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A988
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1a988(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // LDR     R3, [R0,#4]
    // 内存加载操作
    // AND.W   R2, R3, #0x80000000
    // MOVS    R1, #1
    // R1 = 1;
    // LSLS    R3, R3, #0xC
    // LSRS    R3, R3, #0xC
    // STR     R3, [R0,#4]
    // 内存存储操作
    // ITT EQ
    // LDREQ   R3, [R0]
    // CMPEQ   R3, #0
    // BNE     loc_1A9B2
    // 条件跳转
    // B       loc_1A9BE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A9CC
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1a9cc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV.W   R12, #0x200000
    // CMN.W   R12, R1,LSL#1
    // ITE LS
    // CMNLS.W R12, R3,LSL#1
    // BHI     locret_1A9F0
    // ORR.W   R12, R1, R3
    // ORRS.W  R12, R0, R12,LSL#1
    // ORRS.W  R12, R2, R12
    // BCS     loc_1A9F2
    // CMP     R1, R3
    // 比较操作
    // IT EQ
    // CMPEQ   R0, R2
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A9FC
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1a9fc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x7FF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVW    R5, #0x7FF
    // R5 = 0x7FF;
    // EOR.W   R12, R1, R3
    // AND.W   R12, R12, #0x80000000
    // ANDS.W  R4, R5, R1,LSR#20
    // ITTTE NE
    // ANDSNE.W R7, R5, R3,LSR#20
    // CMPNE   R4, R5
    // CMPNE   R7, R5
    // BEQ     loc_1ABF0
    // 条件跳转
    // SBCS    R4, R7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1AC54
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_1ac54(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1ACB2
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_1acb2(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_F= -0xF
    // var_E= -0xE
    // var_A= -0xA
    // var_9= -9
    // var_8= -8
    // var_7= -7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1ACF6
 * @note 指令数: 43, 标签数: 0
 */
void precise_func_1acf6(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20005B3C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8008266;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // MOVS.W  R8, #0
    // LDR.W   R0, =0x8008266
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR.W   R1, =0x20005B3C
    // 内存加载操作
    // STRB.W  R0, [R8,R1]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // LDR.W   R0, =0x8008266
    // 内存加载操作
    // LDRB    R0, [R0,#1]
    // 内存加载操作
    // LDR.W   R1, =0x20005B3C
    // 内存加载操作
    // STRB.W  R0, [R8,R1]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // LDR.W   R0, =0x20005B3C
    // 内存加载操作
    // STRB.W  R4, [R8,R0]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // LDR.W   R0, =0x20005B3C
    // 内存加载操作
    // STRB.W  R5, [R8,R0]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // MOVS    R0, R7
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // LDR.W   R1, =0x20005B3C
    // 内存加载操作
    // STRB.W  R0, [R8,R1]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // LDR.W   R0, =0x20005B3C
    // 内存加载操作
    // STRB.W  R7, [R8,R0]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // UXTH    R7, R7
    // 数据扩展操作
    // CMP     R7, #0
    // 比较操作
    // BEQ     loc_1AD7E
    // 条件跳转
    // UXTH    R7, R7
    // 数据扩展操作
    // LDR.W   R0, =0x20005B3C
    // 内存加载操作
    // ADDS.W  R9, R8, R0
    // 算术运算
    // MOVS    R2, R7
    // MOVS    R1, R6
    // MOV     R0, R9
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // UXTAH.W R8, R8, R7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1ADBE
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_1adbe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x19;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007E40;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // PUSH.W  {R1-R11,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_1ADF4
    // 条件跳转
    // CMP     R0, #5
    // 比较操作
    // BEQ     loc_1AE0E
    // 条件跳转
    // CMP     R0, #6
    // 比较操作
    // BEQ     loc_1AE4A
    // 条件跳转
    // CMP     R0, #0x19
    // 比较操作
    // BNE     loc_1AEBC
    // 条件跳转
    // MOVS    R6, #0x10
    // R6 = 0x10;
    // LDR.W   R7, =0x20007E40
    // 内存加载操作
    // LDR.W   R8, =0x2000533C
    // 内存加载操作
    // MOVS    R2, R6
    // MOVS    R1, R7
    // MOV     R0, R8
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // MOVS    R0, #0x10
    // R0 = 0x10;
    // MOVS    R5, R0
    // B       loc_1AEBC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1AED2
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_1aed2(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_66= -0x66
    // var_62= -0x62
    // var_61= -0x61
    // var_60= -0x60
    // var_5C= -0x5C
    // var_58= -0x58
    // var_54= -0x54
    // var_50= -0x50
    // var_4C= -0x4C
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_32= -0x32
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1B69E
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_1b69e(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x32;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_38= -0x38
    // var_34= -0x34
    // var_32= -0x32
    // var_30= -0x30
    // var_2C= -0x2C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1B804
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_1b804(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_68= -0x68
    // var_64= -0x64
    // var_60= -0x60
    // var_5C= -0x5C
    // var_58= -0x58
    // var_54= -0x54
    // var_50= -0x50
    // var_4C= -0x4C
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1B984
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1b984(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007F78;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000265;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20007F78
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20000265
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1B9A0
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20000265
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1B9C8
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_1b9c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200080F4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007F70;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007F68;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200080F2;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000816D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_1ACB2
    // 调用函数: sub_1ACB2();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x2000816D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20007F68
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x200080F2
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x200080F4
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x20007F70
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x20007F68
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x20007F78
    // 内存加载操作
    // BL      sub_164A4
    // 调用函数: sub_164A4();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20007F78
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1BA5C
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1ba5c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000265;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xB4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // LDR     R0, =0x20000265
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1BA6C
    // 条件跳转
    // MOVS    R0, #0xB4
    // R0 = 0xB4;
    // MOVS    R5, R0
    // B       loc_1BA70
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1BD80
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_1bd80(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80017FC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xAA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20000262;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000025E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // LDR.W   R0, =0x80017FC
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R5]
    // 内存加载操作
    // LDR.W   R1, =0x2000025E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x2000025E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR.W   R1, =0x20000262
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20000262
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xAA
    // 比较操作
    // BEQ     loc_1BDB0
    // 条件跳转
    // MOVS    R0, #0xFF
    // R0 = 0xFF;
    // LDR.W   R1, =0x20000262
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1BE8C
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1be8c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008142;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_21928
    // 调用函数: sub_21928();
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_1BEAA
    // 条件跳转
    // LDR.W   R0, =0x20008142
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ANDS.W  R0, R0, #0xFE
    // LDR.W   R1, =0x20008142
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_1C0EC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C0EE
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1c0ee(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_21928
    // 调用函数: sub_21928();
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_1C104
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_21928
    // 调用函数: sub_21928();
    // CMP     R0, #2
    // 比较操作
    // BNE     loc_1C108
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C10E
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1c10e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8002014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0x10
    // R5 = 0x10;
    // LDR.W   R6, =0x8002014
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R4
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C124
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_1c124(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000008;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x7C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0x7C ; '|'
    // R2 = 0x7C;
    // MOVS    R1, R4
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // BL      sub_217B2
    // 调用函数: sub_217B2();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_1C146
    // 条件跳转
    // MOVS    R5, #0x7C ; '|'
    // R5 = 0x7C;
    // LDR.W   R6, =0x20000008
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R4
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C148
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_1c148(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8001804;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R2, =0x8001800
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // STR     R2, [R0]
    // 内存存储操作
    // LDR.W   R2, =0x8001804
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C15A
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1c15a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200070A6;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0xA
    // R5 = 0xA;
    // LDR.W   R6, =0x200070A6
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R4
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C170
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_1c170(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007054;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x52;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x20007054
    // 内存加载操作
    // MOVS    R5, R0
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // MOVS    R6, R0
    // ADDS.W  R0, R5, #0x52 ; 'R'
    // 算术运算
    // ADDS    R0, R0, R6
    // 算术运算
    // SUBS    R6, R0, R5
    // 算术运算
    // MOVS    R7, #0xA
    // R7 = 0xA;
    // ADDS.W  R8, R5, #0x52 ; 'R'
    // 算术运算
    // MOVS    R2, R7
    // MOVS    R1, R4
    // MOV     R0, R8
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // MOVS    R2, #0xA
    // R2 = 0xA;
    // ADDS.W  R1, R5, #0x52 ; 'R'
    // 算术运算
    // MOVS    R0, R6
    // UXTH    R0, R0
    // 数据扩展操作
    // BL      sub_2188A
    // 调用函数: sub_2188A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1C1AE
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_1C1C0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C1C4
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1c1c4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200070B0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0xA
    // R5 = 0xA;
    // LDR.W   R6, =0x200070B0
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R4
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C1DA
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_1c1da(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007054;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x20007054
    // 内存加载操作
    // MOVS    R5, R0
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // MOVS    R6, R0
    // ADDS.W  R0, R5, #0x5C ; '\'
    // 算术运算
    // ADDS    R0, R0, R6
    // 算术运算
    // SUBS    R6, R0, R5
    // 算术运算
    // MOVS    R7, #0xA
    // R7 = 0xA;
    // ADDS.W  R8, R5, #0x5C ; '\'
    // 算术运算
    // MOVS    R2, R7
    // MOVS    R1, R4
    // MOV     R0, R8
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // MOVS    R2, #0xA
    // R2 = 0xA;
    // ADDS.W  R1, R5, #0x5C ; '\'
    // 算术运算
    // MOVS    R0, R6
    // UXTH    R0, R0
    // 数据扩展操作
    // BL      sub_2188A
    // 调用函数: sub_2188A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1C218
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_1C22A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C22E
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1c22e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x7300;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xB4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // SUB     SP, SP, #0x48
    // 算术运算
    // MOVS    R0, #0xB4
    // R0 = 0xB4;
    // MOVS    R4, R0
    // MOV.W   R0, #0x7300
    // MOV     R8, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C2AC
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_1c2ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xB5;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // SUB     SP, SP, #0x48
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTH    R5, R5
    // 数据扩展操作
    // UXTAH.W R0, R5, R4
    // CMP     R0, #0xB5
    // 比较操作
    // BNE     loc_1C2C4
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_1C3AA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C3B0
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1c3b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xB5;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R9,LR}
    // 栈操作
    // SUB     SP, SP, #0x44
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R0, #0xB5
    // R0 = 0xB5;
    // MOVS    R5, R0
    // LDRH    R0, [R4]
    // 内存加载操作
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R0, R5
    // 比较操作
    // BCC     loc_1C3C8
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_1C47E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C484
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_1c484(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016760;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x168;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x12;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_12= -0x12
    // PUSH    {R4,R5,LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_1C4DC
    // 条件跳转
    // MOV.W   R0, #0x168
    // MOVS    R5, R0
    // MOVS    R2, #0xA
    // R2 = 0xA;
    // ADD     R1, SP, #0x20+var_1C
    // 算术运算
    // ADDS.W  R0, R5, #0x7300
    // 算术运算
    // UXTH    R0, R0
    // 数据扩展操作
    // BL      sub_217B2
    // 调用函数: sub_217B2();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1C4DC
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRB.W  R0, [SP,#0x20+var_12]
    // LDR.W   R1, =0x8016760
    // 内存加载操作
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // BL      sub_196B4
    // 调用函数: sub_196B4();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1C4DC
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // STRH.W  R0, [SP,#0x20+var_20]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C4E0
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1c4e0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x41;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R9,LR}
    // 栈操作
    // SUB     SP, SP, #0x44
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_21920
    // 调用函数: sub_21920();
    // MOV     R9, R0
    // UXTB.W  R9, R9
    // CMP.W   R9, #0x41 ; 'A'
    // BLT     loc_1C4FE
    // 条件跳转
    // MOVS    R0, #0x40 ; '@'
    // R0 = 0x40;
    // MOV     R9, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C600
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1c600(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x41;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x44
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_21920
    // 调用函数: sub_21920();
    // MOVS    R7, R0
    // UXTB    R7, R7
    // 数据扩展操作
    // CMP     R7, #0x41 ; 'A'
    // 比较操作
    // BLT     loc_1C618
    // 条件跳转
    // MOVS    R0, #0x40 ; '@'
    // R0 = 0x40;
    // MOVS    R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C6B0
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_1c6b0(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C6B4
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_1c6b4(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRH.W  R0, [SP,#8+var_8]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C6D8
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1c6d8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007054;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000008;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x7C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_1C6F6
    // 条件跳转
    // MOVS    R5, #0x7C ; '|'
    // R5 = 0x7C;
    // LDR.W   R6, =0x20000008
    // 内存加载操作
    // LDR.W   R7, =0x20007054
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C764
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_1c764(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3890;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_1C784
    // 条件跳转
    // MOVS    R2, #0x80
    // R2 = 0x80;
    // MOVS    R1, R4
    // MOVW    R0, #0x3890
    // R0 = 0x3890;
    // BL      sub_2188A
    // 调用函数: sub_2188A();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1C798
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_1C7BE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C7C8
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_1c7c8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // MOV     R1, SP
    // MOV.W   R0, #0x3800
    // BL      sub_217B2
    // 调用函数: sub_217B2();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1C7E6
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_1C844
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C848
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_1c848(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // MOV     R1, SP
    // MOV.W   R0, #0x3800
    // BL      sub_217B2
    // 调用函数: sub_217B2();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1C866
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_1C8C4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C8C8
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1c8c8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x18
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // MOV     R1, SP
    // MOV.W   R0, #0x3800
    // BL      sub_217B2
    // 调用函数: sub_217B2();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1C8E4
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_1C950
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C95C
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1c95c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3B10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // SUB     SP, SP, #0x40
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R4, R4
    // 数据扩展操作
    // MOV.W   R0, #0x200
    // MOVW    R1, #0x3B10
    // R1 = 0x3B10;
    // MLA.W   R7, R0, R4, R1
    // MOV.W   R8, #0x200
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [R5]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1C9DA
 * @note 指令数: 14, 标签数: 1
 */
void precise_func_1c9da(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007E40;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     loc_1C9FA
    // 条件跳转
    // LDR     R0, =0x20007E40
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R1, R0, R4,LSL#2
    // 算术运算
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_1C95C
    // 调用函数: sub_1C95C();
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_1C9E0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CA00
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1ca00(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x3910;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x40
    // 算术运算
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOV.W   R0, #0x200
    // MOVW    R1, #0x3910
    // R1 = 0x3910;
    // MLA.W   R6, R0, R4, R1
    // MOVS    R7, #0
    // R7 = 0;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CAA2
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1caa2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x4B10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // SUB     SP, SP, #0x40
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R4, R4
    // 数据扩展操作
    // MOV.W   R0, #0x200
    // MOVW    R1, #0x4B10
    // R1 = 0x4B10;
    // MLA.W   R7, R0, R4, R1
    // MOV.W   R8, #0x200
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R1, #0
    // R1 = 0;
    // STRD.W  R0, R1, [R5]
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CBA6
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_1cba6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x4910;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH    {R3-R7,LR}
    // 栈操作
    // VPUSH   {D0}
    // SUB     SP, SP, #0x40
    // 算术运算
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOV.W   R0, #0x200
    // MOVW    R1, #0x4910
    // R1 = 0x4910;
    // MLA.W   R5, R0, R4, R1
    // MOVS    R6, #0
    // R6 = 0;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CCAE
 * @note 指令数: 14, 标签数: 1
 */
void precise_func_1ccae(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007738;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #8
    // 比较操作
    // BGE     loc_1CCCE
    // 条件跳转
    // LDR     R0, =0x20007738
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R1, R0, R4,LSL#3
    // 算术运算
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_1CAA2
    // 调用函数: sub_1CAA2();
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_1CCB4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CCF4
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_1ccf4(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_1CFFE
    // 调用函数: sub_1CFFE();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CCFC
 * @note 指令数: 36, 标签数: 0
 */
void precise_func_1ccfc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x55;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFFFFFFFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x803F800;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x8001800
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, =0x8001804
    // 内存加载操作
    // MOVS    R6, R0
    // LDR     R0, =0x803F800
    // 内存加载操作
    // MOVS    R7, R0
    // LDR     R0, =0x803F804
    // 内存加载操作
    // MOV     R8, R0
    // MOVS.W  R0, #0xFFFFFFFF
    // LDR     R1, =0x200080A4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, [R5]
    // 内存加载操作
    // CMN.W   R0, #1
    // BNE     loc_1CD50
    // 条件跳转
    // LDR     R0, [R6]
    // 内存加载操作
    // CMN.W   R0, #1
    // BNE     loc_1CD50
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008180
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR     R1, =0x2000812A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR     R1, =0x2000812C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x55 ; 'U'
    // R0 = 0x55;
    // LDR     R1, =0x20008128
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_1931E
    // 调用函数: sub_1931E();
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_1931E
    // 调用函数: sub_1931E();
    // B       loc_1CE22
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CEA2
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_1cea2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000812A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8001804;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // LDR     R0, =0x2000812A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0x80
    // 比较操作
    // BNE     loc_1CEFA
    // 条件跳转
    // LDR     R0, =0x8001800
    // 内存加载操作
    // MOVS    R4, R0
    // LDR     R0, =0x8001804
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R4]
    // 内存加载操作
    // CMN.W   R0, #1
    // BNE     loc_1CEC6
    // 条件跳转
    // LDR     R0, [R5]
    // 内存加载操作
    // CMN.W   R0, #1
    // BEQ     loc_1CEF0
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1CFA8
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_1cfa8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008180;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20008180
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1CFB4
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_1CFB6
    // 无条件跳转
}

