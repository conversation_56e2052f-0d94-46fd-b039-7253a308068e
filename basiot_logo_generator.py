#!/usr/bin/env python3
"""
BASIOT Logo生成器
为AT32F403AVG项目生成BASIOT文字logo的位图数据

作者: AT32F403AVG汇编转换项目
日期: 2024
"""

import numpy as np

def create_basiot_font():
    """创建BASIOT字母的8×8像素字体"""
    
    # 定义每个字母的8×8位图 (1=像素点亮, 0=像素熄灭)
    font_data = {
        'B': [
            0b11111100,  # ████████
            0b01000010,  # █    █
            0b01000010,  # █    █
            0b01111100,  # █████
            0b01000010,  # █    █
            0b01000010,  # █    █
            0b11111100,  # ████████
            0b00000000   #
        ],
        'A': [
            0b00111000,  #   ███
            0b01000100,  #  █   █
            0b01000100,  #  █   █
            0b01111100,  #  █████
            0b01000100,  #  █   █
            0b01000100,  #  █   █
            0b01000100,  #  █   █
            0b00000000   #
        ],
        'S': [
            0b01111100,  #  █████
            0b10000000,  # █
            0b10000000,  # █
            0b01111100,  #  █████
            0b00000010,  #      █
            0b00000010,  #      █
            0b11111100,  # ███████
            0b00000000   #
        ],
        'I': [
            0b11111110,  # ███████
            0b00010000,  #    █
            0b00010000,  #    █
            0b00010000,  #    █
            0b00010000,  #    █
            0b00010000,  #    █
            0b11111110,  # ███████
            0b00000000   #
        ],
        'O': [
            0b01111100,  #  █████
            0b10000010,  # █     █
            0b10000010,  # █     █
            0b10000010,  # █     █
            0b10000010,  # █     █
            0b10000010,  # █     █
            0b01111100,  #  █████
            0b00000000   #
        ],
        'T': [
            0b11111110,  # ███████
            0b00010000,  #    █
            0b00010000,  #    █
            0b00010000,  #    █
            0b00010000,  #    █
            0b00010000,  #    █
            0b00010000,  #    █
            0b00000000   #
        ]
    }
    
    return font_data

def create_basiot_bitmap():
    """创建BASIOT的64×32位图"""
    
    # 创建64×32的位图
    bitmap = np.zeros((32, 64), dtype=np.uint8)
    
    # 获取字体数据
    font_data = create_basiot_font()
    
    # BASIOT字母
    letters = ['B', 'A', 'S', 'I', 'O', 'T']
    
    # 计算字母间距和起始位置
    letter_width = 8
    letter_spacing = 2
    total_width = len(letters) * letter_width + (len(letters) - 1) * letter_spacing
    start_x = (64 - total_width) // 2  # 居中显示
    start_y = 12  # 垂直居中 (32-8)/2 = 12
    
    # 绘制每个字母
    for i, letter in enumerate(letters):
        x_offset = start_x + i * (letter_width + letter_spacing)
        
        # 绘制字母的每一行
        for row in range(8):
            byte_data = font_data[letter][row]
            for col in range(8):
                if byte_data & (1 << (7 - col)):  # MSB位序
                    if (start_y + row < 32) and (x_offset + col < 64):
                        bitmap[start_y + row, x_offset + col] = 1
    
    return bitmap

def bitmap_to_c_array(bitmap):
    """将位图转换为C语言数组格式"""
    height, width = bitmap.shape
    
    # 计算需要的字节数 (64×32 = 2048位 = 256字节)
    total_bits = height * width
    total_bytes = (total_bits + 7) // 8
    
    # 转换为字节数组
    byte_array = []
    
    for byte_idx in range(total_bytes):
        byte_val = 0
        for bit in range(8):
            bit_index = byte_idx * 8 + bit
            if bit_index < total_bits:
                y = bit_index // width
                x = bit_index % width
                if y < height and x < width and bitmap[y, x]:
                    byte_val |= (1 << (7 - bit))  # MSB位序
        byte_array.append(byte_val)
    
    # 转换为32位字数组
    word_array = []
    for i in range(0, len(byte_array), 4):
        word = 0
        for j in range(4):
            if i + j < len(byte_array):
                word |= (byte_array[i + j] << (8 * j))  # 小端序
        word_array.append(word)
    
    return word_array

def generate_c_code():
    """生成C语言代码"""
    bitmap = create_basiot_bitmap()
    word_array = bitmap_to_c_array(bitmap)
    
    # 生成C代码
    c_code = '''/**
 * @brief BASIOT Logo数据 - 替换汇编代码中的原logo
 * 
 * 内容: "BASIOT" 文字logo
 * 尺寸: 64×32像素
 * 数据大小: 256字节 (64个32位字)
 * 格式: 单色位图
 * 字体: 8×8像素点阵字体
 * 布局: 居中显示
 * 
 * 替换位置: 汇编代码0x8016A54 (第40137-40144行)
 */
static const uint32_t basiot_logo_data[64] = {
'''
    
    # 按8个字为一行输出
    for i in range(0, len(word_array), 8):
        c_code += "    "
        for j in range(8):
            if i + j < len(word_array):
                c_code += f"0x{word_array[i + j]:08X}"
                if i + j < len(word_array) - 1:
                    c_code += ", "
        c_code += "\n"
    
    c_code += "};\n"
    
    return c_code, bitmap, word_array

def print_ascii_preview(bitmap):
    """打印ASCII预览"""
    height, width = bitmap.shape
    
    print("=== BASIOT Logo ASCII预览 ===")
    print(f"尺寸: {width}×{height}像素")
    print("=" * (width + 4))
    
    for y in range(height):
        print("| ", end="")
        for x in range(width):
            print("█" if bitmap[y, x] else " ", end="")
        print(" |")
    
    print("=" * (width + 4))

def analyze_basiot_logo(bitmap, word_array):
    """分析BASIOT logo数据"""
    height, width = bitmap.shape
    total_pixels = height * width
    white_pixels = np.sum(bitmap)
    black_pixels = total_pixels - white_pixels
    
    print("\n=== BASIOT Logo数据分析 ===")
    print(f"文字内容: BASIOT")
    print(f"总像素数: {total_pixels}")
    print(f"点亮像素: {white_pixels} ({white_pixels/total_pixels*100:.1f}%)")
    print(f"熄灭像素: {black_pixels} ({black_pixels/total_pixels*100:.1f}%)")
    print(f"数据大小: {len(word_array) * 4} 字节")
    print(f"数据字数: {len(word_array)} 个32位字")
    print(f"字体规格: 8×8像素点阵")
    print(f"字母数量: 6个 (B-A-S-I-O-T)")
    print(f"布局方式: 水平居中显示")

def generate_verification_code():
    """生成验证代码"""
    print("\n=== C语言验证代码 ===")
    print("""
// BASIOT logo验证函数
uint8_t verify_basiot_logo(void) {
    // 检查第一个字母'B'的特征像素
    if (get_logo_pixel(16, 12) == 0) return 0;  // B的左上角
    if (get_logo_pixel(20, 15) == 0) return 0;  // B的中间横线
    
    // 检查最后一个字母'T'的特征像素  
    if (get_logo_pixel(56, 12) == 0) return 0;  // T的顶部横线
    if (get_logo_pixel(59, 16) == 0) return 0;  // T的竖线
    
    return 1;  // 验证通过
}

// 显示BASIOT logo信息
void print_basiot_info(void) {
    printf("Logo: BASIOT\\n");
    printf("Size: 64x32 pixels\\n");
    printf("Font: 8x8 dot matrix\\n");
    printf("Layout: Centered\\n");
}
""")

if __name__ == "__main__":
    print("BASIOT Logo生成器")
    print("=" * 50)
    
    # 生成位图和C代码
    c_code, bitmap, word_array = generate_c_code()
    
    # 显示ASCII预览
    print_ascii_preview(bitmap)
    
    # 分析数据
    analyze_basiot_logo(bitmap, word_array)
    
    # 生成验证代码
    generate_verification_code()
    
    # 保存C代码
    with open("basiot_logo_data.c", "w", encoding="utf-8") as f:
        f.write(c_code)
    
    print(f"\n✅ BASIOT logo C代码已保存到: basiot_logo_data.c")
    print("✅ ASCII预览已生成")
    
    print("\n📋 下一步操作:")
    print("1. 将生成的数据替换到 logo_data_handler.c")
    print("2. 更新相关注释和函数名")
    print("3. 重新编译项目")
    print("4. 在硬件上测试BASIOT logo显示")
    
    print("\n🎯 替换说明:")
    print("- 原位置: 汇编代码0x8016A54 (第40137-40144行)")
    print("- 新内容: BASIOT文字logo")
    print("- 格式: 64×32单色位图")
    print("- 字体: 8×8像素点阵字体")
    print("- 布局: 居中显示")
