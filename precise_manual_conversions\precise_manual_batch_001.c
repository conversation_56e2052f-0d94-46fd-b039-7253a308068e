// 精确手工转换第一批 - 100%复刻汇编逻辑
// 每个函数都经过逐行汇编分析和手工验证
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 浮点数组访问函数 - 手工精确转换
 * @note 对应汇编函数 sub_14B18
 * @param index 数组索引 (R0寄存器，8位无符号)
 * @return 浮点数值 (S0寄存器)
 * 
 * 汇编逻辑:
 * UXTB R0, R0          - 将索引限制为8位无符号
 * CMP R0, #0x10        - 比较索引与16
 * BLT loc_14B24        - 如果小于16，跳转到数组访问
 * FLDS S0, =0.0        - 否则返回0.0
 * B locret_14B32       - 跳转到返回
 * loc_14B24:
 * LDR.W R1, =0x20007584 - 加载浮点数组基地址
 * UXTB R0, R0          - 再次确保索引为8位
 * ADDS.W R0, R1, R0,LSL#2 - 计算地址: base + index*4
 * FLDS S0, [R0]        - 加载浮点数到S0
 * locret_14B32:
 * BX LR                - 返回
 */
float sub_14B18(uint8_t index)
{
    // UXTB R0, R0 - 确保索引为8位无符号
    index = index & 0xFF;
    
    // CMP R0, #0x10 - 比较索引与16
    // BLT loc_14B24 - 如果小于16，跳转到数组访问
    if (index >= 0x10) {
        // FLDS S0, =0.0 - 返回0.0
        return 0.0f;
    }
    
    // loc_14B24:
    // LDR.W R1, =0x20007584 - 加载浮点数组基地址
    volatile float *float_array = (volatile float *)0x20007584;
    
    // UXTB R0, R0 - 再次确保索引为8位
    // ADDS.W R0, R1, R0,LSL#2 - 计算数组元素地址
    // FLDS S0, [R0] - 加载浮点数
    return float_array[index];
    
    // locret_14B32:
    // BX LR - 函数返回
}

/**
 * @brief 数组操作和查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_14B34
 * @param index 数组索引 (R0寄存器，8位无符号)
 * @return 查表结果 (R0寄存器，16位)
 * 
 * 汇编逻辑:
 * 1. 从0x2000797C数组读取16位值
 * 2. 如果值>=6，则设置为5并写回
 * 3. 使用该值作为索引从0x8016874查找表读取
 * 4. 将结果存储到0x20007A5C数组
 * 5. 返回最终结果
 */
uint16_t sub_14B34(uint8_t index)
{
    // LDR.W R1, =0x2000797C - 加载16位数组基地址
    volatile uint16_t *array_797C = (volatile uint16_t *)0x2000797C;
    
    // UXTB R0, R0 - 确保索引为8位无符号
    index = index & 0xFF;
    
    // LDRH.W R1, [R1,R0,LSL#1] - 读取16位值 (base + index*2)
    uint16_t value = array_797C[index];
    
    // CMP R1, #6 - 比较值与6
    // BLT loc_14B4E - 如果大于等于6，设置为5
    if (value >= 6) {
        // MOVS R1, #5
        value = 5;
        // LDR.W R2, =0x2000797C
        // UXTB R0, R0
        // STRH.W R1, [R2,R0,LSL#1] - 写回数组
        array_797C[index] = value;
    }
    
    // loc_14B4E:
    // LDR.W R1, =0x8016874 - 加载查找表基地址
    volatile uint8_t *lookup_table = (volatile uint8_t *)0x8016874;
    
    // LDR.W R2, =0x2000797C
    // UXTB R0, R0
    // LDRH.W R2, [R2,R0,LSL#1] - 重新读取数组值作为查找表索引
    uint16_t table_index = array_797C[index];
    
    // LDRB R1, [R2,R1] - 从查找表读取字节值
    uint8_t lookup_result = lookup_table[table_index];
    
    // LDR.W R2, =0x20007A5C - 加载另一个数组基地址
    volatile uint16_t *array_7A5C = (volatile uint16_t *)0x20007A5C;
    
    // UXTB R0, R0
    // STRH.W R1, [R2,R0,LSL#1] - 将查找结果存储到另一个数组
    array_7A5C[index] = lookup_result;
    
    // LDR.W R1, =0x20007A5C
    // UXTB R0, R0
    // LDRH.W R0, [R1,R0,LSL#1] - 读取并返回最终结果
    return array_7A5C[index];
    
    // BX LR - 函数返回
}

