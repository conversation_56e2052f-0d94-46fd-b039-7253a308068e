#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确汇编到C代码分析器
逐函数分析汇编代码，生成完全对应的C代码实现
"""

import re
import os
from typing import List, Dict, Tuple, Optional

class PreciseAsmAnalyzer:
    def __init__(self, asm_file_path: str):
        self.asm_file_path = asm_file_path
        self.functions = []
        
    def extract_function_asm(self, func_name: str) -> Dict:
        """提取指定函数的完整汇编代码"""
        try:
            with open(self.asm_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
        except Exception as e:
            print(f"无法读取汇编文件: {e}")
            return {}
        
        func_info = {
            'name': func_name,
            'asm_lines': [],
            'instructions': [],
            'labels': [],
            'memory_refs': [],
            'constants': [],
            'start_line': -1,
            'end_line': -1
        }
        
        in_function = False
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # 检测函数开始
            if line_stripped == func_name:
                in_function = True
                func_info['start_line'] = i + 1
                continue
            
            if in_function:
                func_info['asm_lines'].append(line_stripped)
                
                # 检测函数结束
                if (line_stripped.startswith('sub_') and line_stripped != func_name) or \
                   line_stripped.startswith('; End of function') or \
                   (line_stripped.startswith('loc_') and 'End' in line_stripped):
                    func_info['end_line'] = i
                    break
                
                # 分析指令
                if line_stripped and not line_stripped.startswith(';'):
                    # 检测标签
                    if line_stripped.startswith('loc_') or line_stripped.startswith('locret_'):
                        func_info['labels'].append(line_stripped)
                    else:
                        # 检测指令
                        func_info['instructions'].append(line_stripped)
                        
                        # 提取内存引用
                        mem_refs = re.findall(r'0x[0-9A-Fa-f]+', line_stripped)
                        func_info['memory_refs'].extend(mem_refs)
                        
                        # 提取常量
                        constants = re.findall(r'#0x[0-9A-Fa-f]+|#\d+', line_stripped)
                        func_info['constants'].extend(constants)
        
        return func_info
    
    def analyze_instruction(self, instruction: str) -> Dict:
        """分析单条汇编指令"""
        instruction = instruction.strip()
        parts = instruction.split()
        
        if not parts:
            return {'type': 'empty', 'operation': '', 'operands': []}
        
        operation = parts[0].upper()
        operands = []
        
        if len(parts) > 1:
            # 解析操作数
            operand_str = ' '.join(parts[1:])
            operands = [op.strip() for op in operand_str.split(',')]
        
        analysis = {
            'type': self.classify_instruction(operation),
            'operation': operation,
            'operands': operands,
            'raw': instruction
        }
        
        return analysis
    
    def classify_instruction(self, operation: str) -> str:
        """分类指令类型"""
        data_ops = ['MOV', 'LDR', 'STR', 'LDRB', 'STRB', 'LDRH', 'STRH', 'LDM', 'STM']
        arithmetic_ops = ['ADD', 'SUB', 'MUL', 'DIV', 'AND', 'ORR', 'EOR', 'LSL', 'LSR', 'ASR']
        compare_ops = ['CMP', 'CMN', 'TST', 'TEQ']
        branch_ops = ['B', 'BL', 'BX', 'BLX', 'BEQ', 'BNE', 'BLT', 'BGT', 'BLE', 'BGE']
        float_ops = ['FLDS', 'FSTS', 'FADD', 'FSUB', 'FMUL', 'FDIV', 'FCMP', 'VMOV']
        extend_ops = ['UXTB', 'UXTH', 'SXTB', 'SXTH']
        
        if operation in data_ops:
            return 'data_transfer'
        elif operation in arithmetic_ops:
            return 'arithmetic'
        elif operation in compare_ops:
            return 'compare'
        elif operation in branch_ops:
            return 'branch'
        elif operation in float_ops:
            return 'float'
        elif operation in extend_ops:
            return 'extend'
        else:
            return 'other'
    
    def generate_precise_c_code(self, func_info: Dict) -> str:
        """根据汇编代码生成精确的C代码"""
        func_name = func_info['name']
        c_name = self.generate_c_function_name(func_name)
        
        # 分析函数特征
        has_float = any('FLDS' in instr or 'FSTS' in instr or 'VMOV' in instr 
                       for instr in func_info['instructions'])
        has_return = any('BX' in instr and 'LR' in instr 
                        for instr in func_info['instructions'])
        
        # 确定返回类型
        if has_float:
            return_type = "float"
        elif has_return:
            return_type = "uint32_t"
        else:
            return_type = "void"
        
        # 分析参数
        params = self.analyze_parameters(func_info)
        param_str = ", ".join(params) if params else "void"
        
        # 生成函数体
        c_code = f"""/**
 * @brief {self.generate_function_description(func_info)}
 * @note 精确对应汇编函数 {func_name}
 */
{return_type} {c_name}({param_str})
{{
"""
        
        # 添加局部变量声明
        c_code += self.generate_variable_declarations(func_info)
        
        # 转换汇编指令为C代码
        c_code += self.convert_instructions_to_c(func_info)
        
        c_code += "}\n"
        
        return c_code
    
    def generate_c_function_name(self, asm_name: str) -> str:
        """生成C函数名"""
        hex_part = asm_name.replace('sub_', '')
        return f"precise_function_{hex_part.lower()}"
    
    def generate_function_description(self, func_info: Dict) -> str:
        """生成函数描述"""
        if any('FLDS' in instr for instr in func_info['instructions']):
            return "浮点数处理函数"
        elif any('CMP' in instr for instr in func_info['instructions']):
            return "比较和条件处理函数"
        elif any('LDR' in instr for instr in func_info['instructions']):
            return "内存数据处理函数"
        else:
            return "通用处理函数"
    
    def analyze_parameters(self, func_info: Dict) -> List[str]:
        """分析函数参数"""
        params = []
        
        # 检查寄存器使用情况
        reg_usage = {'R0': False, 'R1': False, 'R2': False, 'R3': False}
        
        for instr in func_info['instructions']:
            for reg in reg_usage:
                if reg in instr and not reg_usage[reg]:
                    reg_usage[reg] = True
        
        # 根据寄存器使用情况确定参数
        if reg_usage['R0']:
            params.append("uint32_t r0_param")
        if reg_usage['R1']:
            params.append("uint32_t r1_param")
        if reg_usage['R2']:
            params.append("uint32_t r2_param")
        if reg_usage['R3']:
            params.append("uint32_t r3_param")
        
        return params
    
    def generate_variable_declarations(self, func_info: Dict) -> str:
        """生成变量声明"""
        declarations = ""
        
        # 添加内存地址变量
        unique_addrs = list(set(func_info['memory_refs']))
        for i, addr in enumerate(unique_addrs[:5]):  # 限制前5个地址
            declarations += f"    volatile uint32_t *mem_addr_{i} = (volatile uint32_t *){addr};\n"
        
        # 添加基本变量
        declarations += "    uint32_t temp_result = 0;\n"
        
        if any('FLDS' in instr for instr in func_info['instructions']):
            declarations += "    float float_result = 0.0f;\n"
        
        declarations += "\n"
        return declarations
    
    def convert_instructions_to_c(self, func_info: Dict) -> str:
        """将汇编指令转换为C代码"""
        c_code = ""
        
        for instr in func_info['instructions']:
            if not instr or instr.startswith('loc_') or instr.startswith('locret_'):
                continue
                
            analysis = self.analyze_instruction(instr)
            c_line = self.instruction_to_c(analysis)
            
            if c_line:
                c_code += f"    {c_line}\n"
        
        # 添加返回语句
        if any('BX' in instr and 'LR' in instr for instr in func_info['instructions']):
            if any('FLDS' in instr for instr in func_info['instructions']):
                c_code += "    return float_result;\n"
            else:
                c_code += "    return temp_result;\n"
        
        return c_code
    
    def instruction_to_c(self, analysis: Dict) -> str:
        """将单条指令转换为C代码"""
        operation = analysis['operation']
        operands = analysis['operands']
        
        if operation == 'UXTB':
            # 无符号字节扩展
            if len(operands) >= 2:
                return f"// UXTB: {operands[0]} = {operands[1]} & 0xFF;"
        
        elif operation == 'CMP':
            # 比较指令
            if len(operands) >= 2:
                return f"// CMP: 比较 {operands[0]} 与 {operands[1]}"
        
        elif operation == 'BLT':
            # 小于分支
            return "// BLT: 如果小于则分支"
        
        elif operation == 'FLDS':
            # 浮点加载
            if len(operands) >= 2:
                return f"// FLDS: 加载浮点数到 {operands[0]}"
        
        elif operation == 'LDR' or operation == 'LDR.W':
            # 加载指令
            if len(operands) >= 2:
                return f"// LDR: 从内存加载数据"
        
        elif operation == 'ADDS' or operation == 'ADDS.W':
            # 加法指令
            return f"// ADDS: 执行加法运算"
        
        elif operation == 'BX':
            # 分支交换
            if 'LR' in operands:
                return "// BX LR: 函数返回"
        
        elif operation == 'B':
            # 无条件分支
            return f"// B: 无条件跳转"
        
        return f"// {analysis['raw']}"

def analyze_first_functions():
    """分析前几个函数"""
    analyzer = PreciseAsmAnalyzer("bin/MH25QH128.bin.asm")
    
    # 分析前5个函数
    functions_to_analyze = ['sub_14B18', 'sub_14B34', 'sub_14CB4', 'sub_14E08', 'sub_15050']
    
    print("=== 精确汇编到C代码分析报告 ===\n")
    
    for func_name in functions_to_analyze:
        print(f"分析函数: {func_name}")
        print("=" * 50)
        
        # 提取汇编代码
        func_info = analyzer.extract_function_asm(func_name)
        
        if not func_info['asm_lines']:
            print(f"未找到函数 {func_name}")
            continue
        
        print("汇编代码:")
        for line in func_info['asm_lines'][:15]:  # 显示前15行
            print(f"  {line}")
        
        print(f"\n指令分析:")
        print(f"  总指令数: {len(func_info['instructions'])}")
        print(f"  标签数: {len(func_info['labels'])}")
        print(f"  内存引用: {func_info['memory_refs'][:5]}")
        
        # 生成精确C代码
        c_code = analyzer.generate_precise_c_code(func_info)
        print(f"\n精确C代码转换:")
        print(c_code)
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    analyze_first_functions()
