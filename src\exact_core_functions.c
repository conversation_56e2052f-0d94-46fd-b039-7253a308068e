/**
 * @file exact_core_functions.c
 * @brief 精确核心函数模块 - 100%汇编转换
 * <AUTHOR>
 * @date 2024
 * 
 * 本模块包含从汇编代码100%精确转换的核心系统函数：
 * - interrupt_priority_set (sub_8000240) - 中断优先级设置，19条指令
 * - systick_timer_config (sub_800026A) - SysTick定时器配置，23条指令
 * - gpio_pin_control (sub_80002A0) - GPIO引脚控制，13条指令
 * - crc16_checksum_calculate (sub_80002BA) - CRC16校验计算，43条指令
 * 
 * 每个函数都与原汇编代码逐指令对应，确保100%功能一致性
 */

#include "at32f403avg_assembly_conversion.h"

// =============================================================================
// 精确核心函数 (100%汇编转换)
// =============================================================================

/**
 * @brief interrupt_priority_set - 中断优先级设置函数
 * 
 * 汇编函数: sub_8000240 (地址0x8000240, 19条指令)
 * 汇编代码 (行125-148):
 *   PUSH    {R4}
 *   SXTB    R0, R0
 *   CMP     R0, #0
 *   BMI     loc_8000254
 *   LSLS    R2, R1, #4
 *   LDR.W   R3, dword_8000A70  ; 0xE000E400
 *   SXTB    R0, R0
 *   STRB    R2, [R0,R3]
 *   B       loc_8000266
 * loc_8000254:
 *   LSLS    R2, R1, #4
 *   LDR.W   R3, dword_8000A74  ; 0xE000ED18
 *   SXTB    R0, R0
 *   ANDS.W  R4, R0, #0xF
 *   ADDS    R3, R4, R3
 *   STRB.W  R2, [R3,#-4]
 * loc_8000266:
 *   POP     {R4}
 *   BX      LR
 * 
 * @param irq_number 中断号 (有符号8位，负数表示系统异常)
 * @param priority_level 优先级值 (0-15，左移4位后存储)
 */
void interrupt_priority_set(int8_t irq_number, uint8_t priority_level) {
    uint32_t r4_register;  // 对应汇编R4寄存器
    uint32_t r2_register;  // 对应汇编R2寄存器  
    uint32_t r3_register;  // 对应汇编R3寄存器
    
    // PUSH {R4} - 保存R4寄存器
    // SXTB R0, R0 - 符号扩展R0到32位 (irq_number已经是int8_t)
    // CMP R0, #0 - 比较irq_number与0
    // BMI loc_8000254 - 如果irq_number<0跳转到系统异常处理
    
    if (irq_number < 0) {
        // loc_8000254: 系统异常优先级设置分支
        // LSLS R2, R1, #4 - R2 = priority_level << 4
        r2_register = (uint32_t)priority_level << 4;
        
        // LDR.W R3, dword_8000A74 - 加载0xE000ED18 (SCB_SHCSR)
        r3_register = SCB_SHCSR;  // 0xE000ED18
        
        // SXTB R0, R0 - 符号扩展irq_number (已处理)
        // ANDS.W R4, R0, #0xF - R4 = irq_number & 0xF
        r4_register = (uint32_t)irq_number & 0xF;
        
        // ADDS R3, R4, R3 - R3 = R4 + R3
        r3_register = r4_register + r3_register;
        
        // STRB.W R2, [R3,#-4] - 存储优先级到[R3-4]
        *((volatile uint8_t*)(r3_register - 4)) = (uint8_t)r2_register;
    } else {
        // 外部中断优先级设置分支
        // LSLS R2, R1, #4 - R2 = priority_level << 4
        r2_register = (uint32_t)priority_level << 4;
        
        // LDR.W R3, dword_8000A70 - 加载0xE000E400 (NVIC_IPR_BASE)
        r3_register = NVIC_IPR_BASE;  // 0xE000E400
        
        // SXTB R0, R0 - 符号扩展irq_number (已处理)
        // STRB R2, [R0,R3] - 存储优先级到[irq_number+R3]
        *((volatile uint8_t*)((uint32_t)irq_number + r3_register)) = (uint8_t)r2_register;
    }
    
    // loc_8000266:
    // POP {R4} - 恢复R4寄存器
    // BX LR - 返回
}

/**
 * @brief systick_timer_config - SysTick定时器配置函数
 * 
 * 汇编函数: sub_800026A (地址0x800026A, 23条指令)
 * 汇编代码 (行154-181):
 *   PUSH    {R4,LR}
 *   MOVS    R4, R0
 *   SUBS    R0, R4, #1
 *   CMP.W   R0, #0x1000000
 *   BCC     loc_800027A
 *   MOVS    R0, #1
 *   B       locret_800029E
 * loc_800027A:
 *   SUBS    R0, R4, #1
 *   LDR.W   R1, dword_8000A78  ; 0xE000E014
 *   STR     R0, [R1]
 *   MOVS    R1, #0xF
 *   MOVS.W  R0, #0xFFFFFFFF
 *   BL      sub_8000240
 *   MOVS    R0, #0
 *   LDR.W   R1, dword_8000A7C  ; 0xE000E018
 *   STR     R0, [R1]
 *   MOVS    R0, #7
 *   LDR.W   R1, dword_8000A80  ; 0xE000E010
 *   STR     R0, [R1]
 *   MOVS    R0, #0
 * locret_800029E:
 *   POP     {R4,PC}
 * 
 * @param reload_value 定时器重载值 (最大0xFFFFFF)
 * @return 配置结果 (0=成功, 1=失败)
 */
uint32_t systick_timer_config(uint32_t reload_value) {
    uint32_t r4_register;  // 对应汇编R4寄存器
    uint32_t r0_register;  // 对应汇编R0寄存器
    uint32_t r1_register;  // 对应汇编R1寄存器
    
    // PUSH {R4,LR} - 保存寄存器
    // MOVS R4, R0 - R4 = reload_value
    r4_register = reload_value;
    
    // SUBS R0, R4, #1 - R0 = R4 - 1
    r0_register = r4_register - 1;
    
    // CMP.W R0, #0x1000000 - 比较R0与0x1000000
    // BCC loc_800027A - 如果R0 < 0x1000000跳转
    if (r0_register >= 0x1000000) {
        // MOVS R0, #1 - 返回1表示失败
        return 1;
    }
    
    // loc_800027A: 配置成功分支
    // SUBS R0, R4, #1 - R0 = R4 - 1
    r0_register = r4_register - 1;
    
    // LDR.W R1, dword_8000A78 - 加载0xE000E014 (SysTick LOAD)
    r1_register = SYSTICK_LOAD;  // 0xE000E014
    
    // STR R0, [R1] - 设置SysTick重载值
    *((volatile uint32_t*)r1_register) = r0_register;
    
    // MOVS R1, #0xF - R1 = 15
    r1_register = 0xF;
    
    // MOVS.W R0, #0xFFFFFFFF - R0 = -1 (SysTick异常号)
    r0_register = 0xFFFFFFFF;
    
    // BL sub_8000240 - 调用中断优先级设置函数
    interrupt_priority_set((int8_t)r0_register, (uint8_t)r1_register);
    
    // MOVS R0, #0 - R0 = 0
    r0_register = 0;
    
    // LDR.W R1, dword_8000A7C - 加载0xE000E018 (SysTick VAL)
    r1_register = SYSTICK_VAL;  // 0xE000E018
    
    // STR R0, [R1] - 清除SysTick当前值
    *((volatile uint32_t*)r1_register) = r0_register;
    
    // MOVS R0, #7 - R0 = 7 (ENABLE|TICKINT|CLKSOURCE)
    r0_register = 7;
    
    // LDR.W R1, dword_8000A80 - 加载0xE000E010 (SysTick CTRL)
    r1_register = SYSTICK_CTRL;  // 0xE000E010
    
    // STR R0, [R1] - 设置SysTick控制寄存器
    *((volatile uint32_t*)r1_register) = r0_register;
    
    // MOVS R0, #0 - 返回0表示成功
    return 0;
}

/**
 * @brief gpio_pin_control - GPIO引脚控制函数
 * 
 * 汇编函数: sub_80002A0 (地址0x80002A0, 13条指令)
 * 汇编代码 (行187-204):
 *   UXTB    R0, R0
 *   CMP     R0, #0
 *   BEQ     loc_80002B0
 *   MOVS    R1, #0x10
 *   LDR.W   R2, dword_8000A84  ; 0x40011410
 *   STR     R1, [R2]
 *   B       locret_80002B8
 * loc_80002B0:
 *   MOVS    R1, #0x10
 *   LDR.W   R2, dword_8000A88  ; 0x40011414
 *   STR     R1, [R2]
 * locret_80002B8:
 *   BX      LR
 * 
 * @param pin_state 引脚状态 (0=复位GPIOD引脚4, 非0=设置GPIOD引脚4)
 */
void gpio_pin_control(uint8_t pin_state) {
    uint32_t r1_register;  // 对应汇编R1寄存器
    uint32_t r2_register;  // 对应汇编R2寄存器
    
    // UXTB R0, R0 - 零扩展pin_state到32位 (已经是uint8_t)
    // CMP R0, #0 - 比较pin_state与0
    // BEQ loc_80002B0 - 如果pin_state==0跳转
    
    if (pin_state == 0) {
        // loc_80002B0: 复位GPIO引脚分支
        // MOVS R1, #0x10 - R1 = 0x10 (引脚4位掩码)
        r1_register = GPIO_PIN_4;  // 0x10
        
        // LDR.W R2, dword_8000A88 - 加载0x40011414 (GPIOD_BRR)
        r2_register = GPIOD_BRR;  // 0x40011414
        
        // STR R1, [R2] - 复位GPIOD引脚4
        *((volatile uint32_t*)r2_register) = r1_register;
    } else {
        // 设置GPIO引脚分支
        // MOVS R1, #0x10 - R1 = 0x10 (引脚4位掩码)
        r1_register = GPIO_PIN_4;  // 0x10
        
        // LDR.W R2, dword_8000A84 - 加载0x40011410 (GPIOD_BSRR)
        r2_register = GPIOD_BSRR;  // 0x40011410
        
        // STR R1, [R2] - 设置GPIOD引脚4
        *((volatile uint32_t*)r2_register) = r1_register;
    }
    
    // locret_80002B8:
    // BX LR - 返回
}

/**
 * @brief crc16_checksum_calculate - CRC16校验计算函数
 *
 * 汇编函数: sub_80002BA (地址0x80002BA, 43条指令)
 * 汇编代码 (行210-263):
 *   PUSH    {R4-R6}
 *   MOVS    R2, R0
 *   MOVS    R0, #0
 *   ADDS    R1, R1, #2
 * loc_80002C2:
 *   MOVS    R3, R1
 *   SUBS    R1, R3, #1
 *   UXTH    R3, R3
 *   CMP     R3, #0
 *   BEQ     loc_8000302
 *   UXTH    R1, R1
 *   CMP     R1, #2
 *   BGE     loc_80002D8
 *   MOVS    R4, #0
 *   MOVS    R3, R4
 *   B       loc_80002DE
 * loc_80002D8:
 *   LDRB    R4, [R2]
 *   MOVS    R3, R4
 *   ADDS    R2, R2, #1
 * loc_80002DE:
 *   MOVS    R4, #0
 * loc_80002E0:
 *   CMP     R4, #8
 *   BGE     loc_80002C2
 *   MOVS    R5, R0
 *   LSLS    R0, R0, #1
 *   LSLS    R6, R3, #0x18
 *   BPL     loc_80002F0
 *   ORRS.W  R0, R0, #1
 * loc_80002F0:
 *   LSLS    R6, R5, #0x10
 *   BPL     loc_80002FC
 *   EOR.W   R0, R0, #0x1000
 *   EORS.W  R0, R0, #0x21
 * loc_80002FC:
 *   LSLS    R3, R3, #1
 *   ADDS    R4, R4, #1
 *   B       loc_80002E0
 * loc_8000302:
 *   UXTH    R0, R0
 *   POP     {R4-R6}
 *   BX      LR
 *
 * @param data_buffer 数据缓冲区指针
 * @param data_length 数据长度
 * @return CRC16校验值
 */
uint16_t crc16_checksum_calculate(uint8_t* data_buffer, uint16_t data_length) {
    uint32_t r2_register, r3_register, r4_register, r5_register, r6_register;  // 对应汇编寄存器
    uint32_t crc_accumulator = 0;      // 对应汇编中的R0

    // PUSH {R4-R6} - 保存寄存器
    // MOVS R2, R0 - R2 = 数据指针
    r2_register = (uint32_t)data_buffer;

    // MOVS R0, #0 - CRC初始值为0
    crc_accumulator = 0;

    // ADDS R1, R1, #2 - 长度+2
    data_length = data_length + 2;

    // loc_80002C2: 外层循环
    while (1) {
        // MOVS R3, R1 - R3 = data_length
        r3_register = data_length;

        // SUBS R1, R3, #1 - data_length = R3 - 1
        data_length = r3_register - 1;

        // UXTH R3, R3 - 保持R3为16位
        r3_register = r3_register & 0xFFFF;

        // CMP R3, #0 - 检查是否结束
        // BEQ loc_8000302 - 如果R3==0跳转到结束
        if (r3_register == 0) {
            break;  // 跳转到loc_8000302
        }

        // UXTH R1, R1 - 保持data_length为16位
        data_length = data_length & 0xFFFF;

        // CMP R1, #2 - 比较data_length与2
        // BGE loc_80002D8 - 如果data_length>=2跳转
        if (data_length >= 2) {
            // loc_80002D8:
            // LDRB R4, [R2] - 从数据指针读取字节
            r4_register = *((uint8_t*)r2_register);

            // MOVS R3, R4 - R3 = R4
            r3_register = r4_register;

            // ADDS R2, R2, #1 - 数据指针+1
            r2_register = r2_register + 1;
        } else {
            // MOVS R4, #0 - R4 = 0
            r4_register = 0;

            // MOVS R3, R4 - R3 = R4 = 0
            r3_register = r4_register;
        }

        // loc_80002DE:
        // MOVS R4, #0 - 位计数器初始化
        r4_register = 0;

        // loc_80002E0: 内层循环 (处理8位)
        while (1) {
            // CMP R4, #8 - 比较位计数器与8
            // BGE loc_80002C2 - 如果R4>=8跳转到外层循环
            if (r4_register >= 8) {
                break;  // 跳转到loc_80002C2
            }

            // MOVS R5, R0 - 保存当前CRC值
            r5_register = crc_accumulator;

            // LSLS R0, R0, #1 - CRC左移1位
            crc_accumulator = crc_accumulator << 1;

            // LSLS R6, R3, #0x18 - 检查数据位的最高位
            r6_register = r3_register << 24;

            // BPL loc_80002F0 - 如果最高位为0跳转
            if ((r6_register & 0x80000000) != 0) {
                // ORRS.W R0, R0, #1 - 设置CRC最低位
                crc_accumulator = crc_accumulator | 1;
            }

            // loc_80002F0:
            // LSLS R6, R5, #0x10 - 检查CRC的第15位
            r6_register = r5_register << 16;

            // BPL loc_80002FC - 如果第15位为0跳转
            if ((r6_register & 0x80000000) != 0) {
                // EOR.W R0, R0, #0x1000 - CRC异或0x1000
                crc_accumulator = crc_accumulator ^ 0x1000;

                // EORS.W R0, R0, #0x21 - CRC异或0x21
                crc_accumulator = crc_accumulator ^ 0x21;
            }

            // loc_80002FC:
            // LSLS R3, R3, #1 - 数据左移1位
            r3_register = r3_register << 1;

            // ADDS R4, R4, #1 - 位计数器+1
            r4_register = r4_register + 1;

            // B loc_80002E0 - 继续内层循环
        }

        // 继续外层循环
    }

    // loc_8000302:
    // UXTH R0, R0 - 保持结果为16位
    crc_accumulator = crc_accumulator & 0xFFFF;

    // POP {R4-R6} - 恢复寄存器
    // BX LR - 返回
    return (uint16_t)crc_accumulator;
}
