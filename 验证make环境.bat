@echo off
echo ========================================
echo Windows 11 Make环境验证脚本
echo ========================================
echo.

echo 🔍 检查make工具安装情况...
echo.

REM 检查系统PATH中的make
echo [1/4] 检查系统PATH中的make...
where make >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 在系统PATH中找到make
    for /f "tokens=*" %%i in ('where make') do echo    路径: %%i
    for /f "tokens=*" %%i in ('make --version 2^>nul ^| findstr /C:"GNU Make"') do echo    版本: %%i
) else (
    echo ❌ 系统PATH中未找到make
)

echo.

REM 检查mingw32-make
echo [2/4] 检查mingw32-make...
where mingw32-make >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到mingw32-make
    for /f "tokens=*" %%i in ('where mingw32-make') do echo    路径: %%i
    for /f "tokens=*" %%i in ('mingw32-make --version 2^>nul ^| findstr /C:"GNU Make"') do echo    版本: %%i
) else (
    echo ❌ 未找到mingw32-make
)

echo.

REM 检查GCC编译器
echo [3/4] 检查GCC编译器...
where gcc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到GCC编译器
    for /f "tokens=*" %%i in ('where gcc') do echo    路径: %%i
    for /f "tokens=*" %%i in ('gcc --version 2^>nul ^| findstr /C:"gcc"') do echo    版本: %%i
) else (
    echo ❌ 未找到GCC编译器
)

echo.

REM 检查ARM交叉编译器
echo [4/4] 检查ARM交叉编译器...
where arm-none-eabi-gcc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 找到ARM交叉编译器
    for /f "tokens=*" %%i in ('where arm-none-eabi-gcc') do echo    路径: %%i
    for /f "tokens=*" %%i in ('arm-none-eabi-gcc --version 2^>nul ^| findstr /C:"gcc"') do echo    版本: %%i
) else (
    echo ⚠️  未找到ARM交叉编译器 (AT32F403AVG项目需要)
)

echo.

echo ========================================
echo 🎯 推荐的make环境安装方案
echo ========================================
echo.

REM 检查常见的开发环境
echo 检查已安装的开发环境...

if exist "C:\msys64\usr\bin\make.exe" (
    echo ✅ MSYS2已安装: C:\msys64\
    echo    建议使用MSYS2终端进行编译
)

if exist "C:\w64devkit\bin\make.exe" (
    echo ✅ w64devkit已安装: C:\w64devkit\
    echo    可以使用w64devkit终端
)

if exist "C:\MinGW\bin\mingw32-make.exe" (
    echo ✅ MinGW已安装: C:\MinGW\
    echo    可以使用mingw32-make命令
)

echo.

echo ========================================
echo 📋 安装建议
echo ========================================
echo.

where make >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到make工具，建议安装以下之一:
    echo.
    echo 🎯 方案1: MSYS2 (最推荐)
    echo    - 下载: https://www.msys2.org/
    echo    - 完整的Unix环境，包含make和GCC
    echo    - 支持包管理器，易于安装额外工具
    echo.
    echo 🎯 方案2: w64devkit (便携版)
    echo    - 下载: https://github.com/skeeto/w64devkit/releases
    echo    - 便携式，无需安装，解压即用
    echo    - 包含make、GCC、GDB等开发工具
    echo.
    echo 🎯 方案3: WinLibs
    echo    - 下载: https://winlibs.com/
    echo    - 仅编译器和基本工具
    echo    - 需要手动配置PATH环境变量
    echo.
) else (
    echo ✅ make工具已安装，可以编译项目
    echo.
    echo 测试编译AT32F403AVG项目:
    echo   cd 到项目目录
    echo   运行: make all
    echo.
)

echo ========================================
echo 🔧 AT32F403AVG项目编译需求
echo ========================================
echo.

echo 当前项目需要以下工具:
echo ✓ make (构建工具)
echo ✓ arm-none-eabi-gcc (ARM交叉编译器)
echo ✓ arm-none-eabi-objcopy (二进制转换工具)
echo ✓ arm-none-eabi-size (大小分析工具)

echo.

where arm-none-eabi-gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  缺少ARM交叉编译器
    echo.
    echo ARM工具链安装建议:
    echo 1. GNU Arm Embedded Toolchain
    echo    下载: https://developer.arm.com/downloads/-/gnu-rm
    echo.
    echo 2. 通过MSYS2安装:
    echo    pacman -S mingw-w64-x86_64-arm-none-eabi-toolchain
    echo.
    echo 3. 或者使用Keil MDK进行编译 (推荐)
    echo    项目已包含Keil项目文件
) else (
    echo ✅ ARM交叉编译器已安装，可以编译AT32F403AVG项目
)

echo.
echo ========================================
echo 验证完成
echo ========================================
pause
