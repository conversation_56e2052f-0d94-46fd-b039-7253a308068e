// 大规模手工转换批次 1 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 浮点数操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_14B18
 * @note 指令数: 10, 类型: float_operation
 */
float manual_14b18(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20007584 = (volatile uint32_t *)0x20007584;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作逻辑
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_14B34
 * @note 指令数: 21, 类型: array_operation
 */
uint16_t manual_14b34(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8016874 = (volatile uint32_t *)0x8016874;
    volatile uint32_t *addr_2000797C = (volatile uint32_t *)0x2000797C;
    volatile uint32_t *addr_20007A5C = (volatile uint32_t *)0x20007A5C;

    // 局部变量
    uint16_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_14CB4
 * @note 指令数: 127, 类型: array_operation
 */
uint32_t manual_14cb4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200080B4 = (volatile uint32_t *)0x200080B4;
    volatile uint32_t *addr_20007E00 = (volatile uint32_t *)0x20007E00;
    volatile uint32_t *addr_20007A1C = (volatile uint32_t *)0x20007A1C;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_72177617 = (volatile uint32_t *)0x72177617;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 浮点数操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_14E08
 * @note 指令数: 205, 类型: float_operation
 */
float manual_14e08(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8016934 = (volatile uint32_t *)0x8016934;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_20007584 = (volatile uint32_t *)0x20007584;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_200080BE = (volatile uint32_t *)0x200080BE;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作逻辑
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 浮点数操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_15050
 * @note 指令数: 366, 类型: float_operation
 */
float manual_15050(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_F0 = (volatile uint32_t *)0xF0;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20007344 = (volatile uint32_t *)0x20007344;
    volatile uint32_t *addr_200080A8 = (volatile uint32_t *)0x200080A8;
    volatile uint32_t *addr_200079FC = (volatile uint32_t *)0x200079FC;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作逻辑
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 浮点数操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_154F4
 * @note 指令数: 214, 类型: float_operation
 */
float manual_154f4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200075C4 = (volatile uint32_t *)0x200075C4;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_20007584 = (volatile uint32_t *)0x20007584;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_8016600 = (volatile uint32_t *)0x8016600;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作逻辑
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 浮点数操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_157C0
 * @note 指令数: 44, 类型: float_operation
 */
float manual_157c0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000799C = (volatile uint32_t *)0x2000799C;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_200075C4 = (volatile uint32_t *)0x200075C4;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作逻辑
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 浮点数操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_158F0
 * @note 指令数: 364, 类型: float_operation
 */
float manual_158f0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200080B4 = (volatile uint32_t *)0x200080B4;
    volatile uint32_t *addr_20007EA8 = (volatile uint32_t *)0x20007EA8;
    volatile uint32_t *addr_200080B0 = (volatile uint32_t *)0x200080B0;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_200079DC = (volatile uint32_t *)0x200079DC;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作逻辑
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 浮点数操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_15D3C
 * @note 指令数: 478, 类型: float_operation
 */
float manual_15d3c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200080B4 = (volatile uint32_t *)0x200080B4;
    volatile uint32_t *addr_F0 = (volatile uint32_t *)0xF0;
    volatile uint32_t *addr_20007544 = (volatile uint32_t *)0x20007544;
    volatile uint32_t *addr_20007A1C = (volatile uint32_t *)0x20007A1C;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作逻辑
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_162CE
 * @note 指令数: 3, 类型: simple_function
 */
uint32_t manual_162ce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20008135 = (volatile uint32_t *)0x20008135;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_162D6
 * @note 指令数: 3, 类型: simple_function
 */
uint8_t manual_162d6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008135 = (volatile uint32_t *)0x20008135;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_16390
 * @note 指令数: 69, 类型: control_function
 */
void manual_16390(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_16444
 * @note 指令数: 15, 类型: array_operation
 */
uint32_t manual_16444(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200080E4 = (volatile uint32_t *)0x200080E4;
    volatile uint32_t *addr_20007F30 = (volatile uint32_t *)0x20007F30;
    volatile uint32_t *addr_2000806C = (volatile uint32_t *)0x2000806C;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_16466
 * @note 指令数: 3, 类型: simple_function
 */
uint32_t manual_16466(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20008060 = (volatile uint32_t *)0x20008060;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1646C
 * @note 指令数: 3, 类型: simple_function
 */
uint32_t manual_1646c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20008068 = (volatile uint32_t *)0x20008068;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_16472
 * @note 指令数: 23, 类型: simple_function
 */
uint32_t manual_16472(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20008050 = (volatile uint32_t *)0x20008050;
    volatile uint32_t *addr_20008054 = (volatile uint32_t *)0x20008054;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_164A4
 * @note 指令数: 23, 类型: simple_function
 */
uint32_t manual_164a4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000805C = (volatile uint32_t *)0x2000805C;
    volatile uint32_t *addr_20008058 = (volatile uint32_t *)0x20008058;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_164D6
 * @note 指令数: 108, 类型: array_operation
 */
uint32_t manual_164d6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200080E4 = (volatile uint32_t *)0x200080E4;
    volatile uint32_t *addr_2000805C = (volatile uint32_t *)0x2000805C;
    volatile uint32_t *addr_20008050 = (volatile uint32_t *)0x20008050;
    volatile uint32_t *addr_2000806C = (volatile uint32_t *)0x2000806C;
    volatile uint32_t *addr_20000268 = (volatile uint32_t *)0x20000268;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_165BC
 * @note 指令数: 39, 类型: array_operation
 */
uint32_t manual_165bc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200080E4 = (volatile uint32_t *)0x200080E4;
    volatile uint32_t *addr_20008064 = (volatile uint32_t *)0x20008064;
    volatile uint32_t *addr_20008068 = (volatile uint32_t *)0x20008068;
    volatile uint32_t *addr_20008060 = (volatile uint32_t *)0x20008060;
    volatile uint32_t *addr_2000815B = (volatile uint32_t *)0x2000815B;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_16640
 * @note 指令数: 17, 类型: control_function
 */
uint32_t manual_16640(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40000400 = (volatile uint32_t *)0x40000400;
    volatile uint32_t *addr_2000814B = (volatile uint32_t *)0x2000814B;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_16670
 * @note 指令数: 23, 类型: control_function
 */
uint32_t manual_16670(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40000400 = (volatile uint32_t *)0x40000400;
    volatile uint32_t *addr_BB80 = (volatile uint32_t *)0xBB80;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_1C0001 = (volatile uint32_t *)0x1C0001;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_166B6
 * @note 指令数: 18, 类型: control_function
 */
uint32_t manual_166b6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40000400 = (volatile uint32_t *)0x40000400;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_166E6
 * @note 指令数: 4, 类型: simple_function
 */
uint32_t manual_166e6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40000424 = (volatile uint32_t *)0x40000424;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_166F0
 * @note 指令数: 38, 类型: lookup_table
 */
void manual_166f0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_8016678 = (volatile uint32_t *)0x8016678;
    volatile uint32_t *addr_20008148 = (volatile uint32_t *)0x20008148;
    volatile uint32_t *addr_20008149 = (volatile uint32_t *)0x20008149;
    volatile uint32_t *addr_2000814B = (volatile uint32_t *)0x2000814B;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1675C
 * @note 指令数: 4, 类型: simple_function
 */
uint32_t manual_1675c(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200077F0 = (volatile uint32_t *)0x200077F0;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_16766
 * @note 指令数: 10, 类型: simple_function
 */
uint32_t manual_16766(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20008149 = (volatile uint32_t *)0x20008149;
    volatile uint32_t *addr_20007ED0 = (volatile uint32_t *)0x20007ED0;
    volatile uint32_t *addr_20007FD8 = (volatile uint32_t *)0x20007FD8;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 简单处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1677A
 * @note 指令数: 4, 类型: simple_function
 */
uint8_t manual_1677a(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007ED0 = (volatile uint32_t *)0x20007ED0;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_16782
 * @note 指令数: 5, 类型: control_function
 */
uint32_t manual_16782(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40012400 = (volatile uint32_t *)0x40012400;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;
    return result;
}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_1678E
 * @note 指令数: 21, 类型: data_processing
 */
uint32_t manual_1678e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007FDC = (volatile uint32_t *)0x20007FDC;
    volatile uint32_t *addr_20008148 = (volatile uint32_t *)0x20008148;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_167B8
 * @note 指令数: 106, 类型: lookup_table
 */
uint8_t manual_167b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20008148 = (volatile uint32_t *)0x20008148;

    // 局部变量
    uint8_t result = 0;

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];
    return result;
}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_16892
 * @note 指令数: 114, 类型: lookup_table
 */
void manual_16892(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007828 = (volatile uint32_t *)0x20007828;
    volatile uint32_t *addr_20008148 = (volatile uint32_t *)0x20008148;
    volatile uint32_t *addr_200077F0 = (volatile uint32_t *)0x200077F0;
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_20007ED8 = (volatile uint32_t *)0x20007ED8;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_1699C
 * @note 指令数: 19, 类型: control_function
 */
void manual_1699c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000814B = (volatile uint32_t *)0x2000814B;
    volatile uint32_t *addr_2000814A = (volatile uint32_t *)0x2000814A;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_16A18
 * @note 指令数: 27, 类型: data_processing
 */
uint32_t manual_16a18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_FFFFFFFF = (volatile uint32_t *)0xFFFFFFFF;
    volatile uint32_t *addr_38000000 = (volatile uint32_t *)0x38000000;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_16ADC
 * @note 指令数: 143, 类型: data_processing
 */
void manual_16adc(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200000 = (volatile uint32_t *)0x200000;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_7FF = (volatile uint32_t *)0x7FF;
    volatile uint32_t *addr_35 = (volatile uint32_t *)0x35;
    volatile uint32_t *addr_400 = (volatile uint32_t *)0x400;

    // 局部变量

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_16C7E
 * @note 指令数: 5, 类型: data_processing
 */
uint32_t manual_16c7e(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_16C88
 * @note 指令数: 46, 类型: data_processing
 */
uint32_t manual_16c88(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200000 = (volatile uint32_t *)0x200000;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_3FC00000 = (volatile uint32_t *)0x3FC00000;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_16D18
 * @note 指令数: 7, 类型: control_function
 */
void manual_16d18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 数据处理函数 - 手工精确转换
 * @note 对应汇编函数 sub_16D2C
 * @note 指令数: 11, 类型: data_processing
 */
uint32_t manual_16d2c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_420 = (volatile uint32_t *)0x420;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_16D48
 * @note 指令数: 329, 类型: control_function
 */
void manual_16d48(void)
{
    // 内存地址定义
    volatile uint32_t *addr_900000 = (volatile uint32_t *)0x900000;
    volatile uint32_t *addr_300000 = (volatile uint32_t *)0x300000;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_36 = (volatile uint32_t *)0x36;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_170B0
 * @note 指令数: 32, 类型: control_function
 */
void manual_170b0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_22 = (volatile uint32_t *)0x22;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20008070 = (volatile uint32_t *)0x20008070;
    volatile uint32_t *addr_2000015C = (volatile uint32_t *)0x2000015C;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_170FA
 * @note 指令数: 13, 类型: array_operation
 */
uint32_t manual_170fa(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008163 = (volatile uint32_t *)0x20008163;
    volatile uint32_t *addr_200080E6 = (volatile uint32_t *)0x200080E6;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_17118
 * @note 指令数: 13, 类型: array_operation
 */
uint32_t manual_17118(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008163 = (volatile uint32_t *)0x20008163;
    volatile uint32_t *addr_200080E6 = (volatile uint32_t *)0x200080E6;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_17136
 * @note 指令数: 13, 类型: array_operation
 */
uint32_t manual_17136(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008163 = (volatile uint32_t *)0x20008163;
    volatile uint32_t *addr_200080E6 = (volatile uint32_t *)0x200080E6;

    // 局部变量
    uint32_t result = 0;

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;
    return result;
}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_17154
 * @note 指令数: 38, 类型: control_function
 */
void manual_17154(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_3FF00000 = (volatile uint32_t *)0x3FF00000;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_171B2
 * @note 指令数: 62, 类型: lookup_table
 */
void manual_171b2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;
    volatile uint32_t *addr_20008070 = (volatile uint32_t *)0x20008070;
    volatile uint32_t *addr_20006C86 = (volatile uint32_t *)0x20006C86;
    volatile uint32_t *addr_20008162 = (volatile uint32_t *)0x20008162;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 数组操作函数 - 手工精确转换
 * @note 对应汇编函数 sub_17278
 * @note 指令数: 76, 类型: array_operation
 */
void manual_17278(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008154 = (volatile uint32_t *)0x20008154;
    volatile uint32_t *addr_20008130 = (volatile uint32_t *)0x20008130;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;
    volatile uint32_t *addr_20006A28 = (volatile uint32_t *)0x20006A28;

    // 局部变量

    // 数组操作逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    
    result = value;}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_17340
 * @note 指令数: 116, 类型: lookup_table
 */
void manual_17340(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008154 = (volatile uint32_t *)0x20008154;
    volatile uint32_t *addr_20008130 = (volatile uint32_t *)0x20008130;
    volatile uint32_t *addr_20007A0C = (volatile uint32_t *)0x20007A0C;
    volatile uint32_t *addr_2000803C = (volatile uint32_t *)0x2000803C;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_17478
 * @note 指令数: 14, 类型: control_function
 */
void manual_17478(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20006A28 = (volatile uint32_t *)0x20006A28;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

/**
 * @brief 查表函数 - 手工精确转换
 * @note 对应汇编函数 sub_1749C
 * @note 指令数: 104, 类型: lookup_table
 */
void manual_1749c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008154 = (volatile uint32_t *)0x20008154;
    volatile uint32_t *addr_20008130 = (volatile uint32_t *)0x20008130;
    volatile uint32_t *addr_2000798C = (volatile uint32_t *)0x2000798C;
    volatile uint32_t *addr_20006D4C = (volatile uint32_t *)0x20006D4C;
    volatile uint32_t *addr_20008156 = (volatile uint32_t *)0x20008156;

    // 局部变量

    // 查表操作逻辑
    index = index & 0xFF;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint8_t lookup_value = lookup_table[index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief 控制函数 - 手工精确转换
 * @note 对应汇编函数 sub_175B2
 * @note 指令数: 13, 类型: control_function
 */
void manual_175b2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008163 = (volatile uint32_t *)0x20008163;

    // 局部变量

    // 控制函数逻辑
    // 调用外部函数
    extern void external_function(void);
    external_function();
    
    // 基本处理
    result = param0;}

