#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建正确的头文件，包含所有函数声明
"""

import os
import re
import glob

def extract_function_declarations():
    """提取所有函数声明"""
    batch_files = glob.glob("converted_functions/batch_*.c")
    batch_files.sort()
    
    all_declarations = []
    
    for batch_file in batch_files:
        batch_num = os.path.basename(batch_file).replace('batch_', '').replace('.c', '')
        
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找函数定义并提取声明
            lines = content.split('\n')
            i = 0
            while i < len(lines):
                line = lines[i].strip()

                # 查找函数定义行 - 检查返回类型开头
                if (line.startswith('void ') or line.startswith('uint32_t ') or line.startswith('float ')):
                    # 收集完整的函数签名（可能跨多行）
                    func_signature = line
                    j = i + 1

                    # 如果当前行没有包含完整的函数签名，继续收集
                    while j < len(lines) and '{' not in func_signature:
                        next_line = lines[j].strip()
                        if next_line:
                            func_signature += ' ' + next_line
                        j += 1

                    # 如果找到了完整的函数定义
                    if '(' in func_signature and ')' in func_signature and '{' in func_signature:
                        # 清理函数签名
                        func_signature = func_signature.replace('{', '').strip()
                        all_declarations.append(f"extern {func_signature};")
                        i = j
                    else:
                        i += 1
                else:
                    i += 1
        
        except Exception as e:
            print(f"处理 {batch_file} 时出错: {e}")
    
    return all_declarations

def create_header_file():
    """创建头文件"""
    print("创建正确的头文件...")
    
    declarations = extract_function_declarations()
    
    header_content = """#ifndef MH25QH128_ALL_FUNCTIONS_H
#define MH25QH128_ALL_FUNCTIONS_H

#include <stdint.h>

// ============================================================================
// MH25QH128.bin.asm 所有转换函数的声明
// 总计 2380 个函数，从汇编代码转换为C语言实现
// ============================================================================

"""
    
    # 添加所有函数声明
    for declaration in declarations:
        header_content += declaration + "\n"
    
    header_content += """
#endif // MH25QH128_ALL_FUNCTIONS_H
"""
    
    # 保存头文件
    with open("mh25qh128_functions_proper.h", 'w', encoding='utf-8') as f:
        f.write(header_content)
    
    print(f"正确的头文件已创建: mh25qh128_functions_proper.h")
    print(f"包含 {len(declarations)} 个函数声明")

if __name__ == "__main__":
    create_header_file()
