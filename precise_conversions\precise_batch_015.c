// 精确转换批次 15 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B996
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_4b996(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRH    R0, [R1,#8+var_8]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4B9BA
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4b9ba(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000C8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20006714;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x7C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_4B9D4
    // 条件跳转
    // MOVS    R5, #0x7C ; '|'
    // R5 = 0x7C;
    // LDR     R6, =0x200000C8
    // 内存加载操作
    // LDR     R7, =0x20006714
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BA34
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_4ba34(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3890;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_4BA52
    // 条件跳转
    // MOVS    R2, #0x80
    // R2 = 0x80;
    // MOVS    R1, R4
    // LDR     R0, =0x3890
    // 内存加载操作
    // BL      sub_4DD4C
    // 调用函数: sub_4DD4C();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4BA64
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_4BA88
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BA90
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_4ba90(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOVS    R7, R0
    // MOVS    R6, R1
    // MOVS    R5, R2
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // MOV     R1, SP
    // MOVS    R0, #0x3800
    // R0 = 0x3800;
    // BL      sub_4DC70
    // 调用函数: sub_4DC70();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4BAAE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_4BB06
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BB0C
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_4bb0c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOVS    R7, R0
    // MOVS    R6, R1
    // MOVS    R5, R2
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // MOV     R1, SP
    // MOVS    R0, #0x3800
    // R0 = 0x3800;
    // BL      sub_4DC70
    // 调用函数: sub_4DC70();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4BB2A
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_4BB82
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BB86
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4bb86(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3800;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x18
    // 算术运算
    // MOVS    R5, R0
    // MOVS    R6, R1
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // MOV     R1, SP
    // MOVS    R0, #0x3800
    // R0 = 0x3800;
    // BL      sub_4DC70
    // 调用函数: sub_4DC70();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4BBA2
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_4BC04
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BC18
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_4bc18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x58;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x3B10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x40
    // 算术运算
    // MOVS    R7, R1
    // ADD     R0, SP, #0x58+var_18
    // 算术运算
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x200
    // R1 = 0x200;
    // MULS    R0, R1
    // LDR     R1, =0x3B10
    // 内存加载操作
    // ADDS    R6, R0, R1
    // 算术运算
    // MOVS    R5, #0x200
    // R5 = 0x200;
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [R7]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BC90
 * @note 指令数: 16, 标签数: 1
 */
void precise_func_4bc90(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007590;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     loc_4BCB2
    // 条件跳转
    // LDR     R0, =0x20007590
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #4
    // R1 = 4;
    // MULS    R1, R4
    // ADDS    R1, R0, R1
    // 算术运算
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4BC18
    // 调用函数: sub_4BC18();
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_4BC96
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BCB6
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_4bcb6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x40
    // 算术运算
    // MOVS    R7, R0
    // UXTB    R7, R7
    // 数据扩展操作
    // MOVS    R0, #0x200
    // R0 = 0x200;
    // MULS    R0, R7
    // LDR     R1, =(dword_3900+0x10)
    // 内存加载操作
    // ADDS    R5, R0, R1
    // 算术运算
    // MOVS    R6, #0
    // R6 = 0;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BD5C
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_4bd5c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x58;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x40
    // 算术运算
    // MOVS    R7, R1
    // ADD     R0, SP, #0x58+var_18
    // 算术运算
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x200
    // R1 = 0x200;
    // MULS    R0, R1
    // LDR     R1, =(dword_4A98+0x78)
    // 内存加载操作
    // ADDS    R6, R0, R1
    // 算术运算
    // MOVS    R5, #0x200
    // R5 = 0x200;
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R1, #0
    // R1 = 0;
    // STM     R7!, {R0,R1}
    // SUBS    R7, #8
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BE6C
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_4be6c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x68;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH    {R3-R7,LR}
    // 栈操作
    // PUSH    {R2,R3}
    // 栈操作
    // SUB     SP, SP, #0x40
    // 算术运算
    // MOVS    R7, R0
    // UXTB    R7, R7
    // 数据扩展操作
    // MOVS    R0, #0x200
    // R0 = 0x200;
    // MULS    R0, R7
    // LDR     R1, =(dword_48A8+0x68)
    // 内存加载操作
    // ADDS    R5, R0, R1
    // 算术运算
    // MOVS    R6, #0
    // R6 = 0;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BF82
 * @note 指令数: 16, 标签数: 1
 */
void precise_func_4bf82(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006E68;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #8
    // 比较操作
    // BGE     loc_4BFA4
    // 条件跳转
    // LDR     R0, =0x20006E68
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #8
    // R1 = 8;
    // MULS    R1, R4
    // ADDS    R1, R0, R1
    // 算术运算
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4BD5C
    // 调用函数: sub_4BD5C();
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_4BF88
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BFBC
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_4bfbc(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BFC2
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_4bfc2(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BFC8
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_4bfc8(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BFCE
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_4bfce(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x82;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016018;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {R2-R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R1, =0x8016018
    // 内存加载操作
    // MOVS    R0, R4
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // MOV     R1, SP
    // ADD     R0, SP, #0x10+var_C
    // 算术运算
    // BL      sub_4B41A
    // 调用函数: sub_4B41A();
    // LDR     R2, [SP,#0x10+var_10]
    // 内存加载操作
    // ADR     R1, dword_4C024
    // MOVS    R0, R4
    // ADDS    R0, #0x41 ; 'A'
    // 算术运算
    // BL      sub_4C0E0
    // 调用函数: sub_4C0E0();
    // MOVS    R1, R4
    // ADDS    R1, #0x82
    // 算术运算
    // LDR     R0, =(dword_800+0x34)
    // 内存加载操作
    // BL      sub_4C1CE
    // 调用函数: sub_4C1CE();
    // POP     {R0,R1,R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4BFFA
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_4bffa(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078C8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x257;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x25F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // ADDS    R3, R0, R1
    // 算术运算
    // MOVS    R2, R3
    // UXTH    R0, R0
    // 数据扩展操作
    // LDR     R3, =0x257
    // 内存加载操作
    // CMP     R0, R3
    // 比较操作
    // BLT     locret_4C01C
    // 条件跳转
    // UXTH    R2, R2
    // 数据扩展操作
    // LDR     R3, =0x25F
    // 内存加载操作
    // CMP     R2, R3
    // 比较操作
    // BGE     locret_4C01C
    // 条件跳转
    // LDR     R3, =0x200078C8
    // 内存加载操作
    // LDRB    R3, [R3]
    // 内存加载操作
    // MOVS    R4, #4
    // R4 = 4;
    // ORRS    R4, R3
    // LDR     R3, =0x200078C8
    // 内存加载操作
    // STRB    R4, [R3]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C038
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_4c038(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C08A
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_4c08a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000789A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000789A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_4B3B4
    // 调用函数: sub_4B3B4();
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_4C09E
    // 条件跳转
    // BL      sub_4C038
    // 调用函数: sub_4C038();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C0A0
 * @note 指令数: 6, 标签数: 0
 */
uint32_t precise_func_4c0a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000789A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x2000789A
    // 内存加载操作
    // LDRB    R1, [R0]
    // 内存加载操作
    // MOVS    R0, R1
    // LSLS    R0, R0, #0x1F
    // LSRS    R0, R0, #0x1F
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C0D0
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_4c0d0(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R3, #0
    // R3 = 0;
    // B       loc_4C0D6
    // 无条件跳转
    // ADDS    R3, R3, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C0E0
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_4c0e0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // var_C= -0xC
    // varg_r2= -8
    // varg_r3= -4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C118
 * @note 指令数: 24, 标签数: 1
 */
void precise_func_4c118(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8014F70;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8014E70;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // MOVS    R2, R0
    // MOVS    R0, #0xFF
    // R0 = 0xFF;
    // MOVS    R4, #0xFF
    // R4 = 0xFF;
    // MOVS    R5, R1
    // SUBS    R1, R5, #1
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_4C148
    // 条件跳转
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRB    R5, [R2]
    // 内存加载操作
    // EORS    R5, R4
    // MOVS    R3, R5
    // ADDS    R2, R2, #1
    // 算术运算
    // LDR     R5, =0x8014E70
    // 内存加载操作
    // UXTH    R3, R3
    // 数据扩展操作
    // LDRB    R5, [R5,R3]
    // 内存加载操作
    // EORS    R5, R0
    // MOVS    R4, R5
    // LDR     R5, =0x8014F70
    // 内存加载操作
    // UXTH    R3, R3
    // 数据扩展操作
    // LDRB    R5, [R5,R3]
    // 内存加载操作
    // MOVS    R0, R5
    // B       loc_4C120
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C15C
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4c15c(uint8_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x37;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0xA
    // 比较操作
    // BLT     loc_4C16A
    // 条件跳转
    // ADDS    R0, #0x37 ; '7'
    // 算术运算
    // UXTB    R0, R0
    // 数据扩展操作
    // B       locret_4C16E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C170
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_4c170(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #4
    // BL      sub_4C15C
    // 调用函数: sub_4C15C();
    // STRB    R0, [R5]
    // 内存存储操作
    // LSLS    R0, R4, #0x1C
    // LSRS    R0, R0, #0x1C
    // BL      sub_4C15C
    // 调用函数: sub_4C15C();
    // STRB    R0, [R5,#1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R5,#2]
    // 内存存储操作
    // POP     {R0,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C192
 * @note 指令数: 12, 标签数: 0
 */
uint32_t precise_func_4c192(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // MOVS    R2, R1
    // UXTB    R2, R2
    // 数据扩展操作
    // LSRS    R2, R2, #4
    // MOVS    R3, #0xA
    // R3 = 0xA;
    // MULS    R2, R3
    // MOVS    R0, R2
    // LSLS    R2, R1, #0x1C
    // LSRS    R2, R2, #0x1C
    // ADDS    R0, R0, R2
    // 算术运算
    // UXTB    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C1AA
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_4c1aa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #0xA
    // R1 = 0xA;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // LSLS    R0, R0, #4
    // MOVS    R5, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #0xA
    // R1 = 0xA;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // ADDS    R5, R5, R1
    // 算术运算
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // POP     {R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C1CE
 * @note 指令数: 35, 标签数: 0
 */
void precise_func_4c1ce(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8015F30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #0x3E8
    // R1 = 0x3E8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R6, R0
    // UXTH    R4, R4
    // 数据扩展操作
    // UXTH    R6, R6
    // 数据扩展操作
    // MOVS    R0, #0x3E8
    // R0 = 0x3E8;
    // MULS    R0, R6
    // SUBS    R0, R4, R0
    // 算术运算
    // MOVS    R1, #0x64 ; 'd'
    // R1 = 0x64;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOV     R1, SP
    // STRH    R0, [R1,#0x20+var_1C]
    // 内存存储操作
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #0x64 ; 'd'
    // R1 = 0x64;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R7, R1
    // UXTH    R7, R7
    // 数据扩展操作
    // STR     R7, [SP,#0x20+var_20]
    // 内存存储操作
    // MOV     R0, SP
    // LDRH    R3, [R0,#0x20+var_1C]
    // 内存加载操作
    // UXTH    R6, R6
    // 数据扩展操作
    // MOVS    R2, R6
    // LDR     R1, =0x8015F30
    // 内存加载操作
    // MOVS    R0, R5
    // BL      sub_4C0E0
    // 调用函数: sub_4C0E0();
    // POP     {R0-R2,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C21C
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_4c21c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x7FFFFFFF;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x7FFFFFFF
    // 内存加载操作
    // MOVS    R0, R1
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C22C
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_4c22c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078D1;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200078D1
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C234
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_4c234(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007842;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #5
    // R0 = 5;
    // LDR     R1, =0x20007842
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C23C
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_4c23c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x32;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078BC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200078BD;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007428;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200077C4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R5-R7,LR}
    // 栈操作
    // MOV     R0, SP
    // BL      sub_4B42C
    // 调用函数: sub_4B42C();
    // MOV     R2, SP
    // LDR     R1, =0x20007428
    // 内存加载操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_475F0
    // 调用函数: sub_475F0();
    // LDR     R1, =0x200077C4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOV     R0, SP
    // ADDS    R0, R0, #2
    // 算术运算
    // BL      sub_46FE8
    // 调用函数: sub_46FE8();
    // MOVS    R1, #0x2580
    // R1 = 0x2580;
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_4C270
    // 条件跳转
    // MOVS    R0, #0x1E
    // R0 = 0x1E;
    // LDR     R1, =0x200078BC
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x32 ; '2'
    // R0 = 0x32;
    // LDR     R1, =0x200078BD
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_4C27C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C27E
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_4c27e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x800A68D;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200077C4;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200078BA;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200076A8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_4C23C
    // 调用函数: sub_4C23C();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x200077C4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_47736
    // 调用函数: sub_47736();
    // LDR     R1, =0x800A68D
    // 内存加载操作
    // LDR     R0, =0x200077C4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_47C70
    // 调用函数: sub_47C70();
    // BL      sub_4E170
    // 调用函数: sub_4E170();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4C2B0
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x200076A8
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078BA
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       loc_4C2B6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C30C
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_4c30c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077C4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078BA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200076A8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x200077C4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4811C
    // 调用函数: sub_4811C();
    // LDR     R0, =0x200076A8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4C33C
    // 条件跳转
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_48386
    // 调用函数: sub_48386();
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_48386
    // 调用函数: sub_48386();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_48386
    // 调用函数: sub_48386();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078BA
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x64 ; 'd'
    // R0 = 0x64;
    // LDR     R1, =0x200076A8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C420
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_4c420(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078BE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007844;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007840;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20005F94;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200078BB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #1
    // 比较操作
    // BNE     loc_4C4C6
    // 条件跳转
    // LDR     R0, =0x20007840
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_4C510
    // 条件跳转
    // LDR     R0, =0x200078BE
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4C454
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200078BE
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20005F94
    // 内存加载操作
    // STRB    R5, [R0]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20007844
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078BB
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       loc_4C4B2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C520
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4c520(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078BB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x200078BB
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     locret_4C538
    // 条件跳转
    // CPSID   I
    // LDR     R0, =0x200078BB
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x200078BB
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // CPSIE   I
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C558
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4c558(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078BE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R7, R1
    // UXTH    R7, R7
    // 数据扩展操作
    // CMP     R7, #4
    // 比较操作
    // BGE     loc_4C56E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078BE
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_4C6AA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C6AC
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_4c6ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007840;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_4C118
    // 调用函数: sub_4C118();
    // MOVS    R5, R0
    // LDRB    R0, [R4]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4C6CE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007840
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_4C6EE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C724
 * @note 指令数: 75, 标签数: 1
 */
void precise_func_4c724(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006DF4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200071AC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200071EC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000720C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x62;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #8
    // 比较操作
    // BGE     loc_4C7BE
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200071AC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // BL      sub_4C21C
    // 调用函数: sub_4C21C();
    // LDR     R1, =0x200071CC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // BL      sub_4C21C
    // 调用函数: sub_4C21C();
    // LDR     R1, =0x200071EC
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000720C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // LDR     R0, =0x42C80000
    // 内存加载操作
    // LDR     R1, =0x2000722C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // LDR     R0, =0x42C80000
    // 内存加载操作
    // LDR     R1, =0x2000724C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000726C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007560
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #2
    // R2 = 2;
    // MULS    R2, R4
    // STRH    R0, [R1,R2]
    // 内存存储操作
    // LDR     R0, =0x3DCCCCCD
    // 内存加载操作
    // LDR     R1, =0x2000730C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // MOVS    R0, #0x62 ; 'b'
    // R0 = 0x62;
    // LDR     R1, =0x20006DF4
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #8
    // R2 = 8;
    // MULS    R2, R4
    // ADDS    R1, R1, R2
    // 算术运算
    // STRH    R0, [R1,#4]
    // 内存存储操作
    // LDR     R0, =0x3DCCCCCD
    // 内存加载操作
    // LDR     R1, =0x20006DF4
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #8
    // R2 = 8;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_4C72A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C7F6
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_4c7f6(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // BL      sub_4F974
    // 调用函数: sub_4F974();
    // BL      sub_4C724
    // 调用函数: sub_4C724();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4C90C
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4c90c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1F= -0x1F
    // var_1E= -0x1E
    // var_1C= -0x1C
    // var_1A= -0x1A
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4CF18
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_4cf18(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007608;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007894;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // LDR     R0, =0x20007894
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R5, R0
    // MOVS    R7, #1
    // R7 = 1;
    // LDR     R0, =0x20007894
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // LSLS    R7, R1
    // MOVS    R6, R7
    // LDR     R0, =0x20007608
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4CF40
    // 条件跳转
    // B       locret_4D0FC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4D140
 * @note 指令数: 12, 标签数: 1
 */
void precise_func_4d140(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     locret_4D15A
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4F990
    // 调用函数: sub_4F990();
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_4D146
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4D15C
 * @note 指令数: 26, 标签数: 1
 */
void precise_func_4d15c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007490;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200074A8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007478;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0xC
    // 比较操作
    // BGE     loc_4D190
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x200074A8
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007490
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007478
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_4D162
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4D1E0
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_4d1e0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // BL      sub_4F9D4
    // 调用函数: sub_4F9D4();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4D360
 * @note 指令数: 29, 标签数: 0
 */
void precise_func_4d360(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007820;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R1-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R5, R0
    // MOVS    R7, #1
    // R7 = 1;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // LSLS    R7, R1
    // MOVS    R6, R7
    // LDR     R0, =0x20007820
    // 内存加载操作
    // UXTB    R5, R5
    // 数据扩展操作
    // LDRB    R0, [R0,R5]
    // 内存加载操作
    // TST     R0, R6
    // 比较操作
    // BEQ     loc_4D39C
    // 条件跳转
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     loc_4D39A
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4FA24
    // 调用函数: sub_4FA24();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4D530
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_4d530(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // var_17= -0x17
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4D6F4
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_4d6f4(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // var_16= -0x16
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DB18
 * @note 指令数: 12, 标签数: 1
 */
void precise_func_4db18(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     locret_4DB32
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4FA24
    // 调用函数: sub_4FA24();
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_4DB1E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DB34
 * @note 指令数: 53, 标签数: 0
 */
void precise_func_4db34(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015FF0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007794;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #0x18
    // 算术运算
    // MOVS    R4, R0
    // LDR     R1, =0x8015DF4
    // 内存加载操作
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // BL      sub_4C0D0
    // 调用函数: sub_4C0D0();
    // LDR     R0, =0x8015FF0
    // 内存加载操作
    // LDRH    R1, [R0,#4]
    // 内存加载操作
    // SUBS    R1, #0xE
    // 算术运算
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_480BE
    // 调用函数: sub_480BE();
    // LDR     R0, =0x8015E04
    // 内存加载操作
    // BL      sub_49244
    // 调用函数: sub_49244();
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // STR     R1, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADDS    R0, R0, #1
    // 算术运算
    // MOVS    R2, R0
    // LDR     R1, =0x8015E04
    // 内存加载操作
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4803A
    // 调用函数: sub_4803A();
    // LDR     R0, =0x8015FF0
    // 内存加载操作
    // LDRH    R1, [R0,#4]
    // 内存加载操作
    // SUBS    R1, #0xE
    // 算术运算
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_480BE
    // 调用函数: sub_480BE();
    // LDR     R0, =0x8015E04
    // 内存加载操作
    // BL      sub_49244
    // 调用函数: sub_49244();
    // MOVS    R2, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADDS    R2, R2, #1
    // 算术运算
    // ADD     R1, SP, #0x20+var_1C
    // 算术运算
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4807C
    // 调用函数: sub_4807C();
    // LDR     R1, =0x20007836
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R1, =0x8015E04
    // 内存加载操作
    // ADD     R0, SP, #0x20+var_1C
    // 算术运算
    // BL      sub_48774
    // 调用函数: sub_48774();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4DBAA
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_4DBAC
    // 无条件跳转
}

