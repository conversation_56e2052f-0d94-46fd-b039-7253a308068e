#!/usr/bin/env python3
"""
AT32F403AVG Logo数据可视化工具
从汇编代码提取的logo数据转换为图像

作者: AT32F403AVG汇编转换项目
日期: 2024
"""

import struct
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt

# 从汇编代码第40137-40144行提取的logo数据
LOGO_DATA_RAW = [
    # 汇编代码第40137行
    0x646F0E60, 0x3733366A, 0x584B0000, 0x36312D4D, 
    0xE3010050, 0xF1120834, 0x2212011F, 0x00045055,
    
    # 汇编代码第40138行
    0x01020800, 0x06100A0B, 0x611B0100, 0x6E696D64, 
    0x31025052, 0x59343332, 0x807C4BC2, 0x0167A808,
    
    # 汇编代码第40139行
    0x4001A408, 0x79800000, 0x5214B412, 0x015214A0, 
    0x14C01217, 0x00A20390, 0xA0100010, 0xAC0A07F0,
    
    # 汇编代码第40140行
    0x80080168, 0x02000025, 0xB412F2B4, 0xBC12F224, 
    0xC412F224, 0xF4022024, 0x1F20006A, 0x8305C812,
    
    # 汇编代码第40141行
    0x210C6BBC, 0x433C4301, 0x80171510, 0x4B3C0001, 
    0x1201610C, 0xEC2160FC, 0x21E8FC12, 0x04031064,
    
    # 汇编代码第40142行
    0x03080169, 0x080C1208, 0x086273A1, 0x28684228, 
    0x28681443, 0x66D00310, 0x08040801, 0x1208E012,
    
    # 汇编代码第40143行
    0x20120805, 0x08061218, 0x12082C12, 0xF0120807, 
    0xC3492118, 0x43506614, 0x21506914, 0x43502120,
    
    # 汇编代码第40144行
    0x435064F4, 0x43506700, 0x43506710, 0x43506510, 
    0x935064B8, 0x33486720, 0x000B7A12, 0xFF010808
]

def convert_to_bytes():
    """将32位字数据转换为字节数组"""
    byte_data = []
    for word in LOGO_DATA_RAW:
        # 小端序转换
        bytes_le = struct.pack('<I', word)
        byte_data.extend(bytes_le)
    return byte_data

def create_bitmap_variants():
    """创建不同解释方式的位图"""
    byte_data = convert_to_bytes()
    
    variants = []
    
    # 变体1: 64x32, MSB位序
    bitmap1 = np.zeros((32, 64), dtype=np.uint8)
    for y in range(32):
        for x in range(64):
            byte_idx = (y * 64 + x) // 8
            bit_pos = 7 - ((y * 64 + x) % 8)
            if byte_idx < len(byte_data):
                pixel = (byte_data[byte_idx] >> bit_pos) & 1
                bitmap1[y, x] = pixel * 255
    variants.append(("64x32 MSB", bitmap1))
    
    # 变体2: 64x32, LSB位序
    bitmap2 = np.zeros((32, 64), dtype=np.uint8)
    for y in range(32):
        for x in range(64):
            byte_idx = (y * 64 + x) // 8
            bit_pos = (y * 64 + x) % 8
            if byte_idx < len(byte_data):
                pixel = (byte_data[byte_idx] >> bit_pos) & 1
                bitmap2[y, x] = pixel * 255
    variants.append(("64x32 LSB", bitmap2))
    
    # 变体3: 32x64, MSB位序
    bitmap3 = np.zeros((64, 32), dtype=np.uint8)
    for y in range(64):
        for x in range(32):
            byte_idx = (y * 32 + x) // 8
            bit_pos = 7 - ((y * 32 + x) % 8)
            if byte_idx < len(byte_data):
                pixel = (byte_data[byte_idx] >> bit_pos) & 1
                bitmap3[y, x] = pixel * 255
    variants.append(("32x64 MSB", bitmap3))
    
    # 变体4: 按字节排列 (8x32字节)
    bitmap4 = np.zeros((32, 8*8), dtype=np.uint8)
    for byte_idx in range(min(32, len(byte_data))):
        byte_val = byte_data[byte_idx]
        for bit in range(8):
            pixel = (byte_val >> (7-bit)) & 1
            bitmap4[byte_idx, bit] = pixel * 255
    variants.append(("Byte View 32x64", bitmap4))
    
    return variants

def visualize_logo():
    """可视化logo数据的不同解释"""
    variants = create_bitmap_variants()
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('AT32F403AVG Logo数据可视化\n(从汇编代码0x8016A54提取)', fontsize=16)
    
    for i, (title, bitmap) in enumerate(variants):
        row = i // 2
        col = i % 2
        
        axes[row, col].imshow(bitmap, cmap='gray', interpolation='nearest')
        axes[row, col].set_title(f'{title}\n{bitmap.shape[1]}x{bitmap.shape[0]}')
        axes[row, col].grid(True, alpha=0.3)
        axes[row, col].set_xlabel('X (像素)')
        axes[row, col].set_ylabel('Y (像素)')
    
    plt.tight_layout()
    plt.savefig('logo_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()

def save_bitmap_images():
    """保存位图图像文件"""
    variants = create_bitmap_variants()
    
    for title, bitmap in variants:
        # 放大图像以便查看
        scale = 8
        height, width = bitmap.shape
        scaled_bitmap = np.repeat(np.repeat(bitmap, scale, axis=0), scale, axis=1)
        
        # 保存为PNG
        img = Image.fromarray(scaled_bitmap, mode='L')
        filename = f"logo_{title.replace(' ', '_').replace('x', 'x')}.png"
        img.save(filename)
        print(f"保存: {filename} ({width*scale}x{height*scale})")

def analyze_data():
    """分析logo数据"""
    byte_data = convert_to_bytes()
    
    print("=== AT32F403AVG Logo数据分析 ===")
    print(f"数据来源: 汇编代码第40137-40144行")
    print(f"Flash地址: 0x8016A54")
    print(f"数据大小: {len(byte_data)} 字节 ({len(LOGO_DATA_RAW)} 个32位字)")
    print(f"总位数: {len(byte_data) * 8} 位")
    
    # 统计位分布
    total_bits = len(byte_data) * 8
    set_bits = sum(bin(b).count('1') for b in byte_data)
    clear_bits = total_bits - set_bits
    
    print(f"置位数: {set_bits} ({set_bits/total_bits*100:.1f}%)")
    print(f"清零位数: {clear_bits} ({clear_bits/total_bits*100:.1f}%)")
    
    # 检查是否有明显的模式
    zero_bytes = sum(1 for b in byte_data if b == 0)
    ff_bytes = sum(1 for b in byte_data if b == 0xFF)
    
    print(f"全零字节: {zero_bytes}")
    print(f"全一字节: {ff_bytes}")
    
    # 显示原始数据
    print("\n=== 原始32位字数据 ===")
    for i, word in enumerate(LOGO_DATA_RAW):
        if i % 4 == 0:
            print(f"\n行{40137 + i//8}: ", end="")
        print(f"0x{word:08X} ", end="")
    print()

def create_ascii_art():
    """创建ASCII艺术版本"""
    variants = create_bitmap_variants()
    
    print("\n=== ASCII艺术版本 ===")
    
    for title, bitmap in variants:
        print(f"\n{title}:")
        print("=" * (bitmap.shape[1] + 4))
        
        for row in bitmap:
            print("| ", end="")
            for pixel in row:
                print("#" if pixel > 128 else " ", end="")
            print(" |")
        
        print("=" * (bitmap.shape[1] + 4))
        
        # 只显示第一个变体的ASCII，避免输出过长
        break

def compare_with_svg():
    """与SVG logo进行比较分析"""
    print("\n=== 与SVG Logo比较 ===")
    print("SVG信息:")
    print("- 尺寸: 74.86 x 24.92")
    print("- 内容: TRIDIUM品牌标识")
    print("- 格式: 矢量图形")
    print()
    print("位图数据分析:")
    print("- 可能尺寸: 64x32像素")
    print("- 格式: 单色位图")
    print("- 用途: 嵌入式设备显示")
    print("- 关系: SVG的位图化版本")

if __name__ == "__main__":
    print("AT32F403AVG Logo数据分析工具")
    print("=" * 50)
    
    # 分析数据
    analyze_data()
    
    # 创建ASCII艺术
    create_ascii_art()
    
    # 比较分析
    compare_with_svg()
    
    # 保存图像
    try:
        save_bitmap_images()
        print("\n✅ 位图图像已保存")
    except ImportError:
        print("\n⚠️ PIL库未安装，跳过图像保存")
    
    # 可视化
    try:
        visualize_logo()
        print("✅ 可视化图表已生成")
    except ImportError:
        print("⚠️ matplotlib库未安装，跳过可视化")
    
    print("\n🎉 分析完成！")
