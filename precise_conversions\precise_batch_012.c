// 精确转换批次 12 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46AC4
 * @note 指令数: 53, 标签数: 0
 */
void precise_func_46ac4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000783E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000783C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200078B2;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007690;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_47C8C
    // 调用函数: sub_47C8C();
    // LDR     R1, =0x2000783E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BL      sub_47D0E
    // 调用函数: sub_47D0E();
    // LDR     R1, =0x200078B7
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078B8
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000783A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000783A
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000783C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078B1
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      loc_4654C
    // 调用函数: loc_4654C();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x200077BC
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_47736
    // 调用函数: sub_47736();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078B4
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078B2
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007690
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007698
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007688
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007698
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007690
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200076A0
    // 内存加载操作
    // BL      sub_45616
    // 调用函数: sub_45616();
    // BL      sub_483B4
    // 调用函数: sub_483B4();
    // LDR     R1, =0x200078B3
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46B74
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_46b74(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000783E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000789A;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x2000789A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_46B90
    // 条件跳转
    // LDR     R0, =0x2000783E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0xFFFF
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_46B90
    // 条件跳转
    // BL      sub_47D2A
    // 调用函数: sub_47D2A();
    // LDR     R1, =0x2000783E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46D18
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_46d18(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R12, R2
    // MOVS    R2, R0
    // ORRS    R2, R1
    // ADDS    R2, R2, R2
    // 算术运算
    // BEQ     loc_46D34
    // 条件跳转
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_46D34
    // 条件跳转
    // MOVS    R2, #0x1000000
    // R2 = 0x1000000;
    // ADDS    R0, R0, R0
    // 算术运算
    // CMN     R0, R2
    // 比较操作
    // MOV     R0, R1
    // BHI     loc_46D34
    // CMP     R0, R0
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46D38
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_46d38(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LSLS    R2, R2, #0x18
    // LSRS    R3, R2, #8
    // ORRS    R2, R3
    // LSRS    R3, R2, #0x10
    // ORRS    R2, R3
    // NOP
    // TST     R1, R1
    // 比较操作
    // BEQ     locret_46D54
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46D88
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_46d88(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE000E100;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #1
    // R1 = 1;
    // LSLS    R2, R0, #0x1B
    // LSRS    R2, R2, #0x1B
    // LSLS    R1, R2
    // LDR     R2, =0xE000E100
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46D96
 * @note 指令数: 38, 标签数: 0
 */
void precise_func_46d96(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BPL     loc_46DE2
    // LDR     R4, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R2, R0, #0x1C
    // LSRS    R2, R2, #0x1C
    // SUBS    R2, #8
    // 算术运算
    // LSRS    R5, R2, #2
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R5, R2
    // LDR     R2, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R3, R0, #0x1C
    // LSRS    R3, R3, #0x1C
    // SUBS    R3, #8
    // 算术运算
    // LSRS    R3, R3, #2
    // MOVS    R6, #4
    // R6 = 4;
    // MULS    R3, R6
    // LDR     R2, [R2,R3]
    // 内存加载操作
    // MOVS    R3, #0xFF
    // R3 = 0xFF;
    // LSLS    R6, R0, #0x1E
    // LSRS    R6, R6, #0x1E
    // MOVS    R7, #8
    // R7 = 8;
    // MULS    R6, R7
    // LSLS    R3, R6
    // BICS    R2, R3
    // LSLS    R3, R1, #6
    // UXTB    R3, R3
    // 数据扩展操作
    // LSLS    R6, R0, #0x1E
    // LSRS    R6, R6, #0x1E
    // MOVS    R7, #8
    // R7 = 8;
    // MULS    R6, R7
    // LSLS    R3, R6
    // ORRS    R3, R2
    // STR     R3, [R4,R5]
    // 内存存储操作
    // B       locret_46E18
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46E1A
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_46e1a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BPL     loc_46E46
    // LDR     R1, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R2, R0, #0x1C
    // LSRS    R2, R2, #0x1C
    // SUBS    R2, #8
    // 算术运算
    // LSRS    R2, R2, #2
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R2, R3
    // LDR     R1, [R1,R2]
    // 内存加载操作
    // LSLS    R0, R0, #0x1E
    // LSRS    R0, R0, #0x1E
    // MOVS    R2, #8
    // R2 = 8;
    // MULS    R0, R2
    // LSRS    R1, R0
    // LSRS    R0, R1, #6
    // LSLS    R0, R0, #0x1E
    // LSRS    R0, R0, #0x1E
    // B       locret_46E62
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46E64
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_46e64(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // SUBS    R0, R4, #1
    // 算术运算
    // MOVS    R1, #0x1000000
    // R1 = 0x1000000;
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_46E76
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_46E94
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46E96
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_46e96(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R1, R5
    // MOVS    R0, R4
    // SXTB    R0, R0
    // 数据扩展操作
    // BL      sub_46D96
    // 调用函数: sub_46D96();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46EAA
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_46eaa(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // SXTB    R0, R0
    // 数据扩展操作
    // BL      sub_46D88
    // 调用函数: sub_46D88();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46EB8
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_46eb8(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // BL      sub_46E64
    // 调用函数: sub_46E64();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46EC4
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_46ec4(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // SXTB    R0, R0
    // 数据扩展操作
    // BL      sub_46E1A
    // 调用函数: sub_46E1A();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46EEC
 * @note 指令数: 38, 标签数: 0
 */
void precise_func_46eec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BPL     loc_46F38
    // LDR     R4, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R2, R0, #0x1C
    // LSRS    R2, R2, #0x1C
    // SUBS    R2, #8
    // 算术运算
    // LSRS    R5, R2, #2
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R5, R2
    // LDR     R2, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R3, R0, #0x1C
    // LSRS    R3, R3, #0x1C
    // SUBS    R3, #8
    // 算术运算
    // LSRS    R3, R3, #2
    // MOVS    R6, #4
    // R6 = 4;
    // MULS    R3, R6
    // LDR     R2, [R2,R3]
    // 内存加载操作
    // MOVS    R3, #0xFF
    // R3 = 0xFF;
    // LSLS    R6, R0, #0x1E
    // LSRS    R6, R6, #0x1E
    // MOVS    R7, #8
    // R7 = 8;
    // MULS    R6, R7
    // LSLS    R3, R6
    // BICS    R2, R3
    // LSLS    R3, R1, #6
    // UXTB    R3, R3
    // 数据扩展操作
    // LSLS    R6, R0, #0x1E
    // LSRS    R6, R6, #0x1E
    // MOVS    R7, #8
    // R7 = 8;
    // MULS    R6, R7
    // LSLS    R3, R6
    // ORRS    R3, R2
    // STR     R3, [R4,R5]
    // 内存存储操作
    // B       locret_46F6E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46F70
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_46f70(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // SUBS    R0, R4, #1
    // 算术运算
    // MOVS    R1, #0x1000000
    // R1 = 0x1000000;
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_46F82
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_46FA0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46FA2
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_46fa2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007730;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_457A0
    // 调用函数: sub_457A0();
    // LDR     R0, =0x20007730
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x20007730
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46FB4
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_46fb4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000314;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20000314
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R1, =(a031meDSUnsuppo+0xB) ; "d) %s: unsupported frequency configurat"...
    // 内存加载操作
    // BL      sub_4637C
    // 调用函数: sub_4637C();
    // BL      sub_46F70
    // 调用函数: sub_46F70();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46FE8
 * @note 指令数: 11, 标签数: 0
 */
uint32_t precise_func_46fe8(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDRB    R3, [R0,#3]
    // 内存加载操作
    // LDRB    R2, [R0,#2]
    // 内存加载操作
    // LDRB    R1, [R0,#1]
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LSLS    R3, R3, #8
    // ORRS    R2, R3
    // LSLS    R1, R1, #8
    // LSLS    R2, R2, #0x10
    // ORRS    R0, R1
    // ORRS    R0, R2
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47000
 * @note 指令数: 10, 标签数: 0
 */
uint32_t precise_func_47000(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R12, R0
    // STRB    R0, [R1]
    // 内存存储操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R1,#1]
    // 内存存储操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R1,#2]
    // 内存存储操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R1,#3]
    // 内存存储操作
    // MOV     R0, R12
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47014
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_47014(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40000400;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40012C00;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, [R0]
    // 内存加载操作
    // MOVS    R2, R3
    // LDR     R3, =0x40012C00
    // 内存加载操作
    // CMP     R0, R3
    // 比较操作
    // BEQ     loc_47030
    // 条件跳转
    // MOVS    R3, #0x40000000
    // R3 = 0x40000000;
    // CMP     R0, R3
    // 比较操作
    // BEQ     loc_47030
    // 条件跳转
    // LDR     R3, =0x40000400
    // 内存加载操作
    // CMP     R0, R3
    // 比较操作
    // BNE     loc_4703A
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_470BC
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_470bc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R7, #0
    // R7 = 0;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47352
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_47352(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R7, #0
    // R7 = 0;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_474E8
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_474e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R0, R4
    // ADDS    R0, #0x10
    // 算术运算
    // BL      sub_46FE8
    // 调用函数: sub_46FE8();
    // UXTH    R5, R5
    // 数据扩展操作
    // ANDS    R0, R5
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_47504
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // MOVS    R6, R0
    // B       loc_47508
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4750E
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_4750e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // UXTB    R6, R6
    // 数据扩展操作
    // CMP     R6, #0
    // 比较操作
    // BEQ     loc_4752A
    // 条件跳转
    // MOVS    R1, R4
    // ADDS    R1, #0x18
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // MOVS    R0, R5
    // BL      sub_47000
    // 调用函数: sub_47000();
    // B       locret_47534
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47536
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_47536(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R0, R4
    // ADDS    R0, #0x14
    // 算术运算
    // BL      sub_46FE8
    // 调用函数: sub_46FE8();
    // MOVS    R1, R4
    // ADDS    R1, #0x14
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // EORS    R0, R5
    // BL      sub_47000
    // 调用函数: sub_47000();
    // POP     {R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47564
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_47564(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_48516
    // 调用函数: sub_48516();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4756E
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_4756e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x4000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4100;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015FE0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // CMP     R4, #8
    // 比较操作
    // BCS     locret_475A6
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
    // LDR     R0, =0x8015FE0
    // 内存加载操作
    // LDRB    R0, [R0,R4]
    // 内存加载操作
    // MOVS    R1, #0x4100
    // R1 = 0x4100;
    // ORRS    R1, R0
    // MOVS    R6, R1
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R7
    // BL      sub_4866A
    // 调用函数: sub_4866A();
    // MOVS    R0, #0x4000
    // R0 = 0x4000;
    // ORRS    R0, R5
    // MOVS    R6, R0
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R7
    // BL      sub_4866A
    // 调用函数: sub_4866A();
    // B       locret_475A6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_475AC
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_475ac(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_48516
    // 调用函数: sub_48516();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_475B6
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_475b6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A00;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015FE8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
    // LDR     R0, =0x8015FE8
    // 内存加载操作
    // LDRB    R0, [R0,R4]
    // 内存加载操作
    // ORRS    R0, R5
    // MOVS    R1, #0x2A00
    // R1 = 0x2A00;
    // ORRS    R1, R0
    // MOVS    R7, R1
    // MOVS    R1, R7
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R6
    // BL      sub_4866A
    // 调用函数: sub_4866A();
    // POP     {R0,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_475E4
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_475e4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x21;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R4,LR}
    // 栈操作
    // BL      sub_48764
    // 调用函数: sub_48764();
    // MOVS    R1, #0x21 ; '!'
    // R1 = 0x21;
    // STR     R1, [R0]
    // 内存存储操作
    // POP     {R0-R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_475F0
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_475f0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R2-R6,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R6, R1
    // MOVS    R4, R2
    // MOVS    R0, R4
    // ADDS    R0, R0, #2
    // 算术运算
    // BL      sub_46FE8
    // 调用函数: sub_46FE8();
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOV     R1, SP
    // STRH    R0, [R1,#0x18+var_14]
    // 内存存储操作
    // LDRB    R0, [R4]
    // 内存加载操作
    // LSLS    R0, R0, #0x1A
    // BPL     loc_4761A
    // MOV     R0, SP
    // LDRH    R0, [R0,#0x18+var_14]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // ORRS    R1, R0
    // MOV     R0, SP
    // STRH    R1, [R0,#0x18+var_14]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47736
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_47736(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ASRS    R0, R0, #8
    // LDR     R2, =0x2000018C
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STR     R1, [R2,#0x20]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47744
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_47744(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_18= -0x18
    // PUSH    {R2,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R7, R0
    // MOVS    R6, R1
    // MOVS    R5, #0
    // R5 = 0;
    // MVNS    R5, R5
    // LDR     R0, [SP,#0x20+var_18]
    // 内存加载操作
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4781C
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_4781c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R2, R5
    // UXTB    R2, R2
    // 数据扩展操作
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // MOVS    R1, R0
    // LSLS    R1, R1, #0x1F
    // LSRS    R1, R1, #0x1F
    // MOVS    R0, R4
    // BL      sub_49034
    // 调用函数: sub_49034();
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_4785C
    // 条件跳转
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // ORRS    R1, R0
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // STRB    R1, [R0,#0x10]
    // 内存存储操作
    // B       locret_47874
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47876
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_47876(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // ADDS    R0, #0xC
    // 算术运算
    // BL      sub_491E8
    // 调用函数: sub_491E8();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_47892
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_478A2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_478A4
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_478a4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_491B0
    // 调用函数: sub_491B0();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_478BC
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_478bc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_4781C
    // 调用函数: sub_4781C();
    // MOVS    R1, R5
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_48F4A
    // 调用函数: sub_48F4A();
    // MOVS    R0, #0
    // R0 = 0;
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_478D8
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_478d8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x12;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // LSLS    R0, R0, #0x12
    // BPL     loc_4794A
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47A18
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_47a18(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47B36
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_47b36(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47BE6
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_47be6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R1, R4
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MULS    R2, R5
    // ADDS    R0, R0, R2
    // 算术运算
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_4917C
    // 调用函数: sub_4917C();
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47C00
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_47c00(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R6, R1
    // MOVS    R5, R2
    // CMP     R5, #0
    // 比较操作
    // BNE     loc_47C12
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // B       locret_47C20
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47C22
 * @note 指令数: 34, 标签数: 0
 */
void precise_func_47c22(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_47C6A
    // MOVS    R0, R4
    // BL      sub_47876
    // 调用函数: sub_47876();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_47C6A
    // 条件跳转
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_47C6A
    // MOVS    R0, R4
    // BL      sub_49082
    // 调用函数: sub_49082();
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // MOVS    R1, #0xFE
    // R1 = 0xFE;
    // ANDS    R1, R0
    // LDR     R0, =0x2000018C
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // STRB    R1, [R0,#0x10]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47C70
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_47c70(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // ASRS    R5, R5, #8
    // MOVS    R1, R4
    // MOVS    R0, R5
    // BL      sub_490D8
    // 调用函数: sub_490D8();
    // POP     {R0,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47C8C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_47c8c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // BL      sub_47D2A
    // 调用函数: sub_47D2A();
    // MOVS    R5, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x100000
    // R1 = 0x100000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47CE0
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_47ce0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015A8C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_47CFC
    // 条件跳转
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8015A8C
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015A8C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4750E
    // 调用函数: sub_4750E();
    // B       locret_47D0C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47D0E
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_47d0e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015A8C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x8015A8C
    // 内存加载操作
    // LDR     R1, [R0,#0x1C]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015A8C
    // 内存加载操作
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_474E8
    // 调用函数: sub_474E8();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_47D26
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_47D28
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47D2A
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_47d2a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R7, #0x80
    // R7 = 0x80;
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x100000
    // R1 = 0x100000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R1, =0x80153E0
    // 内存加载操作
    // LDR     R0, =0x80153DC
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // LDR     R1, =0x80153F8
    // 内存加载操作
    // LDR     R0, =0x80153DC
    // 内存加载操作
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47EC4
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_47ec4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015D1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015D20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R1, =0x8015D20
    // 内存加载操作
    // LDR     R0, =0x8015D1C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47F18
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_47f18(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_47F1C
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_47f1c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R2, #0
    // R2 = 0;
    // MVNS    R2, R2
    // BL      sub_49254
    // 调用函数: sub_49254();
    // POP     {R2,PC}
    // 栈操作
}

