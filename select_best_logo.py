#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
选择最佳logo图像并重命名
"""

import os
import shutil
from PIL import Image

def analyze_image_content(filename):
    """
    分析图像内容，评估其作为logo的可能性
    """
    try:
        img = Image.open(filename)
        pixels = list(img.getdata())
        
        # 计算图像的复杂度 (边缘变化次数)
        width, height = img.size
        edge_changes = 0
        
        # 水平方向边缘检测
        for y in range(height):
            for x in range(width - 1):
                pixel1 = pixels[y * width + x]
                pixel2 = pixels[y * width + x + 1]
                if abs(pixel1 - pixel2) > 128:  # 显著变化
                    edge_changes += 1
        
        # 垂直方向边缘检测
        for y in range(height - 1):
            for x in range(width):
                pixel1 = pixels[y * width + x]
                pixel2 = pixels[(y + 1) * width + x]
                if abs(pixel1 - pixel2) > 128:  # 显著变化
                    edge_changes += 1
        
        # 计算复杂度分数 (边缘密度)
        total_possible_edges = (width - 1) * height + width * (height - 1)
        complexity_score = edge_changes / total_possible_edges if total_possible_edges > 0 else 0
        
        return complexity_score, edge_changes
        
    except Exception as e:
        print(f"分析图像 {filename} 时出错: {e}")
        return 0, 0

def select_best_logo():
    """
    选择最佳的logo图像
    """
    # 候选文件列表 (按优先级排序)
    candidates = [
        "logo_64x32_LSB_正常.png",
        "logo_64x32_LSB_反转.png", 
        "logo_64x32_MSB_正常.png",
        "logo_64x32_MSB_反转.png",
        "logo_32x64_LSB_正常.png",
        "logo_32x64_LSB_反转.png",
        "logo_32x64_MSB_正常.png",
        "logo_32x64_MSB_反转.png"
    ]
    
    print("分析候选logo图像...")
    results = []
    
    for filename in candidates:
        if os.path.exists(filename):
            complexity, edges = analyze_image_content(filename)
            results.append((filename, complexity, edges))
            print(f"{filename}: 复杂度={complexity:.4f}, 边缘数={edges}")
    
    if not results:
        print("未找到候选图像文件")
        return None
    
    # 选择复杂度适中的图像 (太简单可能是空白，太复杂可能是噪声)
    # 理想的logo应该有一定的复杂度但不会太高
    best_candidate = None
    best_score = 0
    
    for filename, complexity, edges in results:
        # 评分函数: 复杂度在0.05-0.3之间得分最高
        if 0.02 <= complexity <= 0.4:
            score = complexity * (1 - abs(complexity - 0.15) / 0.15)  # 0.15为理想复杂度
        else:
            score = complexity * 0.1  # 复杂度过高或过低的惩罚
        
        print(f"{filename}: 评分={score:.4f}")
        
        if score > best_score:
            best_score = score
            best_candidate = filename
    
    return best_candidate

def main():
    """
    主函数
    """
    print("AT32F403AVG Logo选择工具")
    print("=" * 40)
    
    # 选择最佳logo
    best_logo = select_best_logo()
    
    if best_logo:
        print(f"\n最佳logo候选: {best_logo}")
        
        # 复制为主logo文件
        main_logo_name = "at32f403avg_logo.png"
        shutil.copy2(best_logo, main_logo_name)
        print(f"已复制为: {main_logo_name}")
        
        # 创建一个小尺寸版本 (原始尺寸)
        try:
            img = Image.open(best_logo)
            
            # 确定原始尺寸
            if "64x32" in best_logo:
                original_size = (64, 32)
            elif "32x64" in best_logo:
                original_size = (32, 64)
            elif "128x16" in best_logo:
                original_size = (128, 16)
            elif "16x128" in best_logo:
                original_size = (16, 128)
            else:
                original_size = (64, 32)  # 默认
            
            # 创建原始尺寸版本
            img_small = img.resize(original_size, Image.NEAREST)
            small_logo_name = "at32f403avg_logo_small.png"
            img_small.save(small_logo_name)
            print(f"已创建小尺寸版本: {small_logo_name} ({original_size[0]}x{original_size[1]})")
            
        except Exception as e:
            print(f"创建小尺寸版本时出错: {e}")
        
        print(f"\n推荐使用的logo文件:")
        print(f"  - 显示用 (放大): {main_logo_name}")
        print(f"  - 嵌入式用 (原始): {small_logo_name}")
        
    else:
        print("未能确定最佳logo，请手动检查生成的图像文件")
    
    print(f"\n所有生成的logo变体:")
    logo_files = [f for f in os.listdir('.') if f.startswith('logo_') and f.endswith('.png')]
    for f in sorted(logo_files):
        print(f"  - {f}")

if __name__ == "__main__":
    main()
