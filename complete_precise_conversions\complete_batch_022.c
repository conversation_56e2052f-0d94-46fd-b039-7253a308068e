// 完整精确转换批次 22 - 100%对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45EA0
 * @note 指令数: 23, 标签数: 4
 * @note 内存引用: 4, 函数调用: 0
 */
uint32_t precise_func_45ea0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38000000;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45ED2
 * @note 指令数: 193, 标签数: 12
 * @note 内存引用: 7, 函数调用: 0
 */
void precise_func_45ed2(uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46056
 * @note 指令数: 46, 标签数: 5
 * @note 内存引用: 10, 函数调用: 0
 */
uint32_t precise_func_46056(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x380;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_460B8
 * @note 指令数: 8, 标签数: 0
 * @note 内存引用: 1, 函数调用: 1
 */
void precise_func_460b8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void loc_460D0(void);

    // 汇编逻辑实现

    // 函数调用
    loc_460D0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_460CC
 * @note 指令数: 14, 标签数: 4
 * @note 内存引用: 4, 函数调用: 0
 */
uint32_t precise_func_460cc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x41D;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_460EC
 * @note 指令数: 5, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
void precise_func_460ec(uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_461B2
 * @note 指令数: 118, 标签数: 12
 * @note 内存引用: 9, 函数调用: 0
 */
uint32_t precise_func_461b2(uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x36;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_462A0
 * @note 指令数: 82, 标签数: 12
 * @note 内存引用: 8, 函数调用: 0
 */
uint32_t precise_func_462a0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x7F800000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xFE;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x7F;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46376
 * @note 指令数: 3, 标签数: 0
 * @note 内存引用: 0, 函数调用: 0
 */
void precise_func_46376(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4637C
 * @note 指令数: 86, 标签数: 15
 * @note 内存引用: 2, 函数调用: 1
 */
uint32_t precise_func_4637c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 外部函数声明
    extern void nullsub_26(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 函数调用
    nullsub_26();

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4642A
 * @note 指令数: 93, 标签数: 11
 * @note 内存引用: 6, 函数调用: 1
 */
void precise_func_4642a(uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_475E4(void);

    // 汇编逻辑实现

    // 函数调用
    sub_475E4();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_464E8
 * @note 指令数: 28, 标签数: 6
 * @note 内存引用: 4, 函数调用: 0
 */
uint32_t precise_func_464e8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x7F;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46524
 * @note 指令数: 20, 标签数: 2
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_46524(uint32_t param0, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4654A
 * @note 指令数: 39, 标签数: 1
 * @note 内存引用: 7, 函数调用: 3
 */
void precise_func_4654a(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x22;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38400;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200077BC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xF;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000021C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_475F0(void);
    extern void sub_47000(void);
    extern void sub_47CE0(void);

    // 汇编逻辑实现

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_47000();
    sub_475F0();
    sub_47CE0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46594
 * @note 指令数: 14, 标签数: 2
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_46594(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000783A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_465B0
 * @note 指令数: 14, 标签数: 2
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_465b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000783A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_465CC
 * @note 指令数: 14, 标签数: 2
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_465cc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000783A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_465E8
 * @note 指令数: 39, 标签数: 4
 * @note 内存引用: 2, 函数调用: 4
 */
void precise_func_465e8(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3FF00000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_47F1C(void);
    extern void sub_460CC(void);
    extern void sub_47F28(void);
    extern void sub_460EC(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_460CC();
    sub_47F1C();
    sub_460EC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4663C
 * @note 指令数: 71, 标签数: 1
 * @note 内存引用: 9, 函数调用: 3
 */
void precise_func_4663c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20006346;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200078B6;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x8007852;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x200077BC;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_4803A(void);
    extern void sub_465E8(void);
    extern void sub_47F58(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_47F58();
    sub_465E8();
    sub_4803A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_466CA
 * @note 指令数: 82, 标签数: 4
 * @note 内存引用: 12, 函数调用: 3
 */
void precise_func_466ca(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078A4;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xA8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200078A8;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20007884;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20007710;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_468DC(void);
    extern void sub_47F58(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_47F58();
    sub_47F58();
    sub_468DC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_467A0
 * @note 指令数: 129, 标签数: 2
 * @note 内存引用: 13, 函数调用: 7
 */
void precise_func_467a0(uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078A4;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200078A8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20007884;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20007710;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20007714;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_46376(void);
    extern void sub_47F58(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_47F58();
    sub_47F58();
    sub_46376();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_468B8
 * @note 指令数: 14, 标签数: 2
 * @note 内存引用: 1, 函数调用: 2
 */
void precise_func_468b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200060E8;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_467A0(void);
    extern void sub_466CA(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_466CA();
    sub_467A0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_468DC
 * @note 指令数: 112, 标签数: 8
 * @note 内存引用: 7, 函数调用: 9
 */
void precise_func_468dc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078A8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007884;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000640C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x200070BC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200078AA;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_4663C(void);
    extern void sub_46376(void);
    extern void sub_47F58(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_46376();
    sub_47F58();
    sub_46376();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_469D0
 * @note 指令数: 13, 标签数: 1
 * @note 内存引用: 1, 函数调用: 1
 */
void precise_func_469d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_4663C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_4663C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_469FC
 * @note 指令数: 92, 标签数: 8
 * @note 内存引用: 12, 函数调用: 4
 */
void precise_func_469fc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078B2;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000783C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200078A4;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007698;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x1F4;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200076A0;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x200078A8;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_48386(void);
    extern void sub_469D0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_48386();
    sub_48386();
    sub_48386();
}

