// 大规模手工转换批次 23 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_7BB10
 * @note 指令数: 147
 */
void func_7bb10(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003634 = (volatile uint32_t *)0x20003634;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_20003753 = (volatile uint32_t *)0x20003753;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7BC6C
 * @note 指令数: 402
 */
uint32_t func_7bc6c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_20003702 = (volatile uint32_t *)0x20003702;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7C054
 * @note 指令数: 378
 */
uint32_t func_7c054(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_10000 = (volatile uint32_t *)0x10000;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7C410
 * @note 指令数: 128
 */
void func_7c410(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20003634 = (volatile uint32_t *)0x20003634;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7C550
 * @note 指令数: 271
 */
void func_7c550(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_20003702 = (volatile uint32_t *)0x20003702;
    volatile uint32_t *addr_20003754 = (volatile uint32_t *)0x20003754;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7C7B8
 * @note 指令数: 77
 */
void func_7c7b8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *addr_20003750 = (volatile uint32_t *)0x20003750;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_20003754 = (volatile uint32_t *)0x20003754;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7C8A0
 * @note 指令数: 134
 */
void func_7c8a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40011400 = (volatile uint32_t *)0x40011400;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_40011408 = (volatile uint32_t *)0x40011408;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7C9AE
 * @note 指令数: 228
 */
void func_7c9ae(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7CB84
 * @note 指令数: 9
 */
uint32_t func_7cb84(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8011790 = (volatile uint32_t *)0x8011790;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7CBA8
 * @note 指令数: 43
 */
void func_7cba8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8011790 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *addr_28 = (volatile uint32_t *)0x28;
    volatile uint32_t *addr_19 = (volatile uint32_t *)0x19;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7CC04
 * @note 指令数: 21
 */
void func_7cc04(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_8011790 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7CC2C
 * @note 指令数: 17
 */
void func_7cc2c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_8011790 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7CC50
 * @note 指令数: 13
 */
uint32_t func_7cc50(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_8011790 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7CC74
 * @note 指令数: 23
 */
void func_7cc74(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7CCA0
 * @note 指令数: 8
 */
void func_7cca0(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7CCB2
 * @note 指令数: 7
 */
uint32_t func_7ccb2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8011790 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7CCCC
 * @note 指令数: 48
 */
void func_7cccc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_FFFFF4FF = (volatile uint32_t *)0xFFFFF4FF;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_FFFFCFFF = (volatile uint32_t *)0xFFFFCFFF;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7CD48
 * @note 指令数: 26
 */
void func_7cd48(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000366C = (volatile uint32_t *)0x2000366C;
    volatile uint32_t *addr_20003488 = (volatile uint32_t *)0x20003488;
    volatile uint32_t *addr_200035AC = (volatile uint32_t *)0x200035AC;
    volatile uint32_t *addr_20003731 = (volatile uint32_t *)0x20003731;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7CD86
 * @note 指令数: 202
 */
void func_7cd86(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000366C = (volatile uint32_t *)0x2000366C;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_200035AC = (volatile uint32_t *)0x200035AC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7CF1A
 * @note 指令数: 46
 */
void func_7cf1a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000366C = (volatile uint32_t *)0x2000366C;
    volatile uint32_t *addr_20003731 = (volatile uint32_t *)0x20003731;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7CF74
 * @note 指令数: 16
 */
void func_7cf74(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000366C = (volatile uint32_t *)0x2000366C;
    volatile uint32_t *addr_20003731 = (volatile uint32_t *)0x20003731;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7CF92
 * @note 指令数: 16
 */
void func_7cf92(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000366C = (volatile uint32_t *)0x2000366C;
    volatile uint32_t *addr_20003731 = (volatile uint32_t *)0x20003731;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7CFC0
 * @note 指令数: 36
 */
void func_7cfc0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7D00C
 * @note 指令数: 25
 */
void func_7d00c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40022010 = (volatile uint32_t *)0x40022010;
    volatile uint32_t *addr_4002200C = (volatile uint32_t *)0x4002200C;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7D044
 * @note 指令数: 20
 */
void func_7d044(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7D07C
 * @note 指令数: 334
 */
void func_7d07c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_90 = (volatile uint32_t *)0x90;
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_7FFFFFFF = (volatile uint32_t *)0x7FFFFFFF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7D636
 * @note 指令数: 52
 */
uint32_t func_7d636(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_7A = (volatile uint32_t *)0x7A;
    volatile uint32_t *addr_6C = (volatile uint32_t *)0x6C;
    volatile uint32_t *addr_74 = (volatile uint32_t *)0x74;
    volatile uint32_t *addr_6A = (volatile uint32_t *)0x6A;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7D69E
 * @note 指令数: 153
 */
void func_7d69e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_FFEF = (volatile uint32_t *)0xFFEF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7D7D0
 * @note 指令数: 124
 */
void func_7d7d0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_7D832 = (volatile uint32_t *)0x7D832;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7DC68
 * @note 指令数: 333
 */
void func_7dc68(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_66 = (volatile uint32_t *)0x66;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7DF02
 * @note 指令数: 25
 */
void func_7df02(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7DF38
 * @note 指令数: 67
 */
void func_7df38(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_E000ED1C = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_E000E400 = (volatile uint32_t *)0xE000E400;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7DFBC
 * @note 指令数: 24
 */
void func_7dfbc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_E000E018 = (volatile uint32_t *)0xE000E018;
    volatile uint32_t *addr_E000E014 = (volatile uint32_t *)0xE000E014;
    volatile uint32_t *addr_E000E010 = (volatile uint32_t *)0xE000E010;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7DFEE
 * @note 指令数: 8
 */
uint32_t func_7dfee(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003664 = (volatile uint32_t *)0x20003664;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7E000
 * @note 指令数: 7
 */
void func_7e000(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20000164 = (volatile uint32_t *)0x20000164;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7E034
 * @note 指令数: 37
 */
void func_7e034(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_80115D8 = (volatile uint32_t *)0x80115D8;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7E080
 * @note 指令数: 45
 */
void func_7e080(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80116C0 = (volatile uint32_t *)0x80116C0;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_80115D8 = (volatile uint32_t *)0x80115D8;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7E0DE
 * @note 指令数: 37
 */
void func_7e0de(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_80115D8 = (volatile uint32_t *)0x80115D8;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_40000 = (volatile uint32_t *)0x40000;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7E138
 * @note 指令数: 20
 */
uint32_t func_7e138(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7E160
 * @note 指令数: 278
 */
void func_7e160(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_40010404 = (volatile uint32_t *)0x40010404;
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7E3AE
 * @note 指令数: 14
 */
void func_7e3ae(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7E3CA
 * @note 指令数: 11
 */
void func_7e3ca(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7E404
 * @note 指令数: 21
 */
uint32_t func_7e404(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7E42C
 * @note 指令数: 87
 */
void func_7e42c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200034AC = (volatile uint32_t *)0x200034AC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7E4D8
 * @note 指令数: 46
 */
void func_7e4d8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200034AC = (volatile uint32_t *)0x200034AC;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7E534
 * @note 指令数: 48
 */
void func_7e534(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_20003550 = (volatile uint32_t *)0x20003550;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7E592
 * @note 指令数: 19
 */
void func_7e592(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7E5B6
 * @note 指令数: 23
 */
void func_7e5b6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003550 = (volatile uint32_t *)0x20003550;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7E5EC
 * @note 指令数: 93
 */
void func_7e5ec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_4000 = (volatile uint32_t *)0x4000;
    volatile uint32_t *addr_200034AC = (volatile uint32_t *)0x200034AC;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7EC58
 * @note 指令数: 53
 */
void func_7ec58(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200034AC = (volatile uint32_t *)0x200034AC;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

