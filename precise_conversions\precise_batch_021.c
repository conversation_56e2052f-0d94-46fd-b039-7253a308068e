// 精确转换批次 21 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76870
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_76870(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0
    // R2 = 0;
    // B       loc_76840
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76874
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_76874(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x100;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R3, #0x11
    // R3 = 0x11;
    // MOVS    R2, #0x100
    // R2 = 0x100;
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_7A090
    // 调用函数: sub_7A090();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_768A8
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_768a8(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x100;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R3, #0x11
    // R3 = 0x11;
    // MOVS    R2, #0x100
    // R2 = 0x100;
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_7A090
    // 调用函数: sub_7A090();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_768D8
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_768d8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // SUB     SP, SP, #0x1C
    // 算术运算
    // MOVS    R5, R0
    // MOV     R0, SP
    // MOVS    R1, #0x18
    // R1 = 0x18;
    // BL      sub_76870
    // 调用函数: sub_76870();
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #6
    // 比较操作
    // BGE     loc_7693A
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7693E
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7693e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x24
    // 算术运算
    // MOVS    R7, R0
    // MOVS    R4, R1
    // MOVS    R5, #0
    // R5 = 0;
    // MOV     R0, SP
    // MOVS    R1, #0x20 ; ' '
    // R1 = 0x20;
    // BL      sub_76870
    // 调用函数: sub_76870();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76998
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_76998(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76B34
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_76b34(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // MOVS    R7, R0
    // MOVS    R4, R2
    // MOVS    R3, #0xC
    // R3 = 0xC;
    // MOVS    R2, #0xC0
    // R2 = 0xC0;
    // LDR     R1, [SP,#0x18+var_18]
    // 内存加载操作
    // MOVS    R0, R7
    // BL      sub_7A090
    // 调用函数: sub_7A090();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76B68
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_76b68(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R0,R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #4
    // 算术运算
    // MOVS    R4, R2
    // MOVS    R7, R3
    // MOVS    R3, #0x10
    // R3 = 0x10;
    // MOVS    R2, #8
    // R2 = 8;
    // LDR     R1, [SP,#0x20+var_18]
    // 内存加载操作
    // LDR     R0, [SP,#0x20+var_1C]
    // 内存加载操作
    // BL      sub_7A090
    // 调用函数: sub_7A090();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76BAA
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_76baa(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R0,R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #4
    // 算术运算
    // MOVS    R4, R2
    // MOVS    R7, R3
    // MOVS    R3, #0x10
    // R3 = 0x10;
    // MOVS    R2, #0x10
    // R2 = 0x10;
    // LDR     R1, [SP,#0x20+var_18]
    // 内存加载操作
    // LDR     R0, [SP,#0x20+var_1C]
    // 内存加载操作
    // BL      sub_7A090
    // 调用函数: sub_7A090();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76BEC
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_76bec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #2
    // R0 = 2;
    // MULS    R5, R0
    // MOVS    R3, #2
    // R3 = 2;
    // MOVS    R2, #0xC0
    // R2 = 0xC0;
    // UXTB    R5, R5
    // 数据扩展操作
    // MOVS    R1, R5
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_7A090
    // 调用函数: sub_7A090();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76C2E
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_76c2e(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_1B= -0x1B
    // var_1A= -0x1A
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76D64
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_76d64(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_43= -0x43
    // var_42= -0x42
    // var_40= -0x40
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76EF0
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_76ef0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80124A4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011E18;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_C= -0xC
    // PUSH    {R2-R4,LR}
    // 栈操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x80124A4
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x4C ; 'L'
    // R0 = 0x4C;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011E18
    // 内存加载操作
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_76D64
    // 调用函数: sub_76D64();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76F94
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_76f94(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003743;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20003743
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76FA8
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_76fa8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003747;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // LDR     R0, =0x20003747
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #5
    // 比较操作
    // BEQ     loc_76FB6
    // 条件跳转
    // B       locret_77112
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7711C
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_7711c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003624;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // CMP     R0, #6
    // 比较操作
    // BEQ     loc_7714C
    // 条件跳转
    // CMP     R0, #7
    // 比较操作
    // BNE     locret_77166
    // 条件跳转
    // MOVS    R0, #7
    // R0 = 7;
    // LDR     R1, =0x20003747
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003624
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BL      sub_76874
    // 调用函数: sub_76874();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_76BEC
    // 调用函数: sub_76BEC();
    // BL      sub_75A40
    // 调用函数: sub_75A40();
    // BL      sub_75AE0
    // 调用函数: sub_75AE0();
    // B       locret_77166
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77178
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_77178(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R1
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_77188
    // 条件跳转
    // B       locret_772A6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_772B0
 * @note 指令数: 20, 标签数: 1
 */
void precise_func_772b0(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R3, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTH    R4, R4
    // 数据扩展操作
    // UXTH    R2, R2
    // 数据扩展操作
    // CMP     R4, R2
    // 比较操作
    // BCS     loc_772D8
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R0, #2
    // R0 = 2;
    // MULS    R0, R4
    // ADDS    R0, R3, R0
    // 算术运算
    // MOVS    R5, R0
    // LDRH    R0, [R5]
    // 内存加载操作
    // UXTH    R4, R4
    // 数据扩展操作
    // MOVS    R6, #2
    // R6 = 2;
    // MULS    R6, R4
    // STRH    R0, [R1,R6]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_772B8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_772E8
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_772e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000362C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003747;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003604;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0x78 ; 'x'
    // R0 = 0x78;
    // LDR     R1, =0x2000362C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x3C ; '<'
    // R0 = 0x3C;
    // LDR     R1, =0x20003604
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20003747
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BNE     loc_77310
    // 条件跳转
    // BL      sub_788F0
    // 调用函数: sub_788F0();
    // CMP     R0, #9
    // 比较操作
    // BNE     loc_77310
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #4
    // R0 = 4;
    // BL      sub_77178
    // 调用函数: sub_77178();
    // B       locret_77328
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7732A
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_7732a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003746;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20003746
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BEQ     locret_7734E
    // 条件跳转
    // MOVS    R0, #3
    // R0 = 3;
    // LDR     R1, =0x20003746
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #6
    // R0 = 6;
    // BL      sub_77178
    // 调用函数: sub_77178();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_79DC0
    // 调用函数: sub_79DC0();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_79DFC
    // 调用函数: sub_79DFC();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77350
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_77350(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003746;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x20003746
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BNE     loc_7735E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_77360
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77362
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_77362(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003746;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #3
    // R0 = 3;
    // LDR     R1, =0x20003746
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7737C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7737c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003746;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000374A;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x2000374A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_773A4
    // 条件跳转
    // LDR     R0, =0x20003746
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_7739E
    // 条件跳转
    // LDR     R0, =0x20003747
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BEQ     loc_7739E
    // 条件跳转
    // LDR     R0, =0x20003747
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #4
    // 比较操作
    // BNE     loc_773A2
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77416
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_77416(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20003747
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77430
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_77430(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003520;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x90;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8038800;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_54= -0x54
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x7C
    // 算术运算
    // LDR     R0, =0x8038800
    // 内存加载操作
    // MOVS    R6, R0
    // LDRB    R0, [R6]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     loc_7748A
    // 条件跳转
    // MOVS    R5, #0x3C ; '<'
    // R5 = 0x3C;
    // MOVS    R4, #0
    // R4 = 0;
    // ADD     R7, SP, #0x90+var_54
    // 算术运算
    // MOVS    R2, R4
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R2, #0x18
    // R2 = 0x18;
    // ADD     R1, SP, #0x90+var_54
    // 算术运算
    // LDR     R0, =0x8038800
    // 内存加载操作
    // BL      sub_772B0
    // 调用函数: sub_772B0();
    // ADD     R0, SP, #0x90+var_54
    // 算术运算
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x20 ; ' '
    // 比较操作
    // BLT     loc_77472
    // 条件跳转
    // ADD     R0, SP, #0x90+var_54
    // 算术运算
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x7F
    // 比较操作
    // BGE     loc_77472
    // 条件跳转
    // ADD     R1, SP, #0x90+var_54
    // 算术运算
    // LDR     R0, =0x20003520
    // 内存加载操作
    // BL      sub_78944
    // 调用函数: sub_78944();
    // B       loc_7748A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77508
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_77508(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x803F000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_E= -0xE
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // LDR     R0, =0x803F000
    // 内存加载操作
    // MOVS    R4, R0
    // LDRB    R0, [R4]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BEQ     loc_77520
    // 条件跳转
    // MOVS    R2, #2
    // R2 = 2;
    // MOV     R1, SP
    // LDR     R0, =0x803F000
    // 内存加载操作
    // BL      sub_772B0
    // 调用函数: sub_772B0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_775A0
 * @note 指令数: 57, 标签数: 0
 */
void precise_func_775a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003748;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003624;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003520;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000362C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x78;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_79B10
    // 调用函数: sub_79B10();
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_79DC0
    // 调用函数: sub_79DC0();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003745
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003743
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20003743
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20003744
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #3
    // R0 = 3;
    // LDR     R1, =0x20003746
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003747
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20003747
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20003748
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20003604
    // 内存加载操作
    // BL      sub_78A0A
    // 调用函数: sub_78A0A();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003604
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x2000362C
    // 内存加载操作
    // BL      sub_78A0A
    // 调用函数: sub_78A0A();
    // MOVS    R0, #0x78 ; 'x'
    // R0 = 0x78;
    // LDR     R1, =0x2000362C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20003624
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003624
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R1, =0x801226C
    // 内存加载操作
    // LDR     R0, =0x20003520
    // 内存加载操作
    // BL      sub_78944
    // 调用函数: sub_78944();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x2000360C
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // MOVS    R0, #0x7D0
    // R0 = 0x7D0;
    // LDR     R1, =0x2000360C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20003749
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_77508
    // 调用函数: sub_77508();
    // BL      sub_77430
    // 调用函数: sub_77430();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7762C
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_7762c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003743;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011700;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xB8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_8= -8
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20003743
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_77644
    // 条件跳转
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R2, =0x8011700
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0xB8
    // R0 = 0xB8;
    // BL      sub_76B68
    // 调用函数: sub_76B68();
    // B       locret_77654
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77688
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_77688(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_77694
    // 条件跳转
    // B       locret_777F0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77834
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_77834(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // B       loc_7783A
    // 无条件跳转
    // ADDS    R1, R1, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77844
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_77844(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000E180;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1B;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #1
    // R1 = 1;
    // LSLS    R2, R0, #0x1B
    // LSRS    R2, R2, #0x1B
    // LSLS    R1, R2
    // LDR     R2, =0xE000E180
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77852
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_77852(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20001F00;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x12;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // CPSID   I
    // MOVS    R0, #0x1B
    // R0 = 0x1B;
    // BL      sub_77844
    // 调用函数: sub_77844();
    // MOVS    R0, #0x1C
    // R0 = 0x1C;
    // BL      sub_77844
    // 调用函数: sub_77844();
    // MOVS    R0, #0x1D
    // R0 = 0x1D;
    // BL      sub_77844
    // 调用函数: sub_77844();
    // MOVS    R0, #0xC
    // R0 = 0xC;
    // BL      sub_77844
    // 调用函数: sub_77844();
    // MOVS    R0, #0x10
    // R0 = 0x10;
    // BL      sub_77844
    // 调用函数: sub_77844();
    // MOVS    R0, #0x12
    // R0 = 0x12;
    // BL      sub_77844
    // 调用函数: sub_77844();
    // MOVS    R6, #4
    // R6 = 4;
    // LDR     R5, =0x801249C
    // 内存加载操作
    // LDR     R7, =0x20001F00
    // 内存加载操作
    // MOVS    R2, R6
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_762E4
    // 调用函数: sub_762E4();
    // LDR     R0, [R4]
    // 内存加载操作
    // MSR.W   MSP, R0
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // BLX     R0
    // 调用函数: R0();
    // POP     {R0,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_778A4
 * @note 指令数: 44, 标签数: 0
 */
void precise_func_778a4(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x98;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x7C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xB0;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_B8= -0xB8
    // var_B7= -0xB7
    // var_B6= -0xB6
    // var_B5= -0xB5
    // var_B4= -0xB4
    // var_B0= -0xB0
    // var_AC= -0xAC
    // var_A8= -0xA8
    // var_A4= -0xA4
    // var_A0= -0xA0
    // var_9C= -0x9C
    // var_98= -0x98
    // var_94= -0x94
    // var_90= -0x90
    // var_8C= -0x8C
    // var_88= -0x88
    // var_84= -0x84
    // var_80= -0x80
    // var_7C= -0x7C
    // var_78= -0x78
    // var_74= -0x74
    // var_70= -0x70
    // var_6C= -0x6C
    // var_68= -0x68
    // var_64= -0x64
    // var_60= -0x60
    // var_5C= -0x5C
    // var_58= -0x58
    // var_54= -0x54
    // var_50= -0x50
    // var_4C= -0x4C
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_780E4
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_780e4(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x803F000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_E= -0xE
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, #1
    // R4 = 1;
    // LDR     R0, =0x803F000
    // 内存加载操作
    // BL      sub_7D00C
    // 调用函数: sub_7D00C();
    // LDR     R0, =0x2000374B
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_78100
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // MOV     R1, SP
    // STRH    R0, [R1,#0x10+var_10]
    // 内存存储操作
    // B       loc_78106
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78198
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_78198(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200035F4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003742;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200035EC;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x14;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200035EC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20003742
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_781C0
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20003742
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R2, #0x14
    // R2 = 0x14;
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MOVS    R0, #4
    // R0 = 4;
    // BL      sub_7CF1A
    // 调用函数: sub_7CF1A();
    // MOVS    R0, #0x3E8
    // R0 = 0x3E8;
    // LDR     R1, =0x200035F4
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_781E4
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_781e4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x84;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20001FF8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R5, R0
    // LDR     R0, =0x20001FF8
    // 内存加载操作
    // LDRB    R0, [R0,#1]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_78244
    // 条件跳转
    // LDR     R0, =0x20001FF8
    // 内存加载操作
    // LDR     R1, =0x20001FF8
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // MOVS    R2, #0x84
    // R2 = 0x84;
    // MULS    R1, R2
    // ADDS    R6, R0, R1
    // 算术运算
    // ADDS    R6, R6, #4
    // 算术运算
    // CMP     R6, #0
    // 比较操作
    // BNE     loc_78206
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_78246
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7824C
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_7824c(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // BL      sub_77416
    // 调用函数: sub_77416();
    // CMP     R0, #7
    // 比较操作
    // BEQ     loc_7825C
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_78288
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78298
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_78298(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R7, R0
    // MOVS    R4, R1
    // CMP     R4, #0x80
    // 比较操作
    // BCC     loc_782A6
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_782F4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7832C
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_7832c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xA0;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x9C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_A8= -0xA8
    // var_A4= -0xA4
    // var_A0= -0xA0
    // var_9C= -0x9C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7877C
 * @note 指令数: 23, 标签数: 0
 */
void precise_func_7877c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200035F4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003742;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200035EC;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200035FC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003742
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200035EC
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200035FC
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x200035F4
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200035FC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200035EC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x3E8
    // R0 = 0x3E8;
    // LDR     R1, =0x200035F4
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_787E8
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_787e8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003742;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200035F4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x200035F4
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_78806
    // 条件跳转
    // LDR     R0, =0x20003742
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_78806
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003742
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #4
    // R0 = 4;
    // BL      sub_7CF92
    // 调用函数: sub_7CF92();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_788F0
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_788f0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003741;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20003741
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78930
 * @note 指令数: 10, 标签数: 0
 */
uint32_t precise_func_78930(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R12, R0
    // STRB    R0, [R1]
    // 内存存储操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R1,#1]
    // 内存存储操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R1,#2]
    // 内存存储操作
    // LSRS    R0, R0, #8
    // STRB    R0, [R1,#3]
    // 内存存储操作
    // MOV     R0, R12
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78944
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_78944(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // var_C= -0xC
    // varg_r2= -8
    // varg_r3= -4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7897C
 * @note 指令数: 3, 标签数: 1
 */
void precise_func_7897c(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       loc_78982
    // 无条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // ADDS    R1, R1, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7899E
 * @note 指令数: 1, 标签数: 0
 */
void precise_func_7899e(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // B       sub_7899E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_789A0
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_789a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000368C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036EC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200035B4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_7E000
    // 调用函数: sub_7E000();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200036EC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000368C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200035B4
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200035B4
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_789C2
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_789c2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003680;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20003680
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_789C8
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_789c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003688;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20003688
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_789CE
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_789ce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003674;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_789E0
    // 条件跳转
    // LDR     R0, =0x20003674
    // 内存加载操作
    // MOVS    R6, R0
    // B       loc_789E4
    // 无条件跳转
}

