@echo off
echo 检查make环境...
echo.

echo 检查make工具:
where make 2>nul
if %errorlevel% equ 0 (
    echo ✅ 找到make
    make --version | findstr "GNU Make"
) else (
    echo ❌ 未找到make
)

echo.
echo 检查mingw32-make:
where mingw32-make 2>nul
if %errorlevel% equ 0 (
    echo ✅ 找到mingw32-make
    mingw32-make --version | findstr "GNU Make"
) else (
    echo ❌ 未找到mingw32-make
)

echo.
echo 检查GCC:
where gcc 2>nul
if %errorlevel% equ 0 (
    echo ✅ 找到GCC
    gcc --version | findstr "gcc"
) else (
    echo ❌ 未找到GCC
)

echo.
echo 检查ARM编译器:
where arm-none-eabi-gcc 2>nul
if %errorlevel% equ 0 (
    echo ✅ 找到ARM编译器
    arm-none-eabi-gcc --version | findstr "gcc"
) else (
    echo ❌ 未找到ARM编译器
)

echo.
echo 建议安装MSYS2: https://www.msys2.org/
pause
