#ifndef AT32F403AVG_FIRMWARE_CONVERSION_H
#define AT32F403AVG_FIRMWARE_CONVERSION_H

#include <stdint.h>
#include <stdbool.h>

// 系统寄存器地址定义
#define NVIC_ISER_BASE          0xE000E400  // 中断使能寄存器基地址
#define NVIC_ICER_BASE          0xE000E180  // 中断禁用寄存器基地址
#define SCB_SHPR_BASE           0xE000ED18  // 系统处理器优先级寄存器
#define SYSTICK_LOAD            0xE000E014  // SysTick重载值寄存器
#define SYSTICK_VAL             0xE000E018  // SysTick当前值寄存器
#define SYSTICK_CTRL            0xE000E010  // SysTick控制寄存器
#define SCB_VTOR                0xE000ED08  // 向量表偏移寄存器
#define SCB_CPACR               0xE000ED88  // 协处理器访问控制寄存器

// GPIO寄存器地址定义
#define GPIOA_CRL               0x40010800  // GPIOA配置寄存器低位
#define GPIOA_CRH               0x40010804  // GPIOA配置寄存器高位
#define GPIOA_IDR               0x40010808  // GPIOA输入数据寄存器
#define GPIOA_ODR               0x4001080C  // GPIOA输出数据寄存器
#define GPIOA_BSRR              0x40010810  // GPIOA位设置/复位寄存器
#define GPIOA_BRR               0x40010814  // GPIOA位复位寄存器

#define GPIOB_CRL               0x40010C00  // GPIOB配置寄存器低位
#define GPIOB_CRH               0x40010C04  // GPIOB配置寄存器高位
#define GPIOB_IDR               0x40010C08  // GPIOB输入数据寄存器
#define GPIOB_ODR               0x40010C0C  // GPIOB输出数据寄存器
#define GPIOB_BSRR              0x40010C10  // GPIOB位设置/复位寄存器
#define GPIOB_BRR               0x40010C14  // GPIOB位复位寄存器

#define GPIOC_CRL               0x40011000  // GPIOC配置寄存器低位
#define GPIOC_CRH               0x40011004  // GPIOC配置寄存器高位
#define GPIOC_IDR               0x40011008  // GPIOC输入数据寄存器
#define GPIOC_ODR               0x4001100C  // GPIOC输出数据寄存器
#define GPIOC_BSRR              0x40011010  // GPIOC位设置/复位寄存器
#define GPIOC_BRR               0x40011014  // GPIOC位复位寄存器

#define GPIOD_CRL               0x40011400  // GPIOD配置寄存器低位
#define GPIOD_CRH               0x40011404  // GPIOD配置寄存器高位
#define GPIOD_IDR               0x40011408  // GPIOD输入数据寄存器
#define GPIOD_ODR               0x4001140C  // GPIOD输出数据寄存器
#define GPIOD_BSRR              0x40011410  // GPIOD位设置/复位寄存器
#define GPIOD_BRR               0x40011414  // GPIOD位复位寄存器

// RCC时钟控制寄存器地址定义
#define RCC_CR                  0x40021000  // 时钟控制寄存器
#define RCC_CFGR                0x40021004  // 时钟配置寄存器
#define RCC_CIR                 0x40021008  // 时钟中断寄存器
#define RCC_APB2RSTR            0x4002100C  // APB2外设复位寄存器
#define RCC_APB1RSTR            0x40021010  // APB1外设复位寄存器
#define RCC_AHBENR              0x40021014  // AHB外设时钟使能寄存器
#define RCC_APB2ENR             0x40021018  // APB2外设时钟使能寄存器
#define RCC_APB1ENR             0x4002101C  // APB1外设时钟使能寄存器
#define RCC_BDCR                0x40021020  // 备份域控制寄存器
#define RCC_CSR                 0x40021024  // 控制/状态寄存器
#define RCC_CFGR2               0x4002102C  // 时钟配置寄存器2
#define RCC_PLL2CFGR            0x40021030  // PLL2配置寄存器

// USART寄存器地址定义
#define USART1_SR               0x40013800  // USART1状态寄存器
#define USART1_DR               0x40013804  // USART1数据寄存器
#define USART1_BRR              0x40013808  // USART1波特率寄存器
#define USART1_CR1              0x4001380C  // USART1控制寄存器1
#define USART1_CR2              0x40013810  // USART1控制寄存器2
#define USART1_CR3              0x40013814  // USART1控制寄存器3

#define USART2_SR               0x40004400  // USART2状态寄存器
#define USART2_DR               0x40004404  // USART2数据寄存器
#define USART2_BRR              0x40004408  // USART2波特率寄存器
#define USART2_CR1              0x4000440C  // USART2控制寄存器1
#define USART2_CR2              0x40004410  // USART2控制寄存器2
#define USART2_CR3              0x40004414  // USART2控制寄存器3

// 内存地址定义
#define SRAM_BASE               0x20000000  // SRAM基地址
#define FLASH_BASE              0x08000000  // Flash基地址
#define APP_BASE                0x08002000  // 应用程序基地址
#define BOOTLOADER_BASE         0x08000000  // Bootloader基地址

// 全局变量地址定义
#define TIMER_COUNTER_100MS     0x2000000A  // 100ms定时器计数器
#define SYSTEM_TICK_COUNTER     0x20000004  // 系统滴答计数器
#define DELAY_COUNTER           0x2000000C  // 延时计数器
#define UART_RX_BUFFER_INDEX    0x20000008  // UART接收缓冲区索引
#define UART_MODE_FLAG          0x20000011  // UART模式标志
#define TEMP_DATA_BYTE          0x20000010  // 临时数据字节
#define CRC_RESULT              0x2000000E  // CRC计算结果

// 缓冲区地址定义
#define UART_RX_BUFFER          0x20000FFC  // UART接收缓冲区
#define COMMAND_BUFFER          0x20001F00  // 命令缓冲区
#define RESPONSE_BUFFER         0x20001C00  // 响应缓冲区

// 常量定义
#define MAGIC_NUMBER            0xAA55AA55  // 魔数常量
#define BUFFER_SIZE             0x0C00      // 缓冲区大小
#define MAX_DELAY_COUNT         0xEA60      // 最大延时计数值

// 命令字符串定义
#define CMD_G0B1                "G0B1"      // G0B1命令
#define CMD_BOOT                "bOoT"      // 启动命令
#define CMD_ECHO                "EcHo"      // 回显命令

// 设备信息字符串
#define DEVICE_NAME             "KXM-16P BL"
#define BUILD_DATE              "Jun  2 2022"
#define BUILD_TIME              "16:16:17"

// 函数声明
void reset_handler(void);
void system_clock_config(void);
void gpio_init(void);
void usart_init(void);
void systick_init(void);
void nvic_config(void);

// 中断处理函数声明
void nmi_handler(void);
void hardfault_handler(void);
void memmanage_handler(void);
void busfault_handler(void);
void usagefault_handler(void);
void svc_handler(void);
void debugmon_handler(void);
void pendsv_handler(void);
void systick_handler(void);

// 应用函数声明
void set_interrupt_priority(int8_t irq_num, uint8_t priority);
uint32_t configure_systick(uint32_t ticks);
void control_led(uint8_t state);
uint16_t calculate_crc16(uint8_t *data, uint16_t length);
void timer_interrupt_handler(void);
uint8_t check_application_valid(void);
void jump_to_application(uint32_t *app_vector);
uint8_t compare_memory(uint8_t *src1, uint8_t *src2, uint32_t length);
uint8_t check_gpio_state(void);
void main_application_loop(void);
void system_initialization(void);
void memory_set(uint8_t *dest, uint32_t length, uint8_t value);
void data_decompression(uint32_t *compressed_data);
void run_constructors(void);
void fpu_enable(void);
void startup_main(void);
void error_handler(uint32_t error_code);

// 默认中断处理函数声明
void default_handler(void);

#endif // AT32F403AVG_FIRMWARE_CONVERSION_H
