// 完整精确转换批次 45 - 100%对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7BB10
 * @note 指令数: 146, 标签数: 20
 * @note 内存引用: 11, 函数调用: 10
 */
void precise_func_7bb10(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003753;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F4;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000374F;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76398(void);
    extern void sub_7A478(void);
    extern void sub_7B7BA(void);
    extern void sub_7A510(void);
    extern void sub_7A960(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7B7BA();
    sub_7A478();
    sub_7A510();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7BC6C
 * @note 指令数: 452, 标签数: 38
 * @note 内存引用: 29, 函数调用: 32
 */
void precise_func_7bc6c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xBF800000;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20003634;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7F6CE(void);
    extern void sub_7A478(void);
    extern void sub_76398(void);
    extern void sub_7A0E0(void);
    extern void sub_7B7BA(void);
    extern void sub_7639E(void);
    extern void sub_7A320(void);
    extern void sub_7F6B0(void);
    extern void sub_762E4(void);
    extern void sub_7A510(void);
    extern void sub_7A960(void);
    extern void sub_7F688(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7B7BA();
    sub_7A960();
    sub_7A478();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C054
 * @note 指令数: 443, 标签数: 37
 * @note 内存引用: 29, 函数调用: 31
 */
void precise_func_7c054(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xBF800000;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20003634;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7F6CE(void);
    extern void sub_7A478(void);
    extern void sub_76398(void);
    extern void sub_7A0E0(void);
    extern void sub_7B7BA(void);
    extern void sub_7639E(void);
    extern void sub_7A320(void);
    extern void sub_7F6B0(void);
    extern void sub_762E4(void);
    extern void sub_7A510(void);
    extern void sub_7A960(void);
    extern void sub_7F688(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7B7BA();
    sub_7A960();
    sub_7A478();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C410
 * @note 指令数: 127, 标签数: 10
 * @note 内存引用: 17, 函数调用: 9
 */
void precise_func_7c410(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003538;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1F4;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20000130;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7B7FE(void);
    extern void sub_76820(void);
    extern void sub_78944(void);
    extern void sub_7A960(void);
    extern void sub_77178(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7B7FE();
    sub_7A960();
    sub_76820();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C550
 * @note 指令数: 270, 标签数: 24
 * @note 内存引用: 26, 函数调用: 21
 */
void precise_func_7c550(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003634;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20000978;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000374F;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20003751;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7A478(void);
    extern void sub_7B7FE(void);
    extern void sub_7B7BA(void);
    extern void sub_7F40E(void);
    extern void sub_76820(void);
    extern void sub_78944(void);
    extern void sub_7A510(void);
    extern void sub_7A960(void);
    extern void sub_77178(void);
    extern void sub_76F94(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7B7BA();
    sub_7A960();
    sub_76F94();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C7B8
 * @note 指令数: 76, 标签数: 8
 * @note 内存引用: 7, 函数调用: 20
 */
void precise_func_7c7b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003752;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003750;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20003754;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x200036B4;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7C550(void);
    extern void sub_7B7FE(void);
    extern void sub_7BC6C(void);
    extern void sub_7649E(void);
    extern void sub_78C4A(void);
    extern void sub_7BB10(void);
    extern void sub_7646C(void);
    extern void sub_7A478(void);
    extern void sub_7C410(void);
    extern void sub_76502(void);
    extern void sub_7656C(void);
    extern void sub_7A4A4(void);
    extern void sub_76534(void);
    extern void sub_77416(void);
    extern void sub_7B9B4(void);
    extern void sub_764D0(void);
    extern void sub_7C054(void);
    extern void sub_7A960(void);
    extern void sub_7B8CC(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_78C4A();
    sub_77416();
    sub_7A960();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C8A0
 * @note 指令数: 133, 标签数: 11
 * @note 内存引用: 17, 函数调用: 2
 */
void precise_func_7c8a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x4001141C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1A;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x40011420;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x17;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7FD1E(void);
    extern void sub_7FA18(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7FD1E();
    sub_7FA18();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C9AE
 * @note 指令数: 228, 标签数: 9
 * @note 内存引用: 25, 函数调用: 8
 */
void precise_func_7c9ae(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021018;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x1000;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x400;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7FE88(void);
    extern void sub_7644C(void);
    extern void sub_76820(void);
    extern void sub_7CCCC(void);
    extern void sub_7E160(void);
    extern void sub_7FE9C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76820();
    sub_7FE88();
    sub_7FE9C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CB84
 * @note 指令数: 9, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_7cb84(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011790;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CBA8
 * @note 指令数: 43, 标签数: 3
 * @note 内存引用: 6, 函数调用: 0
 */
void precise_func_7cba8(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x19;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x50;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CC04
 * @note 指令数: 20, 标签数: 2
 * @note 内存引用: 4, 函数调用: 0
 */
void precise_func_7cc04(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CC2C
 * @note 指令数: 17, 标签数: 0
 * @note 内存引用: 3, 函数调用: 1
 */
void precise_func_7cc2c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7FA18(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7FA18();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CC50
 * @note 指令数: 13, 标签数: 0
 * @note 内存引用: 3, 函数调用: 0
 */
uint32_t precise_func_7cc50(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CC74
 * @note 指令数: 22, 标签数: 3
 * @note 内存引用: 0, 函数调用: 0
 */
void precise_func_7cc74(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CCA0
 * @note 指令数: 8, 标签数: 0
 * @note 内存引用: 0, 函数调用: 1
 */
void precise_func_7cca0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7CC74(void);

    // 汇编逻辑实现

    // 函数调用
    sub_7CC74();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CCB2
 * @note 指令数: 7, 标签数: 0
 * @note 内存引用: 2, 函数调用: 0
 */
uint32_t precise_func_7ccb2(uint32_t param0, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011790;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CCCC
 * @note 指令数: 48, 标签数: 0
 * @note 内存引用: 9, 函数调用: 2
 */
void precise_func_7cccc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFFFFCFFF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFFFFF4FF;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xFFFFF7FF;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7FEF0(void);
    extern void sub_7639E(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7FEF0();
    sub_7639E();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CD48
 * @note 指令数: 26, 标签数: 0
 * @note 内存引用: 5, 函数调用: 5
 */
void precise_func_7cd48(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003488;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200035AC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000366C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x23;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003731;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7CF92(void);
    extern void sub_76820(void);
    extern void sub_7E0DE(void);
    extern void sub_789CE(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76820();
    sub_789CE();
    sub_7E0DE();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CD86
 * @note 指令数: 201, 标签数: 13
 * @note 内存引用: 10, 函数调用: 1
 */
void precise_func_7cd86(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200035AC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000366C;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7E080(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7E080();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CF1A
 * @note 指令数: 45, 标签数: 1
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_7cf1a(uint8_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003731;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000366C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CF74
 * @note 指令数: 15, 标签数: 1
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_7cf74(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003731;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000366C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CF92
 * @note 指令数: 15, 标签数: 1
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_7cf92(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003731;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000366C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CFC0
 * @note 指令数: 35, 标签数: 4
 * @note 内存引用: 1, 函数调用: 4
 */
void precise_func_7cfc0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7FFDC(void);
    extern void sub_7FF14(void);
    extern void sub_7FFB8(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_7FFB8();
    sub_7FF14();
    sub_7FFDC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7D00C
 * @note 指令数: 25, 标签数: 1
 * @note 内存引用: 4, 函数调用: 3
 */
void precise_func_7d00c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022010;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x4002200C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7FFEC(void);
    extern void sub_7FFDC(void);
    extern void sub_7FFB8(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7FFB8();
    sub_7FFEC();
    sub_7FFDC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7D044
 * @note 指令数: 19, 标签数: 2
 * @note 内存引用: 0, 函数调用: 4
 */
void precise_func_7d044(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7FFDC(void);
    extern void sub_7FF14(void);
    extern void sub_7FFB8(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 函数调用
    sub_7FFB8();
    sub_7FF14();
    sub_7FFDC();
}

