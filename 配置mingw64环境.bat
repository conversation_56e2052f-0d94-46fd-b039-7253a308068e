@echo off
echo ========================================
echo 配置MinGW64环境变量
echo ========================================
echo.

set MINGW_PATH=C:\mingw64\bin

echo 检查MinGW64安装...
if exist "%MINGW_PATH%\gcc.exe" (
    echo ✅ 找到GCC: %MINGW_PATH%\gcc.exe
) else (
    echo ❌ 未找到GCC
    goto :error
)

if exist "%MINGW_PATH%\mingw32-make.exe" (
    echo ✅ 找到make: %MINGW_PATH%\mingw32-make.exe
) else (
    echo ❌ 未找到make
    goto :error
)

echo.
echo 当前PATH环境变量:
echo %PATH% | findstr /i "mingw64"
if %errorlevel% equ 0 (
    echo ✅ MinGW64已在PATH中
) else (
    echo ⚠️  MinGW64不在PATH中
    echo.
    echo 临时添加到当前会话的PATH...
    set PATH=%MINGW_PATH%;%PATH%
    echo ✅ 已临时添加MinGW64到PATH
)

echo.
echo ========================================
echo 测试编译工具
echo ========================================
echo.

echo 测试GCC版本:
"%MINGW_PATH%\gcc.exe" --version | findstr "gcc"

echo.
echo 测试make版本:
"%MINGW_PATH%\mingw32-make.exe" --version | findstr "GNU Make"

echo.
echo ========================================
echo 为AT32F403AVG项目配置make
echo ========================================
echo.

echo 更新Makefile以使用mingw32-make...

REM 检查是否存在Makefile
if exist "Makefile" (
    echo ✅ 找到Makefile
    
    REM 创建一个使用mingw32-make的包装脚本
    echo @echo off > make.bat
    echo echo 使用MinGW64编译AT32F403AVG项目... >> make.bat
    echo echo. >> make.bat
    echo "%MINGW_PATH%\mingw32-make.exe" %%* >> make.bat
    
    echo ✅ 创建了make.bat包装脚本
    echo.
    echo 现在可以使用以下命令编译项目:
    echo   make.bat all
    echo   make.bat clean
    echo   make.bat help
    
) else (
    echo ❌ 未找到Makefile
)

echo.
echo ========================================
echo 永久配置环境变量 (可选)
echo ========================================
echo.
echo 要永久添加MinGW64到系统PATH，请执行以下步骤:
echo.
echo 1. 按Win+R，输入sysdm.cpl，按回车
echo 2. 点击"环境变量"按钮
echo 3. 在"系统变量"中找到"Path"，点击"编辑"
echo 4. 点击"新建"，添加: %MINGW_PATH%
echo 5. 点击"确定"保存所有更改
echo 6. 重新打开命令提示符
echo.
echo 或者运行以下PowerShell命令 (需要管理员权限):
echo [Environment]::SetEnvironmentVariable("Path", $env:Path + ";%MINGW_PATH%", "Machine")

echo.
echo ========================================
echo 测试编译项目
echo ========================================
echo.

if exist "make.bat" (
    echo 测试编译项目...
    echo 运行: make.bat help
    echo.
    call make.bat help
) else (
    echo 直接使用mingw32-make测试...
    "%MINGW_PATH%\mingw32-make.exe" help 2>nul || echo 请确保Makefile存在
)

goto :end

:error
echo.
echo ❌ MinGW64配置失败
echo 请检查C:\mingw64目录是否正确安装
goto :end

:end
echo.
echo 配置完成！
pause
