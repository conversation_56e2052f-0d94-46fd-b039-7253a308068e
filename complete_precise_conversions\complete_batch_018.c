// 完整精确转换批次 18 - 100%对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24C84
 * @note 指令数: 12, 标签数: 0
 * @note 内存引用: 5, 函数调用: 1
 */
void precise_func_24c84(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000020C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8016924;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016838;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_255D4(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_255D4();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24CB4
 * @note 指令数: 12, 标签数: 1
 * @note 内存引用: 0, 函数调用: 0
 */
uint16_t precise_func_24cb4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint16_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24CCC
 * @note 指令数: 46, 标签数: 4
 * @note 内存引用: 3, 函数调用: 3
 */
void precise_func_24ccc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007EE8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80165A0;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_25604(void);
    extern void sub_184CE(void);
    extern void sub_183AC(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_25604();
    sub_183AC();
    sub_184CE();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24D38
 * @note 指令数: 18, 标签数: 2
 * @note 内存引用: 3, 函数调用: 2
 */
void precise_func_24d38(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007EE8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000263;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_24FCC(void);
    extern void sub_24D62(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_24D62();
    sub_24FCC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24D62
 * @note 指令数: 224, 标签数: 14
 * @note 内存引用: 9, 函数调用: 15
 */
void precise_func_24d62(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x42;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007EE8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xF;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x40;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_256AA(void);
    extern void sub_25986(void);
    extern void sub_25780(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_256AA();
    sub_25986();
    sub_25986();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24F88
 * @note 指令数: 29, 标签数: 2
 * @note 内存引用: 3, 函数调用: 4
 */
void precise_func_24f88(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016778;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007EF0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x801677C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_184CE(void);
    extern void sub_17DF4(void);
    extern void sub_25A14(void);
    extern void sub_183AC(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_17DF4();
    sub_183AC();
    sub_184CE();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24FCC
 * @note 指令数: 32, 标签数: 3
 * @note 内存引用: 1, 函数调用: 1
 */
void precise_func_24fcc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007EF0;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_25014(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_25014();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25014
 * @note 指令数: 53, 标签数: 2
 * @note 内存引用: 6, 函数调用: 3
 */
void precise_func_25014(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007EF0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x8016778;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_25B28(void);
    extern void sub_184CE(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_184CE();
    sub_25B28();
    sub_184CE();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_250A8
 * @note 指令数: 373, 标签数: 56
 * @note 内存引用: 37, 函数调用: 8
 */
void precise_func_250a8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x58;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x70;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x6F;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2D;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x2E;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x69;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x75;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_1A2BC(void);
    extern void sub_2542E(void);
    extern void sub_25BF4(void);
    extern void sub_2547A(void);
    extern void sub_25BDC(void);
    extern void sub_25548(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_25BDC();
    sub_1A2BC();
    sub_25BF4();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2542E
 * @note 指令数: 34, 标签数: 4
 * @note 内存引用: 6, 函数调用: 0
 */
uint32_t precise_func_2542e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x6C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x68;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x62;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x7A;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x74;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2547A
 * @note 指令数: 93, 标签数: 7
 * @note 内存引用: 18, 函数调用: 0
 */
uint16_t precise_func_2547a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x69;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x3A;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xFFEF;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x24;

    // 局部变量
    uint16_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25548
 * @note 指令数: 21, 标签数: 3
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_25548(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25580
 * @note 指令数: 2, 标签数: 0
 * @note 内存引用: 0, 函数调用: 0
 */
void precise_func_25580(uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25588
 * @note 指令数: 12, 标签数: 1
 * @note 内存引用: 1, 函数调用: 0
 */
void precise_func_25588(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_255A8
 * @note 指令数: 22, 标签数: 4
 * @note 内存引用: 0, 函数调用: 0
 */
uint32_t precise_func_255a8(uint32_t param0, uint32_t param3)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_255D4
 * @note 指令数: 19, 标签数: 2
 * @note 内存引用: 1, 函数调用: 2
 */
void precise_func_255d4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_255A8(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_255A8();
    sub_255A8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25604
 * @note 指令数: 49, 标签数: 1
 * @note 内存引用: 8, 函数调用: 8
 */
void precise_func_25604(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x180002;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x180003;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_256AA(void);
    extern void sub_259BE(void);
    extern void sub_25780(void);
    extern void sub_17DF4(void);
    extern void sub_183AC(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_17DF4();
    sub_17DF4();
    sub_183AC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2569A
 * @note 指令数: 8, 标签数: 2
 * @note 内存引用: 0, 函数调用: 0
 */
uint32_t precise_func_2569a(uint8_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_256AA
 * @note 指令数: 71, 标签数: 0
 * @note 内存引用: 5, 函数调用: 10
 */
void precise_func_256aa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80165B8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_184CE(void);
    extern void sub_183AC(void);
    extern void sub_2569A(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_183AC();
    sub_184CE();
    sub_184CE();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25774
 * @note 指令数: 5, 标签数: 0
 * @note 内存引用: 0, 函数调用: 1
 */
void precise_func_25774(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_256AA(void);

    // 汇编逻辑实现

    // 函数调用
    sub_256AA();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25780
 * @note 指令数: 59, 标签数: 0
 * @note 内存引用: 5, 函数调用: 8
 */
void precise_func_25780(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80165B8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_184CE(void);
    extern void sub_183AC(void);
    extern void sub_2569A(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_183AC();
    sub_184CE();
    sub_2569A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2581A
 * @note 指令数: 55, 标签数: 2
 * @note 内存引用: 4, 函数调用: 7
 */
void precise_func_2581a(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016148;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_184CE(void);
    extern void sub_2569A(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_184CE();
    sub_184CE();
    sub_2569A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_258A6
 * @note 指令数: 58, 标签数: 0
 * @note 内存引用: 6, 函数调用: 7
 */
void precise_func_258a6(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80165B8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x80165D0;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_18496(void);
    extern void sub_184CE(void);
    extern void sub_183AC(void);
    extern void sub_2569A(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_183AC();
    sub_184CE();
    sub_2569A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2593C
 * @note 指令数: 31, 标签数: 5
 * @note 内存引用: 1, 函数调用: 4
 */
void precise_func_2593c(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_258A6(void);
    extern void sub_2581A(void);
    extern void sub_193F6(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 函数调用
    sub_193F6();
    sub_258A6();
    sub_2581A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25986
 * @note 指令数: 24, 标签数: 5
 * @note 内存引用: 1, 函数调用: 3
 */
void precise_func_25986(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_2581A(void);
    extern void sub_258A6(void);
    extern void sub_193F6(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 函数调用
    sub_193F6();
    sub_2581A();
    sub_258A6();
}

