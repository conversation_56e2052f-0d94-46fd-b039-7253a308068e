// 精确转换批次 3 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18362
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_18362(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0]
    // 内存加载操作
    // BFI.W   R2, R1, #4, #3
    // STR     R2, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1836E
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_1836e(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R2, R2
    // 数据扩展操作
    // CMP     R2, #1
    // 比较操作
    // BNE     loc_1837C
    // 条件跳转
    // LDR     R3, [R0,#0xC]
    // 内存加载操作
    // ORRS    R3, R1
    // STR     R3, [R0,#0xC]
    // 内存存储操作
    // B       locret_18388
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1838A
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1838a(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R3, [R2,#0x10]
    // 内存加载操作
    // TST     R3, R1
    // 比较操作
    // BEQ     loc_1839A
    // 条件跳转
    // MOVS    R3, #1
    // R3 = 1;
    // MOVS    R0, R3
    // B       loc_1839E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_183A2
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_183a2(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, [R0,#0x10]
    // 内存加载操作
    // BICS    R2, R1
    // STR     R2, [R0,#0x10]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_183AC
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_183ac(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // LDR     R5, [R1]
    // 内存加载操作
    // MOVS    R4, R5
    // LDRB    R5, [R1,#6]
    // 内存加载操作
    // CMP     R5, #3
    // 比较操作
    // BNE     loc_183BE
    // 条件跳转
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R3, R5
    // B       loc_183FC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1847E
 * @note 指令数: 11, 标签数: 0
 */
uint32_t precise_func_1847e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFF;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVW    R1, #0xFFFF
    // R1 = 0xFFFF;
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#6]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#4]
    // 内存存储操作
    // MOVS    R1, #4
    // R1 = 4;
    // STRB    R1, [R0,#5]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // STRB    R1, [R0,#7]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18496
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_18496(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #0
    // R0 = 0;
    // UXTH    R1, R1
    // 数据扩展操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R3, [R2,#8]
    // 内存加载操作
    // ANDS    R3, R1
    // CMP     R1, R3
    // 比较操作
    // BEQ     loc_184AC
    // 条件跳转
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R0, R3
    // B       loc_184B0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_184B4
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_184b4(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R3, [R2,#0xC]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // TST     R3, R1
    // 比较操作
    // BEQ     loc_184C6
    // 条件跳转
    // MOVS    R3, #1
    // R3 = 1;
    // MOVS    R0, R3
    // B       loc_184CA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_184CE
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_184ce(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R2, R2
    // 数据扩展操作
    // CMP     R2, #0
    // 比较操作
    // BEQ     loc_184DA
    // 条件跳转
    // UXTH    R1, R1
    // 数据扩展操作
    // STR     R1, [R0,#0x10]
    // 内存存储操作
    // B       locret_184DE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_184E0
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_184e0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFFFFFFFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40010000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // LDR     R7, =0x40010000
    // 内存加载操作
    // ADDS.W  R7, R7, R0,LSR#24
    // 算术运算
    // MOVS    R2, R7
    // LSRS    R7, R0, #0x10
    // MOVS    R4, R7
    // LSRS    R7, R0, #8
    // MOVS    R5, R7
    // MOVS    R7, R0
    // MOVS    R6, R7
    // MOVS.W  R7, #0xFFFFFFFF
    // RSBS.W  R12, R5, #0x20 ; ' '
    // SUBS.W  R12, R12, R4
    // LSLS.W  R7, R7, R12
    // MOVS    R3, R7
    // RSBS.W  R7, R5, #0x20 ; ' '
    // SUBS    R7, R7, R4
    // 算术运算
    // LSRS    R3, R7
    // LSRS    R3, R4
    // LSLS    R3, R4
    // LDR     R7, [R2]
    // 内存加载操作
    // BICS    R7, R3
    // STR     R7, [R2]
    // 内存存储操作
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_1852E
    // 条件跳转
    // LDR     R7, [R2]
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // LSLS.W  R12, R6, R4
    // ORRS.W  R7, R12, R7
    // STR     R7, [R2]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18538
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_18538(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_194DA
    // 调用函数: sub_194DA();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18542
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_18542(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x4000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4100;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8016894;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // CMP     R4, #8
    // 比较操作
    // BCS     locret_18576
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
    // LDR     R0, =0x8016894
    // 内存加载操作
    // LDRB    R0, [R4,R0]
    // 内存加载操作
    // ORRS.W  R0, R0, #0x4100
    // MOVS    R6, R0
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R7
    // BL      sub_19644
    // 调用函数: sub_19644();
    // ORRS.W  R0, R5, #0x4000
    // MOVS    R6, R0
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R7
    // BL      sub_19644
    // 调用函数: sub_19644();
    // B       locret_18576
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1857C
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_1857c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_194DA
    // 调用函数: sub_194DA();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18586
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_18586(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A00;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x801689C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
    // LDR     R0, =0x801689C
    // 内存加载操作
    // LDRB    R0, [R4,R0]
    // 内存加载操作
    // ORRS    R0, R5
    // ORRS.W  R0, R0, #0x2A00
    // MOVS    R6, R0
    // MOVS    R1, R6
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R7
    // BL      sub_19644
    // 调用函数: sub_19644();
    // POP     {R0,R4-R7,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_185AC
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_185ac(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x21;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R4,LR}
    // 栈操作
    // BL      sub_196A8
    // 调用函数: sub_196A8();
    // MOVS    R1, #0x21 ; '!'
    // R1 = 0x21;
    // STR     R1, [R0]
    // 内存存储操作
    // POP     {R0-R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_185B8
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_185b8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // PUSH    {R2-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // LDR.W   R0, [R6,#2]
    // 内存加载操作
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRH.W  R0, [SP,#0x18+var_14]
    // 内存存储操作
    // LDRB    R0, [R6]
    // 内存加载操作
    // LSLS    R0, R0, #0x1A
    // BPL     loc_185DE
    // LDRH.W  R0, [SP,#0x18+var_14]
    // 内存加载操作
    // ORRS.W  R0, R0, #1
    // STRH.W  R0, [SP,#0x18+var_14]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_186FE
 * @note 指令数: 6, 标签数: 0
 */
uint32_t precise_func_186fe(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // ASRS    R0, R0, #8
    // LDR.W   R2, =0x200000CC
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R2, R3, R0, R2
    // STR     R1, [R2,#0x20]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1870E
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1870e(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS.W  R8, #0xFFFFFFFF
    // MOV     R9, R6
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_187EA
 * @note 指令数: 25, 标签数: 0
 */
void precise_func_187ea(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R2, R5
    // UXTB    R2, R2
    // 数据扩展操作
    // LDR.W   R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // ANDS.W  R1, R0, #1
    // MOVS    R0, R4
    // BL      sub_1A100
    // 调用函数: sub_1A100();
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_1882E
    // 条件跳转
    // LDR.W   R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // ORRS.W  R0, R0, #1
    // LDR.W   R1, =0x200000CC
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R1, R2, R4, R1
    // STRB    R0, [R1,#0x10]
    // 内存存储操作
    // B       locret_1884A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1884C
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1884c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // ADDS    R0, #0xC
    // 算术运算
    // BL      sub_1A266
    // 调用函数: sub_1A266();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1886A
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_1887A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1887C
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1887c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_1A22E
    // 调用函数: sub_1A22E();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18892
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_18892(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_187EA
    // 调用函数: sub_187EA();
    // MOVS    R1, R5
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R0, R4
    // BL      sub_1A01A
    // 调用函数: sub_1A01A();
    // MOVS    R0, #0
    // R0 = 0;
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_188AE
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_188ae(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x12;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // LSLS    R0, R0, #0x12
    // BPL     loc_18928
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_189E2
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_189e2(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // arg_0=  0
    // PUSH.W  {R3-R11,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOV     R11, R2
    // MOVS    R6, R3
    // LDRSH.W R7, [SP,#0x28+arg_0]
    // LDR     R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R8, R1, R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // STRB.W  R0, [SP,#0x28+var_28]
    // CMN.W   R11, #1
    // BNE     loc_18A0E
    // 条件跳转
    // MOVS    R0, R5
    // BL      sub_1A2BC
    // 调用函数: sub_1A2BC();
    // MOV     R11, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18AF6
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_18af6(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
    // PUSH.W  {R3-R11,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // LDRSH.W R8, [SP,#0x28+arg_0]
    // LDR     R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R11, R1, R4, R0
    // LDR.W   R0, [R11,#0xC]
    // 内存加载操作
    // BL      sub_1A29C
    // 调用函数: sub_1A29C();
    // MOV     R9, R0
    // CMN.W   R6, #1
    // BNE     loc_18B24
    // 条件跳转
    // SXTH.W  R9, R9
    // MOV     R6, R9
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18BA0
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_18ba0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R1, R5
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x200000CC
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_1A1FA
    // 调用函数: sub_1A1FA();
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18BBA
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_18bba(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // CMP     R6, #0
    // 比较操作
    // BNE     loc_18BCC
    // 条件跳转
    // MOVS.W  R0, #0xFFFFFFFF
    // B       locret_18BDA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18BDC
 * @note 指令数: 29, 标签数: 0
 */
void precise_func_18bdc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDRH    R0, [R0,#8]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_18C24
    // MOVS    R0, R4
    // BL      sub_1884C
    // 调用函数: sub_1884C();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_18C24
    // 条件跳转
    // LDR     R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_18C24
    // MOVS    R0, R4
    // BL      sub_1A12C
    // 调用函数: sub_1A12C();
    // LDR     R0, =0x200000CC
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDRB    R0, [R0,#0x10]
    // 内存加载操作
    // ANDS.W  R0, R0, #0xFE
    // LDR     R1, =0x200000CC
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R1, R2, R4, R1
    // STRB    R0, [R1,#0x10]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18C26
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_18c26(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // ASRS    R4, R4, #8
    // MOVS    R1, R5
    // MOVS    R0, R4
    // BL      sub_1A182
    // 调用函数: sub_1A182();
    // POP     {R0,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18C54
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_18c54(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x180005;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x180002;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // BL      sub_18CE8
    // 调用函数: sub_18CE8();
    // MOVS    R5, R0
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180005
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18C9E
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_18c9e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016588;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_18CBA
    // 条件跳转
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016588
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016588
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // B       locret_18CCA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18CCC
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_18ccc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016588;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x8016588
    // 内存加载操作
    // LDR     R1, [R0,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016588
    // 内存加载操作
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_18496
    // 调用函数: sub_18496();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_18CE4
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_18CE6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18CE8
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_18ce8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x180003;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016100;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8016104;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R7, #0x80
    // R7 = 0x80;
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =dword_180004
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180005
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // LDR     R1, =0x8016104
    // 内存加载操作
    // LDR     R0, =0x8016100
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // LDR     R1, =0x8016110
    // 内存加载操作
    // LDR     R0, =0x8016100
    // 内存加载操作
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18E6C
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_18e6c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801676C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016770;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x180002;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // LDR     R1, =0x8016770
    // 内存加载操作
    // LDR     R0, =0x801676C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18EC8
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_18ec8(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18ECC
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_18ecc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOV.W   R2, #0xFFFFFFFF
    // FMRRD   R0, R1, D0
    // BL      sub_1A2F4
    // 调用函数: sub_1A2F4();
    // FMDRR   D0, R0, R1
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18EE0
 * @note 指令数: 14, 标签数: 0
 */
uint32_t precise_func_18ee0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x400;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LSRS    R0, R0, #0x15
    // ORR.W   R0, R0, R1,LSL#11
    // LSLS    R1, R1, #1
    // BCS.W   loc_18F06
    // ORR.W   R0, R0, #0x80000000
    // LSRS    R1, R1, #0x15
    // SUB.W   R1, R1, #0x400
    // ADDS    R1, R1, #1
    // 算术运算
    // BMI     loc_18F06
    // RSBS.W  R1, R1, #0x1F
    // ITE PL
    // LSRPL   R0, R1
    // ASRMI   R0, R0, #0x1F
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18F0C
 * @note 指令数: 9, 标签数: 1
 */
void precise_func_18f0c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // CBZ     R2, locret_18F68
    // LSLS    R3, R1, #0x1E
    // BEQ     loc_18F24
    // 条件跳转
    // SUBS    R2, R2, #1
    // 算术运算
    // LDRB.W  R3, [R1],#1
    // STRB.W  R3, [R0],#1
    // BEQ.W   locret_18F68
    // LSLS    R3, R1, #0x1E
    // BNE     loc_18F12
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18F84
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_18f84(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // BL      sub_1A5DC
    // 调用函数: sub_1A5DC();
    // MOV     R8, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_18FE2
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_18fe2(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015EF0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // ANDS.W  R5, R4, #0xF
    // LDR     R0, =0x8015EF0
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MLA.W   R0, R1, R5, R0
    // LDR     R0, [R0,#0x1C]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_19008
    // 条件跳转
    // ASRS    R0, R4, #8
    // LDR     R1, =0x8015EF0
    // 内存加载操作
    // MOVS    R2, #0x30 ; '0'
    // R2 = 0x30;
    // MLA.W   R1, R2, R5, R1
    // LDR     R1, [R1,#0x1C]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
    // B       locret_1900C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1900E
 * @note 指令数: 28, 标签数: 0
 */
void precise_func_1900e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015EF0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // arg_0=  0
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // LDRSH.W R8, [SP,#0x20+arg_0]
    // ANDS.W  R9, R4, #0xF
    // LDR     R0, =0x8015EF0
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MLA.W   R0, R1, R9, R0
    // LDR     R0, [R0,#8]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_19052
    // 条件跳转
    // MOV     R0, R8
    // SXTH    R0, R0
    // 数据扩展操作
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, R7
    // MOVS    R2, R6
    // MOVS    R1, R5
    // ASRS    R0, R4, #8
    // LDR.W   R12, =0x8015EF0
    // 内存加载操作
    // MOVS.W  LR, #0x30 ; '0'
    // MLA.W   R12, LR, R9, R12
    // LDR.W   R12, [R12,#8]
    // 内存加载操作
    // BLX     R12
    // 调用函数: R12();
    // B       locret_19056
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1905A
 * @note 指令数: 28, 标签数: 0
 */
void precise_func_1905a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015EF0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // arg_0=  0
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // LDRSH.W R8, [SP,#0x20+arg_0]
    // ANDS.W  R9, R4, #0xF
    // LDR     R0, =0x8015EF0
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MLA.W   R0, R1, R9, R0
    // LDR     R0, [R0,#4]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1909E
    // 条件跳转
    // MOV     R0, R8
    // SXTH    R0, R0
    // 数据扩展操作
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOVS    R3, R7
    // MOVS    R2, R6
    // MOVS    R1, R5
    // ASRS    R0, R4, #8
    // LDR.W   R12, =0x8015EF0
    // 内存加载操作
    // MOVS.W  LR, #0x30 ; '0'
    // MLA.W   R12, LR, R9, R12
    // LDR.W   R12, [R12,#4]
    // 内存加载操作
    // BLX     R12
    // 调用函数: R12();
    // B       locret_190A2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_190A6
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_190a6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015EF0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // ANDS.W  R6, R4, #0xF
    // LDR     R0, =0x8015EF0
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MLA.W   R0, R1, R6, R0
    // LDR     R0, [R0,#0x14]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_190D0
    // 条件跳转
    // MOVS    R1, R5
    // ASRS    R0, R4, #8
    // LDR     R2, =0x8015EF0
    // 内存加载操作
    // MOVS    R3, #0x30 ; '0'
    // R3 = 0x30;
    // MLA.W   R2, R3, R6, R2
    // LDR     R2, [R2,#0x14]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
    // B       locret_190D4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_190D6
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_190d6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015EF0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // ANDS.W  R5, R4, #0xF
    // LDR     R0, =0x8015EF0
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MLA.W   R0, R1, R5, R0
    // LDR     R0, [R0,#0x24]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_190FE
    // 条件跳转
    // ASRS    R0, R4, #8
    // LDR     R1, =0x8015EF0
    // 内存加载操作
    // MOVS    R2, #0x30 ; '0'
    // R2 = 0x30;
    // MLA.W   R1, R2, R5, R1
    // LDR     R1, [R1,#0x24]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
    // SXTH    R0, R0
    // 数据扩展操作
    // B       locret_19102
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19104
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_19104(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015EF0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // ANDS.W  R5, R4, #0xF
    // LDR     R0, =0x8015EF0
    // 内存加载操作
    // MOVS    R1, #0x30 ; '0'
    // R1 = 0x30;
    // MLA.W   R0, R1, R5, R0
    // LDR     R0, [R0,#0x2C]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_19128
    // 条件跳转
    // ASRS    R0, R4, #8
    // LDR     R1, =0x8015EF0
    // 内存加载操作
    // MOVS    R2, #0x30 ; '0'
    // R2 = 0x30;
    // MLA.W   R1, R2, R5, R1
    // LDR     R1, [R1,#0x2C]
    // 内存加载操作
    // BLX     R1
    // 调用函数: R1();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19130
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_19130(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000804C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007958;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007F28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000815A;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x23;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R0, #5
    // R0 = 5;
    // LDR     R1, =0x2000815A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R4, #0x23 ; '#'
    // R4 = 0x23;
    // MOVS    R5, #0
    // R5 = 0;
    // LDR     R6, =0x20007958
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R4
    // MOVS    R0, R6
    // BL      sub_17CB8
    // 调用函数: sub_17CB8();
    // LDR     R0, =0x20007958
    // 内存加载操作
    // LDR     R1, =0x2000804C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007F28
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007F28
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // BL      sub_1A61E
    // 调用函数: sub_1A61E();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19162
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_19162(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007F28;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // LDR     R0, =0x20007F28
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xA
    // 比较操作
    // BLT.W   locret_192CE
    // CPSID   I
    // LDR     R0, =0x20007F28
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // SUBS    R0, #0xA
    // 算术运算
    // LDR     R1, =0x20007F28
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // CPSIE   I
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
    // MOVS    R0, #1
    // R0 = 1;
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_192D0
 * @note 指令数: 32, 标签数: 0
 */
void precise_func_192d0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000804C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000815A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // LDR     R3, =0x2000815A
    // 内存加载操作
    // LDRB    R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R3
    // 比较操作
    // BCS     loc_1931A
    // MOVS    R3, #3
    // R3 = 3;
    // LDR     R4, =0x2000804C
    // 内存加载操作
    // LDR     R4, [R4]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R5, #7
    // R5 = 7;
    // MLA.W   R4, R5, R0, R4
    // STRB    R3, [R4,#4]
    // 内存存储操作
    // LDR     R3, =0x2000804C
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #7
    // R4 = 7;
    // MLA.W   R3, R4, R0, R3
    // STRH    R1, [R3]
    // 内存存储操作
    // LDR     R3, =0x2000804C
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #7
    // R4 = 7;
    // MLA.W   R3, R4, R0, R3
    // STRH    R2, [R3,#2]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // LDR     R4, =0x2000804C
    // 内存加载操作
    // LDR     R4, [R4]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R5, #7
    // R5 = 7;
    // MLA.W   R4, R5, R0, R4
    // STRH.W  R3, [R4,#5]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1931E
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1931e(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000804C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000815A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x2000815A
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R1
    // 比较操作
    // BCS     locret_19338
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R2, =0x2000804C
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #7
    // R3 = 7;
    // MLA.W   R2, R3, R0, R2
    // STRB    R1, [R2,#4]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1933A
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1933a(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000804C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000815A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x2000815A
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R1
    // 比较操作
    // BCS     locret_19354
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x2000804C
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #7
    // R3 = 7;
    // MLA.W   R2, R3, R0, R2
    // STRB    R1, [R2,#4]
    // 内存存储操作
}

