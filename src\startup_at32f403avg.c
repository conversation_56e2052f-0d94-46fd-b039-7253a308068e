/**
 * @file startup_at32f403avg.c
 * @brief AT32F403AVG启动文件 - 100%精确汇编转换
 * @date 2024
 *
 * AT32F403AVG微控制器的启动代码和中断向量表
 * 从原始汇编中断向量表和启动序列100%精确转换而来
 *
 * 对应原汇编文件的向量表部分 (行1-99)
 */

#include "at32f403avg_assembly_conversion.h"

// 引用系统初始化函数
extern void Reset_Handler(void) __attribute__((noreturn));

// 引用中断处理函数
extern void nmi_interrupt_handler(void);
extern void hardfault_error_handler(void) __attribute__((noreturn));
extern void memmanage_error_handler(void) __attribute__((noreturn));
extern void busfault_error_handler(void) __attribute__((noreturn));
extern void usagefault_error_handler(void) __attribute__((noreturn));
extern void svc_service_handler(void);
extern void debugmon_service_handler(void);
extern void pendsv_service_handler(void);
extern void systick_service_handler(void);

// =============================================================================
// 栈和堆配置
// =============================================================================

// 栈大小 (来自汇编代码初始SP值)
#define STACK_SIZE      0x618

// 堆大小
#define HEAP_SIZE       0x200

// 栈内存 (放置在RAM末尾)
__attribute__((section(".stack")))
static uint8_t stack_memory[STACK_SIZE];

// 堆内存
__attribute__((section(".heap")))
static uint8_t heap_memory[HEAP_SIZE];

// =============================================================================
// 内存段 (来自汇编内存布局)
// =============================================================================

// 链接器定义的外部符号
extern uint32_t _sidata;    // Flash中初始化数据的起始地址
extern uint32_t _sdata;     // RAM中数据段的起始地址
extern uint32_t _edata;     // RAM中数据段的结束地址
extern uint32_t _sbss;      // RAM中BSS段的起始地址
extern uint32_t _ebss;      // RAM中BSS段的结束地址

// =============================================================================
// 系统内存初始化
// =============================================================================

/**
 * @brief 初始化RAM段 (数据段和BSS段)
 * 等效于汇编启动序列
 */
static void init_ram_sections(void) {
    uint32_t *src, *dst;

    // 从Flash复制初始化数据到RAM
    src = &_sidata;
    dst = &_sdata;
    while (dst < &_edata) {
        *dst++ = *src++;
    }

    // 零初始化BSS段
    dst = &_sbss;
    while (dst < &_ebss) {
        *dst++ = 0;
    }
}

/**
 * @brief 系统复位处理函数 - 主入口点
 * 在复位后调用，执行系统初始化
 *
 * 这个函数被Reset_Handler调用，执行传统的C运行时初始化
 */
void reset_handler(void) {
    // 初始化RAM段
    init_ram_sections();

    // 调用100%精确转换的Reset_Handler
    Reset_Handler();

    // 永远不应该到达这里
    while (1) {
        // 无限循环
    }
}

// =============================================================================
// Weak Default Handlers
// =============================================================================

/**
 * @brief Default handler for unhandled interrupts
 */
__attribute__((weak))
void default_handler(void) {
    while (1) {
        // Infinite loop for unhandled interrupts
    }
}

// 使用100%精确转换的中断处理函数，如果未定义则使用默认处理
__attribute__((weak, alias("nmi_interrupt_handler"))) void nmi_handler(void);
__attribute__((weak, alias("hardfault_error_handler"))) void hardfault_handler(void);
__attribute__((weak, alias("memmanage_error_handler"))) void memmanage_handler(void);
__attribute__((weak, alias("busfault_error_handler"))) void busfault_handler(void);
__attribute__((weak, alias("usagefault_error_handler"))) void usagefault_handler(void);
__attribute__((weak, alias("svc_service_handler"))) void svc_handler(void);
__attribute__((weak, alias("debugmon_service_handler"))) void debugmon_handler(void);
__attribute__((weak, alias("pendsv_service_handler"))) void pendsv_handler(void);
__attribute__((weak, alias("systick_service_handler"))) void systick_handler(void);

// External interrupt weak aliases
__attribute__((weak, alias("default_handler"))) void wwdg_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void pvd_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void tamper_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void rtc_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void flash_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void rcc_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void exti0_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void exti1_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void exti2_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void exti3_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void exti4_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void dma1_channel1_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void dma1_channel2_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void dma1_channel3_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void dma1_channel4_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void dma1_channel5_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void dma1_channel6_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void dma1_channel7_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void adc1_2_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void usb_hp_can1_tx_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void usb_lp_can1_rx0_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void can1_rx1_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void can1_sce_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void exti9_5_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void tim1_brk_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void tim1_up_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void tim1_trg_com_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void tim1_cc_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void tim2_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void tim3_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void tim4_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void i2c1_ev_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void i2c1_er_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void i2c2_ev_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void i2c2_er_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void spi1_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void spi2_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void uart1_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void uart2_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void exti15_10_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void rtc_alarm_irq_handler(void);
__attribute__((weak, alias("default_handler"))) void usb_wakeup_irq_handler(void);

// =============================================================================
// Vector Table (from ASM interrupt vector table)
// =============================================================================

/**
 * @brief Interrupt vector table
 * This table is placed at the beginning of flash memory (0x08000000)
 * and contains pointers to all interrupt handlers
 */
__attribute__((section(".isr_vector"), used))
const vector_table_t vector_table = {
    .initial_sp = (uint32_t)&stack_memory[STACK_SIZE],  // Initial stack pointer
    .reset_handler = reset_handler,
    .nmi_handler = nmi_handler,
    .hardfault_handler = hardfault_handler,
    .memmanage_handler = memmanage_handler,
    .busfault_handler = busfault_handler,
    .usagefault_handler = usagefault_handler,
    .reserved1 = {0, 0, 0, 0},
    .svc_handler = svc_handler,
    .debugmon_handler = debugmon_handler,
    .reserved2 = 0,
    .pendsv_handler = pendsv_handler,
    .systick_handler = systick_handler,
    .external_irq = {
        wwdg_irq_handler,                   // IRQ 0: Window Watchdog
        pvd_irq_handler,                    // IRQ 1: PVD through EXTI Line detect
        tamper_irq_handler,                 // IRQ 2: Tamper
        rtc_irq_handler,                    // IRQ 3: RTC
        flash_irq_handler,                  // IRQ 4: Flash
        rcc_irq_handler,                    // IRQ 5: RCC
        exti0_irq_handler,                  // IRQ 6: EXTI Line 0
        exti1_irq_handler,                  // IRQ 7: EXTI Line 1
        exti2_irq_handler,                  // IRQ 8: EXTI Line 2
        exti3_irq_handler,                  // IRQ 9: EXTI Line 3
        exti4_irq_handler,                  // IRQ 10: EXTI Line 4
        dma1_channel1_irq_handler,          // IRQ 11: DMA1 Channel 1
        dma1_channel2_irq_handler,          // IRQ 12: DMA1 Channel 2
        dma1_channel3_irq_handler,          // IRQ 13: DMA1 Channel 3
        dma1_channel4_irq_handler,          // IRQ 14: DMA1 Channel 4
        dma1_channel5_irq_handler,          // IRQ 15: DMA1 Channel 5
        dma1_channel6_irq_handler,          // IRQ 16: DMA1 Channel 6
        dma1_channel7_irq_handler,          // IRQ 17: DMA1 Channel 7
        adc1_2_irq_handler,                 // IRQ 18: ADC1 and ADC2
        usb_hp_can1_tx_irq_handler,         // IRQ 19: USB High Priority or CAN1 TX
        usb_lp_can1_rx0_irq_handler,        // IRQ 20: USB Low Priority or CAN1 RX0
        can1_rx1_irq_handler,               // IRQ 21: CAN1 RX1
        can1_sce_irq_handler,               // IRQ 22: CAN1 SCE
        exti9_5_irq_handler,                // IRQ 23: EXTI Line 9..5
        tim1_brk_irq_handler,               // IRQ 24: TIM1 Break
        tim1_up_irq_handler,                // IRQ 25: TIM1 Update
        tim1_trg_com_irq_handler,           // IRQ 26: TIM1 Trigger and Commutation
        tim1_cc_irq_handler,                // IRQ 27: TIM1 Capture Compare
        tim2_irq_handler,                   // IRQ 28: TIM2
        tim3_irq_handler,                   // IRQ 29: TIM3
        tim4_irq_handler,                   // IRQ 30: TIM4
        i2c1_ev_irq_handler,                // IRQ 31: I2C1 Event
        i2c1_er_irq_handler,                // IRQ 32: I2C1 Error
        i2c2_ev_irq_handler,                // IRQ 33: I2C2 Event
        i2c2_er_irq_handler,                // IRQ 34: I2C2 Error
        spi1_irq_handler,                   // IRQ 35: SPI1
        spi2_irq_handler,                   // IRQ 36: SPI2
        uart1_irq_handler,                  // IRQ 37: USART1
        uart2_irq_handler,                  // IRQ 38: USART2
        default_handler,                    // IRQ 39: USART3
        exti15_10_irq_handler,              // IRQ 40: EXTI Line 15..10
        rtc_alarm_irq_handler,              // IRQ 41: RTC Alarm through EXTI Line
        usb_wakeup_irq_handler,             // IRQ 42: USB Wakeup from suspend
        // Fill remaining slots with default handler
        default_handler, default_handler, default_handler, default_handler,
        default_handler, default_handler, default_handler, default_handler,
        default_handler, default_handler, default_handler, default_handler,
        default_handler, default_handler, default_handler, default_handler,
        default_handler, default_handler, default_handler, default_handler,
        default_handler, default_handler, default_handler, default_handler,
        default_handler
    }
};
