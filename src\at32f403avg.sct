; **********************************************************
; *** AT32F403AVG分散加载文件 - 从汇编代码内存布局转换
; *** 文件: at32f403avg.sct
; *** 日期: 2024
; *** 
; *** 保持与原始汇编代码相同的内存布局:
; *** - 引导程序: 0x08000000 - 0x08001FFF (8KB)
; *** - 应用程序: 0x08002000 - 0x080FFFFF (1016KB)  
; *** - MAC地址:  0x08001810
; *** - RAM:      0x20000000 - 0x20017FFF (96KB)
; *** - 栈指针:   0x20000618 (来自汇编代码)
; **********************************************************

; 引导程序区域 (Boot Loader Region)
BOOT_REGION 0x08000000 0x2000
{
    ; 中断向量表和启动代码
    VECTORS 0x08000000 0x400
    {
        startup_at32f403avg.o (RESET, +First)
        * (InRoot$$Sections)
    }
    
    ; 程序代码段
    CODE +0
    {
        * (+RO-CODE)
        * (+RO-DATA)
    }
    
    ; 只读常量数据
    CONST +0
    {
        * (+RO)
    }
}

; MAC地址存储区域 (从汇编代码0x08001810位置)
MAC_REGION 0x08001810 0x10
{
    MAC_DATA 0x08001810 0x10
    {
        * (.mac_address)
    }
}

; 应用程序区域 (Application Region)
APP_REGION 0x08002000 0xFE000
{
    ; 应用程序代码 (预留给将来的应用程序)
    APP_CODE 0x08002000
    {
        * (.application)
    }
}

; Logo数据区域 (从汇编代码0x08016A54位置)
LOGO_REGION 0x08016A54 0x100
{
    LOGO_DATA 0x08016A54
    {
        logo_data.o (+RO)
        * (.logo_data)
    }
}

; RAM区域 (RAM Region)
RAM_REGION 0x20000000 0x18000
{
    ; 初始化数据段 (从Flash复制到RAM)
    DATA 0x20000000
    {
        * (+RW)
    }
    
    ; 零初始化数据段
    BSS +0
    {
        * (+ZI)
    }
    
    ; 系统变量区域 (从汇编代码分析的固定地址)
    SYSTEM_VARS 0x20000004 0x20
    {
        ; 系统时间变量 0x20000004
        ; 缓冲区索引     0x20000008  
        ; 系统滴答计数   0x2000000A
        ; 延时计数器     0x2000000C
        ; CRC值         0x2000000E
        ; 临时缓冲区     0x20000010
        ; UART模式标志   0x20000011
    }
    
    ; 主接收缓冲区 (从汇编代码0x20001000)
    RX_BUFFER 0x20001000 0x0C00
    {
        ; 3KB接收缓冲区
    }
    
    ; 主发送缓冲区 (从汇编代码0x20001C00)  
    TX_BUFFER 0x20001C00 0x0300
    {
        ; 768字节发送缓冲区
    }
    
    ; 工作缓冲区 (从汇编代码0x20001F00)
    WORK_BUFFER 0x20001F00 0x0100
    {
        ; 256字节工作缓冲区
    }
    
    ; 堆区域
    HEAP +0 EMPTY 0x200
    {
        ; 512字节堆空间
    }
    
    ; 栈区域 (栈指针初始值来自汇编代码0x20000618)
    STACK 0x20000000 EMPTY 0x618
    {
        ; 栈空间，向下增长到0x20000000
        ; 栈顶在0x20000618 (来自汇编代码初始SP值)
    }
}

; **********************************************************
; *** 内存映射总结
; **********************************************************
; Flash总计: 1MB
; - 引导程序:   8KB   (0x08000000 - 0x08001FFF)
; - MAC地址:    16B   (0x08001810 - 0x0800181F)  
; - 应用程序:   1016KB(0x08002000 - 0x080FFFFF)
; - Logo数据:   256B  (0x08016A54 - 0x08016B53)
;
; RAM总计: 96KB  
; - 数据段:     动态分配
; - 系统变量:   32B   (0x20000004 - 0x20000023)
; - 接收缓冲区: 3KB   (0x20001000 - 0x20001BFF)
; - 发送缓冲区: 768B  (0x20001C00 - 0x20001EFF)
; - 工作缓冲区: 256B  (0x20001F00 - 0x20001FFF)
; - 堆空间:     512B  (动态分配)
; - 栈空间:     1560B (0x20000000 - 0x20000617)
; **********************************************************
