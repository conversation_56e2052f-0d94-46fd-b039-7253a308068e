// 精确转换批次 23 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7BB10
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7bb10(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200036B4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R4, #0
    // R4 = 0;
    // BL      sub_7B7BA
    // 调用函数: sub_7B7BA();
    // MOVS    R5, R0
    // LDR     R0, =0x20003751
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7BBE0
    // 条件跳转
    // LDR     R0, =0x200036B4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7A478
    // 调用函数: sub_7A478();
    // MOVS    R4, R0
    // LDRB    R0, [R4,#7]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_7BB36
    // 条件跳转
    // B       locret_7BC48
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7BC6C
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_7bc6c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_2B= -0x2B
    // var_2A= -0x2A
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C054
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_7c054(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_2B= -0x2B
    // var_2A= -0x2A
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C410
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_7c410(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003754;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [SP,#0x20+var_1C]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003754
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20003751
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7C438
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003751
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_7B7FE
    // 调用函数: sub_7B7FE();
    // BL      sub_7A960
    // 调用函数: sub_7A960();
    // B       locret_7C51C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C550
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_7c550(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOVS    R4, #0
    // R4 = 0;
    // BL      sub_7B7BA
    // 调用函数: sub_7B7BA();
    // MOVS    R6, R0
    // LDR     R0, =0x2000374B
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_7C56E
    // 条件跳转
    // LDR     R0, =0x20003706
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_7C56E
    // 条件跳转
    // SUBS    R6, R6, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C7B8
 * @note 指令数: 48, 标签数: 0
 */
void precise_func_7c7b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003754;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003751;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036B4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // BL      sub_78C4A
    // 调用函数: sub_78C4A();
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_7C876
    // 条件跳转
    // BL      sub_77416
    // 调用函数: sub_77416();
    // CMP     R0, #6
    // 比较操作
    // BNE     locret_7C876
    // 条件跳转
    // LDR     R0, =0x20003750
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7C82A
    // 条件跳转
    // LDR     R0, =0x20003754
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_7C82A
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003750
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_7A960
    // 调用函数: sub_7A960();
    // LDR     R0, =0x20003751
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7C82A
    // 条件跳转
    // LDR     R0, =0x20003752
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7C82A
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003752
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200036B4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_7A478
    // 调用函数: sub_7A478();
    // MOVS    R4, R0
    // LDRB    R0, [R4,#7]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_7C82A
    // 条件跳转
    // LDR     R0, [R4,#0x14]
    // 内存加载操作
    // LDR     R1, [R4,#0x18]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_7C824
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20003751
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_7B7FE
    // 调用函数: sub_7B7FE();
    // BL      sub_7A960
    // 调用函数: sub_7A960();
    // B       loc_7C82A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C8A0
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_7c8a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40011420;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x4001141C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40011400;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x4001141C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_7C8CA
    // LDR     R0, =0x40011400
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x17
    // BPL     loc_7C8CA
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x40011420
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20003668
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7C8CA
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR     R2, =0x20003668
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7C9AE
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7c9ae(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // var_14= -0x14
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CB84
 * @note 指令数: 9, 标签数: 0
 */
uint32_t precise_func_7cb84(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011790;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x8011790
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // BICS    R0, R1
    // LDR     R1, =0x8011790
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CBA8
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_7cba8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x19;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011790;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // LDR     R0, =0x8011790
    // 内存加载操作
    // MOVS    R1, #0x50 ; 'P'
    // R1 = 0x50;
    // MULS    R1, R5
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x19
    // BMI     loc_7CBCA
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CC04
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_7cc04(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011790;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // LDR     R0, =0x8011790
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R1
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // LDR     R0, [R0,#0x1C]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_7CC28
    // LDR     R0, =0x8011790
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R1
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // LDR     R0, [R0,#0x1C]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_7CC28
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_7CC2A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CC2C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7cc2c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011790;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x8011790
    // 内存加载操作
    // MOVS    R1, #0x50 ; 'P'
    // R1 = 0x50;
    // MULS    R1, R4
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40 ; '@'
    // R1 = 0x40;
    // ORRS    R1, R0
    // LDR     R0, =0x8011790
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, R4
    // BL      sub_7FA18
    // 调用函数: sub_7FA18();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CC50
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_7cc50(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011790;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x8011790
    // 内存加载操作
    // MOVS    R2, #0x50 ; 'P'
    // R2 = 0x50;
    // MULS    R2, R0
    // LDR     R1, [R1,R2]
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #0x40 ; '@'
    // R2 = 0x40;
    // BICS    R1, R2
    // LDR     R2, =0x8011790
    // 内存加载操作
    // MOVS    R3, #0x50 ; 'P'
    // R3 = 0x50;
    // MULS    R3, R0
    // LDR     R2, [R2,R3]
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CC74
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_7cc74(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_7CC98
    // 条件跳转
    // UXTB    R6, R6
    // 数据扩展操作
    // CMP     R6, #0
    // 比较操作
    // BEQ     loc_7CC90
    // 条件跳转
    // MOVS    R0, R4
    // NOP
    // NOP
    // B       locret_7CC9E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CCA0
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7cca0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_7CC74
    // 调用函数: sub_7CC74();
    // MOVS    R0, #1
    // R0 = 1;
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CCB2
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_7ccb2(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8011790;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, =0x8011790
    // 内存加载操作
    // MOVS    R3, #0x50 ; 'P'
    // R3 = 0x50;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // LDR     R2, [R2,#4]
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CCCC
 * @note 指令数: 48, 标签数: 0
 */
void precise_func_7cccc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFFFFF7FF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xEFFF69F3;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFFFFF4FF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R0, [R5,#4]
    // 内存加载操作
    // LDR     R1, [R5,#0xC]
    // 内存加载操作
    // ORRS    R1, R0
    // LDR     R0, [R5,#0x10]
    // 内存加载操作
    // ORRS    R0, R1
    // LDR     R1, [R5,#0x18]
    // 内存加载操作
    // ORRS    R1, R0
    // MOVS    R6, R1
    // LDR     R0, [R4]
    // 内存加载操作
    // LDR     R1, =0xEFFF69F3
    // 内存加载操作
    // ANDS    R1, R0
    // ORRS    R1, R6
    // STR     R1, [R4]
    // 内存存储操作
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // LDR     R1, =0xFFFFCFFF
    // 内存加载操作
    // ANDS    R1, R0
    // LDR     R0, [R5,#8]
    // 内存加载操作
    // ORRS    R0, R1
    // STR     R0, [R4,#4]
    // 内存存储操作
    // LDR     R0, [R5,#0x14]
    // 内存加载操作
    // LDR     R1, [R5,#0x1C]
    // 内存加载操作
    // ORRS    R1, R0
    // MOVS    R6, R1
    // LDR     R0, [R4,#8]
    // 内存加载操作
    // LDR     R1, =0xFFFFF4FF
    // 内存加载操作
    // ANDS    R1, R0
    // ORRS    R1, R6
    // STR     R1, [R4,#8]
    // 内存存储操作
    // BL      sub_7FEF0
    // 调用函数: sub_7FEF0();
    // MOVS    R1, #2
    // R1 = 2;
    // MULS    R0, R1
    // LDR     R1, [R5]
    // 内存加载操作
    // BL      sub_7639E
    // 调用函数: sub_7639E();
    // UXTH    R0, R0
    // 数据扩展操作
    // STR     R0, [R4,#0xC]
    // 内存存储操作
    // LDR     R0, [R4,#4]
    // 内存加载操作
    // LDR     R1, =0xFFFFF7FF
    // 内存加载操作
    // ANDS    R1, R0
    // STR     R1, [R4,#4]
    // 内存存储操作
    // LDR     R0, [R4,#8]
    // 内存加载操作
    // MOVS    R1, #8
    // R1 = 8;
    // BICS    R0, R1
    // STR     R0, [R4,#8]
    // 内存存储操作
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CD48
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_7cd48(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200035AC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003731;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x23;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000366C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003488;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R0, #5
    // R0 = 5;
    // LDR     R1, =0x20003731
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R5, #0x23 ; '#'
    // R5 = 0x23;
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R6, =0x20003488
    // 内存加载操作
    // MOVS    R2, R4
    // MOVS    R1, R5
    // MOVS    R0, R6
    // BL      sub_76820
    // 调用函数: sub_76820();
    // LDR     R0, =0x20003488
    // 内存加载操作
    // LDR     R1, =0x2000366C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200035AC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200035AC
    // 内存加载操作
    // BL      sub_789CE
    // 调用函数: sub_789CE();
    // BL      sub_7E0DE
    // 调用函数: sub_7E0DE();
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_7CF92
    // 调用函数: sub_7CF92();
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_7CF92
    // 调用函数: sub_7CF92();
    // POP     {R4-R6,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CD86
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7cd86(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200035AC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // LDR     R0, =0x200035AC
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xA
    // 比较操作
    // BGE     loc_7CD92
    // 条件跳转
    // B       locret_7CF18
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CF1A
 * @note 指令数: 44, 标签数: 0
 */
void precise_func_7cf1a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003731;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000366C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // LDR     R3, =0x20003731
    // 内存加载操作
    // LDRB    R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R3
    // 比较操作
    // BCS     locret_7CF72
    // MOVS    R3, #3
    // R3 = 3;
    // LDR     R4, =0x2000366C
    // 内存加载操作
    // LDR     R4, [R4]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R5, #7
    // R5 = 7;
    // MULS    R5, R0
    // ADDS    R4, R4, R5
    // 算术运算
    // STRB    R3, [R4,#4]
    // 内存存储操作
    // LDR     R3, =0x2000366C
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #7
    // R4 = 7;
    // MULS    R4, R0
    // ADDS    R3, R3, R4
    // 算术运算
    // MOVS    R4, R1
    // STRB    R4, [R3]
    // 内存存储操作
    // LSRS    R4, R4, #8
    // STRB    R4, [R3,#1]
    // 内存存储操作
    // LDR     R3, =0x2000366C
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #7
    // R4 = 7;
    // MULS    R4, R0
    // ADDS    R3, R3, R4
    // 算术运算
    // MOVS    R4, R2
    // STRB    R4, [R3,#2]
    // 内存存储操作
    // LSRS    R4, R4, #8
    // STRB    R4, [R3,#3]
    // 内存存储操作
    // LDR     R3, =0x2000366C
    // 内存加载操作
    // LDR     R3, [R3]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R4, #7
    // R4 = 7;
    // MULS    R4, R0
    // ADDS    R3, R3, R4
    // 算术运算
    // MOVS    R4, #0
    // R4 = 0;
    // STRB    R4, [R3,#5]
    // 内存存储操作
    // LSRS    R4, R4, #8
    // STRB    R4, [R3,#6]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CF74
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_7cf74(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003731;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000366C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R1, =0x20003731
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R1
    // 比较操作
    // BCS     locret_7CF90
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R2, =0x2000366C
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #7
    // R3 = 7;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STRB    R1, [R2,#4]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CF92
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_7cf92(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003731;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000366C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R1, =0x20003731
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R1
    // 比较操作
    // BCS     locret_7CFAE
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x2000366C
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #7
    // R3 = 7;
    // MULS    R3, R0
    // ADDS    R2, R2, R3
    // 算术运算
    // STRB    R1, [R2,#4]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7CFC0
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7cfc0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R2,R4-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R4, R1
    // CPSID   I
    // BL      sub_7FFB8
    // 调用函数: sub_7FFB8();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7D00C
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7d00c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CPSID   I
    // BL      sub_7FFB8
    // 调用函数: sub_7FFB8();
    // MOVS    R0, R4
    // BL      sub_7FFEC
    // 调用函数: sub_7FFEC();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7D044
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_7d044(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // CPSID   I
    // BL      sub_7FFB8
    // 调用函数: sub_7FFB8();
    // MOVS    R2, R4
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R1, R5
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_7FF14
    // 调用函数: sub_7FF14();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_7D068
    // 条件跳转
    // BL      sub_7FFDC
    // 调用函数: sub_7FFDC();
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_7D070
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7D07C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_7d07c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x98;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x90;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x88;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_A0= -0xA0
    // var_9C= -0x9C
    // var_98= -0x98
    // var_90= -0x90
    // var_8C= -0x8C
    // var_88= -0x88
    // var_84= -0x84
    // var_80= -0x80
    // var_7C= -0x7C
    // var_78= -0x78
    // var_74= -0x74
    // var_70= -0x70
    // var_6C= -0x6C
    // var_68= -0x68
    // var_64= -0x64
    // var_58= -0x58
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7D636
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_7d636(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x71;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x74;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x62;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x6C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x7A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R1
    // CMP     R0, #0x62 ; 'b'
    // 比较操作
    // BEQ     loc_7D682
    // 条件跳转
    // CMP     R0, #0x68 ; 'h'
    // 比较操作
    // BEQ     loc_7D674
    // 条件跳转
    // CMP     R0, #0x6A ; 'j'
    // 比较操作
    // BEQ     loc_7D656
    // 条件跳转
    // CMP     R0, #0x6C ; 'l'
    // 比较操作
    // BEQ     loc_7D690
    // 条件跳转
    // CMP     R0, #0x71 ; 'q'
    // 比较操作
    // BEQ     loc_7D656
    // 条件跳转
    // CMP     R0, #0x74 ; 't'
    // 比较操作
    // BEQ     loc_7D690
    // 条件跳转
    // CMP     R0, #0x7A ; 'z'
    // 比较操作
    // BEQ     loc_7D666
    // 条件跳转
    // B       loc_7D690
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7D69E
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_7d69e(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7D7D0
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7d7d0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x58;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x60;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_60= -0x60
    // var_5C= -0x5C
    // var_58= -0x58
    // var_50= -0x50
    // var_4C= -0x4C
    // var_48= -0x48
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7DC68
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7dc68(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7DF02
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7df02(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // MOVS    R6, R1
    // MOVS    R4, R2
    // MOVS    R7, #0
    // R7 = 0;
    // CMP     R3, #0
    // 比较操作
    // BEQ     loc_7DF2E
    // 条件跳转
    // MOVS    R5, R3
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7DF38
 * @note 指令数: 38, 标签数: 0
 */
void precise_func_7df38(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BPL     loc_7DF84
    // LDR     R4, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R2, R0, #0x1C
    // LSRS    R2, R2, #0x1C
    // SUBS    R2, #8
    // 算术运算
    // LSRS    R5, R2, #2
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R5, R2
    // LDR     R2, =0xE000ED1C
    // 内存加载操作
    // SXTB    R0, R0
    // 数据扩展操作
    // LSLS    R3, R0, #0x1C
    // LSRS    R3, R3, #0x1C
    // SUBS    R3, #8
    // 算术运算
    // LSRS    R3, R3, #2
    // MOVS    R6, #4
    // R6 = 4;
    // MULS    R3, R6
    // LDR     R2, [R2,R3]
    // 内存加载操作
    // MOVS    R3, #0xFF
    // R3 = 0xFF;
    // LSLS    R6, R0, #0x1E
    // LSRS    R6, R6, #0x1E
    // MOVS    R7, #8
    // R7 = 8;
    // MULS    R6, R7
    // LSLS    R3, R6
    // BICS    R2, R3
    // LSLS    R3, R1, #6
    // UXTB    R3, R3
    // 数据扩展操作
    // LSLS    R6, R0, #0x1E
    // LSRS    R6, R6, #0x1E
    // MOVS    R7, #8
    // R7 = 8;
    // MULS    R6, R7
    // LSLS    R3, R6
    // ORRS    R3, R2
    // STR     R3, [R4,R5]
    // 内存存储操作
    // B       locret_7DFBA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7DFBC
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7dfbc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // SUBS    R0, R4, #1
    // 算术运算
    // MOVS    R1, #0x1000000
    // R1 = 0x1000000;
    // CMP     R0, R1
    // 比较操作
    // BCC     loc_7DFCE
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_7DFEC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7DFEE
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7dfee(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003664;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_78B94
    // 调用函数: sub_78B94();
    // LDR     R0, =0x20003664
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x20003664
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E000
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_7e000(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000164;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20000164
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R1, =(a031meDSUnsuppo+0xB) ; "d) %s: unsupported frequency configurat"...
    // 内存加载操作
    // BL      sub_7639E
    // 调用函数: sub_7639E();
    // BL      sub_7DFBC
    // 调用函数: sub_7DFBC();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E034
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_7e034(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80115D8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BNE     loc_7E060
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x80115D8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x18
    // R1 = 0x18;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x80115D8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R3, #0x18
    // R3 = 0x18;
    // MULS    R3, R4
    // LDR     R0, [R0,R3]
    // 内存加载操作
    // BL      sub_7E3CA
    // 调用函数: sub_7E3CA();
    // B       locret_7E07E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E080
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_7e080(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R0-R6,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOV     R0, SP
    // MOVS    R1, #0x10
    // R1 = 0x10;
    // BL      sub_76870
    // 调用函数: sub_76870();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // MOVS    R0, #1
    // R0 = 1;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E0DE
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_7e0de(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E138
 * @note 指令数: 5, 标签数: 1
 */
void precise_func_7e138(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // B       loc_7E142
    // 无条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // ADDS    R1, R1, #1
    // 算术运算
    // SUBS    R2, R2, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E160
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_7e160(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R7, #0
    // R7 = 0;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E3AE
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_7e3ae(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R2, R0
    // LDR     R3, [R2,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // ANDS    R3, R1
    // CMP     R3, #0
    // 比较操作
    // BEQ     loc_7E3C2
    // 条件跳转
    // MOVS    R3, #1
    // R3 = 1;
    // MOVS    R0, R3
    // B       loc_7E3C6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E3CA
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_7e3ca(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // UXTB    R2, R2
    // 数据扩展操作
    // CMP     R2, #0
    // 比较操作
    // BEQ     loc_7E3D8
    // 条件跳转
    // UXTH    R1, R1
    // 数据扩展操作
    // STR     R1, [R0,#0x18]
    // 内存存储操作
    // B       locret_7E3DC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E404
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_7e404(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R2,R3}
    // 栈操作
    // MOVS    R2, #0x1000000
    // R2 = 0x1000000;
    // ADDS    R3, R0, R0
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BHI     loc_7E420
    // ADDS    R3, R1, R1
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BHI     loc_7E420
    // MOVS    R2, R0
    // ORRS    R2, R1
    // ADDS    R2, R2, R2
    // 算术运算
    // BCS     loc_7E424
    // CMP     R1, R0
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E42C
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_7e42c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // MOVS    R2, R0
    // ADDS    R1, R1, #1
    // 算术运算
    // CMP     R1, #1
    // 比较操作
    // BLT     loc_7E43A
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R1, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E4D8
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_7e4d8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200034AC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R5, #0x20 ; ' '
    // R5 = 0x20;
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R7, =0x200034AC
    // 内存加载操作
    // MOVS    R2, R6
    // MOVS    R1, R5
    // MOVS    R0, R7
    // BL      sub_76820
    // 调用函数: sub_76820();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E534
 * @note 指令数: 43, 标签数: 1
 */
void precise_func_7e534(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003550;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R1, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R2, R0
    // CMP     R2, #1
    // 比较操作
    // BGE     loc_7E58E
    // 条件跳转
    // LDR     R0, =0x20003550
    // 内存加载操作
    // MOVS    R3, #0x14
    // R3 = 0x14;
    // MULS    R3, R2
    // LDRH    R0, [R0,R3]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_7E58A
    // 条件跳转
    // LDR     R0, =0x20003550
    // 内存加载操作
    // MOVS    R3, #0x14
    // R3 = 0x14;
    // MULS    R3, R2
    // STRH    R1, [R0,R3]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R3, =0x20003550
    // 内存加载操作
    // MOVS    R4, #0x14
    // R4 = 0x14;
    // MULS    R4, R2
    // ADDS    R3, R3, R4
    // 算术运算
    // STRB    R0, [R3,#8]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R3, =0x20003550
    // 内存加载操作
    // MOVS    R4, #0x14
    // R4 = 0x14;
    // MULS    R4, R2
    // ADDS    R3, R3, R4
    // 算术运算
    // STR     R0, [R3,#0xC]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R3, =0x20003550
    // 内存加载操作
    // MOVS    R4, #0x14
    // R4 = 0x14;
    // MULS    R4, R2
    // ADDS    R3, R3, R4
    // 算术运算
    // STRB    R0, [R3,#0x10]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // LDR     R3, =0x20003550
    // 内存加载操作
    // MOVS    R4, #0x14
    // R4 = 0x14;
    // MULS    R4, R2
    // ADDS    R3, R3, R4
    // 算术运算
    // STR     R0, [R3,#4]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_7E590
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E592
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_7e592(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // CMP     R1, #0
    // 比较操作
    // BNE     loc_7E59E
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_7E5B4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E5B6
 * @note 指令数: 18, 标签数: 1
 */
void precise_func_7e5b6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003550;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R2, R0
    // CMP     R2, #1
    // 比较操作
    // BGE     loc_7E5DE
    // 条件跳转
    // LDR     R0, =0x20003550
    // 内存加载操作
    // MOVS    R3, #0x14
    // R3 = 0x14;
    // MULS    R3, R2
    // LDRH    R0, [R0,R3]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_7E5DA
    // 条件跳转
    // LDR     R0, =0x20003550
    // 内存加载操作
    // MOVS    R3, #0x14
    // R3 = 0x14;
    // MULS    R2, R3
    // ADDS    R0, R0, R2
    // 算术运算
    // B       locret_7E5E0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7E5EC
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_7e5ec(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R0,R2,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #4
    // 算术运算
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // MOV     R0, SP
    // LDRH    R0, [R0,#0x20+var_1C]
    // 内存加载操作
    // BL      sub_7E5B6
    // 调用函数: sub_7E5B6();
    // MOVS    R4, R0
    // CMP     R4, #0
    // 比较操作
    // BNE     loc_7E606
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_7EC56
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7EC58
 * @note 指令数: 51, 标签数: 0
 */
void precise_func_7ec58(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200034AC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // UXTH    R0, R0
    // 数据扩展操作
    // BL      sub_7E5B6
    // 调用函数: sub_7E5B6();
    // MOVS    R5, R0
    // CMP     R5, #0
    // 比较操作
    // BEQ     locret_7ECC2
    // 条件跳转
    // LDR     R0, [R5,#4]
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // CMP     R0, R1
    // 比较操作
    // BEQ     locret_7ECC2
    // 条件跳转
    // LDR     R0, [R5,#0xC]
    // 内存加载操作
    // LDR     R0, [R0,#4]
    // 内存加载操作
    // LDR     R1, =0xFFFF7FFF
    // 内存加载操作
    // ANDS    R1, R0
    // LDR     R0, [R5,#0xC]
    // 内存加载操作
    // STR     R1, [R0,#4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R5,#0x10]
    // 内存存储操作
    // LDR     R0, =0x200034AC
    // 内存加载操作
    // LDR     R1, [R5,#4]
    // 内存加载操作
    // MOVS    R2, #0x20 ; ' '
    // R2 = 0x20;
    // MULS    R1, R2
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, =0x200034AC
    // 内存加载操作
    // LDR     R2, [R5,#4]
    // 内存加载操作
    // MOVS    R3, #0x20 ; ' '
    // R3 = 0x20;
    // MULS    R2, R3
    // ADDS    R1, R1, R2
    // 算术运算
    // LDRB    R1, [R1,#4]
    // 内存加载操作
    // ADDS    R1, R1, #1
    // 算术运算
    // STRB    R1, [R0,#4]
    // 内存存储操作
    // LDR     R1, [R5,#4]
    // 内存加载操作
    // MOVS    R0, R4
    // UXTH    R0, R0
    // 数据扩展操作
    // BL      sub_7E42C
    // 调用函数: sub_7E42C();
    // STR     R0, [R5,#4]
    // 内存存储操作
    // LDR     R0, [R5,#4]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BMI     locret_7ECC2
    // LDR     R0, =0x200034AC
    // 内存加载操作
    // LDR     R1, [R5,#4]
    // 内存加载操作
    // MOVS    R2, #0x20 ; ' '
    // R2 = 0x20;
    // MULS    R1, R2
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R0, [R0,#0x14]
    // 内存加载操作
    // STR     R0, [R5,#0xC]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // STRB    R0, [R5,#8]
    // 内存存储操作
}

