// 精确转换批次 11 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_455D4
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_455d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077B4;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x200077B4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_455DA
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_455da(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077A0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_455EC
    // 条件跳转
    // LDR     R0, =0x200077A0
    // 内存加载操作
    // MOVS    R6, R0
    // B       loc_455F0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45616
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_45616(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077A8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_45628
    // 条件跳转
    // LDR     R0, =0x200077A8
    // 内存加载操作
    // MOVS    R6, R0
    // B       loc_4562C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45652
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_45652(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007838;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R1, =0x20007838
    // 内存加载操作
    // LDRH    R1, [R1]
    // 内存加载操作
    // CMP     R1, #0
    // 比较操作
    // BNE     loc_4565E
    // 条件跳转
    // B       locret_4579E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_457A0
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_457a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077AC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x200077AC
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_457B0
    // 条件跳转
    // LDR     R0, =0x200077AC
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BLX     R0
    // 调用函数: R0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45828
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_45828(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000410;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x4000040C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000789F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x40000410
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_45854
    // LDR     R0, =0x4000040C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     locret_45854
    // MOVS    R0, #1
    // R0 = 1;
    // MVNS    R0, R0
    // LDR     R1, =0x40000410
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x4000040C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #1
    // R1 = 1;
    // BICS    R0, R1
    // LDR     R1, =0x4000040C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000789F
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45856
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_45856(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_14= -0x14
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_458AE
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_458ae(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000410;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4000040C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // MVNS    R1, R1
    // LDR     R2, =0x40000410
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_458CC
    // 条件跳转
    // LDR     R1, =0x4000040C
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #1
    // R2 = 1;
    // ORRS    R2, R1
    // LDR     R1, =0x4000040C
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // B       locret_458D8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_458DA
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_458da(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000424;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x40000424
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_458E2
 * @note 指令数: 39, 标签数: 0
 */
void precise_func_458e2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000789D;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015C54;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8015C4C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40012404;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000789C
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R1, =0x8015C54
    // 内存加载操作
    // LDR     R0, =0x8015C4C
    // 内存加载操作
    // LDR     R0, [R0,#4]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // MOVS    R3, #2
    // R3 = 2;
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8015C4C
    // 内存加载操作
    // LDR     R1, [R0]
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x40012400
    // 内存加载操作
    // BL      sub_45556
    // 调用函数: sub_45556();
    // LDR     R0, =0x40012404
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #4
    // R1 = 4;
    // ORRS    R1, R0
    // LDR     R0, =0x40012404
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000789D
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007724
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20000322
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BNE     loc_45938
    // 条件跳转
    // BL      sub_47564
    // 调用函数: sub_47564();
    // B       loc_4593C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45954
 * @note 指令数: 6, 标签数: 0
 */
uint32_t precise_func_45954(uint8_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006F20;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20006F20
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R0, R2
    // LDR     R0, [R1,R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45960
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_45960(uint8_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007724;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000789D;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007620;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, =0x20007620
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R2,R0]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x20007724
    // 内存加载操作
    // STR     R2, [R3]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x2000789D
    // 内存加载操作
    // STRB    R2, [R3]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45974
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_45974(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007620;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20007620
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LDRB    R0, [R1,R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4597C
 * @note 指令数: 7, 标签数: 0
 */
uint32_t precise_func_4597c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40012408;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x40012408
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #4
    // R1 = 4;
    // ORRS    R1, R0
    // LDR     R0, =0x40012408
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4598A
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_4598a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000789C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007728;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x2000789C
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x2000789C
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000789C
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #8
    // 比较操作
    // BLT     locret_459B2
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000789C
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007728
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_459B2
    // 条件跳转
    // LDR     R0, =0x20007728
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BLX     R0
    // 调用函数: R0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_459CC
 * @note 指令数: 29, 标签数: 0
 */
void precise_func_459cc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007620;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // BL      sub_4598A
    // 调用函数: sub_4598A();
    // LDR     R0, =0x2000789C
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #8
    // 比较操作
    // BGE     locret_45AA4
    // 条件跳转
    // LDR     R0, =0x20000322
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BNE     loc_45A44
    // 条件跳转
    // LDR     R0, =0x20007620
    // 内存加载操作
    // LDR     R1, =0x2000789C
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // LDRB    R0, [R0,R1]
    // 内存加载操作
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_45A08
    // 条件跳转
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_45A0E
    // 条件跳转
    // CMP     R0, #5
    // 比较操作
    // BEQ     loc_45A14
    // 条件跳转
    // CMP     R0, #8
    // 比较操作
    // BEQ     loc_45A1A
    // 条件跳转
    // CMP     R0, #0xA
    // 比较操作
    // BEQ     loc_45A20
    // 条件跳转
    // CMP     R0, #0x10
    // 比较操作
    // BEQ     loc_45A26
    // 条件跳转
    // CMP     R0, #0x20 ; ' '
    // 比较操作
    // BEQ     loc_45A2C
    // 条件跳转
    // B       loc_45A32
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45AD8
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_45ad8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40012400;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x40012400
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1D
    // BMI     loc_45AE4
    // B       locret_45BF8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45C04
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_45c04(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000789E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x2000789E
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_45C1A
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000789E
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_458AE
    // 调用函数: sub_458AE();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45C68
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_45c68(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x9D;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0x9D
    // R1 = 0x9D;
    // MOVS    R2, R0
    // BPL     sub_45C84
    // MOV     R12, LR
    // NEGS    R2, R2
    // BL      sub_45C84
    // 调用函数: sub_45C84();
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ORRS    R0, R2
    // BX      R12
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45C80
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_45c80(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x9D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0x9D
    // R1 = 0x9D;
    // MOVS    R2, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45C84
 * @note 指令数: 5, 标签数: 1
 */
void precise_func_45c84(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // BEQ     locret_45C9C
    // 条件跳转
    // BMI     loc_45C8E
    // SUBS    R1, R1, #1
    // 算术运算
    // ADDS    R2, R2, R2
    // 算术运算
    // BPL     loc_45C88
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45C9E
 * @note 指令数: 39, 标签数: 0
 */
void precise_func_45c9e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R5, R0
    // EORS    R5, R1
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ANDS    R5, R2
    // ADDS    R3, R1, R1
    // 算术运算
    // ADDS    R4, R0, R0
    // 算术运算
    // LSRS    R4, R4, #0x18
    // BEQ     loc_45D10
    // 条件跳转
    // LSRS    R3, R3, #0x18
    // BEQ     loc_45D1C
    // 条件跳转
    // CMP     R4, #0xFF
    // 比较操作
    // BEQ     loc_45D24
    // 条件跳转
    // CMP     R3, #0xFF
    // 比较操作
    // BEQ     loc_45D34
    // 条件跳转
    // ADDS    R4, R4, R3
    // 算术运算
    // LSLS    R0, R0, #8
    // LSLS    R1, R1, #8
    // ORRS    R0, R2
    // ORRS    R1, R2
    // LSRS    R0, R0, #8
    // LSRS    R1, R1, #8
    // MOV     R12, R2
    // UXTB    R2, R0
    // 数据扩展操作
    // MULS    R2, R1
    // LSRS    R0, R0, #8
    // UXTB    R3, R0
    // 数据扩展操作
    // MULS    R3, R1
    // LSRS    R0, R0, #8
    // MULS    R0, R1
    // LSRS    R1, R2, #8
    // ADDS    R3, R3, R1
    // 算术运算
    // ORRS    R2, R3
    // UXTB    R2, R2
    // 数据扩展操作
    // LSRS    R1, R3, #8
    // ADDS    R1, R1, R0
    // 算术运算
    // BMI     loc_45CEE
    // ADDS    R1, R1, R1
    // 算术运算
    // SUBS    R4, R4, #1
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45D4E
 * @note 指令数: 8, 标签数: 1
 */
void precise_func_45d4e(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // MOVS    R3, R0
    // EORS    R3, R1
    // BMI     loc_45DB6
    // SUBS    R3, R0, R1
    // 算术运算
    // BCS     loc_45D60
    // SUBS    R0, R0, R3
    // 算术运算
    // ADDS    R1, R1, R3
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45E6E
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_45e6e(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, [R0]
    // 内存加载操作
    // ADDS    R3, R2, #1
    // 算术运算
    // STR     R3, [R0]
    // 内存存储操作
    // STRB    R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45E78
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_45e78(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R2,R3}
    // 栈操作
    // MOVS    R2, #0x1000000
    // R2 = 0x1000000;
    // ADDS    R3, R0, R0
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BHI     loc_45E94
    // ADDS    R3, R1, R1
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BHI     loc_45E94
    // MOVS    R2, R0
    // ORRS    R2, R1
    // ADDS    R2, R2, R2
    // 算术运算
    // BCS     loc_45E98
    // CMP     R1, R0
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45EA0
 * @note 指令数: 14, 标签数: 0
 */
uint32_t precise_func_45ea0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38000000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0x1000000
    // R2 = 0x1000000;
    // ADDS    R1, R0, R0
    // 算术运算
    // CMN     R2, R1
    // 比较操作
    // BCS     loc_45EC0
    // CMP     R2, R1
    // 比较操作
    // BHI     loc_45ECA
    // MOVS    R2, #0x38000000
    // R2 = 0x38000000;
    // LSRS    R1, R1, #4
    // ADDS    R1, R1, R2
    // 算术运算
    // LSRS    R2, R0, #0x1F
    // LSLS    R2, R2, #0x1F
    // ORRS    R1, R2
    // LSLS    R0, R0, #0x1D
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_45ED2
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_45ed2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R6, R1
    // EORS    R6, R3
    // MOVS    R5, #0x80000000
    // R5 = 0x80000000;
    // ANDS    R6, R5
    // MOV     R12, R6
    // LSRS    R6, R5, #0xA
    // ADDS    R7, R3, R3
    // 算术运算
    // ADDS    R4, R1, R1
    // 算术运算
    // CMN     R4, R6
    // 比较操作
    // BCS     loc_45EFC
    // CMN     R7, R6
    // 比较操作
    // BCS     loc_45F0C
    // LSRS    R4, R4, #0x15
    // BEQ     loc_45EF6
    // 条件跳转
    // LSRS    R7, R7, #0x15
    // BNE     loc_45F1C
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46056
 * @note 指令数: 6, 标签数: 0
 */
uint32_t precise_func_46056(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0x200000
    // R2 = 0x200000;
    // ADDS    R3, R1, R1
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BLS     loc_46064
    // ASRS    R0, R3, #0x15
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_460B8
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_460b8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // BPL     loc_460D0
    // MOV     R12, LR
    // NEGS    R0, R0
    // BL      loc_460D0
    // 调用函数: loc_460D0();
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ORRS    R1, R2
    // BX      R12
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_460CC
 * @note 指令数: 6, 标签数: 1
 */
void precise_func_460cc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x41D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // NOP
    // MOVS    R2, #0x41D
    // R2 = 0x41D;
    // TST     R0, R0
    // 比较操作
    // BEQ     locret_460EA
    // 条件跳转
    // BMI     loc_460E2
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_460EC
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_460ec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // MOVS    R5, #0x80000000
    // R5 = 0x80000000;
    // MOVS    R7, R1
    // EORS    R7, R3
    // BMI     loc_46198
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_461B2
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_461b2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // MOVS    R5, #0x80000000
    // R5 = 0x80000000;
    // MOVS    R7, R1
    // EORS    R7, R3
    // BMI     loc_4619C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_462A0
 * @note 指令数: 28, 标签数: 0
 */
void precise_func_462a0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // MOVS    R5, R0
    // EORS    R5, R1
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ANDS    R5, R2
    // ADDS    R3, R1, R1
    // 算术运算
    // ADDS    R4, R0, R0
    // 算术运算
    // LSRS    R4, R4, #0x18
    // BEQ     loc_46306
    // 条件跳转
    // LSRS    R3, R3, #0x18
    // BEQ     loc_46318
    // 条件跳转
    // CMP     R4, #0xFF
    // 比较操作
    // BEQ     loc_46326
    // 条件跳转
    // CMP     R3, #0xFF
    // 比较操作
    // BEQ     loc_46330
    // 条件跳转
    // SUBS    R4, R4, R3
    // 算术运算
    // LSLS    R0, R0, #8
    // LSLS    R1, R1, #8
    // ORRS    R0, R2
    // ORRS    R1, R2
    // LSRS    R3, R0, #1
    // LSRS    R0, R1, #1
    // LSRS    R1, R2, #0x17
    // SUBS    R3, R3, R0
    // 算术运算
    // BCS     loc_462DA
    // SUBS    R4, R4, #1
    // 算术运算
    // ADDS    R3, R3, R3
    // 算术运算
    // ADDS    R3, R3, R0
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46376
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_46376(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV     R3, R0
    // ORRS    R3, R1
    // BMI     loc_4634A
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4637C
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_4637c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0
    // R2 = 0;
    // LSRS    R3, R0, #8
    // CMP     R3, R1
    // 比较操作
    // BCS     loc_4639C
    // LSRS    R3, R0, #4
    // CMP     R3, R1
    // 比较操作
    // BCS     loc_463BE
    // LSRS    R3, R0, #1
    // CMP     R3, R1
    // 比较操作
    // BCS     loc_463EE
    // SUBS    R1, R0, R1
    // 算术运算
    // BCS     loc_46396
    // MOV     R1, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4642A
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_4642a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0x200000
    // R2 = 0x200000;
    // ADDS    R3, R1, R1
    // 算术运算
    // BCS     loc_464D4
    // CMN     R3, R2
    // 比较操作
    // BCS     locret_464D2
    // LSRS    R3, R3, #0x15
    // BEQ     locret_464D2
    // 条件跳转
    // PUSH    {R4,R5}
    // 栈操作
    // LSLS    R4, R3, #0x14
    // BICS    R1, R4
    // LSRS    R5, R2, #1
    // ORRS    R1, R5
    // LSRS    R4, R2, #0xB
    // SUBS    R4, R4, #3
    // 算术运算
    // ADDS    R3, R3, R4
    // 算术运算
    // LSRS    R3, R3, #1
    // BCC     loc_46452
    // ADDS    R0, R0, R0
    // 算术运算
    // ADCS    R1, R1
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_464E8
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_464e8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x7F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0x80000000
    // R2 = 0x80000000;
    // ADDS    R1, R0, R0
    // 算术运算
    // LSRS    R1, R1, #0x18
    // SUBS    R1, #0x7F
    // 算术运算
    // BMI     loc_46516
    // NEGS    R1, R1
    // ADDS    R1, #0x1F
    // 算术运算
    // BMI     loc_4651A
    // ADDS    R0, R0, R0
    // 算术运算
    // BCS     loc_4650A
    // LSLS    R0, R0, #7
    // ORRS    R0, R2
    // LSRS    R0, R1
    // BPL     locret_46508
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46524
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_46524(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // varg_r2= -8
    // varg_r3= -4
    // PUSH    {R2,R3}
    // 栈操作
    // MOVS    R2, #0x1000000
    // R2 = 0x1000000;
    // ADDS    R3, R0, R0
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BHI     loc_46540
    // ADDS    R3, R1, R1
    // 算术运算
    // CMN     R2, R3
    // 比较操作
    // BHI     loc_46540
    // MOVS    R2, R0
    // ORRS    R2, R1
    // ADDS    R2, R2, R2
    // 算术运算
    // BCS     loc_46544
    // CMP     R0, R1
    // 比较操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4654A
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_4654a(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_F= -0xF
    // var_A= -0xA
    // var_9= -9
    // var_8= -8
    // var_7= -7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_46594
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_46594(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000783A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x200078B7
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_465AA
    // 条件跳转
    // LDR     R0, =0x2000783A
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_465AA
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_465AC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_465B0
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_465b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000783A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x200078B7
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_465C6
    // 条件跳转
    // LDR     R0, =0x2000783A
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BNE     loc_465C6
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_465C8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_465CC
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_465cc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000783A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x200078B7
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_465E2
    // 条件跳转
    // LDR     R0, =0x2000783A
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #2
    // 比较操作
    // BNE     loc_465E2
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_465E4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_465E8
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_465e8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R7, R1
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4663C
 * @note 指令数: 48, 标签数: 0
 */
void precise_func_4663c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20006344;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R0,R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #4
    // 算术运算
    // MOVS    R6, R2
    // MOVS    R5, R3
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R0, =0x8007852
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20006344
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // LDR     R0, =0x8007852
    // 内存加载操作
    // LDRB    R0, [R0,#1]
    // 内存加载操作
    // LDR     R1, =0x20006344
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // MOVS    R0, #0x16
    // R0 = 0x16;
    // LDR     R1, =0x20006344
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x20+var_1C]
    // 内存加载操作
    // LDR     R1, =0x20006344
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x20+var_18]
    // 内存加载操作
    // LDR     R1, =0x20006344
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // MOVS    R0, R5
    // LDR     R1, =0x20006344
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_46698
    // 条件跳转
    // UXTH    R5, R5
    // 数据扩展操作
    // LDR     R0, =0x20006344
    // 内存加载操作
    // ADDS    R7, R0, R4
    // 算术运算
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // UXTH    R5, R5
    // 数据扩展操作
    // ADDS    R4, R4, R5
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_466CA
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_466ca(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000783A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R1-R7,LR}
    // 栈操作
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xA8
    // 比较操作
    // BNE     loc_466DC
    // 条件跳转
    // MOVS    R0, #4
    // R0 = 4;
    // LDR     R1, =0x2000783A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // B       loc_466E4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_467A0
 * @note 指令数: 90, 标签数: 0
 */
void precise_func_467a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007788;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007884;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007714;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007892;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R1-R7,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // LDRB    R0, [R0,#2]
    // 内存加载操作
    // CMP     R0, #7
    // 比较操作
    // BNE     locret_468A8
    // 条件跳转
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // LSLS    R0, R0, #0x1C
    // LSRS    R0, R0, #0x1C
    // LDR     R1, =0x200078A8
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // LDRB    R0, [R0,#4]
    // 内存加载操作
    // LSLS    R0, R0, #0x1C
    // LSRS    R0, R0, #0x1C
    // LDR     R1, =0x200078A4
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #4
    // LDR     R1, =0x20007892
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // LDRB    R0, [R0,#4]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #4
    // LDR     R1, =0x20007884
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #2
    // R0 = 2;
    // MOVS    R4, R0
    // MOVS    R0, #4
    // R0 = 4;
    // STR     R0, [SP,#0x20+var_1C]
    // 内存存储操作
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS    R0, R0, R4
    // 算术运算
    // ADDS    R0, R0, #4
    // 算术运算
    // STR     R0, [SP,#0x20+var_18]
    // 内存存储操作
    // MOV     R5, SP
    // LDR     R2, [SP,#0x20+var_1C]
    // 内存加载操作
    // LDR     R1, [SP,#0x20+var_18]
    // 内存加载操作
    // MOVS    R0, R5
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // LDR     R0, [SP,#0x20+var_20]
    // 内存加载操作
    // LDR     R1, =0x20007710
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // ADDS    R4, R4, #4
    // 算术运算
    // MOVS    R5, #4
    // R5 = 4;
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS    R6, R0, R4
    // 算术运算
    // ADDS    R6, R6, #4
    // 算术运算
    // MOV     R7, SP
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // LDR     R0, [SP,#0x20+var_20]
    // 内存加载操作
    // LDR     R1, =0x20007714
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // ADDS    R4, R4, #4
    // 算术运算
    // LDR     R0, =0x200078A4
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_46858
    // 条件跳转
    // LDR     R0, =0x200078A4
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #7
    // 算术运算
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R5, R0
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS    R6, R0, R4
    // 算术运算
    // ADDS    R6, R6, #4
    // 算术运算
    // LDR     R7, =0x20007788
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // LDR     R0, =0x200078A4
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #7
    // 算术运算
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // ADDS    R4, R4, R0
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_468B8
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_468b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200060E8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // LDRB    R0, [R0,#1]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BGE     loc_468D0
    // 条件跳转
    // LDR     R0, =0x200060E8
    // 内存加载操作
    // LDRB    R0, [R0,#2]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_468D0
    // 条件跳转
    // BL      sub_466CA
    // 调用函数: sub_466CA();
    // B       loc_468D4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_468DC
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_468dc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x18+var_18]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_468F2
    // 条件跳转
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_46926
    // 条件跳转
    // CMP     R0, #6
    // 比较操作
    // BEQ     loc_46952
    // 条件跳转
    // B       loc_469B2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_469D0
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_469d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x200078B7
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_469EA
    // 条件跳转
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, R4
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_4663C
    // 调用函数: sub_4663C();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_469FC
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_469fc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B7;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078B2;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x200078B7
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_46A0C
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078B2
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

