#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建简单的头文件，只包含函数声明
"""

import os
import re
import glob

def extract_function_names():
    """提取所有函数名"""
    batch_files = glob.glob("converted_functions/batch_*.c")
    batch_files.sort()
    
    function_names = []
    
    for batch_file in batch_files:
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式查找函数定义
            pattern = r'^(void|uint32_t|float)\s+(\w+)\s*\([^)]*\)'
            matches = re.findall(pattern, content, re.MULTILINE)
            
            for return_type, func_name in matches:
                # 重新构建函数签名
                # 查找完整的参数列表
                func_pattern = rf'{return_type}\s+{func_name}\s*\([^)]*\)'
                full_match = re.search(func_pattern, content)
                if full_match:
                    function_names.append(full_match.group(0))
        
        except Exception as e:
            print(f"处理 {batch_file} 时出错: {e}")
    
    return function_names

def create_simple_header():
    """创建简单的头文件"""
    print("创建简单的头文件...")
    
    function_names = extract_function_names()
    
    header_content = """#ifndef MH25QH128_FUNCTIONS_H
#define MH25QH128_FUNCTIONS_H

#include <stdint.h>

// ============================================================================
// MH25QH128.bin.asm 所有转换函数的声明
// 总计 2380 个函数，从汇编代码转换为C语言实现
// ============================================================================

"""
    
    # 添加所有函数声明
    for func_sig in function_names:
        header_content += f"extern {func_sig};\n"
    
    header_content += """
#endif // MH25QH128_FUNCTIONS_H
"""
    
    # 保存头文件
    with open("mh25qh128_functions_simple.h", 'w', encoding='utf-8') as f:
        f.write(header_content)
    
    print(f"简单头文件已创建: mh25qh128_functions_simple.h")
    print(f"包含 {len(function_names)} 个函数声明")

if __name__ == "__main__":
    create_simple_header()
