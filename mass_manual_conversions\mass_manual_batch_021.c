// 大规模手工转换批次 21 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_76870
 * @note 指令数: 2
 */
void func_76870(void)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_76874
 * @note 指令数: 22
 */
void func_76874(void)
{
    // 内存地址定义
    volatile uint32_t *addr_100 = (volatile uint32_t *)0x100;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_768A8
 * @note 指令数: 22
 */
void func_768a8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_100 = (volatile uint32_t *)0x100;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_768D8
 * @note 指令数: 47
 */
void func_768d8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7693E
 * @note 指令数: 42
 */
void func_7693e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76998
 * @note 指令数: 185
 */
void func_76998(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_461C4000 = (volatile uint32_t *)0x461C4000;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_2E = (volatile uint32_t *)0x2E;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76B34
 * @note 指令数: 26
 */
void func_76b34(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_C0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76B68
 * @note 指令数: 34
 */
void func_76b68(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76BAA
 * @note 指令数: 34
 */
void func_76baa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76BEC
 * @note 指令数: 31
 */
void func_76bec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_C0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_76C2E
 * @note 指令数: 158
 */
void func_76c2e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_A1 = (volatile uint32_t *)0xA1;
    volatile uint32_t *addr_5E = (volatile uint32_t *)0x5E;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76D64
 * @note 指令数: 200
 */
void func_76d64(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_80124B4 = (volatile uint32_t *)0x80124B4;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76EF0
 * @note 指令数: 79
 */
void func_76ef0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_80124A4 = (volatile uint32_t *)0x80124A4;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76F94
 * @note 指令数: 3
 */
uint8_t func_76f94(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003743 = (volatile uint32_t *)0x20003743;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_76FA8
 * @note 指令数: 170
 */
void func_76fa8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003614 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *addr_80124AC = (volatile uint32_t *)0x80124AC;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_20003745 = (volatile uint32_t *)0x20003745;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7711C
 * @note 指令数: 31
 */
void func_7711c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003624 = (volatile uint32_t *)0x20003624;
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_77178
 * @note 指令数: 137
 */
void func_77178(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_C0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *addr_2000361C = (volatile uint32_t *)0x2000361C;
    volatile uint32_t *addr_20003624 = (volatile uint32_t *)0x20003624;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_772B0
 * @note 指令数: 29
 */
void func_772b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_FFFF = (volatile uint32_t *)0xFFFF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_772E8
 * @note 指令数: 30
 */
void func_772e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_20003604 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *addr_2000362C = (volatile uint32_t *)0x2000362C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7732A
 * @note 指令数: 17
 */
void func_7732a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003746 = (volatile uint32_t *)0x20003746;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_77350
 * @note 指令数: 10
 */
void func_77350(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003746 = (volatile uint32_t *)0x20003746;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_77362
 * @note 指令数: 4
 */
uint32_t func_77362(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003746 = (volatile uint32_t *)0x20003746;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7737C
 * @note 指令数: 68
 */
void func_7737c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000374A = (volatile uint32_t *)0x2000374A;
    volatile uint32_t *addr_2000362C = (volatile uint32_t *)0x2000362C;
    volatile uint32_t *addr_20003746 = (volatile uint32_t *)0x20003746;
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_77416
 * @note 指令数: 3
 */
uint8_t func_77416(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003747 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_77430
 * @note 指令数: 96
 */
void func_77430(void)
{
    // 内存地址定义
    volatile uint32_t *addr_90 = (volatile uint32_t *)0x90;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200036FE = (volatile uint32_t *)0x200036FE;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_77508
 * @note 指令数: 71
 */
void func_77508(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000374B = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *addr_20003614 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *addr_803F004 = (volatile uint32_t *)0x803F004;
    volatile uint32_t *addr_803F000 = (volatile uint32_t *)0x803F000;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_775A0
 * @note 指令数: 57
 */
void func_775a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_20003744 = (volatile uint32_t *)0x20003744;
    volatile uint32_t *addr_20003604 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *addr_2000360C = (volatile uint32_t *)0x2000360C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7762C
 * @note 指令数: 21
 */
void func_7762c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003743 = (volatile uint32_t *)0x20003743;
    volatile uint32_t *addr_B8 = (volatile uint32_t *)0xB8;
    volatile uint32_t *addr_8011700 = (volatile uint32_t *)0x8011700;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_77688
 * @note 指令数: 158
 */
void func_77688(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003744 = (volatile uint32_t *)0x20003744;
    volatile uint32_t *addr_20003604 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *addr_2000360C = (volatile uint32_t *)0x2000360C;
    volatile uint32_t *addr_B8 = (volatile uint32_t *)0xB8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_77834
 * @note 指令数: 8
 */
uint32_t func_77834(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_77844
 * @note 指令数: 7
 */
uint32_t func_77844(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;
    volatile uint32_t *addr_E000E180 = (volatile uint32_t *)0xE000E180;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_77852
 * @note 指令数: 27
 */
uint32_t func_77852(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_778A4
 * @note 指令数: 437
 */
void func_778a4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_90 = (volatile uint32_t *)0x90;
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_780E4
 * @note 指令数: 73
 */
void func_780e4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000374B = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *addr_FEFF = (volatile uint32_t *)0xFEFF;
    volatile uint32_t *addr_20003614 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *addr_803F004 = (volatile uint32_t *)0x803F004;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_78198
 * @note 指令数: 30
 */
void func_78198(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200035FC = (volatile uint32_t *)0x200035FC;
    volatile uint32_t *addr_200035EC = (volatile uint32_t *)0x200035EC;
    volatile uint32_t *addr_20003742 = (volatile uint32_t *)0x20003742;
    volatile uint32_t *addr_20003741 = (volatile uint32_t *)0x20003741;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_781E4
 * @note 指令数: 50
 */
void func_781e4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20001FF8 = (volatile uint32_t *)0x20001FF8;
    volatile uint32_t *addr_84 = (volatile uint32_t *)0x84;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7824C
 * @note 指令数: 31
 */
void func_7824c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_72 = (volatile uint32_t *)0x72;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;
    volatile uint32_t *addr_20003739 = (volatile uint32_t *)0x20003739;
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_78298
 * @note 指令数: 47
 */
void func_78298(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_84 = (volatile uint32_t *)0x84;
    volatile uint32_t *addr_20001FF8 = (volatile uint32_t *)0x20001FF8;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7832C
 * @note 指令数: 463
 */
void func_7832c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_90 = (volatile uint32_t *)0x90;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_200036F4 = (volatile uint32_t *)0x200036F4;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7877C
 * @note 指令数: 23
 */
uint32_t func_7877c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200035FC = (volatile uint32_t *)0x200035FC;
    volatile uint32_t *addr_20003742 = (volatile uint32_t *)0x20003742;
    volatile uint32_t *addr_200035F4 = (volatile uint32_t *)0x200035F4;
    volatile uint32_t *addr_3E8 = (volatile uint32_t *)0x3E8;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_787E8
 * @note 指令数: 128
 */
void func_787e8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200035FC = (volatile uint32_t *)0x200035FC;
    volatile uint32_t *addr_B8 = (volatile uint32_t *)0xB8;
    volatile uint32_t *addr_20003742 = (volatile uint32_t *)0x20003742;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_788F0
 * @note 指令数: 3
 */
uint8_t func_788f0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003741 = (volatile uint32_t *)0x20003741;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_78930
 * @note 指令数: 10
 */
uint32_t func_78930(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_78944
 * @note 指令数: 30
 */
void func_78944(void)
{
    // 内存地址定义
    volatile uint32_t *addr_7895C = (volatile uint32_t *)0x7895C;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7897C
 * @note 指令数: 17
 */
uint32_t func_7897c(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7899E
 * @note 指令数: 1
 */
void func_7899e(void)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_789A0
 * @note 指令数: 15
 */
uint32_t func_789a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200036EC = (volatile uint32_t *)0x200036EC;
    volatile uint32_t *addr_200035B4 = (volatile uint32_t *)0x200035B4;
    volatile uint32_t *addr_2000368C = (volatile uint32_t *)0x2000368C;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_789C2
 * @note 指令数: 3
 */
uint32_t func_789c2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003680 = (volatile uint32_t *)0x20003680;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_789C8
 * @note 指令数: 3
 */
uint32_t func_789c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003688 = (volatile uint32_t *)0x20003688;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_789CE
 * @note 指令数: 29
 */
void func_789ce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003670 = (volatile uint32_t *)0x20003670;
    volatile uint32_t *addr_20003674 = (volatile uint32_t *)0x20003674;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

