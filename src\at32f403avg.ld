/**
 * @file at32f403avg.ld
 * @brief Linker script for AT32F403AVG - converted from ASM memory layout
 * @date 2024
 * 
 * Memory layout and section definitions for AT32F403AVG microcontroller
 * Based on the original ASM file memory organization
 */

/* =============================================================================
 * Memory Layout (from ASM analysis)
 * =============================================================================
 * 
 * Original ASM memory layout:
 * - Boot loader: 0x08000000 - 0x08001FFF (8KB)
 * - Application: 0x08002000 - 0x080FFFFF (1016KB)
 * - MAC Address: 0x08001810
 * - RAM:         0x20000000 - 0x20017FFF (96KB)
 * - Stack:       0x20000618 (initial SP from ASM)
 */

/* Entry point */
ENTRY(reset_handler)

/* Memory regions */
MEMORY
{
    /* Flash memory - total 1MB */
    FLASH_BOOT (rx)  : ORIGIN = 0x08000000, LENGTH = 8K      /* Boot loader */
    FLASH_MAC (r)    : ORIGIN = 0x08001810, LENGTH = 16      /* MAC address */
    FLASH_APP (rx)   : ORIGIN = 0x08002000, LENGTH = 1016K   /* Application */
    
    /* RAM memory - total 96KB */
    RAM (rwx)        : ORIGIN = 0x20000000, LENGTH = 96K
    
    /* Special regions for data storage */
    LOGO_DATA (r)    : ORIGIN = 0x08016A54, LENGTH = 256     /* Logo data location */
}

/* Stack size (from ASM initial SP) */
_stack_size = 0x618;

/* Heap size */
_heap_size = 0x200;

/* =============================================================================
 * Section Definitions
 * =============================================================================
 */

SECTIONS
{
    /* Vector table and startup code */
    .isr_vector :
    {
        . = ALIGN(4);
        KEEP(*(.isr_vector))    /* Vector table from startup file */
        . = ALIGN(4);
    } > FLASH_BOOT
    
    /* Program code and constants */
    .text :
    {
        . = ALIGN(4);
        *(.text)                /* Program code */
        *(.text*)
        *(.rodata)              /* Read-only data */
        *(.rodata*)
        *(.glue_7)              /* ARM/Thumb interworking */
        *(.glue_7t)
        *(.eh_frame)
        
        KEEP (*(.init))
        KEEP (*(.fini))
        
        . = ALIGN(4);
        _etext = .;             /* End of code section */
    } > FLASH_BOOT
    
    /* ARM exception handling */
    .ARM.extab :
    {
        *(.ARM.extab* .gnu.linkonce.armextab.*)
    } > FLASH_BOOT
    
    .ARM :
    {
        __exidx_start = .;
        *(.ARM.exidx*)
        __exidx_end = .;
    } > FLASH_BOOT
    
    /* Constructor/destructor tables */
    .preinit_array :
    {
        PROVIDE_HIDDEN (__preinit_array_start = .);
        KEEP (*(.preinit_array*))
        PROVIDE_HIDDEN (__preinit_array_end = .);
    } > FLASH_BOOT
    
    .init_array :
    {
        PROVIDE_HIDDEN (__init_array_start = .);
        KEEP (*(SORT(.init_array.*)))
        KEEP (*(.init_array*))
        PROVIDE_HIDDEN (__init_array_end = .);
    } > FLASH_BOOT
    
    .fini_array :
    {
        PROVIDE_HIDDEN (__fini_array_start = .);
        KEEP (*(SORT(.fini_array.*)))
        KEEP (*(.fini_array*))
        PROVIDE_HIDDEN (__fini_array_end = .);
    } > FLASH_BOOT
    
    /* Initialized data section */
    .data :
    {
        . = ALIGN(4);
        _sdata = .;             /* Start of data section in RAM */
        *(.data)                /* Initialized data */
        *(.data*)
        
        . = ALIGN(4);
        _edata = .;             /* End of data section in RAM */
    } > RAM AT> FLASH_BOOT
    
    _sidata = LOADADDR(.data); /* Start of initialization data in flash */
    
    /* Uninitialized data section */
    .bss :
    {
        . = ALIGN(4);
        _sbss = .;              /* Start of BSS section */
        __bss_start__ = _sbss;
        *(.bss)
        *(.bss*)
        *(COMMON)
        
        . = ALIGN(4);
        _ebss = .;              /* End of BSS section */
        __bss_end__ = _ebss;
    } > RAM
    
    /* Heap section */
    .heap :
    {
        . = ALIGN(4);
        _heap_start = .;
        . = . + _heap_size;
        . = ALIGN(4);
        _heap_end = .;
    } > RAM
    
    /* Stack section */
    .stack :
    {
        . = ALIGN(8);
        _stack_end = .;
        . = . + _stack_size;
        . = ALIGN(8);
        _stack_start = .;
    } > RAM
    
    /* MAC address section (from ASM MAC_Addr location) */
    .mac_address :
    {
        . = ALIGN(4);
        KEEP(*(.mac_address))
        . = ALIGN(4);
    } > FLASH_MAC
    
    /* Logo data section (from ASM logo data location) */
    .logo_data :
    {
        . = ALIGN(4);
        *(.logo_data)
        . = ALIGN(4);
    } > LOGO_DATA
    
    /* Application section (for future application code) */
    .application :
    {
        . = ALIGN(4);
        *(.application)
        . = ALIGN(4);
    } > FLASH_APP
    
    /* Debug sections */
    .debug_info     0 : { *(.debug_info .gnu.linkonce.wi.*) }
    .debug_abbrev   0 : { *(.debug_abbrev) }
    .debug_line     0 : { *(.debug_line) }
    .debug_frame    0 : { *(.debug_frame) }
    .debug_str      0 : { *(.debug_str) }
    .debug_loc      0 : { *(.debug_loc) }
    .debug_macinfo  0 : { *(.debug_macinfo) }
    .debug_ranges   0 : { *(.debug_ranges) }
    .debug_pubtypes 0 : { *(.debug_pubtypes) }
    .debug_pubnames 0 : { *(.debug_pubnames) }
    
    /* ARM-specific debug sections */
    .ARM.attributes 0 : { *(.ARM.attributes) }
    .comment        0 : { *(.comment) }
    
    /* Discard unwanted sections */
    /DISCARD/ :
    {
        libc.a ( * )
        libm.a ( * )
        libgcc.a ( * )
    }
}

/* =============================================================================
 * Symbol Definitions
 * =============================================================================
 */

/* Provide symbols for memory regions */
_flash_start = ORIGIN(FLASH_BOOT);
_flash_size = LENGTH(FLASH_BOOT) + LENGTH(FLASH_APP);
_ram_start = ORIGIN(RAM);
_ram_size = LENGTH(RAM);

/* Stack pointer initial value (from ASM) */
_initial_sp = _stack_start;

/* Check for stack overflow */
ASSERT(_stack_end >= _heap_end, "Stack and heap collision detected!")

/* Check flash usage */
ASSERT(_etext <= (ORIGIN(FLASH_BOOT) + LENGTH(FLASH_BOOT)), "Flash boot section overflow!")

/* Provide end of RAM for heap management */
_end = _heap_end;
