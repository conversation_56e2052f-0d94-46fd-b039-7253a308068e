#ifndef AT32F403AVG_FIRMWARE_CONVERSION_H
#define AT32F403AVG_FIRMWARE_CONVERSION_H

#include <stdint.h>
#include <stdbool.h>

// ============================================================================
// AT32F403AVG 固件转换基础定义
// 为MH25QH128.bin.asm转换提供基础类型和宏定义
// ============================================================================

// 基础数据类型定义
typedef uint8_t  u8;
typedef uint16_t u16;
typedef uint32_t u32;
typedef int8_t   s8;
typedef int16_t  s16;
typedef int32_t  s32;

// ARM Cortex-M4 寄存器映射
#define REG_R0  0
#define REG_R1  1
#define REG_R2  2
#define REG_R3  3

// 常用内存地址定义（基于AT32F403AVG）
#define FLASH_BASE          0x08000000UL
#define SRAM_BASE           0x20000000UL
#define PERIPH_BASE         0x40000000UL

// GPIO寄存器基地址
#define GPIOA_BASE          (PERIPH_BASE + 0x10800)
#define GPIOB_BASE          (PERIPH_BASE + 0x10C00)
#define GPIOC_BASE          (PERIPH_BASE + 0x11000)
#define GPIOD_BASE          (PERIPH_BASE + 0x11400)

// 定时器寄存器基地址
#define TMR1_BASE           (PERIPH_BASE + 0x12C00)
#define TMR2_BASE           (PERIPH_BASE + 0x00000)
#define TMR3_BASE           (PERIPH_BASE + 0x00400)
#define TMR4_BASE           (PERIPH_BASE + 0x00800)

// USART寄存器基地址
#define USART1_BASE         (PERIPH_BASE + 0x13800)
#define USART2_BASE         (PERIPH_BASE + 0x04400)
#define USART3_BASE         (PERIPH_BASE + 0x04800)

// SPI寄存器基地址
#define SPI1_BASE           (PERIPH_BASE + 0x13000)
#define SPI2_BASE           (PERIPH_BASE + 0x03800)

// I2C寄存器基地址
#define I2C1_BASE           (PERIPH_BASE + 0x05400)
#define I2C2_BASE           (PERIPH_BASE + 0x05800)

// ADC寄存器基地址
#define ADC1_BASE           (PERIPH_BASE + 0x12400)
#define ADC2_BASE           (PERIPH_BASE + 0x12800)

// 常用位操作宏
#define BIT(n)              (1UL << (n))
#define SET_BIT(reg, bit)   ((reg) |= (bit))
#define CLEAR_BIT(reg, bit) ((reg) &= ~(bit))
#define READ_BIT(reg, bit)  ((reg) & (bit))

// 内存访问宏
#define REG32(addr)         (*(volatile uint32_t *)(addr))
#define REG16(addr)         (*(volatile uint16_t *)(addr))
#define REG8(addr)          (*(volatile uint8_t *)(addr))

// 函数返回状态定义
typedef enum {
    FUNC_SUCCESS = 0,
    FUNC_ERROR   = 1,
    FUNC_BUSY    = 2,
    FUNC_TIMEOUT = 3
} func_status_t;

// 浮点数处理相关定义
#define FLOAT_PRECISION     0.0001f
#define FLOAT_MAX_VALUE     3.4028235e+38f
#define FLOAT_MIN_VALUE     1.175494e-38f

// 数据处理相关定义
#define DATA_BUFFER_SIZE    256
#define MAX_PARAM_COUNT     8

// 配置管理相关定义
#define CONFIG_MAGIC_NUMBER 0x12345678UL
#define CONFIG_VERSION      0x0001

// 通信处理相关定义
#define COMM_TIMEOUT_MS     1000
#define COMM_RETRY_COUNT    3

// 系统服务相关定义
#define SYSTEM_CLOCK_HZ     144000000UL
#define TICK_RATE_HZ        1000UL

// 中断向量表相关定义
#define NVIC_PRIO_BITS      4
#define NVIC_PRIO_MASK      0x0F

// 内存保护相关宏
#define MEMORY_BARRIER()    __asm volatile ("dmb" ::: "memory")
#define INSTRUCTION_BARRIER() __asm volatile ("isb" ::: "memory")

// 调试相关宏
#ifdef DEBUG
#define DEBUG_PRINT(fmt, ...) printf(fmt, ##__VA_ARGS__)
#else
#define DEBUG_PRINT(fmt, ...)
#endif

// 错误处理宏
#define ASSERT(condition) \
    do { \
        if (!(condition)) { \
            while(1); \
        } \
    } while(0)

// 函数属性宏
#define INLINE              __inline
#define STATIC_INLINE       static __inline
#define WEAK                __weak
#define PACKED              __packed

// 对齐宏
#define ALIGN_4             __attribute__((aligned(4)))
#define ALIGN_8             __attribute__((aligned(8)))

// 常用数学宏
#define MIN(a, b)           ((a) < (b) ? (a) : (b))
#define MAX(a, b)           ((a) > (b) ? (a) : (b))
#define ABS(x)              ((x) < 0 ? -(x) : (x))

// 数组大小宏
#define ARRAY_SIZE(arr)     (sizeof(arr) / sizeof((arr)[0]))

// 字节序转换宏（小端序）
#define SWAP16(x)           ((((x) & 0xFF) << 8) | (((x) >> 8) & 0xFF))
#define SWAP32(x)           ((((x) & 0xFF) << 24) | \
                            (((x) & 0xFF00) << 8) | \
                            (((x) & 0xFF0000) >> 8) | \
                            (((x) >> 24) & 0xFF))

// 时间相关宏
#define MS_TO_TICKS(ms)     ((ms) * TICK_RATE_HZ / 1000)
#define TICKS_TO_MS(ticks)  ((ticks) * 1000 / TICK_RATE_HZ)

#endif // AT32F403AVG_FIRMWARE_CONVERSION_H
