<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_proj.xsd">

  <SchemaVersion>1.0</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>AT32F403AVG_Firmware</TargetName>
      <ToolsetNumber>0x3</ToolsetNumber>
      <ToolsetName>ARM-GNU</ToolsetName>
      <TargetOption>
        <TargetCommonOption>
          <Device>AT32F403AVG7</Device>
          <Vendor>ArteryTek</Vendor>
          <Cpu>IRAM(0x20000000-0x20017FFF) IROM(0x08000000-0x080FFFFF) CLOCK(8000000) CPUTYPE("Cortex-M4")</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile>"Startup\AT32F403AVG_startup.s" ("AT32F403AVG Startup Code")</StartupFile>
          <FlashDriverDll>UL2CM3(-O14 -S0 -C0 -N00("ARM Cortex-M4") -D00(2BA01477) -L00(4) -FO7 -********** -FC800 -FN1 -FF0AT32F403_1024 -********** -*********)</FlashDriverDll>
          <DeviceId>4118</DeviceId>
          <RegisterFile>AT32F403AVG7.H</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath>AT32F403AVG7\</RegisterFilePath>
          <DBRegisterFilePath>AT32F403AVG7\</DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>at32f403avg_firmware</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments></SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>7</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <Flash2>Segger\JL2CM3.dll</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
        </Utilities>
        <TargetArm>
          <ArmMisc>
            <asLst>0</asLst>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <GCPUTYP>"Cortex-M4"</GCPUTYP>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x18000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <IRAM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IRAM2>
              <IROM2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </IROM2>
            </OnChipMemories>
          </ArmMisc>
          <Carm>
            <arpcs>1</arpcs>
            <stkchk>0</stkchk>
            <reentr>0</reentr>
            <interw>1</interw>
            <bigend>0</bigend>
            <Strict>0</Strict>
            <Optim>0</Optim>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Carm>
          <Aarm>
            <bBE>0</bBE>
            <interw>1</interw>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aarm>
          <LDarm>
            <umfTarg>1</umfTarg>
            <enaGarb>0</enaGarb>
            <noStart>1</noStart>
            <noStLib>0</noStLib>
            <uMathLib>1</uMathLib>
            <TextAddressRange></TextAddressRange>
            <DataAddressRange></DataAddressRange>
            <BSSAddressRange></BSSAddressRange>
            <IncludeLibs></IncludeLibs>
            <IncludeDir></IncludeDir>
            <Misc></Misc>
            <ScatterFile></ScatterFile>
          </LDarm>
        </TargetArm>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>源文件</GroupName>
          <Files>
            <File>
              <FileName>at32f403avg_firmware.c</FileName>
              <FileType>1</FileType>
              <FilePath>../src/at32f403avg_firmware.c</FilePath>
            </File>
            <File>
              <FileName>logo_data.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\logo_data.c</FilePath>
            </File>
            <File>
              <FileName>startup_at32f403avg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\startup_at32f403avg.c</FilePath>
            </File>
            <File>
              <FileName>web_server.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\web_server.c</FilePath>
            </File>
            <File>
              <FileName>web_server_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\web_server_utils.c</FilePath>
            </File>
            <File>
              <FileName>web_page_generator.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\web_page_generator.c</FilePath>
            </File>
            <File>
              <FileName>crc_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\crc_utils.c</FilePath>
            </File>
            <File>
              <FileName>string_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\string_utils.c</FilePath>
            </File>
            <File>
              <FileName>communication_protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\communication_protocol.c</FilePath>
            </File>
            <File>
              <FileName>math_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\math_utils.c</FilePath>
            </File>
            <File>
              <FileName>hardware_drivers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\hardware_drivers.c</FilePath>
            </File>
            <File>
              <FileName>system_management.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\system_management.c</FilePath>
            </File>
            <File>
              <FileName>peripheral_management.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\peripheral_management.c</FilePath>
            </File>
            <File>
              <FileName>data_processing.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\data_processing.c</FilePath>
            </File>
            <File>
              <FileName>network_communication.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\network_communication.c</FilePath>
            </File>
            <File>
              <FileName>device_management.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\device_management.c</FilePath>
            </File>
            <File>
              <FileName>advanced_communication.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\advanced_communication.c</FilePath>
            </File>
            <File>
              <FileName>flash_storage.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\flash_storage.c</FilePath>
            </File>
            <File>
              <FileName>interrupt_service.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\interrupt_service.c</FilePath>
            </File>
            <File>
              <FileName>application_tasks.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\application_tasks.c</FilePath>
            </File>
            <File>
              <FileName>rtc_management.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rtc_management.c</FilePath>
            </File>
            <File>
              <FileName>config_management.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\config_management.c</FilePath>
            </File>
            <File>
              <FileName>debug_diagnostics.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\debug_diagnostics.c</FilePath>
            </File>
            <File>
              <FileName>low_level_hardware.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\low_level_hardware.c</FilePath>
            </File>
            <File>
              <FileName>advanced_string_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\advanced_string_utils.c</FilePath>
            </File>
            <File>
              <FileName>final_system_functions.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\final_system_functions.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
