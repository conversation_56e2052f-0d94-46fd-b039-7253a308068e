// 精确转换批次 1 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_14B18
 * @note 指令数: 5, 标签数: 0
 */
float precise_func_14b18(uint8_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;

    // 局部变量
    float result = 0.0f;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0x10
    // 比较操作
    // BLT     loc_14B24
    // 条件跳转
    // FLDS    S0, =0.0
    // 浮点数内存操作
    // B       locret_14B32
    // 无条件跳转

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_14B34
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_14b34(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000797C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R1, =0x2000797C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LDRH.W  R1, [R1,R0,LSL#1]
    // 内存加载操作
    // CMP     R1, #6
    // 比较操作
    // BLT     loc_14B4E
    // 条件跳转
    // MOVS    R1, #5
    // R1 = 5;
    // LDR.W   R2, =0x2000797C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRH.W  R1, [R2,R0,LSL#1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_14CB4
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_14cb4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008131;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080BE;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // BL      sub_16390
    // 调用函数: sub_16390();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x200080BE
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // BL      off_14B74
    // 调用函数: off_14B74();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008131
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_14E08
 * @note 指令数: 23, 标签数: 0
 */
void precise_func_14e08(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007EB0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016934;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // VPUSH   {D8}
    // MOVS    R5, R0
    // MOVS    R7, R1
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_1677A
    // 调用函数: sub_1677A();
    // LDR.W   R1, =0x8016934
    // 内存加载操作
    // LDR.W   R2, =0x20007EB0
    // 内存加载操作
    // UXTB    R5, R5
    // 数据扩展操作
    // LDRB    R2, [R5,R2]
    // 内存加载操作
    // LDRB    R1, [R2,R1]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, R1
    // 比较操作
    // BEQ     loc_14E46
    // 条件跳转
    // LDR.W   R0, =0x8016934
    // 内存加载操作
    // LDR.W   R1, =0x20007EB0
    // 内存加载操作
    // UXTB    R5, R5
    // 数据扩展操作
    // LDRB    R1, [R5,R1]
    // 内存加载操作
    // LDRB    R1, [R1,R0]
    // 内存加载操作
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_16766
    // 调用函数: sub_16766();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_15050
 * @note 指令数: 34, 标签数: 0
 */
void precise_func_15050(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xF0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007A5C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // VPUSH   {D8}
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, #8
    // R0 = 8;
    // SDIV.W  R0, R4, R0
    // MOV     R9, R0
    // MOVS    R0, #1
    // R0 = 1;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R2, R4, R1
    // MLS.W   R2, R2, R1, R4
    // LSLS    R0, R2
    // MOV     R10, R0
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_1675C
    // 调用函数: sub_1675C();
    // MOVS    R7, R0
    // LDR.W   R0, =0x20007A5C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRB.W  R0, [R0,R4,LSL#1]
    // MOVS    R1, #0xF0
    // R1 = 0xF0;
    // TST     R0, R1
    // 比较操作
    // BNE     loc_150F2
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
    // LDR.W   R0, =0x20007A5C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRH.W  R0, [R0,R4,LSL#1]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_150A8
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
    // B       loc_150C0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_154F4
 * @note 指令数: 38, 标签数: 0
 */
float precise_func_154f4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200075C4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007A5C;

    // 局部变量
    float result = 0.0f;
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // VPUSH   {D8}
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, #8
    // R0 = 8;
    // SDIV.W  R0, R4, R0
    // MOVS    R6, R0
    // MOVS    R0, #1
    // R0 = 1;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R2, R4, R1
    // MLS.W   R2, R2, R1, R4
    // LSLS    R0, R2
    // MOVS    R5, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
    // LDR     R0, =0x20007A5C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRH.W  R0, [R0,R4,LSL#1]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_155A8
    // 条件跳转
    // LDR.W   R0, =0x200075C4
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FLDS    S0, [R0]
    // 浮点数内存操作
    // FCMPZS  S0
    // VMRS    APSR_nzcv, FPSCR
    // BLT     loc_15564
    // 条件跳转
    // LDR.W   R0, =0x200075C4
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS.W  R0, R0, R4,LSL#2
    // 算术运算
    // FLDS    S0, [R0]
    // 浮点数内存操作
    // VMOV.F32 S1, #-4.0
    // FADDS   S0, S0, S1
    // VMOV.F32 S1, #16.0
    // FDIVS   S0, S0, S1
    // FCPYS   S16, S0
    // B       loc_15586
    // 无条件跳转

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_157C0
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_157c0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000799C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // VPUSH   {D8}
    // MOVS    R4, R0
    // LDR     R0, =0x2000799C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRH.W  R0, [R0,R4,LSL#1]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_157E0
    // 条件跳转
    // LDR     R0, =0x2000799C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRH.W  R0, [R0,R4,LSL#1]
    // 内存加载操作
    // CMP     R0, #0x11
    // 比较操作
    // BLT     loc_157EA
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_158F0
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_158f0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007EA8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // LDR.W   R2, =0x20007EA8
    // 内存加载操作
    // LDRH    R2, [R2]
    // 内存加载操作
    // CMP.W   R2, #0x3E8
    // BLT     loc_15940
    // 条件跳转
    // LDR.W   R2, =0x20007EA8
    // 内存加载操作
    // LDRH    R2, [R2]
    // 内存加载操作
    // SUBS.W  R2, R2, #0x3E8
    // LDR.W   R3, =0x20007EA8
    // 内存加载操作
    // STRH    R2, [R3]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, R2
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_15D3C
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_15d3c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008133;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007EA0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // BL      sub_1699C
    // 调用函数: sub_1699C();
    // LDR.W   R0, =0x20007EA0
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #5
    // 比较操作
    // BLT     loc_15D7E
    // 条件跳转
    // LDR.W   R0, =0x20007EA0
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // SUBS    R0, R0, #5
    // 算术运算
    // LDR.W   R1, =0x20007EA0
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20008133
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #6
    // 比较操作
    // BGE     loc_15D70
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008133
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       loc_15D7E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_162CE
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_162ce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008135;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R1, =0x20008135
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_162D6
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_162d6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008135;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R0, =0x20008135
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16390
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_16390(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x16;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_17= -0x17
    // var_16= -0x16
    // var_15= -0x15
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16444
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_16444(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007F30;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000806C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200080E4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_18318
    // 调用函数: sub_18318();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200080E4
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000806C
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007F30
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007F30
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16466
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_16466(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008060;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20008060
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1646C
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_1646c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008068;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20008068
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16472
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_16472(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008054;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_16480
    // 条件跳转
    // LDR     R2, =0x20008054
    // 内存加载操作
    // MOVS    R3, R2
    // B       loc_16484
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_164A4
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_164a4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000805C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_164B2
    // 条件跳转
    // LDR     R2, =0x2000805C
    // 内存加载操作
    // MOVS    R3, R2
    // B       loc_164B6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_164D6
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_164d6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008050;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000806C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200080E4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x200080E4
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_165BA
    // 条件跳转
    // CPSID   I
    // LDR     R0, =0x200080E4
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, R0
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R3, =0x200080E4
    // 内存加载操作
    // STRH    R0, [R3]
    // 内存存储操作
    // CPSIE   I
    // LDR     R0, =0x2000806C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // UXTAH.W R0, R0, R1
    // LDR     R3, =0x2000806C
    // 内存加载操作
    // STR     R0, [R3]
    // 内存存储操作
    // LDR     R0, =0x20008050
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R2, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_165BC
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_165bc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008060;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20008060
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_165CC
    // 条件跳转
    // LDR     R0, =0x20008060
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BLX     R0
    // 调用函数: R0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16640
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_16640(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000400;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000814B;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_1838A
    // 调用函数: sub_1838A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_1666E
    // 条件跳转
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_183A2
    // 调用函数: sub_183A2();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_1836E
    // 调用函数: sub_1836E();
    // MOVS    R0, #1
    // R0 = 1;
    // LDR.W   R1, =0x2000814B
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16670
 * @note 指令数: 23, 标签数: 0
 */
void precise_func_16670(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000400;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C0001;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xBB80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x1C0001
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #3
    // R1 = 3;
    // MOVS    R0, #0x1D
    // R0 = 0x1D;
    // BL      sub_1824C
    // 调用函数: sub_1824C();
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_18362
    // 调用函数: sub_18362();
    // MOVS    R2, #2
    // R2 = 2;
    // MOVW    R1, #0xBB80
    // R1 = 0xBB80;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_18354
    // 调用函数: sub_18354();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_18348
    // 调用函数: sub_18348();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_1836E
    // 调用函数: sub_1836E();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_166B6
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_166b6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000400;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_183A2
    // 调用函数: sub_183A2();
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_166D8
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40000400
    // 内存加载操作
    // BL      sub_1836E
    // 调用函数: sub_1836E();
    // B       locret_166E4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_166E6
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_166e6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000424;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x40000424
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_166F0
 * @note 指令数: 36, 标签数: 0
 */
void precise_func_166f0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000262;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007FD8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000814B;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008148;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8016670;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008148
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR.W   R1, =0x8016678
    // 内存加载操作
    // LDR.W   R0, =0x8016670
    // 内存加载操作
    // LDR     R0, [R0,#4]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R2, #1
    // R2 = 1;
    // MOVS    R1, #0x20 ; ' '
    // R1 = 0x20;
    // LDR.W   R0, =0x40012400
    // 内存加载操作
    // BL      sub_179BC
    // 调用函数: sub_179BC();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008149
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20007FD8
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BL      sub_16670
    // 调用函数: sub_16670();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_166B6
    // 调用函数: sub_166B6();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000814B
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x2000814A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20000262
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BNE     loc_16756
    // 条件跳转
    // BL      sub_18538
    // 调用函数: sub_18538();
    // B       locret_1675A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1675C
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_1675c(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077F0;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x200077F0
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LDR.W   R0, [R1,R0,LSL#2]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16766
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_16766(uint8_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008149;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007ED0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007FD8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, =0x20007ED0
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R0,R2]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x20007FD8
    // 内存加载操作
    // STR     R2, [R3]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x20008149
    // 内存加载操作
    // STRB    R2, [R3]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1677A
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_1677a(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007ED0;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20007ED0
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LDRB    R0, [R0,R1]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16782
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_16782(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40012400;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x40012400
    // 内存加载操作
    // BL      sub_17C5E
    // 调用函数: sub_17C5E();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1678E
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_1678e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008148;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007FDC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20008148
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x20008148
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20008148
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #8
    // 比较操作
    // BLT     locret_167B6
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20008148
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007FDC
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_167B6
    // 条件跳转
    // LDR     R0, =0x20007FDC
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BLX     R0
    // 调用函数: R0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_167B8
 * @note 指令数: 29, 标签数: 0
 */
void precise_func_167b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007ED0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000262;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008148;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // BL      sub_1678E
    // 调用函数: sub_1678E();
    // LDR     R0, =0x20008148
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #8
    // 比较操作
    // BGE     locret_16890
    // 条件跳转
    // LDR     R0, =0x20000262
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BNE     loc_16830
    // 条件跳转
    // LDR     R0, =0x20007ED0
    // 内存加载操作
    // LDR     R1, =0x20008148
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // LDRB    R0, [R1,R0]
    // 内存加载操作
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_167F4
    // 条件跳转
    // CMP     R0, #4
    // 比较操作
    // BEQ     loc_167FA
    // 条件跳转
    // CMP     R0, #5
    // 比较操作
    // BEQ     loc_16800
    // 条件跳转
    // CMP     R0, #8
    // 比较操作
    // BEQ     loc_16806
    // 条件跳转
    // CMP     R0, #0xA
    // 比较操作
    // BEQ     loc_1680C
    // 条件跳转
    // CMP     R0, #0x10
    // 比较操作
    // BEQ     loc_16812
    // 条件跳转
    // CMP     R0, #0x20 ; ' '
    // 比较操作
    // BEQ     loc_16818
    // 条件跳转
    // B       loc_1681E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16892
 * @note 指令数: 67, 标签数: 0
 */
void precise_func_16892(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007FD4;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007ED0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200077B8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008148;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200077F0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #2
    // R1 = 2;
    // LDR     R0, =0x40012400
    // 内存加载操作
    // BL      sub_17C94
    // 调用函数: sub_17C94();
    // CMP     R0, #1
    // 比较操作
    // BNE     locret_1699A
    // 条件跳转
    // MOVS    R1, #2
    // R1 = 2;
    // LDR     R0, =0x40012400
    // 内存加载操作
    // BL      sub_17CAE
    // 调用函数: sub_17CAE();
    // BL      sub_17C6A
    // 调用函数: sub_17C6A();
    // LDR     R1, =0x20007FD4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007FD4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20007828
    // 内存加载操作
    // LDR     R2, =0x20008148
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // STR.W   R0, [R1,R2,LSL#2]
    // 内存存储操作
    // LDR     R0, =0x20007ED8
    // 内存加载操作
    // LDR     R1, =0x20008148
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // LDRB    R0, [R1,R0]
    // 内存加载操作
    // LDR     R1, =0x20007ED0
    // 内存加载操作
    // LDR     R2, =0x20008148
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // LDRB    R1, [R2,R1]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BEQ     loc_1692C
    // 条件跳转
    // LDR     R0, =0x20007ED0
    // 内存加载操作
    // LDR     R1, =0x20008148
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // LDRB    R0, [R1,R0]
    // 内存加载操作
    // LDR     R1, =0x20007ED8
    // 内存加载操作
    // LDR     R2, =0x20008148
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // STRB    R0, [R2,R1]
    // 内存存储操作
    // LDR     R0, =0x20007FD4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x200077B8
    // 内存加载操作
    // LDR     R2, =0x20008148
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // STR.W   R0, [R1,R2,LSL#2]
    // 内存存储操作
    // LDR     R0, =0x200077B8
    // 内存加载操作
    // LDR     R1, =0x20008148
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // LDR.W   R0, [R0,R1,LSL#2]
    // 内存加载操作
    // LSLS    R0, R0, #0xA
    // LDR     R1, =0x200077B8
    // 内存加载操作
    // LDR     R2, =0x20008148
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // STR.W   R0, [R1,R2,LSL#2]
    // 内存存储操作
    // LDR     R0, =0x20007FD4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0xA
    // LDR     R1, =0x20007FD4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200077B8
    // 内存加载操作
    // LDR     R1, =0x20008148
    // 内存加载操作
    // LDRB    R1, [R1]
    // 内存加载操作
    // LDR.W   R0, [R0,R1,LSL#2]
    // 内存加载操作
    // ASRS    R0, R0, #0xA
    // LDR     R1, =0x200077F0
    // 内存加载操作
    // LDR     R2, =0x20008148
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // STR.W   R0, [R1,R2,LSL#2]
    // 内存存储操作
    // BL      sub_16782
    // 调用函数: sub_16782();
    // B       locret_1699A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1699C
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_1699c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000814A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x2000814A
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_169B2
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000814A
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_166B6
    // 调用函数: sub_166B6();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16A18
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_16a18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x70000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38000000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS.W  R12, #0xFF
    // ANDS.W  R1, R12, R0,LSR#23
    // ITE NE
    // CMPNE   R1, R12
    // BEQ     loc_16A34
    // 条件跳转
    // ASRS    R1, R0, #3
    // LSLS    R0, R0, #0x1D
    // BIC.W   R1, R1, #0x70000000
    // ADD.W   R1, R1, #0x38000000
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16ADC
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_16adc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x7FF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,R7,LR}
    // 栈操作
    // EOR.W   R12, R1, R3
    // AND.W   R12, R12, #0x80000000
    // MOVW    R5, #0x7FF
    // R5 = 0x7FF;
    // ANDS.W  R4, R5, R1,LSR#20
    // ITTTE NE
    // ANDSNE.W R7, R5, R3,LSR#20
    // CMPNE   R4, R5
    // CMPNE   R7, R5
    // BEQ     loc_16B6E
    // 条件跳转
    // ADDS    R4, R4, R7
    // 算术运算
    // BIC.W   R3, R3, R5,LSL#21
    // ORR.W   R3, R3, #0x100000
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16C7E
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_16c7e(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, [R0]
    // 内存加载操作
    // ADDS    R3, R2, #1
    // 算术运算
    // STR     R3, [R0]
    // 内存存储操作
    // STRB    R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16C88
 * @note 指令数: 16, 标签数: 0
 */
uint32_t precise_func_16c88(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x70000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x3FC00000;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOV.W   R12, R1,ROR#31
    // SUBS.W  R12, R12, #0x70000000
    // ITE CS
    // CMPCS.W R12, #0x200000
    // BCC     loc_16CBC
    // MOVS.W  R12, R12,LSL#1
    // ITE CC
    // CMPCC.W R12, #0x3FC00000
    // BCS     loc_16D02
    // MOVS.W  R12, R12,LSR#2
    // MOV.W   R12, R12,LSL#4
    // MOV.W   R12, R12,RRX
    // LSLS    R1, R0, #3
    // SBCS.W  R1, R1, #0x80000000
    // ADC.W   R0, R12, R0,LSR#29
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16D18
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_16d18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // BPL.W   loc_16D30
    // MOV     R12, LR
    // NEGS    R0, R0
    // BL      loc_16D30
    // 调用函数: loc_16D30();
    // ORRS.W  R1, R1, #0x80000000
    // BX      R12
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16D2C
 * @note 指令数: 10, 标签数: 1
 */
void precise_func_16d2c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x420;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // NOP
    // BEQ     locret_16D46
    // 条件跳转
    // CLZ.W   R1, R0
    // LSLS    R0, R1
    // ADDS    R1, R1, #3
    // 算术运算
    // RSB.W   R1, R1, #0x420
    // LSLS    R1, R1, #0x14
    // ADD.W   R1, R1, R0,LSR#11
    // LSLS    R0, R0, #0x15
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16D48
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_16d48(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // MOV.W   R5, #0x80000000
    // TEQ.W   R1, R3
    // ITT MI
    // EORMI   R3, R5
    // BMI.W   loc_16FDC
    // NOP
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_170B0
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_170b0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_F= -0xF
    // var_E= -0xE
    // var_A= -0xA
    // var_9= -9
    // var_8= -8
    // var_7= -7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_170FA
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_170fa(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200080E6;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008163;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R0, =0x20008163
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_17112
    // 条件跳转
    // LDR.W   R0, =0x200080E6
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_17112
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_17114
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17118
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_17118(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200080E6;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008163;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R0, =0x20008163
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_17130
    // 条件跳转
    // LDR.W   R0, =0x200080E6
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BNE     loc_17130
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_17132
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17136
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_17136(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200080E6;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008163;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R0, =0x20008163
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1714E
    // 条件跳转
    // LDR.W   R0, =0x200080E6
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #2
    // 比较操作
    // BNE     loc_1714E
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       loc_17150
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17154
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_17154(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_171B2
 * @note 指令数: 41, 标签数: 0
 */
void precise_func_171b2(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006C84;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8008082;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // MOVS.W  R8, #0
    // LDR.W   R0, =0x8008082
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR.W   R1, =0x20006C84
    // 内存加载操作
    // STRB.W  R0, [R8,R1]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // LDR.W   R0, =0x8008082
    // 内存加载操作
    // LDRB    R0, [R0,#1]
    // 内存加载操作
    // LDR.W   R1, =0x20006C84
    // 内存加载操作
    // STRB.W  R0, [R8,R1]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // MOVS    R0, #0x16
    // R0 = 0x16;
    // LDR.W   R1, =0x20006C84
    // 内存加载操作
    // STRB.W  R0, [R8,R1]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // LDR.W   R0, =0x20006C84
    // 内存加载操作
    // STRB.W  R4, [R8,R0]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // LDR.W   R0, =0x20006C84
    // 内存加载操作
    // STRB.W  R5, [R8,R0]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // LDR.W   R0, =0x20006C84
    // 内存加载操作
    // STRB.W  R7, [R8,R0]
    // ADDS.W  R8, R8, #1
    // 算术运算
    // UXTH    R7, R7
    // 数据扩展操作
    // CMP     R7, #0
    // 比较操作
    // BEQ     loc_17236
    // 条件跳转
    // UXTH    R7, R7
    // 数据扩展操作
    // LDR.W   R0, =0x20006C84
    // 内存加载操作
    // ADDS.W  R9, R8, R0
    // 算术运算
    // MOVS    R2, R7
    // MOVS    R1, R6
    // MOV     R0, R9
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // UXTAH.W R8, R8, R7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17278
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_17278(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006A28;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080E6;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xA8
    // 比较操作
    // BNE     loc_17290
    // 条件跳转
    // MOVS    R0, #4
    // R0 = 4;
    // LDR.W   R1, =0x200080E6
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // B       loc_1729C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17340
 * @note 指令数: 83, 标签数: 0
 */
void precise_func_17340(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008150;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000813E;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008130;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008154;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // PUSH.W  {R3-R9,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // LDRB    R0, [R0,#2]
    // 内存加载操作
    // CMP     R0, #7
    // 比较操作
    // BNE.W   locret_17474
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // ANDS.W  R0, R0, #0xF
    // LDR.W   R1, =0x20008154
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // LDRB    R0, [R0,#4]
    // 内存加载操作
    // ANDS.W  R0, R0, #0xF
    // LDR.W   R1, =0x20008150
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // LDRB    R0, [R0,#5]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #4
    // LDR.W   R1, =0x2000813E
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // LDRB    R0, [R0,#4]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #4
    // LDR.W   R1, =0x20008130
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #2
    // R0 = 2;
    // MOVS    R4, R0
    // MOVS    R5, #4
    // R5 = 4;
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS    R0, R4, R0
    // 算术运算
    // ADDS    R6, R0, #4
    // 算术运算
    // MOV     R7, SP
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // LDR     R0, [SP,#0x20+var_20]
    // 内存加载操作
    // LDR.W   R1, =0x20007FC0
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // ADDS    R4, R4, #4
    // 算术运算
    // MOVS    R7, #4
    // R7 = 4;
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS    R0, R4, R0
    // 算术运算
    // ADDS.W  R8, R0, #4
    // 算术运算
    // MOV     R9, SP
    // MOVS    R2, R7
    // MOV     R1, R8
    // MOV     R0, R9
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // LDR     R0, [SP,#0x20+var_20]
    // 内存加载操作
    // LDR.W   R1, =0x20007FC4
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // ADDS    R4, R4, #4
    // 算术运算
    // LDR.W   R0, =0x20008150
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1741C
    // 条件跳转
    // LDR.W   R0, =0x20008150
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #7
    // 算术运算
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R5, R0, R1
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // ADDS    R0, R4, R0
    // 算术运算
    // ADDS    R6, R0, #4
    // 算术运算
    // LDR.W   R7, =0x2000803C
    // 内存加载操作
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_18F0C
    // 调用函数: sub_18F0C();
    // LDR.W   R0, =0x20008150
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #7
    // 算术运算
    // MOVS    R1, #8
    // R1 = 8;
    // SDIV.W  R0, R0, R1
    // ADDS    R4, R0, R4
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17478
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_17478(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006A28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // LDRB    R0, [R0,#1]
    // 内存加载操作
    // CMP     R0, #3
    // 比较操作
    // BGE     loc_17494
    // 条件跳转
    // LDR.W   R0, =0x20006A28
    // 内存加载操作
    // LDRB    R0, [R0,#2]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_17494
    // 条件跳转
    // BL      sub_17278
    // 调用函数: sub_17278();
    // B       loc_17498
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1749C
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1749c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #0
    // R5 = 0;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // CMP     R0, #1
    // 比较操作
    // BEQ     loc_174B6
    // 条件跳转
    // CMP     R0, #2
    // 比较操作
    // BEQ     loc_174F2
    // 条件跳转
    // CMP     R0, #6
    // 比较操作
    // BEQ     loc_17524
    // 条件跳转
    // B       loc_17594
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_175B2
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_175b2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008163;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x20008163
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_175CC
    // 条件跳转
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, R4
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_171B2
    // 调用函数: sub_171B2();
}

