// 完整IDA风格转换批次 5 - 专业级转换
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_1A962
 * @note 指令数: 5
 * @note 类型: simple_function
 */
uint32_t ida_1a962(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_1A96C
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint32_t ida_1a96c(uint16_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_1A972
 * @note 指令数: 8
 * @note 类型: simple_function
 */
uint32_t ida_1a972(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_1A982
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint32_t ida_1a982(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_1A988
 * @note 指令数: 31
 * @note 类型: computation
 */
uint32_t ida_1a988(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_1A9CC
 * @note 指令数: 17
 * @note 类型: simple_function
 */
uint32_t ida_1a9cc(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200000 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_1A9FC
 * @note 指令数: 204
 * @note 类型: computation
 */
void ida_1a9fc(void)
{
    // 内存地址定义
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1AC54
 * @note 指令数: 38
 * @note 类型: control_function
 */
void ida_1ac54(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_3FF00000 = (volatile uint32_t *)0x3FF00000;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1ACB2
 * @note 指令数: 30
 * @note 类型: control_function
 */
uint32_t ida_1acb2(void)
{
    // 内存地址定义
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;
    volatile uint32_t *addr_20008080 = (volatile uint32_t *)0x20008080;
    volatile uint32_t *addr_22 = (volatile uint32_t *)0x22;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_1ACF6
 * @note 指令数: 64
 * @note 类型: lookup_table
 */
void ida_1acf6(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20005B3C = (volatile uint32_t *)0x20005B3C;
    volatile uint32_t *addr_20005B3E = (volatile uint32_t *)0x20005B3E;
    volatile uint32_t *addr_20008080 = (volatile uint32_t *)0x20008080;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1ADBE
 * @note 指令数: 107
 * @note 类型: control_function
 */
void ida_1adbe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007E40 = (volatile uint32_t *)0x20007E40;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1AED2
 * @note 指令数: 764
 * @note 类型: array_access
 */
void ida_1aed2(void)
{
    // 内存地址定义
    volatile uint32_t *addr_66 = (volatile uint32_t *)0x66;
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;
    volatile uint32_t *addr_20007E40 = (volatile uint32_t *)0x20007E40;
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1B69E
 * @note 指令数: 139
 * @note 类型: array_access
 */
void ida_1b69e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20007FB8 = (volatile uint32_t *)0x20007FB8;
    volatile uint32_t *addr_20007FBC = (volatile uint32_t *)0x20007FBC;
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_1B804
 * @note 指令数: 173
 * @note 类型: lookup_table
 */
void ida_1b804(void)
{
    // 内存地址定义
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;
    volatile uint32_t *addr_20007E40 = (volatile uint32_t *)0x20007E40;
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;
    volatile uint32_t *addr_68 = (volatile uint32_t *)0x68;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1B984
 * @note 指令数: 23
 * @note 类型: array_access
 */
void ida_1b984(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003B38 = (volatile uint32_t *)0x20003B38;
    volatile uint32_t *addr_20007F78 = (volatile uint32_t *)0x20007F78;
    volatile uint32_t *addr_20000265 = (volatile uint32_t *)0x20000265;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1B9C8
 * @note 指令数: 27
 * @note 类型: array_access
 */
uint32_t ida_1b9c8(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007F68 = (volatile uint32_t *)0x20007F68;
    volatile uint32_t *addr_200080F4 = (volatile uint32_t *)0x200080F4;
    volatile uint32_t *addr_20007F70 = (volatile uint32_t *)0x20007F70;
    volatile uint32_t *addr_200080F2 = (volatile uint32_t *)0x200080F2;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1BA5C
 * @note 指令数: 305
 * @note 类型: array_access
 */
void ida_1ba5c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_8008266 = (volatile uint32_t *)0x8008266;
    volatile uint32_t *addr_20007F68 = (volatile uint32_t *)0x20007F68;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_65 = (volatile uint32_t *)0x65;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1BD80
 * @note 指令数: 92
 * @note 类型: array_access
 */
uint32_t ida_1bd80(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007FB8 = (volatile uint32_t *)0x20007FB8;
    volatile uint32_t *addr_20007FBC = (volatile uint32_t *)0x20007FBC;
    volatile uint32_t *addr_80017FC = (volatile uint32_t *)0x80017FC;
    volatile uint32_t *addr_8001814 = (volatile uint32_t *)0x8001814;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1BE8C
 * @note 指令数: 211
 * @note 类型: array_access
 */
void ida_1be8c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20007FB8 = (volatile uint32_t *)0x20007FB8;
    volatile uint32_t *addr_20007FBC = (volatile uint32_t *)0x20007FBC;
    volatile uint32_t *addr_8001814 = (volatile uint32_t *)0x8001814;
    volatile uint32_t *addr_FD = (volatile uint32_t *)0xFD;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C0EE
 * @note 指令数: 14
 * @note 类型: control_function
 */
void ida_1c0ee(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C10E
 * @note 指令数: 9
 * @note 类型: control_function
 */
void ida_1c10e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_8002014 = (volatile uint32_t *)0x8002014;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C124
 * @note 指令数: 15
 * @note 类型: control_function
 */
void ida_1c124(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_7C = (volatile uint32_t *)0x7C;
    volatile uint32_t *addr_20000008 = (volatile uint32_t *)0x20000008;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_1C148
 * @note 指令数: 7
 * @note 类型: simple_function
 */
uint32_t ida_1c148(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8001804 = (volatile uint32_t *)0x8001804;
    volatile uint32_t *addr_8001800 = (volatile uint32_t *)0x8001800;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C15A
 * @note 指令数: 9
 * @note 类型: control_function
 */
void ida_1c15a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200070A6 = (volatile uint32_t *)0x200070A6;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C170
 * @note 指令数: 32
 * @note 类型: control_function
 */
void ida_1c170(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_52 = (volatile uint32_t *)0x52;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_2000005A = (volatile uint32_t *)0x2000005A;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C1C4
 * @note 指令数: 9
 * @note 类型: control_function
 */
void ida_1c1c4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200070B0 = (volatile uint32_t *)0x200070B0;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C1DA
 * @note 指令数: 32
 * @note 类型: control_function
 */
void ida_1c1da(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;
    volatile uint32_t *addr_20000064 = (volatile uint32_t *)0x20000064;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1C22E
 * @note 指令数: 53
 * @note 类型: array_access
 */
void ida_1c22e(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_B4 = (volatile uint32_t *)0xB4;
    volatile uint32_t *addr_80152C4 = (volatile uint32_t *)0x80152C4;
    volatile uint32_t *addr_7300 = (volatile uint32_t *)0x7300;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1C2AC
 * @note 指令数: 109
 * @note 类型: array_access
 */
void ida_1c2ac(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_B4 = (volatile uint32_t *)0xB4;
    volatile uint32_t *addr_80152C4 = (volatile uint32_t *)0x80152C4;
    volatile uint32_t *addr_7300 = (volatile uint32_t *)0x7300;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1C3B0
 * @note 指令数: 89
 * @note 类型: array_access
 */
void ida_1c3b0(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_80152C4 = (volatile uint32_t *)0x80152C4;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_7300 = (volatile uint32_t *)0x7300;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1C484
 * @note 指令数: 38
 * @note 类型: array_access
 */
void ida_1c484(void)
{
    // 内存地址定义
    volatile uint32_t *addr_8016760 = (volatile uint32_t *)0x8016760;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_168 = (volatile uint32_t *)0x168;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1C4E0
 * @note 指令数: 89
 * @note 类型: array_access
 */
void ida_1c4e0(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_80134EC = (volatile uint32_t *)0x80134EC;
    volatile uint32_t *addr_280 = (volatile uint32_t *)0x280;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1C600
 * @note 指令数: 72
 * @note 类型: array_access
 */
void ida_1c600(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_8015598 = (volatile uint32_t *)0x8015598;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_1C6B0
 * @note 指令数: 2
 * @note 类型: simple_function
 */
uint32_t ida_1c6b0(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数据访问函数
 * @note 原函数: sub_1C6B4
 * @note 指令数: 15
 * @note 类型: data_access
 */
void ida_1c6b4(uint32_t param0)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_1C6D8
 * @note 指令数: 46
 * @note 类型: lookup_table
 */
void ida_1c6d8(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20000008 = (volatile uint32_t *)0x20000008;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_20008142 = (volatile uint32_t *)0x20008142;
    volatile uint32_t *addr_7C = (volatile uint32_t *)0x7C;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C764
 * @note 指令数: 36
 * @note 类型: control_function
 */
void ida_1c764(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_3810 = (volatile uint32_t *)0x3810;
    volatile uint32_t *addr_3890 = (volatile uint32_t *)0x3890;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_3800 = (volatile uint32_t *)0x3800;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C7C8
 * @note 指令数: 53
 * @note 类型: control_function
 */
void ida_1c7c8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_8015888 = (volatile uint32_t *)0x8015888;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C848
 * @note 指令数: 53
 * @note 类型: control_function
 */
void ida_1c848(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_8015888 = (volatile uint32_t *)0x8015888;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C8C8
 * @note 指令数: 57
 * @note 类型: control_function
 */
void ida_1c8c8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_3810 = (volatile uint32_t *)0x3810;
    volatile uint32_t *addr_8015888 = (volatile uint32_t *)0x8015888;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C95C
 * @note 指令数: 50
 * @note 类型: control_function
 */
void ida_1c95c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_3B10 = (volatile uint32_t *)0x3B10;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1C9DA
 * @note 指令数: 16
 * @note 类型: control_function
 */
void ida_1c9da(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007E40 = (volatile uint32_t *)0x20007E40;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1CA00
 * @note 指令数: 64
 * @note 类型: control_function
 */
void ida_1ca00(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_58 = (volatile uint32_t *)0x58;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_1CAA2
 * @note 指令数: 100
 * @note 类型: lookup_table
 */
void ida_1caa2(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_3D = (volatile uint32_t *)0x3D;
    volatile uint32_t *addr_3A = (volatile uint32_t *)0x3A;
    volatile uint32_t *addr_38 = (volatile uint32_t *)0x38;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_1CBA6
 * @note 指令数: 108
 * @note 类型: lookup_table
 */
void ida_1cba6(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_4910 = (volatile uint32_t *)0x4910;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_4C = (volatile uint32_t *)0x4C;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1CCAE
 * @note 指令数: 16
 * @note 类型: control_function
 */
void ida_1ccae(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007738 = (volatile uint32_t *)0x20007738;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_1CCF4
 * @note 指令数: 3
 * @note 类型: control_function
 */
void ida_1ccf4(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1CCFC
 * @note 指令数: 183
 * @note 类型: array_access
 */
void ida_1ccfc(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_803F800 = (volatile uint32_t *)0x803F800;
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;
    volatile uint32_t *addr_55 = (volatile uint32_t *)0x55;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_1CEA2
 * @note 指令数: 113
 * @note 类型: array_access
 */
void ida_1cea2(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_8001804 = (volatile uint32_t *)0x8001804;
    volatile uint32_t *addr_82 = (volatile uint32_t *)0x82;
    volatile uint32_t *addr_803F800 = (volatile uint32_t *)0x803F800;
    volatile uint32_t *addr_200080A4 = (volatile uint32_t *)0x200080A4;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_1CFA8
 * @note 指令数: 8
 * @note 类型: simple_function
 */
uint32_t ida_1cfa8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008180 = (volatile uint32_t *)0x20008180;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

