#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用IDA Pro进行汇编到C代码转换
利用IDA Pro的强大反编译功能提高转换质量
"""

import os
import subprocess
import time
from typing import List, Dict, Tuple, Optional

class IDAConverter:
    def __init__(self):
        self.ida_path = r"K:\baiduyun\IDA Pro\IDA_Pro_v7.5_Portable"
        self.ida_exe = os.path.join(self.ida_path, "ida64.exe")
        self.binary_path = "bin/AT32F403AVG-FLASH-J201.bin"
        
    def check_ida_availability(self) -> bool:
        """检查IDA Pro是否可用"""
        if not os.path.exists(self.ida_exe):
            print(f"❌ IDA Pro未找到: {self.ida_exe}")
            return False
        
        if not os.path.exists(self.binary_path):
            print(f"❌ 二进制文件未找到: {self.binary_path}")
            return False
        
        print(f"✅ IDA Pro路径: {self.ida_exe}")
        print(f"✅ 二进制文件: {self.binary_path}")
        return True
    
    def create_ida_script(self) -> str:
        """创建IDA Pro自动化脚本"""
        script_content = '''
import idaapi
import idc
import ida_funcs
import ida_hexrays
import ida_kernwin
import ida_auto

def wait_for_analysis():
    """等待IDA完成自动分析"""
    print("等待IDA完成自动分析...")
    ida_auto.auto_wait()
    print("自动分析完成")

def get_all_functions():
    """获取所有函数信息"""
    functions = []
    
    # 遍历所有函数
    for func_ea in idautils.Functions():
        func = ida_funcs.get_func(func_ea)
        if func:
            func_name = idc.get_func_name(func_ea)
            func_start = func.start_ea
            func_end = func.end_ea
            
            functions.append({
                'name': func_name,
                'start': func_start,
                'end': func_end,
                'size': func_end - func_start
            })
    
    return functions

def decompile_function(func_ea, func_name):
    """使用Hex-Rays反编译器反编译函数"""
    try:
        # 检查Hex-Rays是否可用
        if not ida_hexrays.init_hexrays_plugin():
            print(f"Hex-Rays反编译器不可用")
            return None
        
        # 反编译函数
        cfunc = ida_hexrays.decompile(func_ea)
        if cfunc:
            # 获取反编译的C代码
            c_code = str(cfunc)
            return c_code
        else:
            print(f"无法反编译函数 {func_name}")
            return None
            
    except Exception as e:
        print(f"反编译函数 {func_name} 时出错: {e}")
        return None

def export_functions_to_c():
    """导出所有函数到C文件"""
    print("开始导出函数...")
    
    # 等待分析完成
    wait_for_analysis()
    
    # 获取所有函数
    functions = get_all_functions()
    print(f"找到 {len(functions)} 个函数")
    
    # 创建输出目录
    output_dir = "ida_conversions"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 分批处理函数
    batch_size = 50
    batch_num = 1
    
    for i in range(0, len(functions), batch_size):
        batch_functions = functions[i:i + batch_size]
        
        c_content = f"""// IDA Pro反编译批次 {batch_num} - 高质量反编译结果
#include <stdint.h>
#include <stdbool.h>

"""
        
        converted_count = 0
        
        for func_info in batch_functions:
            func_name = func_info['name']
            func_ea = func_info['start']
            
            print(f"反编译函数: {func_name} @ 0x{func_ea:08X}")
            
            # 反编译函数
            c_code = decompile_function(func_ea, func_name)
            
            if c_code:
                # 添加函数注释
                c_content += f"""/**
 * @brief IDA Pro反编译函数
 * @note 原函数: {func_name}
 * @note 地址: 0x{func_ea:08X}
 * @note 大小: {func_info['size']} 字节
 */
{c_code}

"""
                converted_count += 1
            else:
                # 如果反编译失败，添加占位符
                c_content += f"""/**
 * @brief 反编译失败的函数
 * @note 原函数: {func_name}
 * @note 地址: 0x{func_ea:08X}
 */
void {func_name}_placeholder(void)
{{
    // 反编译失败，需要手工转换
}}

"""
        
        # 保存批次文件
        output_file = os.path.join(output_dir, f"ida_batch_{batch_num:03d}.c")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(c_content)
        
        print(f"批次 {batch_num} 完成，成功反编译 {converted_count}/{len(batch_functions)} 个函数")
        print(f"保存到: {output_file}")
        
        batch_num += 1
    
    print("所有函数导出完成！")

def main():
    """主函数"""
    print("IDA Pro自动化反编译脚本启动")
    
    # 导出函数
    export_functions_to_c()
    
    # 退出IDA
    idc.qexit(0)

if __name__ == "__main__":
    main()
'''
        
        # 保存脚本文件
        script_path = "ida_auto_script.py"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return script_path
    
    def run_ida_conversion(self) -> bool:
        """运行IDA Pro进行转换"""
        print("🚀 启动IDA Pro进行自动化转换")
        print("=" * 80)
        
        # 检查IDA可用性
        if not self.check_ida_availability():
            return False
        
        # 创建IDA脚本
        script_path = self.create_ida_script()
        print(f"✅ IDA脚本已创建: {script_path}")
        
        # 构建IDA命令
        ida_cmd = [
            self.ida_exe,
            "-A",  # 自动分析
            "-S" + script_path,  # 执行脚本
            self.binary_path  # 目标文件
        ]
        
        print(f"🔄 执行IDA命令: {' '.join(ida_cmd)}")
        
        try:
            # 启动IDA Pro
            process = subprocess.Popen(
                ida_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.getcwd()
            )
            
            print("⏳ IDA Pro正在运行，请等待...")
            print("   这可能需要几分钟时间来分析和反编译所有函数")
            
            # 等待进程完成
            stdout, stderr = process.communicate(timeout=1800)  # 30分钟超时
            
            if process.returncode == 0:
                print("✅ IDA Pro转换完成！")
                return True
            else:
                print(f"❌ IDA Pro执行失败，返回码: {process.returncode}")
                if stderr:
                    print(f"错误信息: {stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⏰ IDA Pro执行超时")
            process.kill()
            return False
        except Exception as e:
            print(f"❌ 执行IDA Pro时出错: {e}")
            return False
    
    def analyze_ida_results(self) -> None:
        """分析IDA转换结果"""
        print("\n📊 分析IDA转换结果")
        print("=" * 50)
        
        ida_dir = "ida_conversions"
        if not os.path.exists(ida_dir):
            print("❌ IDA转换结果目录不存在")
            return
        
        # 统计转换结果
        batch_files = [f for f in os.listdir(ida_dir) if f.startswith("ida_batch_")]
        batch_files.sort()
        
        total_functions = 0
        successful_conversions = 0
        failed_conversions = 0
        
        for batch_file in batch_files:
            file_path = os.path.join(ida_dir, batch_file)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 统计函数数量
                functions_in_batch = content.count('@brief')
                successful_in_batch = content.count('IDA Pro反编译函数')
                failed_in_batch = content.count('反编译失败的函数')
                
                total_functions += functions_in_batch
                successful_conversions += successful_in_batch
                failed_conversions += failed_in_batch
                
                print(f"📁 {batch_file}: {functions_in_batch}个函数 "
                      f"(成功:{successful_in_batch}, 失败:{failed_in_batch})")
        
        success_rate = (successful_conversions / total_functions * 100) if total_functions > 0 else 0
        
        print(f"\n📈 转换统计:")
        print(f"   总函数数: {total_functions}")
        print(f"   成功转换: {successful_conversions}")
        print(f"   转换失败: {failed_conversions}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate > 80:
            print("🎉 IDA Pro转换质量优秀！")
        elif success_rate > 60:
            print("✅ IDA Pro转换质量良好")
        else:
            print("⚠️ IDA Pro转换质量需要改进")
    
    def compare_with_manual_conversion(self) -> None:
        """与手工转换结果对比"""
        print("\n🔍 与手工转换结果对比")
        print("=" * 50)
        
        comparison_report = """
## IDA Pro vs 手工转换对比

| 转换方法 | 自动化程度 | 转换速度 | 代码质量 | 准确性 |
|----------|------------|----------|----------|--------|
| **IDA Pro** | 🤖 全自动 | ⚡ 极快 | 🎯 高质量 | 📊 待验证 |
| **手工转换** | 👨‍💻 手工 | 🐌 较慢 | ✅ 已验证 | 🎯 95/100 |

### IDA Pro优势
- ✅ 完全自动化，无需人工干预
- ✅ 转换速度极快（分钟级别）
- ✅ 利用专业反编译器，代码质量高
- ✅ 支持复杂控制流和数据结构

### 手工转换优势  
- ✅ 已验证的高准确性（95/100分）
- ✅ 完全理解汇编逻辑
- ✅ 可控的转换质量
- ✅ 详细的注释和文档

### 建议策略
1. **优先使用IDA Pro结果** - 对于成功反编译的函数
2. **手工转换补充** - 对于IDA反编译失败的函数
3. **质量验证** - 对关键函数进行手工验证
4. **混合使用** - 结合两种方法的优势
"""
        
        with open("ida_vs_manual_comparison.md", 'w', encoding='utf-8') as f:
            f.write(comparison_report)
        
        print("📄 对比报告已生成: ida_vs_manual_comparison.md")

def main():
    converter = IDAConverter()
    
    # 运行IDA转换
    if converter.run_ida_conversion():
        # 分析结果
        converter.analyze_ida_results()
        
        # 与手工转换对比
        converter.compare_with_manual_conversion()
        
        print("\n🎉 IDA Pro转换流程完成！")
        print("📁 结果保存在 ida_conversions/ 目录")
    else:
        print("\n❌ IDA Pro转换失败")
        print("💡 建议检查IDA Pro安装和二进制文件路径")

if __name__ == "__main__":
    main()
