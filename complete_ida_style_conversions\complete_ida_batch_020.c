// 完整IDA风格转换批次 20 - 专业级转换
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_74360
 * @note 指令数: 121
 * @note 类型: array_access
 */
void ida_74360(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200036F2 = (volatile uint32_t *)0x200036F2;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_74470
 * @note 指令数: 88
 * @note 类型: array_access
 */
uint32_t ida_74470(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_200036F2 = (volatile uint32_t *)0x200036F2;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_74534
 * @note 指令数: 39
 * @note 类型: lookup_table
 */
void ida_74534(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_2000373C = (volatile uint32_t *)0x2000373C;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_8011D88 = (volatile uint32_t *)0x8011D88;
    volatile uint32_t *addr_8012464 = (volatile uint32_t *)0x8012464;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_74598
 * @note 指令数: 66
 * @note 类型: control_function
 */
uint32_t ida_74598(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000373B = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *addr_20003576 = (volatile uint32_t *)0x20003576;
    volatile uint32_t *addr_200036F6 = (volatile uint32_t *)0x200036F6;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_74638
 * @note 指令数: 83
 * @note 类型: lookup_table
 */
uint32_t ida_74638(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003574 = (volatile uint32_t *)0x20003574;
    volatile uint32_t *addr_8012444 = (volatile uint32_t *)0x8012444;
    volatile uint32_t *addr_2000373B = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *addr_8011F68 = (volatile uint32_t *)0x8011F68;

    // 局部变量
    uint32_t result = 0;

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_746F8
 * @note 指令数: 74
 * @note 类型: lookup_table
 */
uint32_t ida_746f8(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_20003574 = (volatile uint32_t *)0x20003574;
    volatile uint32_t *addr_88 = (volatile uint32_t *)0x88;
    volatile uint32_t *addr_2000373B = (volatile uint32_t *)0x2000373B;

    // 局部变量
    uint32_t result = 0;

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_74798
 * @note 指令数: 12
 * @note 类型: simple_function
 */
uint32_t ida_74798(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_747B0
 * @note 指令数: 12
 * @note 类型: simple_function
 */
uint32_t ida_747b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_747D0
 * @note 指令数: 12
 * @note 类型: simple_function
 */
uint32_t ida_747d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_74810
 * @note 指令数: 121
 * @note 类型: array_access
 */
void ida_74810(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_8011D28 = (volatile uint32_t *)0x8011D28;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_74938
 * @note 指令数: 95
 * @note 类型: array_access
 */
void ida_74938(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_8011BE8 = (volatile uint32_t *)0x8011BE8;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_74A14
 * @note 指令数: 121
 * @note 类型: lookup_table
 */
void ida_74a14(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_74B24
 * @note 指令数: 95
 * @note 类型: array_access
 */
void ida_74b24(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_74BF8
 * @note 指令数: 145
 * @note 类型: lookup_table
 */
void ida_74bf8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_74D3C
 * @note 指令数: 131
 * @note 类型: array_access
 */
uint32_t ida_74d3c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_74E60
 * @note 指令数: 115
 * @note 类型: lookup_table
 */
void ida_74e60(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200031B5 = (volatile uint32_t *)0x200031B5;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_54 = (volatile uint32_t *)0x54;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_74F58
 * @note 指令数: 87
 * @note 类型: array_access
 */
uint32_t ida_74f58(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200031B5 = (volatile uint32_t *)0x200031B5;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_75018
 * @note 指令数: 80
 * @note 类型: control_function
 */
uint32_t ida_75018(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_801247C = (volatile uint32_t *)0x801247C;
    volatile uint32_t *addr_2000373B = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *addr_20003739 = (volatile uint32_t *)0x20003739;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_750C6
 * @note 指令数: 12
 * @note 类型: simple_function
 */
uint32_t ida_750c6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_750EC
 * @note 指令数: 136
 * @note 类型: lookup_table
 */
void ida_750ec(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75210
 * @note 指令数: 102
 * @note 类型: array_access
 */
void ida_75210(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_8011FCC = (volatile uint32_t *)0x8011FCC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75304
 * @note 指令数: 59
 * @note 类型: array_access
 */
uint32_t ida_75304(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_8011E78 = (volatile uint32_t *)0x8011E78;
    volatile uint32_t *addr_23 = (volatile uint32_t *)0x23;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_8011FE0 = (volatile uint32_t *)0x8011FE0;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75394
 * @note 指令数: 63
 * @note 类型: array_access
 */
uint32_t ida_75394(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_23 = (volatile uint32_t *)0x23;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_E = (volatile uint32_t *)0xE;
    volatile uint32_t *addr_801210C = (volatile uint32_t *)0x801210C;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75428
 * @note 指令数: 59
 * @note 类型: array_access
 */
uint32_t ida_75428(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_23 = (volatile uint32_t *)0x23;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_38 = (volatile uint32_t *)0x38;
    volatile uint32_t *addr_200035E4 = (volatile uint32_t *)0x200035E4;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_754B8
 * @note 指令数: 60
 * @note 类型: array_access
 */
uint32_t ida_754b8(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_23 = (volatile uint32_t *)0x23;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_8012378 = (volatile uint32_t *)0x8012378;
    volatile uint32_t *addr_38 = (volatile uint32_t *)0x38;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_7557C
 * @note 指令数: 367
 * @note 类型: lookup_table
 */
void ida_7557c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003660 = (volatile uint32_t *)0x20003660;
    volatile uint32_t *addr_2E = (volatile uint32_t *)0x2E;
    volatile uint32_t *addr_68 = (volatile uint32_t *)0x68;
    volatile uint32_t *addr_200035CC = (volatile uint32_t *)0x200035CC;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_758C8
 * @note 指令数: 38
 * @note 类型: control_function
 */
void ida_758c8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003740 = (volatile uint32_t *)0x20003740;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75920
 * @note 指令数: 61
 * @note 类型: array_access
 */
uint32_t ida_75920(void)
{
    // 内存地址定义
    volatile uint32_t *addr_23 = (volatile uint32_t *)0x23;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_20003736 = (volatile uint32_t *)0x20003736;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_759C0
 * @note 指令数: 40
 * @note 类型: lookup_table
 */
void ida_759c0(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_801219C = (volatile uint32_t *)0x801219C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_20003736 = (volatile uint32_t *)0x20003736;
    volatile uint32_t *addr_21 = (volatile uint32_t *)0x21;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75A40
 * @note 指令数: 47
 * @note 类型: array_access
 */
uint32_t ida_75a40(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_E1 = (volatile uint32_t *)0xE1;
    volatile uint32_t *addr_2000373D = (volatile uint32_t *)0x2000373D;
    volatile uint32_t *addr_200036F6 = (volatile uint32_t *)0x200036F6;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75AE0
 * @note 指令数: 225
 * @note 类型: array_access
 */
void ida_75ae0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_80116E0 = (volatile uint32_t *)0x80116E0;
    volatile uint32_t *addr_20003508 = (volatile uint32_t *)0x20003508;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200036F6 = (volatile uint32_t *)0x200036F6;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75CFC
 * @note 指令数: 106
 * @note 类型: array_access
 */
void ida_75cfc(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_20003736 = (volatile uint32_t *)0x20003736;
    volatile uint32_t *addr_20003739 = (volatile uint32_t *)0x20003739;
    volatile uint32_t *addr_80121AC = (volatile uint32_t *)0x80121AC;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_75E3C
 * @note 指令数: 526
 * @note 类型: array_access
 */
void ida_75e3c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_762E4
 * @note 指令数: 20
 * @note 类型: computation
 */
uint32_t ida_762e4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7630C
 * @note 指令数: 26
 * @note 类型: array_access
 */
uint16_t ida_7630c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量
    uint16_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_76340
 * @note 指令数: 22
 * @note 类型: computation
 */
uint32_t ida_76340(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_76398
 * @note 指令数: 3
 * @note 类型: simple_function
 */
void ida_76398(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7639E
 * @note 指令数: 87
 * @note 类型: computation
 */
void ida_7639e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_7644C
 * @note 指令数: 11
 * @note 类型: simple_function
 */
uint32_t ida_7644c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_76462
 * @note 指令数: 5
 * @note 类型: computation
 */
uint32_t ida_76462(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7646C
 * @note 指令数: 23
 * @note 类型: control_function
 */
void ida_7646c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000371E = (volatile uint32_t *)0x2000371E;
    volatile uint32_t *addr_2000371F = (volatile uint32_t *)0x2000371F;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7649E
 * @note 指令数: 23
 * @note 类型: control_function
 */
void ida_7649e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003721 = (volatile uint32_t *)0x20003721;
    volatile uint32_t *addr_20003722 = (volatile uint32_t *)0x20003722;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_764D0
 * @note 指令数: 23
 * @note 类型: control_function
 */
void ida_764d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003725 = (volatile uint32_t *)0x20003725;
    volatile uint32_t *addr_20003724 = (volatile uint32_t *)0x20003724;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_76502
 * @note 指令数: 23
 * @note 类型: control_function
 */
void ida_76502(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003728 = (volatile uint32_t *)0x20003728;
    volatile uint32_t *addr_20003727 = (volatile uint32_t *)0x20003727;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_76534
 * @note 指令数: 23
 * @note 类型: control_function
 */
void ida_76534(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000372D = (volatile uint32_t *)0x2000372D;
    volatile uint32_t *addr_2000372E = (volatile uint32_t *)0x2000372E;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_76566
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint8_t ida_76566(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003730 = (volatile uint32_t *)0x20003730;

    // 局部变量
    uint8_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7656C
 * @note 指令数: 23
 * @note 类型: control_function
 */
void ida_7656c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000372B = (volatile uint32_t *)0x2000372B;
    volatile uint32_t *addr_2000372A = (volatile uint32_t *)0x2000372A;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7659E
 * @note 指令数: 6
 * @note 类型: control_function
 */
uint32_t ida_7659e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200035A4 = (volatile uint32_t *)0x200035A4;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_765D0
 * @note 指令数: 233
 * @note 类型: array_access
 */
uint32_t ida_765d0(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_2000372E = (volatile uint32_t *)0x2000372E;
    volatile uint32_t *addr_200036E2 = (volatile uint32_t *)0x200036E2;
    volatile uint32_t *addr_20003730 = (volatile uint32_t *)0x20003730;
    volatile uint32_t *addr_200036E6 = (volatile uint32_t *)0x200036E6;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_76820
 * @note 指令数: 39
 * @note 类型: array_access
 */
uint16_t ida_76820(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量
    uint16_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

