// 精确转换批次 9 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_232B8
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_232b8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007FE0;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x20007FE0
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_232BE
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_232be(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40001400;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x40001400
    // 内存加载操作
    // BL      sub_183A2
    // 调用函数: sub_183A2();
    // MOVS    R2, #1
    // R2 = 1;
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x40001400
    // 内存加载操作
    // BL      sub_1836E
    // 调用函数: sub_1836E();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_232E4
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_232e4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4002200C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #3
    // R0 = 3;
    // LDR.W   R1, =0x4002200C
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // LSLS    R1, R1, #0x1F
    // BPL     loc_232F6
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // B       loc_23322
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23326
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_23326(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x4002204C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #3
    // R0 = 3;
    // LDR.W   R1, =0x4002204C
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // LSLS    R1, R1, #0x1F
    // BPL     loc_23338
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // B       loc_23364
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23368
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_23368(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4002208C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #3
    // R0 = 3;
    // LDR.W   R1, =0x4002208C
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // LSLS    R1, R1, #0x1F
    // BPL     loc_2337A
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // B       loc_233A6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_233AA
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_233aa(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #3
    // R5 = 3;
    // BL      sub_232E4
    // 调用函数: sub_232E4();
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_233D8
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_233d8(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #3
    // R5 = 3;
    // BL      sub_23326
    // 调用函数: sub_23326();
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23406
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_23406(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #3
    // R5 = 3;
    // BL      sub_23368
    // 调用函数: sub_23368();
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23434
 * @note 指令数: 13, 标签数: 0
 */
uint32_t precise_func_23434(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40022044;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022004;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x45670123;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xCDEF89AB;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R0, =0x45670123
    // 内存加载操作
    // LDR.W   R1, =0x40022004
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0xCDEF89AB
    // 内存加载操作
    // LDR.W   R1, =0x40022004
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x45670123
    // 内存加载操作
    // LDR.W   R1, =0x40022044
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0xCDEF89AB
    // 内存加载操作
    // LDR.W   R1, =0x40022044
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2345E
 * @note 指令数: 11, 标签数: 0
 */
uint32_t precise_func_2345e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022050;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40022010;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR.W   R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ORRS.W  R0, R0, #0x80
    // LDR.W   R1, =0x40022010
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x40022050
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ORRS.W  R0, R0, #0x80
    // LDR.W   R1, =0x40022050
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23480
 * @note 指令数: 35, 标签数: 0
 */
void precise_func_23480(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40022014;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8000000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x8080000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #3
    // R5 = 3;
    // CMP.W   R4, #0x8000000
    // BCC     loc_234E8
    // LDR.W   R0, =0x8080000
    // 内存加载操作
    // CMP     R4, R0
    // 比较操作
    // BCS     loc_234E8
    // MOVS.W  R0, #0x40000000
    // BL      sub_233AA
    // 调用函数: sub_233AA();
    // MOVS    R5, R0
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #3
    // 比较操作
    // BNE.W   loc_235A4
    // LDR.W   R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ORRS.W  R0, R0, #2
    // LDR.W   R1, =0x40022010
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x40022014
    // 内存加载操作
    // STR     R4, [R0]
    // 内存存储操作
    // LDR.W   R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ORRS.W  R0, R0, #0x40 ; '@'
    // LDR.W   R1, =0x40022010
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS.W  R0, #0x40000000
    // BL      sub_233AA
    // 调用函数: sub_233AA();
    // MOVS    R5, R0
    // LDR.W   R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BICS.W  R0, R0, #2
    // LDR.W   R1, =0x40022010
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // B       loc_235A4
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_235AA
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_235aa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022010;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8000000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8080000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #3
    // R6 = 3;
    // CMP.W   R4, #0x8000000
    // BCC     loc_235FE
    // LDR.W   R0, =0x8080000
    // 内存加载操作
    // CMP     R4, R0
    // 比较操作
    // BCS     loc_235FE
    // MOVS.W  R0, #0x100000
    // BL      sub_233AA
    // 调用函数: sub_233AA();
    // MOVS    R6, R0
    // UXTB    R6, R6
    // 数据扩展操作
    // CMP     R6, #3
    // 比较操作
    // BNE     loc_2368C
    // 条件跳转
    // LDR.W   R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ORRS.W  R0, R0, #1
    // LDR.W   R1, =0x40022010
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // STR     R5, [R4]
    // 内存存储操作
    // MOVS.W  R0, #0x100000
    // BL      sub_233AA
    // 调用函数: sub_233AA();
    // MOVS    R6, R0
    // LDR.W   R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSRS    R0, R0, #1
    // LSLS    R0, R0, #1
    // LDR.W   R1, =0x40022010
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // B       loc_2368C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_236CC
 * @note 指令数: 31, 标签数: 1
 */
void precise_func_236cc(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007C7C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3F800000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007C5C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007C9C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007C3C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #8
    // 比较操作
    // BGE     loc_23724
    // 条件跳转
    // LDR.W   R1, =0x20007C3C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R2, #0
    // R2 = 0;
    // STR.W   R2, [R1,R0,LSL#2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20007E80
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRH.W  R1, [R2,R0,LSL#1]
    // 内存存储操作
    // LDR.W   R1, =0x20007C5C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS.W  R2, #0x3F800000
    // STR.W   R2, [R1,R0,LSL#2]
    // 内存存储操作
    // LDR.W   R1, =0x20007C9C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R2, #0
    // R2 = 0;
    // STR.W   R2, [R1,R0,LSL#2]
    // 内存存储操作
    // LDR.W   R1, =0x20007C7C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R2, #0
    // R2 = 0;
    // STR.W   R2, [R1,R0,LSL#2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R2, =0x20007E90
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRH.W  R1, [R2,R0,LSL#1]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_236D0
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23752
 * @note 指令数: 29, 标签数: 1
 */
void precise_func_23752(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008182;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008185;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000818B;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008184;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000818A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // BL      sub_236CC
    // 调用函数: sub_236CC();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BNE     loc_23798
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008182
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R4,R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x2000818A
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R4,R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008184
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R4,R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008185
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R4,R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x2000818B
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R4,R1]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_2375C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_237FC
 * @note 指令数: 30, 标签数: 0
 */
void precise_func_237fc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008189;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007FB0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20008188;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // VPUSH   {D8}
    // LDR.W   R0, =0x20007FB0
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP.W   R0, #0x3E8
    // BLT     loc_23858
    // 条件跳转
    // LDR.W   R0, =0x20007FB0
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // SUBS.W  R0, R0, #0x3E8
    // LDR.W   R1, =0x20007FB0
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008187
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20008188
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR.W   R1, =0x20008188
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20008188
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x1E
    // 比较操作
    // BLT     loc_23850
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008188
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR.W   R1, =0x20008189
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       loc_23858
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23C54
 * @note 指令数: 53, 标签数: 0
 */
void precise_func_23c54(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008116;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008102;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20008112;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x18
    // 算术运算
    // LDR.W   R0, =0x2000810A
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_21CDC
    // 调用函数: sub_21CDC();
    // LDR.W   R1, =0x200080FC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x2000810C
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_21CDC
    // 调用函数: sub_21CDC();
    // LDR.W   R1, =0x200080FE
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x2000810E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_21CDC
    // 调用函数: sub_21CDC();
    // LDR.W   R1, =0x20008100
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20008110
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_21CDC
    // 调用函数: sub_21CDC();
    // LDR.W   R1, =0x20008102
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20008116
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR.W   R1, =0x20008108
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20008112
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_21CDC
    // 调用函数: sub_21CDC();
    // LDR.W   R1, =0x20008104
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x20008114
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // ADDS    R0, #0x30 ; '0'
    // 算术运算
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_21CDC
    // 调用函数: sub_21CDC();
    // ADDS.W  R0, R0, #0x2000
    // 算术运算
    // LDR.W   R1, =0x20008106
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR.W   R0, =0x200080FC
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x5A ; 'Z'
    // 比较操作
    // BLT     loc_23CE8
    // 条件跳转
    // MOVS    R0, #0x59 ; 'Y'
    // R0 = 0x59;
    // LDR.W   R1, =0x200080FC
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_23E76
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_23e76(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x19;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x24;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_1B= -0x1B
    // var_1A= -0x1A
    // var_19= -0x19
    // var_18= -0x18
    // var_17= -0x17
    // var_16= -0x16
    // var_15= -0x15
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24144
 * @note 指令数: 40, 标签数: 0
 */
void precise_func_24144(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801692C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008174;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008110;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016844;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007F88;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR.W   R1, =0x20008174
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x20007F88
    // 内存加载操作
    // BL      sub_16472
    // 调用函数: sub_16472();
    // LDR.W   R2, =0x801692C
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R0, =0x8016844
    // 内存加载操作
    // BL      sub_18F84
    // 调用函数: sub_18F84();
    // LDR.W   R1, =0x20008094
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_23E76
    // 调用函数: sub_23E76();
    // LDR.W   R0, =0x20008174
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1E
    // BMI     loc_241A8
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000810A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000810C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000810E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008110
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008116
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20008112
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVW    R0, #0x7E2
    // R0 = 0x7E2;
    // LDR     R1, =0x20008114
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_241BA
 * @note 指令数: 61, 标签数: 0
 */
void precise_func_241ba(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000811E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008120;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008110;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000811C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x3C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6}
    // 栈操作
    // LDR     R4, =0x2000810A
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // LDR.W   R5, =0x20008118
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR     R4, =0x2000810C
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // LDR.W   R5, =0x2000811A
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR     R4, =0x2000810E
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // LDR.W   R5, =0x2000811C
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR     R4, =0x20008110
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // LDR.W   R5, =0x2000811E
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR     R4, =0x20008112
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // LDR.W   R5, =0x20008120
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR     R4, =0x20008114
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // LDR.W   R5, =0x20008122
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR     R4, =0x20008116
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // LDR.W   R5, =0x20008126
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR.W   R4, =0x20008124
    // 内存加载操作
    // LDRSH.W R4, [R4]
    // NEGS    R4, R4
    // MOVS    R2, R4
    // SXTH    R2, R2
    // 数据扩展操作
    // CMP     R2, #1
    // 比较操作
    // BLT.W   loc_2433E
    // LDR.W   R4, =0x2000811A
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // ADDS    R4, R2, R4
    // 算术运算
    // LDR.W   R5, =0x2000811A
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR.W   R4, =0x2000811A
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // CMP     R4, #0x3C ; '<'
    // 比较操作
    // BLT     loc_2425E
    // 条件跳转
    // LDR.W   R4, =0x2000811C
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // LDR.W   R5, =0x2000811A
    // 内存加载操作
    // LDRH    R5, [R5]
    // 内存加载操作
    // MOVS    R6, #0x3C ; '<'
    // R6 = 0x3C;
    // SDIV.W  R5, R5, R6
    // ADDS    R4, R5, R4
    // 算术运算
    // LDR.W   R5, =0x2000811C
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
    // LDR.W   R4, =0x2000811A
    // 内存加载操作
    // LDRH    R4, [R4]
    // 内存加载操作
    // MOVS    R5, #0x3C ; '<'
    // R5 = 0x3C;
    // SDIV.W  R6, R4, R5
    // MLS.W   R4, R5, R6, R4
    // LDR.W   R5, =0x2000811A
    // 内存加载操作
    // STRH    R4, [R5]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_244F8
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_244f8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24A84
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_24a84(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000812E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007FA8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007FA8
    // 内存加载操作
    // BL      sub_164A4
    // 调用函数: sub_164A4();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007FA8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000812E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24A9C
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_24a9c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000812E;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007FA8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20007FA8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x3C ; '<'
    // 比较操作
    // BLT     locret_24AC0
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007FA8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000812E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVW    R1, #0xFFFF
    // R1 = 0xFFFF;
    // CMP     R0, R1
    // 比较操作
    // BEQ     locret_24AC0
    // 条件跳转
    // LDR     R0, =0x2000812E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x2000812E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24B30
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_24b30(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R10,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R5, #0
    // R5 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R7, #0
    // R7 = 0;
    // MOVS.W  R8, #0
    // MOVS.W  R9, #0
    // BL      sub_17FDA
    // 调用函数: sub_17FDA();
    // MOV     R10, R0
    // UXTB.W  R10, R10
    // CMP.W   R10, #0
    // BEQ     loc_24B5E
    // 条件跳转
    // CMP.W   R10, #2
    // BEQ     loc_24B8E
    // 条件跳转
    // BCC     loc_24B86
    // B       loc_24C00
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24C64
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_24c64(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000194;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x801691C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R2, =0x801691C
    // 内存加载操作
    // MOVS    R1, R4
    // LDR     R0, =0x20000194
    // 内存加载操作
    // BL      sub_255A8
    // 调用函数: sub_255A8();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24C74
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_24c74(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x801691C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200001BC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R2, =0x801691C
    // 内存加载操作
    // MOVS    R1, R4
    // LDR     R0, =0x200001BC
    // 内存加载操作
    // BL      sub_255A8
    // 调用函数: sub_255A8();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24C84
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_24c84(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016924;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000020C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8016838;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // LDR     R0, =0x8016838
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // LDR     R3, =0x8016924
    // 内存加载操作
    // MOVS    R2, #0x80
    // R2 = 0x80;
    // MOVS    R1, R4
    // LDR     R0, =0x2000020C
    // 内存加载操作
    // BL      sub_255D4
    // 调用函数: sub_255D4();
    // POP     {R1,R2,R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24CB4
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_24cb4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_24CC8
    // 条件跳转
    // LDRB    R0, [R2]
    // 内存加载操作
    // LSLS    R0, R0, #8
    // STRH    R0, [R1]
    // 内存存储操作
    // LDRH    R0, [R1]
    // 内存加载操作
    // LDRB    R3, [R2,#1]
    // 内存加载操作
    // ORRS    R0, R3
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24CCC
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_24ccc(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_25604
    // 调用函数: sub_25604();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24D38
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_24d38(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000263;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007EE8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDR     R0, =0x20000263
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BNE     loc_24D54
    // 条件跳转
    // LDR     R0, =0x20007EE8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRH.W  R5, [R0,R4,LSL#1]
    // 内存存储操作
    // BL      sub_24D62
    // 调用函数: sub_24D62();
    // B       locret_24D60
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24D62
 * @note 指令数: 32, 标签数: 0
 */
void precise_func_24d62(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007EE8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xC0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_F= -0xF
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, #1
    // R5 = 1;
    // MOVS    R0, #0x40 ; '@'
    // R0 = 0x40;
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // STRB.W  R0, [SP,#0x10+var_10]
    // MOVS    R0, #0
    // R0 = 0;
    // STRB.W  R0, [SP,#0x10+var_F]
    // LDR     R0, =0x20007EE8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // STRB.W  R0, [SP,#0x10+var_10]
    // LDR     R0, =0x20007EE8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // ASRS    R0, R0, #8
    // ANDS.W  R0, R0, #0xF
    // ORRS.W  R0, R0, #0x80
    // STRB.W  R0, [SP,#0x10+var_F]
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_256AA
    // 调用函数: sub_256AA();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #0xC0
    // R1 = 0xC0;
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_25986
    // 调用函数: sub_25986();
    // MOVS    R5, R0
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BNE     loc_24DAE
    // 条件跳转
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // B       locret_24F7A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24F88
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_24f88(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016778;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x801677C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =dword_180004
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // LDR     R1, =0x801677C
    // 内存加载操作
    // LDR     R0, =0x8016778
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016778
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016778
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_25A14
    // 调用函数: sub_25A14();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_24FCC
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_24fcc(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #2
    // 比较操作
    // BLT     loc_24FF0
    // 条件跳转
    // UXTB    R4, R4
    // 数据扩展操作
    // SUBS    R0, R4, #1
    // 算术运算
    // MOVS    R1, #2
    // R1 = 2;
    // SDIV.W  R0, R0, R1
    // MOVS    R6, R0
    // UXTB    R6, R6
    // 数据扩展操作
    // SUBS.W  R0, R4, R6,LSL#1
    // SUBS    R4, R0, #1
    // 算术运算
    // B       loc_25002
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25014
 * @note 指令数: 43, 标签数: 0
 */
void precise_func_25014(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016778;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007EF0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_17= -0x17
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, #1
    // R5 = 1;
    // MOVS    R7, R4
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8016778
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016778
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
    // MOVS    R0, #0
    // R0 = 0;
    // STRB.W  R0, [SP,#0x18+var_18]
    // MOVS    R0, #0
    // R0 = 0;
    // STRB.W  R0, [SP,#0x18+var_17]
    // LDR     R0, =0x20007EF0
    // 内存加载操作
    // UXTB    R7, R7
    // 数据扩展操作
    // LDRH.W  R0, [R0,R7,LSL#1]
    // 内存加载操作
    // STRB.W  R0, [SP,#0x18+var_18]
    // LDR     R0, =0x20007EF0
    // 内存加载操作
    // UXTB    R7, R7
    // 数据扩展操作
    // LDRH.W  R0, [R0,R7,LSL#1]
    // 内存加载操作
    // ASRS    R0, R0, #8
    // ANDS.W  R0, R0, #0xF
    // ORRS.W  R0, R0, R4,LSL#6
    // ORRS.W  R0, R0, #0x10
    // STRB.W  R0, [SP,#0x18+var_17]
    // MOVS    R3, #0
    // R3 = 0;
    // LDRB.W  R2, [SP,#0x18+var_17]
    // LDRB.W  R1, [SP,#0x18+var_18]
    // MOVS    R0, R6
    // BL      sub_25B28
    // 调用函数: sub_25B28();
    // MOVS    R5, R0
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BNE     loc_25080
    // 条件跳转
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // B       locret_25094
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_250A8
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_250a8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x70;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x6C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x42;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x68;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_70= -0x70
    // var_6C= -0x6C
    // var_68= -0x68
    // var_42= -0x42
    // var_40= -0x40
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2542E
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_2542e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x74;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x62;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x6C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x7A;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x68;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // CMP     R0, #0x62 ; 'b'
    // 比较操作
    // BEQ     loc_2545E
    // 条件跳转
    // CMP     R0, #0x68 ; 'h'
    // 比较操作
    // BEQ     loc_25450
    // 条件跳转
    // CMP     R0, #0x6C ; 'l'
    // 比较操作
    // IT NE
    // CMPNE   R0, #0x74 ; 't'
    // BEQ     loc_2546C
    // 条件跳转
    // CMP     R0, #0x7A ; 'z'
    // 比较操作
    // BNE     loc_2546C
    // 条件跳转
    // LDR     R2, [R1]
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // LDR.W   R0, [R2],#4
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2547A
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_2547a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7}
    // 栈操作
    // LDR     R2, [R0,#0xC]
    // 内存加载操作
    // CMP     R1, #0x6F ; 'o'
    // 比较操作
    // IT EQ
    // MOVEQ   R4, #8
    // BEQ     loc_25492
    // 条件跳转
    // ORR.W   R3, R1, #0x20 ; ' '
    // CMP     R3, #0x78 ; 'x'
    // 比较操作
    // ITE NE
    // MOVNE   R4, #0xA
    // MOVEQ   R4, #0x10
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25548
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_25548(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOV     R4, R0
    // MOV     R5, R1
    // MOV     R6, R2
    // MOV     R7, R3
    // MOVS    R0, #0
    // R0 = 0;
    // CBZ     R7, locret_25574
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25580
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_25580(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0
    // R2 = 0;
    // B.W     sub_25C4C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25588
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_25588(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // CMP     R0, R2
    // 比较操作
    // BNE     locret_255A6
    // 条件跳转
    // ORR.W   R12, R1, R3
    // ORRS.W  R12, R0, R12,LSL#1
    // BEQ     locret_255A6
    // 条件跳转
    // CMP     R1, R3
    // 比较操作
    // BNE     locret_255A6
    // 条件跳转
    // MOV.W   R12, #0x200000
    // CMN.W   R12, R1,LSL#1
    // IT CC
    // CMPCC   R1, R1
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_255A8
 * @note 指令数: 13, 标签数: 1
 */
void precise_func_255a8(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // MOVS    R3, #0
    // R3 = 0;
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_255C6
    // 条件跳转
    // LDR     R4, [R0,#4]
    // 内存加载操作
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_255C6
    // 条件跳转
    // LDR     R4, [R0]
    // 内存加载操作
    // CMP     R4, R1
    // 比较操作
    // BNE     loc_255C2
    // 条件跳转
    // LDR     R4, [R0,#4]
    // 内存加载操作
    // MOVS    R3, R4
    // B       loc_255C6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_255D4
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_255d4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // arg_0=  0
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // LDR.W   R8, [SP,#0x18+arg_0]
    // 内存加载操作
    // CMP     R5, R6
    // 比较操作
    // BCS     loc_255F4
    // MOVS    R2, R7
    // MOVS    R1, R5
    // MOVS    R0, R4
    // BL      sub_255A8
    // 调用函数: sub_255A8();
    // B       locret_255FE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25604
 * @note 指令数: 44, 标签数: 0
 */
void precise_func_25604(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x180003;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // ADDS    R1, R0, #4
    // 算术运算
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // ADDS.W  R1, R0, #0x10
    // 算术运算
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_2568C
    // 条件跳转
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // ADDS.W  R1, R0, #0x1C
    // 算术运算
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0,#0x18]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_259BE
    // 调用函数: sub_259BE();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2569A
 * @note 指令数: 7, 标签数: 1
 */
void precise_func_2569a(uint8_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #2
    // 比较操作
    // BGE     locret_256A8
    // 条件跳转
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_2569E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_256AA
 * @note 指令数: 71, 标签数: 0
 */
void precise_func_256aa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80165B8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR.W   R0, =0x80165B8
    // 内存加载操作
    // MOVS    R1, #0xC
    // R1 = 0xC;
    // MLA.W   R0, R1, R4, R0
    // ADDS    R1, R0, #4
    // 算术运算
    // LDR.W   R0, =0x80165B8
    // 内存加载操作
    // MOVS    R2, #0xC
    // R2 = 0xC;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR.W   R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25774
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_25774(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // BL      sub_256AA
    // 调用函数: sub_256AA();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25780
 * @note 指令数: 59, 标签数: 0
 */
void precise_func_25780(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80165B8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x80165B8
    // 内存加载操作
    // MOVS    R1, #0xC
    // R1 = 0xC;
    // MLA.W   R0, R1, R4, R0
    // ADDS    R1, R0, #4
    // 算术运算
    // LDR     R0, =0x80165B8
    // 内存加载操作
    // MOVS    R2, #0xC
    // R2 = 0xC;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2581A
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_2581a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_25844
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // B       loc_25860
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_258A6
 * @note 指令数: 58, 标签数: 0
 */
void precise_func_258a6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016148;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80165B8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x80165D0
    // 内存加载操作
    // MOVS    R1, #0xC
    // R1 = 0xC;
    // MLA.W   R0, R1, R4, R0
    // ADDS    R1, R0, #4
    // 算术运算
    // LDR     R0, =0x80165D0
    // 内存加载操作
    // MOVS    R2, #0xC
    // R2 = 0xC;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x10]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R2, #0x24 ; '$'
    // R2 = 0x24;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // BL      sub_18496
    // 调用函数: sub_18496();
    // MOVS    R5, R0
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R1, #0x24 ; '$'
    // R1 = 0x24;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016148
    // 内存加载操作
    // MOVS    R3, #0x24 ; '$'
    // R3 = 0x24;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // LDR     R0, =0x80165B8
    // 内存加载操作
    // MOVS    R1, #0xC
    // R1 = 0xC;
    // MLA.W   R0, R1, R4, R0
    // ADDS    R1, R0, #4
    // 算术运算
    // LDR     R0, =0x80165B8
    // 内存加载操作
    // MOVS    R2, #0xC
    // R2 = 0xC;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // BL      sub_2569A
    // 调用函数: sub_2569A();
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // POP     {R1,R4,R5,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_2593C
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_2593c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_25986
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_25986(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R7, R0
}

