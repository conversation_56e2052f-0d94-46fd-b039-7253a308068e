#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确汇编到C代码转换器
完全按照汇编逻辑生成对应的C代码
"""

import re
import os
from typing import List, Dict, Tuple, Optional

class ExactAsmConverter:
    def __init__(self, asm_file_path: str):
        self.asm_file_path = asm_file_path
        
    def convert_sub_14B18(self) -> str:
        """精确转换sub_14B18函数"""
        return """/**
 * @brief 浮点数数组访问函数 - 精确复刻汇编逻辑
 * @note 对应汇编函数 sub_14B18
 * @param index 数组索引 (对应R0)
 * @return 浮点数值 (对应S0)
 */
float precise_function_14b18(uint8_t index)
{
    // UXTB R0, R0 - 确保索引为8位无符号
    index = index & 0xFF;
    
    // CMP R0, #0x10 - 比较索引与16
    if (index >= 0x10) {
        // BLT loc_14B24 - 如果大于等于16，返回0.0
        // FLDS S0, =0.0
        return 0.0f;
    }
    
    // loc_14B24:
    // LDR.W R1, =0x20007584 - 加载浮点数组基地址
    volatile float *float_array = (volatile float *)0x20007584;
    
    // UXTB R0, R0 - 再次确保索引为8位
    // ADDS.W R0, R1, R0,LSL#2 - 计算数组元素地址 (base + index*4)
    // FLDS S0, [R0] - 加载浮点数
    return float_array[index];
    
    // locret_14B32:
    // BX LR - 函数返回
}"""

    def convert_sub_14B34(self) -> str:
        """精确转换sub_14B34函数"""
        return """/**
 * @brief 数组元素处理和查表函数 - 精确复刻汇编逻辑
 * @note 对应汇编函数 sub_14B34
 * @param index 数组索引 (对应R0)
 * @return 查表结果 (对应R0)
 */
uint16_t precise_function_14b34(uint8_t index)
{
    // LDR.W R1, =0x2000797C - 加载16位数组基地址
    volatile uint16_t *array_797C = (volatile uint16_t *)0x2000797C;
    
    // UXTB R0, R0 - 确保索引为8位无符号
    index = index & 0xFF;
    
    // LDRH.W R1, [R1,R0,LSL#1] - 读取16位值 (base + index*2)
    uint16_t value = array_797C[index];
    
    // CMP R1, #6 - 比较值与6
    if (value >= 6) {
        // BLT loc_14B4E - 如果大于等于6，设置为5
        // MOVS R1, #5
        value = 5;
        // LDR.W R2, =0x2000797C
        // UXTB R0, R0
        // STRH.W R1, [R2,R0,LSL#1] - 写回数组
        array_797C[index] = value;
    }
    
    // loc_14B4E:
    // LDR.W R1, =0x8016874 - 加载查找表基地址
    volatile uint8_t *lookup_table = (volatile uint8_t *)0x8016874;
    
    // LDR.W R2, =0x2000797C
    // UXTB R0, R0
    // LDRH.W R2, [R2,R0,LSL#1] - 重新读取数组值作为查找表索引
    uint16_t table_index = array_797C[index];
    
    // LDRB R1, [R2,R1] - 从查找表读取字节值
    uint8_t lookup_result = lookup_table[table_index];
    
    // LDR.W R2, =0x20007A5C - 加载另一个数组基地址
    volatile uint16_t *array_7A5C = (volatile uint16_t *)0x20007A5C;
    
    // UXTB R0, R0
    // STRH.W R1, [R2,R0,LSL#1] - 将查找结果存储到另一个数组
    array_7A5C[index] = lookup_result;
    
    // LDR.W R1, =0x20007A5C
    // UXTB R0, R0
    // LDRH.W R0, [R1,R0,LSL#1] - 读取并返回最终结果
    return array_7A5C[index];
    
    // BX LR - 函数返回
}"""

    def convert_sub_14CB4(self) -> str:
        """精确转换sub_14CB4函数"""
        return """/**
 * @brief 系统初始化函数 - 精确复刻汇编逻辑
 * @note 对应汇编函数 sub_14CB4
 */
void precise_function_14cb4(void)
{
    // PUSH {R4,LR} - 保存寄存器
    
    // BL sub_16390 - 调用子函数
    extern void sub_16390(void);
    sub_16390();
    
    // MOVS R0, #0
    // LDR.W R1, =0x200080BE
    // STRH R0, [R1] - 清零16位变量
    *(volatile uint16_t *)0x200080BE = 0;
    
    // BL off_14B74 - 调用函数指针
    extern void (*off_14B74)(void);
    off_14B74();
    
    // MOVS R0, #0
    // LDR.W R1, =0x20008131
    // STRB R0, [R1] - 清零8位变量
    *(volatile uint8_t *)0x20008131 = 0;
    
    // 初始化循环变量
    uint8_t loop_counter = 0;
    
    // loc_14CD2: 第一个循环 - 初始化16位数组
    volatile uint16_t *array_797C = (volatile uint16_t *)0x2000797C;
    volatile uint16_t *array_7DE0 = (volatile uint16_t *)0x20007DE0;
    
    for (loop_counter = 0; loop_counter < 8; loop_counter++) {
        // UXTB R4, R4 - 确保循环变量为8位
        // CMP R4, #8 - 比较循环变量与8
        // BGE loc_14CF0 - 如果大于等于8，退出循环
        
        // LDR.W R0, =0x2000797C
        // UXTB R4, R4
        // LDRH.W R0, [R0,R4,LSL#1] - 读取源数组
        uint16_t src_value = array_797C[loop_counter];
        
        // LDR.W R1, =0x20007DE0
        // UXTB R4, R4
        // STRH.W R0, [R1,R4,LSL#1] - 写入目标数组
        array_7DE0[loop_counter] = src_value;
        
        // ADDS R4, R4, #1 - 循环变量递增
    }
    
    // loc_14CF0: 第二个循环 - 初始化32位数组
    volatile uint32_t *array_7684 = (volatile uint32_t *)0x20007684;
    volatile uint16_t *array_7604 = (volatile uint16_t *)0x20007604;
    
    for (loop_counter = 0; loop_counter < 8; loop_counter++) {
        // 清零32位数组元素
        array_7684[loop_counter] = 0;
        // 清零16位数组元素
        array_7604[loop_counter] = 0;
    }
    
    // 第三个循环 - 初始化多个8位数组
    volatile uint8_t *array_8134 = (volatile uint8_t *)0x20008134;
    volatile uint8_t *array_8136 = (volatile uint8_t *)0x20008136;
    volatile uint8_t *array_8138 = (volatile uint8_t *)0x20008138;
    volatile uint8_t *array_813A = (volatile uint8_t *)0x2000813A;
    volatile uint8_t *array_813C = (volatile uint8_t *)0x2000813C;
    
    for (loop_counter = 0; loop_counter < 2; loop_counter++) {
        array_8134[loop_counter] = 0;
        array_8136[loop_counter] = 0;
        array_8138[loop_counter] = 0;
        array_813A[loop_counter] = 0;
        array_813C[loop_counter] = 0;
    }
    
    // 第四个循环 - 初始化更多数组
    volatile uint8_t *array_80C0 = (volatile uint8_t *)0x200080C0;
    volatile uint16_t *array_80BC = (volatile uint16_t *)0x200080BC;
    volatile uint32_t *array_7220 = (volatile uint32_t *)0x20007220;
    volatile uint32_t *array_7304 = (volatile uint32_t *)0x20007304;
    volatile uint8_t *array_7384 = (volatile uint8_t *)0x20007384;
    volatile uint16_t *array_7404 = (volatile uint16_t *)0x20007404;
    
    for (loop_counter = 0; loop_counter < 16; loop_counter++) {
        array_80C0[loop_counter] = 0;
        array_80BC[loop_counter] = 0;
        array_7220[loop_counter] = 0x20007220; // 特殊初始化值
        array_7304[loop_counter] = 0x20007304; // 特殊初始化值
        array_7384[loop_counter] = 0;
        array_7404[loop_counter] = 0;
    }
    
    // 调用配置函数
    extern void sub_16472(uint32_t, uint32_t);
    sub_16472(1, 0x200080C2);
    sub_16472(1, 0x200080C4);
    
    // 清零最终的状态变量
    *(volatile uint8_t *)0x200080C6 = 0;
    *(volatile uint8_t *)0x200080C8 = 0;
    *(volatile uint8_t *)0x200080CA = 0;
    *(volatile uint8_t *)0x200080CC = 0;
    *(volatile uint8_t *)0x200080CE = 0;
    
    // POP {R4,PC} - 恢复寄存器并返回
}"""

    def generate_exact_conversions(self) -> None:
        """生成精确转换的C代码文件"""
        print("生成精确汇编转换...")
        
        # 创建精确转换目录
        os.makedirs("exact_conversions", exist_ok=True)
        
        # 转换前几个关键函数
        conversions = {
            "sub_14B18": self.convert_sub_14B18(),
            "sub_14B34": self.convert_sub_14B34(),
            "sub_14CB4": self.convert_sub_14CB4()
        }
        
        # 生成C文件
        c_content = """// 精确汇编转换结果 - 完全复刻汇编逻辑
#include <stdint.h>

"""
        
        for func_name, c_code in conversions.items():
            c_content += c_code + "\n\n"
        
        # 保存到文件
        with open("exact_conversions/precise_functions.c", 'w', encoding='utf-8') as f:
            f.write(c_content)
        
        # 生成头文件
        header_content = """#ifndef PRECISE_FUNCTIONS_H
#define PRECISE_FUNCTIONS_H

#include <stdint.h>

// 精确转换的函数声明
float precise_function_14b18(uint8_t index);
uint16_t precise_function_14b34(uint8_t index);
void precise_function_14cb4(void);

#endif // PRECISE_FUNCTIONS_H
"""
        
        with open("exact_conversions/precise_functions.h", 'w', encoding='utf-8') as f:
            f.write(header_content)
        
        print("精确转换完成！")
        print("文件保存在 exact_conversions/ 目录")

def main():
    converter = ExactAsmConverter("bin/MH25QH128.bin.asm")
    converter.generate_exact_conversions()
    
    print("\n=== 精确转换对比分析 ===")
    print("原始转换 vs 精确转换:")
    print("1. 原始转换：使用通用模板，没有分析具体汇编逻辑")
    print("2. 精确转换：逐行分析汇编指令，完全复刻原始逻辑")
    print("\n关键差异:")
    print("- sub_14B18: 浮点数组访问，带边界检查")
    print("- sub_14B34: 复杂的数组操作和查表逻辑")
    print("- sub_14CB4: 系统初始化，多个数组的初始化循环")

if __name__ == "__main__":
    main()
