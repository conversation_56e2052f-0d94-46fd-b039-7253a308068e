// 大规模手工转换批次 17 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_4FB08
 * @note 指令数: 16
 */
void func_4fb08(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_45670123 = (volatile uint32_t *)0x45670123;
    volatile uint32_t *addr_CDEF89AB = (volatile uint32_t *)0xCDEF89AB;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_40022004 = (volatile uint32_t *)0x40022004;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4FB2C
 * @note 指令数: 8
 */
uint32_t func_4fb2c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40022010 = (volatile uint32_t *)0x40022010;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4FB3C
 * @note 指令数: 18
 */
uint32_t func_4fb3c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40022014 = (volatile uint32_t *)0x40022014;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20007368 = (volatile uint32_t *)0x20007368;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4FB60
 * @note 指令数: 11
 */
uint32_t func_4fb60(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_40022010 = (volatile uint32_t *)0x40022010;
    volatile uint32_t *addr_20007368 = (volatile uint32_t *)0x20007368;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4FB76
 * @note 指令数: 41
 */
void func_4fb76(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;
    volatile uint32_t *addr_4002200C = (volatile uint32_t *)0x4002200C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4FBCC
 * @note 指令数: 25
 */
uint32_t func_4fbcc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;
    volatile uint32_t *addr_20007368 = (volatile uint32_t *)0x20007368;
    volatile uint32_t *addr_4002200C = (volatile uint32_t *)0x4002200C;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4FC1C
 * @note 指令数: 150
 */
void func_4fc1c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_2000784C = (volatile uint32_t *)0x2000784C;
    volatile uint32_t *addr_20007508 = (volatile uint32_t *)0x20007508;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4FD56
 * @note 指令数: 114
 */
void func_4fd56(void)
{
    // 内存地址定义
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_9600 = (volatile uint32_t *)0x9600;
    volatile uint32_t *addr_200077D0 = (volatile uint32_t *)0x200077D0;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4FE3C
 * @note 指令数: 71
 */
void func_4fe3c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20005F8E = (volatile uint32_t *)0x20005F8E;
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_31 = (volatile uint32_t *)0x31;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4FF04
 * @note 指令数: 117
 */
void func_4ff04(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200076D0 = (volatile uint32_t *)0x200076D0;
    volatile uint32_t *addr_2000784C = (volatile uint32_t *)0x2000784C;
    volatile uint32_t *addr_200077D4 = (volatile uint32_t *)0x200077D4;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4FFFC
 * @note 指令数: 19
 */
void func_4fffc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200077D8 = (volatile uint32_t *)0x200077D8;
    volatile uint32_t *addr_200078C7 = (volatile uint32_t *)0x200078C7;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_5005C
 * @note 指令数: 19
 */
void func_5005c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_200077D8 = (volatile uint32_t *)0x200077D8;
    volatile uint32_t *addr_200078C7 = (volatile uint32_t *)0x200078C7;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_50090
 * @note 指令数: 77
 */
void func_50090(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10000 = (volatile uint32_t *)0x10000;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_200078C6 = (volatile uint32_t *)0x200078C6;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_5013C
 * @note 指令数: 15
 */
void func_5013c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000784C = (volatile uint32_t *)0x2000784C;
    volatile uint32_t *addr_200069B0 = (volatile uint32_t *)0x200069B0;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_50180
 * @note 指令数: 154
 */
void func_50180(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_60 = (volatile uint32_t *)0x60;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_502B0
 * @note 指令数: 73
 */
uint16_t func_502b0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_2000751C = (volatile uint32_t *)0x2000751C;

    // 局部变量
    uint16_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_5034C
 * @note 指令数: 21
 */
void func_5034c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000751C = (volatile uint32_t *)0x2000751C;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_50378
 * @note 指令数: 17
 */
void func_50378(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000751C = (volatile uint32_t *)0x2000751C;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_503A0
 * @note 指令数: 34
 */
void func_503a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007508 = (volatile uint32_t *)0x20007508;
    volatile uint32_t *addr_20A = (volatile uint32_t *)0x20A;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_503EC
 * @note 指令数: 26
 */
void func_503ec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_2000784C = (volatile uint32_t *)0x2000784C;
    volatile uint32_t *addr_204 = (volatile uint32_t *)0x204;
    volatile uint32_t *addr_2000784E = (volatile uint32_t *)0x2000784E;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_50428
 * @note 指令数: 139
 */
void func_50428(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_55 = (volatile uint32_t *)0x55;
    volatile uint32_t *addr_26 = (volatile uint32_t *)0x26;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_50544
 * @note 指令数: 37
 */
void func_50544(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_50582
 * @note 指令数: 127
 */
void func_50582(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_50680
 * @note 指令数: 46
 */
void func_50680(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_28 = (volatile uint32_t *)0x28;
    volatile uint32_t *addr_20007508 = (volatile uint32_t *)0x20007508;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_506F8
 * @note 指令数: 51
 */
void func_506f8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007758 = (volatile uint32_t *)0x20007758;
    volatile uint32_t *addr_2000774C = (volatile uint32_t *)0x2000774C;
    volatile uint32_t *addr_20007748 = (volatile uint32_t *)0x20007748;
    volatile uint32_t *addr_20006E34 = (volatile uint32_t *)0x20006E34;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_5075C
 * @note 指令数: 111
 */
void func_5075c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000778C = (volatile uint32_t *)0x2000778C;
    volatile uint32_t *addr_20007760 = (volatile uint32_t *)0x20007760;
    volatile uint32_t *addr_2000734C = (volatile uint32_t *)0x2000734C;
    volatile uint32_t *addr_200078A5 = (volatile uint32_t *)0x200078A5;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_508AC
 * @note 指令数: 319
 */
void func_508ac(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20007760 = (volatile uint32_t *)0x20007760;
    volatile uint32_t *addr_2000734C = (volatile uint32_t *)0x2000734C;
    volatile uint32_t *addr_20007758 = (volatile uint32_t *)0x20007758;
    volatile uint32_t *addr_20006FC4 = (volatile uint32_t *)0x20006FC4;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_50B28
 * @note 指令数: 24
 */
void func_50b28(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007788 = (volatile uint32_t *)0x20007788;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_50B8C
 * @note 指令数: 158
 */
void func_50b8c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007768 = (volatile uint32_t *)0x20007768;
    volatile uint32_t *addr_20007774 = (volatile uint32_t *)0x20007774;
    volatile uint32_t *addr_2000776C = (volatile uint32_t *)0x2000776C;
    volatile uint32_t *addr_20007784 = (volatile uint32_t *)0x20007784;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_50D08
 * @note 指令数: 330
 */
void func_50d08(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_2000778C = (volatile uint32_t *)0x2000778C;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_200078A5 = (volatile uint32_t *)0x200078A5;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_50FC4
 * @note 指令数: 3
 */
uint32_t func_50fc4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200078A6 = (volatile uint32_t *)0x200078A6;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_50FCA
 * @note 指令数: 3
 */
uint8_t func_50fca(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200078A6 = (volatile uint32_t *)0x200078A6;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_51004
 * @note 指令数: 73
 */
void func_51004(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200077E8 = (volatile uint32_t *)0x200077E8;
    volatile uint32_t *addr_200076E0 = (volatile uint32_t *)0x200076E0;
    volatile uint32_t *addr_200078CF = (volatile uint32_t *)0x200078CF;
    volatile uint32_t *addr_200077E4 = (volatile uint32_t *)0x200077E4;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_51096
 * @note 指令数: 33
 */
void func_51096(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007580 = (volatile uint32_t *)0x20007580;
    volatile uint32_t *addr_3F800000 = (volatile uint32_t *)0x3F800000;
    volatile uint32_t *addr_200078CA = (volatile uint32_t *)0x200078CA;
    volatile uint32_t *addr_20007570 = (volatile uint32_t *)0x20007570;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_510DA
 * @note 指令数: 110
 */
uint32_t func_510da(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200078CD = (volatile uint32_t *)0x200078CD;
    volatile uint32_t *addr_200078CE = (volatile uint32_t *)0x200078CE;
    volatile uint32_t *addr_200076F0 = (volatile uint32_t *)0x200076F0;
    volatile uint32_t *addr_200078CB = (volatile uint32_t *)0x200078CB;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_51210
 * @note 指令数: 315
 */
void func_51210(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200077E8 = (volatile uint32_t *)0x200077E8;
    volatile uint32_t *addr_200076E8 = (volatile uint32_t *)0x200076E8;
    volatile uint32_t *addr_200078CA = (volatile uint32_t *)0x200078CA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_5149A
 * @note 指令数: 3
 */
uint32_t func_5149a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200078D0 = (volatile uint32_t *)0x200078D0;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_514A0
 * @note 指令数: 3
 */
uint8_t func_514a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200078D0 = (volatile uint32_t *)0x200078D0;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_514EC
 * @note 指令数: 65
 */
void func_514ec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200078D7 = (volatile uint32_t *)0x200078D7;
    volatile uint32_t *addr_20007408 = (volatile uint32_t *)0x20007408;
    volatile uint32_t *addr_200075E0 = (volatile uint32_t *)0x200075E0;
    volatile uint32_t *addr_200075D0 = (volatile uint32_t *)0x200075D0;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_5156E
 * @note 指令数: 71
 */
uint32_t func_5156e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200078DB = (volatile uint32_t *)0x200078DB;
    volatile uint32_t *addr_20006EA8 = (volatile uint32_t *)0x20006EA8;
    volatile uint32_t *addr_200078DE = (volatile uint32_t *)0x200078DE;
    volatile uint32_t *addr_200078DC = (volatile uint32_t *)0x200078DC;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_51654
 * @note 指令数: 400
 */
void func_51654(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200075D0 = (volatile uint32_t *)0x200075D0;
    volatile uint32_t *addr_200078DD = (volatile uint32_t *)0x200078DD;
    volatile uint32_t *addr_20006E68 = (volatile uint32_t *)0x20006E68;
    volatile uint32_t *addr_200078DA = (volatile uint32_t *)0x200078DA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_519F4
 * @note 指令数: 210
 */
void func_519f4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20007856 = (volatile uint32_t *)0x20007856;
    volatile uint32_t *addr_20007866 = (volatile uint32_t *)0x20007866;
    volatile uint32_t *addr_20007858 = (volatile uint32_t *)0x20007858;
    volatile uint32_t *addr_5A = (volatile uint32_t *)0x5A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_51BF8
 * @note 指令数: 283
 */
void func_51bf8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20007856 = (volatile uint32_t *)0x20007856;
    volatile uint32_t *addr_20007866 = (volatile uint32_t *)0x20007866;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_51E58
 * @note 指令数: 45
 */
void func_51e58(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8015FA8 = (volatile uint32_t *)0x8015FA8;
    volatile uint32_t *addr_20007866 = (volatile uint32_t *)0x20007866;
    volatile uint32_t *addr_8016078 = (volatile uint32_t *)0x8016078;
    volatile uint32_t *addr_20007868 = (volatile uint32_t *)0x20007868;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_51F14
 * @note 指令数: 192
 */
void func_51f14(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007866 = (volatile uint32_t *)0x20007866;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_20007870 = (volatile uint32_t *)0x20007870;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_521D0
 * @note 指令数: 341
 */
void func_521d0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20007866 = (volatile uint32_t *)0x20007866;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_200078C9 = (volatile uint32_t *)0x200078C9;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_526D4
 * @note 指令数: 11
 */
uint32_t func_526d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200076F8 = (volatile uint32_t *)0x200076F8;
    volatile uint32_t *addr_20007882 = (volatile uint32_t *)0x20007882;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_526EC
 * @note 指令数: 20
 */
void func_526ec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200076F8 = (volatile uint32_t *)0x200076F8;
    volatile uint32_t *addr_FFFF = (volatile uint32_t *)0xFFFF;
    volatile uint32_t *addr_20007882 = (volatile uint32_t *)0x20007882;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_52720
 * @note 指令数: 48
 */
uint32_t func_52720(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007648 = (volatile uint32_t *)0x20007648;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_200078A0 = (volatile uint32_t *)0x200078A0;
    volatile uint32_t *addr_8015D50 = (volatile uint32_t *)0x8015D50;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_5278C
 * @note 指令数: 22
 */
void func_5278c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200078A1 = (volatile uint32_t *)0x200078A1;
    volatile uint32_t *addr_200078A0 = (volatile uint32_t *)0x200078A0;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

