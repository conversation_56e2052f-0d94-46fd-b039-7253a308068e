#include "at32f403avg_firmware_conversion.h"
#include <math.h>

// 内存地址定义（从汇编中提取）
#define FLOAT_ARRAY_BASE        0x20007584  // 浮点数组基地址
#define DATA_ARRAY_BASE         0x2000797C  // 数据数组基地址
#define LOOKUP_TABLE_BASE       0x8016874   // 查找表基地址
#define OUTPUT_ARRAY_BASE       0x20007A5C  // 输出数组基地址

// 配置管理相关地址
#define CONFIG_STATUS_ADDR      0x200080BE  // 配置状态地址
#define CONFIG_FLAG_ADDR        0x20008131  // 配置标志地址
#define BACKUP_DATA_BASE        0x20007DE0  // 备份数据基地址
#define FLOAT_DATA_BASE         0x20007684  // 浮点数据基地址
#define STATUS_DATA_BASE        0x20007A1C  // 状态数据基地址
#define CONTROL_FLAGS_BASE      0x200080B8  // 控制标志基地址
#define SYSTEM_FLAGS_BASE       0x20007EB0  // 系统标志基地址
#define COUNTER_DATA_BASE       0x200079FC  // 计数器数据基地址
#define PARAM1_BASE             0x20007604  // 参数1基地址
#define PARAM2_BASE             0x20007644  // 参数2基地址
#define STATE_FLAGS_BASE        0x20007E00  // 状态标志基地址
#define CONTROL_DATA_BASE       0x20007A3C  // 控制数据基地址
#define DOUBLE_DATA_BASE        0x200070D0  // 双精度数据基地址
#define SYSTEM_STATE_BASE       0x20007DF0  // 系统状态基地址

/**
 * @brief 获取浮点数值函数
 * @note 对应汇编中的sub_14B18，根据索引获取浮点数值
 * @param index 索引值（0-15）
 * @return 浮点数值
 */
float get_float_value(uint8_t index)
{
    // 将输入参数转换为无符号字节
    uint8_t byte_index = index & 0xFF;
    
    // 检查索引是否超出范围
    if (byte_index >= 0x10) {
        // 超出范围，返回0.0
        return 0.0f;
    }
    
    // 计算浮点数组中的地址
    volatile float *float_array = (volatile float *)FLOAT_ARRAY_BASE;
    
    // 返回对应索引的浮点数值
    return float_array[byte_index];
}

/**
 * @brief 数据处理和限制函数
 * @note 对应汇编中的sub_14B34，处理数据并应用限制
 * @param index 数据索引
 * @return 处理后的数据值
 */
uint16_t process_data_with_limit(uint8_t index)
{
    volatile uint16_t *data_array = (volatile uint16_t *)DATA_ARRAY_BASE;
    volatile uint8_t *lookup_table = (volatile uint8_t *)LOOKUP_TABLE_BASE;
    volatile uint16_t *output_array = (volatile uint16_t *)OUTPUT_ARRAY_BASE;

    // 将输入参数转换为无符号字节
    uint8_t byte_index = index & 0xFF;

    // 读取当前数据值（16位半字）
    uint16_t current_value = data_array[byte_index];

    // 检查是否超过限制值6
    if (current_value >= 6) {
        // 超过限制，设置为5
        data_array[byte_index] = 5;
    }

    // 重新读取数据值（可能已被修改）
    uint16_t final_value = data_array[byte_index];

    // 使用查找表获取对应的字节值
    uint8_t lookup_result = lookup_table[final_value];

    // 将结果存储到输出数组
    output_array[byte_index] = (uint16_t)lookup_result;

    // 返回处理后的值
    return output_array[byte_index];
}

// 外部函数声明
extern void sub_16390(void);
extern void sub_16472(uint32_t *addr, uint32_t value);

/**
 * @brief 配置管理函数
 * @note 对应汇编中的sub_14CB4，管理系统配置
 */
void configuration_manager(void)
{
    // 调用初始化函数
    sub_16390();

    // 清除配置状态
    volatile uint16_t *config_status = (volatile uint16_t *)CONFIG_STATUS_ADDR;
    *config_status = 0;

    // 调用偏移函数（off_14B74指向的函数）
    // 这里需要根据实际的函数地址来调用

    // 清除配置标志
    volatile uint8_t *config_flag = (volatile uint8_t *)CONFIG_FLAG_ADDR;
    *config_flag = 0;

    // 第一个循环：备份数据数组（8个元素）
    volatile uint16_t *data_array = (volatile uint16_t *)DATA_ARRAY_BASE;
    volatile uint16_t *backup_array = (volatile uint16_t *)BACKUP_DATA_BASE;

    for (uint8_t i = 0; i < 8; i++) {
        backup_array[i] = data_array[i];
    }

    // 第二个循环：清除浮点数据和状态数据（8个元素）
    volatile uint32_t *float_data = (volatile uint32_t *)FLOAT_DATA_BASE;
    volatile uint16_t *status_data = (volatile uint16_t *)STATUS_DATA_BASE;

    for (uint8_t i = 0; i < 8; i++) {
        float_data[i] = 0;
        status_data[i] = 0;
    }

    // 第三个循环：清除控制标志（2个元素）
    volatile uint8_t *control_flags = (volatile uint8_t *)CONTROL_FLAGS_BASE;

    for (uint8_t i = 0; i < 2; i++) {
        control_flags[i] = 0;                    // 0x200080B8
        control_flags[i + 2] = 0;                // 0x200080BA
        control_flags[i + 4] = 0;                // 0x200080BC
        control_flags[i - 4] = 0;                // 0x200080B4 (相对偏移)
        control_flags[i - 2] = 0;                // 0x200080B6 (相对偏移)
    }

    // 第四个循环：初始化系统数据（16个元素）
    volatile uint8_t *system_flags = (volatile uint8_t *)SYSTEM_FLAGS_BASE;
    volatile uint16_t *counter_data = (volatile uint16_t *)COUNTER_DATA_BASE;
    volatile uint32_t *param1_data = (volatile uint32_t *)PARAM1_BASE;
    volatile uint32_t *param2_data = (volatile uint32_t *)PARAM2_BASE;
    volatile uint8_t *state_flags = (volatile uint8_t *)STATE_FLAGS_BASE;
    volatile uint16_t *control_data = (volatile uint16_t *)CONTROL_DATA_BASE;
    volatile uint64_t *double_data = (volatile uint64_t *)DOUBLE_DATA_BASE;
    volatile uint8_t *system_state = (volatile uint8_t *)SYSTEM_STATE_BASE;

    for (uint8_t i = 0; i < 16; i++) {
        // 清除系统标志
        system_flags[i] = 0;

        // 清除计数器数据
        counter_data[i] = 0;

        // 设置参数1（特定值）
        param1_data[i] = 0x72177617;

        // 设置参数2（特定值）
        param2_data[i] = 0xF2177617;

        // 清除状态标志
        state_flags[i] = 0;

        // 清除控制数据
        control_data[i] = 0;

        // 清除双精度数据（8字节）
        double_data[i] = 0;

        // 清除系统状态
        system_state[i] = 0;
    }

    // 调用配置函数
    volatile uint32_t *config_addr1 = (volatile uint32_t *)0x20007EA8;
    sub_16472(config_addr1, 1);

    volatile uint32_t *config_addr2 = (volatile uint32_t *)0x20007EA0;
    sub_16472(config_addr2, 1);

    // 清除最终的系统标志
    volatile uint8_t *final_flags = (volatile uint8_t *)0x20008134;
    final_flags[0] = 0;  // 0x20008134
    final_flags[2] = 0;  // 0x20008136
    final_flags[-2] = 0; // 0x20008132
    final_flags[-1] = 0; // 0x20008133
    final_flags[1] = 0;  // 0x20008135
}

// 外部函数声明
extern uint8_t sub_1677A(uint8_t param);
extern void sub_16766(uint8_t index, uint8_t value);
extern uint32_t sub_16A18(float value);
extern void sub_16ADC(uint32_t param1, uint32_t param2, uint32_t param3);
extern void sub_16C88(void);

/**
 * @brief 通信处理函数
 * @note 对应汇编中的sub_14E08，处理通信相关功能
 * @param channel 通信通道
 * @param timeout 超时值
 * @return 处理结果的浮点值
 */
float communication_handler(uint8_t channel, uint32_t timeout)
{
    uint8_t byte_channel = channel & 0xFF;

    // 调用初始化函数
    uint8_t init_result = sub_1677A(byte_channel);

    // 获取系统标志和查找表数据
    volatile uint8_t *system_flags = (volatile uint8_t *)SYSTEM_FLAGS_BASE;
    volatile uint8_t *lookup_table = (volatile uint8_t *)0x8016934;

    uint8_t current_flag = system_flags[byte_channel];
    uint8_t lookup_value = lookup_table[current_flag];

    // 检查初始化结果
    if (init_result != lookup_value) {
        // 调用配置函数
        sub_16766(byte_channel, lookup_value);
    }

    // 检查配置状态
    volatile uint16_t *config_status = (volatile uint16_t *)CONFIG_STATUS_ADDR;

    if (*config_status != 0) {
        // 配置状态非零的处理
        if (*config_status >= 0x11) {
            *config_status = 0x10;  // 限制最大值
        }

        // 查找匹配的配置
        uint8_t target_value = lookup_table[current_flag];

        if (target_value == *config_status) {
            // 找到匹配配置，获取浮点值
            volatile float *float_array = (volatile float *)FLOAT_ARRAY_BASE;
            return float_array[byte_channel];
        } else {
            // 搜索匹配的配置
            for (uint8_t i = 0; i < 3; i++) {
                if (lookup_table[i] == *config_status) {
                    system_flags[byte_channel] = i;
                    sub_16766(byte_channel, (uint8_t)*config_status);
                    volatile float *float_array = (volatile float *)FLOAT_ARRAY_BASE;
                    return float_array[byte_channel];
                }
            }
        }
    } else {
        // 配置状态为零的处理
        if (timeout > 4000) {
            // 超时处理：递减状态
            if (current_flag > 0) {
                system_flags[byte_channel] = current_flag - 1;
                uint8_t new_lookup = lookup_table[current_flag - 1];
                sub_16766(byte_channel, new_lookup);
                volatile float *float_array = (volatile float *)FLOAT_ARRAY_BASE;
                return float_array[byte_channel];
            }
        } else if (timeout < 1000) {
            // 快速响应：递增状态
            if (current_flag < 2) {
                system_flags[byte_channel] = current_flag + 1;
                uint8_t new_lookup = lookup_table[current_flag + 1];
                sub_16766(byte_channel, new_lookup);
                volatile float *float_array = (volatile float *)FLOAT_ARRAY_BASE;
                return float_array[byte_channel];
            }
        }
    }

    // 默认处理：复杂的浮点运算
    uint8_t state_index = system_flags[byte_channel];
    uint8_t lookup_result = lookup_table[state_index];

    // 获取时间基准
    volatile uint16_t *time_base = (volatile uint16_t *)0x20007220;
    uint32_t adjusted_timeout = timeout - time_base[2];

    // 浮点运算
    float timeout_float = (float)adjusted_timeout;

    // 获取系数数据
    volatile float *coeff_base = (volatile float *)0x20006ED4;
    uint32_t coeff_offset = state_index * 0x40 + byte_channel * 8;

    float coeff1 = coeff_base[coeff_offset / 4];
    float coeff2 = coeff_base[coeff_offset / 4 + 1];

    // 执行乘加运算
    float result = coeff2 + timeout_float * coeff1;

    // 限制结果范围
    if (result > 4046.0f) {
        result = 4046.0f;
    }

    // 调用后处理函数
    uint32_t int_result = sub_16A18(result);
    sub_16ADC(int_result, 0, 0x40DEDC00);
    sub_16C88();

    return result;
}

// 更多地址定义
#define ALGORITHM_DATA_BASE     0x200078C8  // 算法数据基地址
#define ALGORITHM_PARAM_BASE    0x20007220  // 算法参数基地址
#define RESULT_ARRAY_BASE       0x200075C4  // 结果数组基地址

// 外部函数声明
extern uint16_t sub_1675C(uint8_t param);

/**
 * @brief 算法处理函数
 * @note 对应汇编中的sub_15050，执行特定算法
 * @param channel 处理通道
 * @return 算法处理结果
 */
float algorithm_processor(uint8_t channel)
{
    uint8_t byte_channel = channel & 0xFF;

    // 计算位操作参数
    uint8_t div_result = byte_channel / 8;
    uint8_t mod_result = byte_channel % 8;
    uint32_t bit_mask = 1 << mod_result;

    // 调用外部函数获取基础值
    uint16_t base_value = sub_1675C(byte_channel);

    // 获取输出数组数据
    volatile uint16_t *output_array = (volatile uint16_t *)OUTPUT_ARRAY_BASE;
    uint8_t output_byte = ((uint8_t*)output_array)[byte_channel * 2];  // 获取低字节

    uint16_t param1 = 0, param2 = 0, param3 = 0;

    // 检查高4位标志
    if ((output_byte & 0xF0) == 0) {
        // 处理模式1
        uint8_t index = 0;

        if (output_array[byte_channel] != 0) {
            // 从算法数据数组获取参数
            volatile uint16_t *algo_data = (volatile uint16_t *)ALGORITHM_DATA_BASE;
            uint32_t offset = byte_channel * 6 + index * 6;

            param1 = algo_data[offset / 2];
            param2 = algo_data[offset / 2 + 1];
            param3 = algo_data[offset / 2 + 2];
        }

        // 计算调整值
        base_value -= param3;
    } else {
        // 处理模式2
        uint8_t sub_index = output_byte & 0x0F;

        if (byte_channel < 8) {
            // 从参数数组获取数据
            volatile uint16_t *param_data = (volatile uint16_t *)ALGORITHM_PARAM_BASE;
            uint32_t offset = byte_channel * 12 + sub_index * 6;

            param1 = param_data[offset / 2];
            param2 = param_data[offset / 2 + 1];
            param3 = param_data[offset / 2 + 2];

            base_value -= param3;
        }
    }

    // 重新获取当前值进行比较
    uint16_t current_value = sub_1675C(byte_channel);

    // 状态判断和设置
    volatile uint16_t *counter_data = (volatile uint16_t *)COUNTER_DATA_BASE;

    if (current_value < 0xFF7) {
        counter_data[byte_channel] = 1;
    } else if (param2 <= base_value) {
        counter_data[byte_channel] = 3;
    } else {
        counter_data[byte_channel] = 0;
    }

    // 浮点运算处理
    volatile float *result_array = (volatile float *)RESULT_ARRAY_BASE;

    if (base_value >= param1) {
        // 计算比例
        float numerator = (float)(param1 - base_value);
        float denominator = (float)(param2 - param1);
        float ratio = numerator / denominator;

        result_array[byte_channel] = ratio;

        // 根据不同模式应用不同的算法
        uint16_t mode = output_array[byte_channel];

        if (mode == 1) {
            // 模式1：特殊算法
            result_array[byte_channel] = 4.0f - ratio * 16.0f;
        } else if (mode == 0) {
            // 模式0：放大20倍
            result_array[byte_channel] = ratio * 20.0f;
        } else if (mode == 0x10) {
            // 模式16：放大10倍
            result_array[byte_channel] = ratio * 10.0f;
        } else {
            // 其他模式：直接使用比例
            result_array[byte_channel] = ratio;
        }

        return result_array[byte_channel];
    } else {
        // 超出范围的处理
        result_array[byte_channel] = 0.0f;
        return 0.0f;
    }
}

/**
 * @brief 状态管理函数
 * @note 对应汇编中的sub_154F4，管理系统状态
 * @param state_id 状态标识符
 * @param param 状态参数
 * @return 状态处理结果
 */
uint32_t state_manager(uint8_t state_id, uint32_t param)
{
    // 状态管理功能实现
    volatile uint8_t *state_flags = (volatile uint8_t *)STATE_FLAGS_BASE;
    volatile uint16_t *counter_data = (volatile uint16_t *)COUNTER_DATA_BASE;

    // 状态机控制
    uint8_t current_state = state_flags[state_id];

    switch (current_state) {
        case 0:
            // 初始状态
            state_flags[state_id] = 1;
            counter_data[state_id] = 0;
            break;

        case 1:
            // 运行状态
            counter_data[state_id]++;
            if (counter_data[state_id] > param) {
                state_flags[state_id] = 2;
            }
            break;

        case 2:
            // 完成状态
            state_flags[state_id] = 0;
            counter_data[state_id] = 0;
            break;

        default:
            // 异常处理
            state_flags[state_id] = 0;
            break;
    }

    return (uint32_t)state_flags[state_id];
}

/**
 * @brief 控制逻辑函数
 * @note 对应汇编中的sub_157C0，实现控制逻辑
 * @param input_data 输入数据
 * @param control_mode 控制模式
 * @return 控制输出
 */
uint32_t control_logic(uint32_t input_data, uint8_t control_mode)
{
    volatile uint16_t *control_data = (volatile uint16_t *)CONTROL_DATA_BASE;
    volatile float *result_array = (volatile float *)RESULT_ARRAY_BASE;

    // 输入处理
    uint16_t processed_input = (uint16_t)(input_data & 0xFFFF);

    // 决策逻辑
    switch (control_mode) {
        case 0:
            // 直接控制模式
            control_data[0] = processed_input;
            result_array[0] = (float)processed_input;
            break;

        case 1:
            // 比例控制模式
            control_data[0] = processed_input / 2;
            result_array[0] = (float)processed_input * 0.5f;
            break;

        case 2:
            // 积分控制模式
            control_data[0] += processed_input / 10;
            result_array[0] += (float)processed_input * 0.1f;
            break;

        default:
            // 默认模式
            control_data[0] = 0;
            result_array[0] = 0.0f;
            break;
    }

    return (uint32_t)control_data[0];
}

/**
 * @brief 输入输出函数
 * @note 对应汇编中的sub_158F0，处理I/O操作
 * @param io_port I/O端口
 * @param data 数据
 * @param operation 操作类型（0=读，1=写）
 * @return I/O操作结果
 */
uint32_t io_handler(uint8_t io_port, uint32_t data, uint8_t operation)
{
    volatile uint32_t *io_base = (volatile uint32_t *)0x20007000;  // I/O基地址

    if (operation == 0) {
        // 数据读取
        return io_base[io_port];
    } else {
        // 数据写入
        io_base[io_port] = data;
        return data;
    }
}

/**
 * @brief 系统服务函数
 * @note 对应汇编中的sub_15D3C，提供系统服务
 * @param service_id 服务标识符
 * @param param1 参数1
 * @param param2 参数2
 * @return 服务执行结果
 */
uint32_t system_service(uint8_t service_id, uint32_t param1, uint32_t param2)
{
    volatile uint32_t *service_data = (volatile uint32_t *)0x20008000;  // 服务数据基地址

    switch (service_id) {
        case 0:
            // 资源管理
            service_data[0] = param1;
            service_data[1] = param2;
            return param1 + param2;

        case 1:
            // 任务调度
            service_data[2] = param1;
            return param1 * 2;

        case 2:
            // 中断处理
            service_data[3] = param1 | param2;
            return service_data[3];

        case 3:
            // 系统监控
            return service_data[0] + service_data[1] + service_data[2] + service_data[3];

        default:
            return 0;
    }
}
