// 大规模手工转换批次 11 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_455D4
 * @note 指令数: 3
 */
uint32_t func_455d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200077B4 = (volatile uint32_t *)0x200077B4;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_455DA
 * @note 指令数: 29
 */
void func_455da(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200077A0 = (volatile uint32_t *)0x200077A0;
    volatile uint32_t *addr_2000779C = (volatile uint32_t *)0x2000779C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_45616
 * @note 指令数: 29
 */
void func_45616(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200077A8 = (volatile uint32_t *)0x200077A8;
    volatile uint32_t *addr_200077A4 = (volatile uint32_t *)0x200077A4;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_45652
 * @note 指令数: 163
 */
void func_45652(void)
{
    // 内存地址定义
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;
    volatile uint32_t *addr_20007680 = (volatile uint32_t *)0x20007680;
    volatile uint32_t *addr_20007838 = (volatile uint32_t *)0x20007838;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_457A0
 * @note 指令数: 40
 */
void func_457a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200077B0 = (volatile uint32_t *)0x200077B0;
    volatile uint32_t *addr_20007838 = (volatile uint32_t *)0x20007838;
    volatile uint32_t *addr_200077B4 = (volatile uint32_t *)0x200077B4;
    volatile uint32_t *addr_200078AF = (volatile uint32_t *)0x200078AF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_45828
 * @note 指令数: 24
 */
void func_45828(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_4000040C = (volatile uint32_t *)0x4000040C;
    volatile uint32_t *addr_2000789F = (volatile uint32_t *)0x2000789F;
    volatile uint32_t *addr_40000410 = (volatile uint32_t *)0x40000410;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_45856
 * @note 指令数: 46
 */
void func_45856(void)
{
    // 内存地址定义
    volatile uint32_t *addr_380 = (volatile uint32_t *)0x380;
    volatile uint32_t *addr_4002101C = (volatile uint32_t *)0x4002101C;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_40000400 = (volatile uint32_t *)0x40000400;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_458AE
 * @note 指令数: 23
 */
void func_458ae(void)
{
    // 内存地址定义
    volatile uint32_t *addr_4000040C = (volatile uint32_t *)0x4000040C;
    volatile uint32_t *addr_40000410 = (volatile uint32_t *)0x40000410;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_458DA
 * @note 指令数: 4
 */
uint32_t func_458da(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40000424 = (volatile uint32_t *)0x40000424;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_458E2
 * @note 指令数: 50
 */
uint32_t func_458e2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8015C4C = (volatile uint32_t *)0x8015C4C;
    volatile uint32_t *addr_20007724 = (volatile uint32_t *)0x20007724;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_40012404 = (volatile uint32_t *)0x40012404;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45954
 * @note 指令数: 6
 */
uint32_t func_45954(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20006F20 = (volatile uint32_t *)0x20006F20;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45960
 * @note 指令数: 10
 */
uint32_t func_45960(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007724 = (volatile uint32_t *)0x20007724;
    volatile uint32_t *addr_20007620 = (volatile uint32_t *)0x20007620;
    volatile uint32_t *addr_2000789D = (volatile uint32_t *)0x2000789D;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45974
 * @note 指令数: 4
 */
uint8_t func_45974(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007620 = (volatile uint32_t *)0x20007620;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4597C
 * @note 指令数: 7
 */
uint32_t func_4597c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40012408 = (volatile uint32_t *)0x40012408;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4598A
 * @note 指令数: 22
 */
void func_4598a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000789C = (volatile uint32_t *)0x2000789C;
    volatile uint32_t *addr_20007728 = (volatile uint32_t *)0x20007728;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_459CC
 * @note 指令数: 107
 */
void func_459cc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_20007620 = (volatile uint32_t *)0x20007620;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_45AD8
 * @note 指令数: 142
 */
void func_45ad8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007724 = (volatile uint32_t *)0x20007724;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_40012440 = (volatile uint32_t *)0x40012440;
    volatile uint32_t *addr_20007620 = (volatile uint32_t *)0x20007620;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_45C04
 * @note 指令数: 20
 */
void func_45c04(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000789F = (volatile uint32_t *)0x2000789F;
    volatile uint32_t *addr_2000789E = (volatile uint32_t *)0x2000789E;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_45C68
 * @note 指令数: 9
 */
void func_45c68(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_9D = (volatile uint32_t *)0x9D;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_45C80
 * @note 指令数: 2
 */
void func_45c80(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_9D = (volatile uint32_t *)0x9D;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_45C84
 * @note 指令数: 14
 */
uint32_t func_45c84(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45C9E
 * @note 指令数: 86
 */
uint32_t func_45c9e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_FE = (volatile uint32_t *)0xFE;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45D4E
 * @note 指令数: 142
 */
uint32_t func_45d4e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_19 = (volatile uint32_t *)0x19;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45E6E
 * @note 指令数: 5
 */
uint32_t func_45e6e(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45E78
 * @note 指令数: 21
 */
uint32_t func_45e78(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45EA0
 * @note 指令数: 23
 */
uint32_t func_45ea0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_38000000 = (volatile uint32_t *)0x38000000;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_45ED2
 * @note 指令数: 194
 */
void func_45ed2(void)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_46056
 * @note 指令数: 46
 */
uint32_t func_46056(void)
{
    // 内存地址定义
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_380 = (volatile uint32_t *)0x380;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_460B8
 * @note 指令数: 8
 */
void func_460b8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_460CC
 * @note 指令数: 15
 */
uint32_t func_460cc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_41D = (volatile uint32_t *)0x41D;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_460EC
 * @note 指令数: 5
 */
void func_460ec(void)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_461B2
 * @note 指令数: 118
 */
uint32_t func_461b2(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_462A0
 * @note 指令数: 82
 */
uint32_t func_462a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_FE = (volatile uint32_t *)0xFE;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_46376
 * @note 指令数: 3
 */
void func_46376(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_4637C
 * @note 指令数: 86
 */
uint32_t func_4637c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4642A
 * @note 指令数: 94
 */
void func_4642a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_200000 = (volatile uint32_t *)0x200000;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_464E8
 * @note 指令数: 29
 */
uint32_t func_464e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_46524
 * @note 指令数: 20
 */
uint32_t func_46524(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t result = 0;

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_4654A
 * @note 指令数: 39
 */
void func_4654a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200077BC = (volatile uint32_t *)0x200077BC;
    volatile uint32_t *addr_22 = (volatile uint32_t *)0x22;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_2000021C = (volatile uint32_t *)0x2000021C;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_46594
 * @note 指令数: 14
 */
void func_46594(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000783A = (volatile uint32_t *)0x2000783A;
    volatile uint32_t *addr_200078B7 = (volatile uint32_t *)0x200078B7;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_465B0
 * @note 指令数: 14
 */
void func_465b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000783A = (volatile uint32_t *)0x2000783A;
    volatile uint32_t *addr_200078B7 = (volatile uint32_t *)0x200078B7;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_465CC
 * @note 指令数: 14
 */
void func_465cc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000783A = (volatile uint32_t *)0x2000783A;
    volatile uint32_t *addr_200078B7 = (volatile uint32_t *)0x200078B7;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_465E8
 * @note 指令数: 39
 */
void func_465e8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_3FF00000 = (volatile uint32_t *)0x3FF00000;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_4663C
 * @note 指令数: 71
 */
void func_4663c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200077BC = (volatile uint32_t *)0x200077BC;
    volatile uint32_t *addr_200078B6 = (volatile uint32_t *)0x200078B6;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_466CA
 * @note 指令数: 83
 */
void func_466ca(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20007714 = (volatile uint32_t *)0x20007714;
    volatile uint32_t *addr_20007710 = (volatile uint32_t *)0x20007710;
    volatile uint32_t *addr_200060E8 = (volatile uint32_t *)0x200060E8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_467A0
 * @note 指令数: 130
 */
void func_467a0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20007714 = (volatile uint32_t *)0x20007714;
    volatile uint32_t *addr_20007710 = (volatile uint32_t *)0x20007710;
    volatile uint32_t *addr_200060E8 = (volatile uint32_t *)0x200060E8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_468B8
 * @note 指令数: 14
 */
void func_468b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200060E8 = (volatile uint32_t *)0x200060E8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_468DC
 * @note 指令数: 113
 */
void func_468dc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007884 = (volatile uint32_t *)0x20007884;
    volatile uint32_t *addr_200070BC = (volatile uint32_t *)0x200070BC;
    volatile uint32_t *addr_200078A8 = (volatile uint32_t *)0x200078A8;
    volatile uint32_t *addr_200078AA = (volatile uint32_t *)0x200078AA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_469D0
 * @note 指令数: 14
 */
void func_469d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200078B7 = (volatile uint32_t *)0x200078B7;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_469FC
 * @note 指令数: 93
 */
void func_469fc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200078A4 = (volatile uint32_t *)0x200078A4;
    volatile uint32_t *addr_200076A0 = (volatile uint32_t *)0x200076A0;
    volatile uint32_t *addr_20007884 = (volatile uint32_t *)0x20007884;
    volatile uint32_t *addr_20007698 = (volatile uint32_t *)0x20007698;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

