// 完整精确转换批次 41 - 100%对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76870
 * @note 指令数: 2, 标签数: 0
 * @note 内存引用: 0, 函数调用: 0
 */
void precise_func_76870(uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76874
 * @note 指令数: 22, 标签数: 4
 * @note 内存引用: 3, 函数调用: 3
 */
void precise_func_76874(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x100;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7A090(void);
    extern void sub_79FE2(void);
    extern void sub_7762C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 函数调用
    sub_7A090();
    sub_79FE2();
    sub_7762C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_768A8
 * @note 指令数: 21, 标签数: 4
 * @note 内存引用: 3, 函数调用: 2
 */
void precise_func_768a8(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x100;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFF;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7A090(void);
    extern void sub_79FE2(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 函数调用
    sub_7A090();
    sub_79FE2();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_768D8
 * @note 指令数: 47, 标签数: 5
 * @note 内存引用: 3, 函数调用: 4
 */
void precise_func_768d8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7762C(void);
    extern void sub_76870(void);
    extern void sub_76B68(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 函数调用
    sub_76870();
    sub_76B68();
    sub_7762C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7693E
 * @note 指令数: 42, 标签数: 4
 * @note 内存引用: 4, 函数调用: 3
 */
void precise_func_7693e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x30;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7639E(void);
    extern void sub_76870(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76870();
    sub_7639E();
    sub_7639E();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76998
 * @note 指令数: 185, 标签数: 17
 * @note 内存引用: 16, 函数调用: 15
 */
void precise_func_76998(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2E;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x42C80000;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x447A0000;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x48;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7A40C(void);
    extern void sub_7A108(void);
    extern void sub_7A0E0(void);
    extern void sub_7A13A(void);
    extern void sub_7A320(void);
    extern void sub_76870(void);
    extern void sub_7A2BE(void);
    extern void sub_7A3D0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76870();
    sub_7A0E0();
    sub_7A108();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76B34
 * @note 指令数: 25, 标签数: 4
 * @note 内存引用: 3, 函数调用: 2
 */
void precise_func_76b34(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC0;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7A090(void);
    extern void sub_79FE2(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_7A090();
    sub_79FE2();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76B68
 * @note 指令数: 33, 标签数: 5
 * @note 内存引用: 4, 函数调用: 2
 */
void precise_func_76b68(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7A090(void);
    extern void sub_79FE2(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7A090();
    sub_79FE2();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76BAA
 * @note 指令数: 33, 标签数: 5
 * @note 内存引用: 4, 函数调用: 2
 */
void precise_func_76baa(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7A090(void);
    extern void sub_79FE2(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7A090();
    sub_79FE2();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76BEC
 * @note 指令数: 30, 标签数: 4
 * @note 内存引用: 2, 函数调用: 3
 */
void precise_func_76bec(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7A090(void);
    extern void sub_79FE2(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 函数调用
    sub_7A090();
    sub_79FE2();
    sub_79FE2();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76C2E
 * @note 指令数: 157, 标签数: 6
 * @note 内存引用: 14, 函数调用: 3
 */
void precise_func_76c2e(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xF8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2AD;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xAC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x80124B4;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_79ED8(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_79ED8();
    sub_79ED8();
    sub_79ED8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76D64
 * @note 指令数: 200, 标签数: 6
 * @note 内存引用: 20, 函数调用: 6
 */
void precise_func_76d64(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xAC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x7F;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xF8;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xB0;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x42;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x2AD;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_79ED8(void);
    extern void sub_76BAA(void);
    extern void sub_76B68(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_79ED8();
    sub_76BAA();
    sub_79ED8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76EF0
 * @note 指令数: 78, 标签数: 6
 * @note 内存引用: 11, 函数调用: 6
 */
void precise_func_76ef0(uint32_t param0, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000361C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8011E18;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x39;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x4C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76BEC(void);
    extern void sub_76D64(void);
    extern void sub_76874(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76D64();
    sub_76BEC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76F94
 * @note 指令数: 3, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_76f94(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003743;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_76FA8
 * @note 指令数: 169, 标签数: 17
 * @note 内存引用: 13, 函数调用: 14
 */
void precise_func_76fa8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000361C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8011E30;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003748;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x80124AC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7646C(void);
    extern void sub_764D0(void);
    extern void sub_76D64(void);
    extern void sub_76EF0(void);
    extern void sub_7711C(void);
    extern void sub_76534(void);
    extern void sub_77178(void);
    extern void sub_7649E(void);
    extern void sub_76502(void);
    extern void sub_7656C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7711C();
    sub_77178();
    sub_76502();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7711C
 * @note 指令数: 30, 标签数: 2
 * @note 内存引用: 2, 函数调用: 8
 */
void precise_func_7711c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003624;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7B7FE(void);
    extern void sub_75AE0(void);
    extern void sub_76874(void);
    extern void sub_76BEC(void);
    extern void sub_7A960(void);
    extern void sub_75A40(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76874();
    sub_76BEC();
    sub_75A40();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77178
 * @note 指令数: 136, 标签数: 16
 * @note 内存引用: 10, 函数调用: 18
 */
void precise_func_77178(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003748;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003624;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20003520;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003743;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x2000361C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7B774(void);
    extern void sub_7B7FE(void);
    extern void sub_75AE0(void);
    extern void sub_76D64(void);
    extern void sub_76B34(void);
    extern void sub_76874(void);
    extern void sub_76EF0(void);
    extern void sub_76820(void);
    extern void sub_76BEC(void);
    extern void sub_7B6D4(void);
    extern void sub_7A960(void);
    extern void sub_75A40(void);
    extern void sub_77834(void);
    extern void sub_78C4A(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_78C4A();
    sub_76874();
    sub_76B34();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_772B0
 * @note 指令数: 28, 标签数: 4
 * @note 内存引用: 1, 函数调用: 0
 */
void precise_func_772b0(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_772E8
 * @note 指令数: 29, 标签数: 2
 * @note 内存引用: 5, 函数调用: 4
 */
void precise_func_772e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000362C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x78;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_788F0(void);
    extern void sub_77178(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_788F0();
    sub_77178();
    sub_788F0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7732A
 * @note 指令数: 16, 标签数: 1
 * @note 内存引用: 1, 函数调用: 3
 */
void precise_func_7732a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003746;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_79DC0(void);
    extern void sub_79DFC(void);
    extern void sub_77178(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_77178();
    sub_79DC0();
    sub_79DFC();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77350
 * @note 指令数: 9, 标签数: 2
 * @note 内存引用: 1, 函数调用: 0
 */
void precise_func_77350(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003746;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77362
 * @note 指令数: 4, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_77362(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003746;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7737C
 * @note 指令数: 67, 标签数: 7
 * @note 内存引用: 4, 函数调用: 10
 */
void precise_func_7737c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000362C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003746;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003747;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000374A;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7646C(void);
    extern void sub_764D0(void);
    extern void sub_76534(void);
    extern void sub_77178(void);
    extern void sub_7649E(void);
    extern void sub_76502(void);
    extern void sub_7732A(void);
    extern void sub_7656C(void);
    extern void sub_79DC0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7732A();
    sub_7656C();
    sub_76534();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77416
 * @note 指令数: 3, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_77416(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003747;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77430
 * @note 指令数: 96, 标签数: 5
 * @note 内存引用: 16, 函数调用: 9
 */
void precise_func_77430(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8039000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200036FE;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000358C;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x801226C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_772B0(void);
    extern void sub_76820(void);
    extern void sub_78944(void);
    extern void sub_7B440(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76820();
    sub_772B0();
    sub_78944();
}

