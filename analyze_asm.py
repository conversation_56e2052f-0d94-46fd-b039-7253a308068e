#!/usr/bin/env python3
"""
分析AT32F403AVG汇编文件的结构
提取所有函数、数据结构、常量定义等
"""

import re
import sys

def analyze_asm_file(filename):
    """分析汇编文件结构"""
    functions = []
    data_sections = []
    constants = []
    vector_table = []
    current_function = None
    
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    
    print(f"分析文件: {filename}")
    print(f"总行数: {len(lines)}")
    print("=" * 80)
    
    # 分析中断向量表
    print("中断向量表分析:")
    vector_count = 0
    for i, line in enumerate(lines[:200]):  # 向量表通常在文件开头
        line = line.strip()
        if line.startswith('DCD') and ('sub_' in line or 'Handler' in line or '0x' in line):
            vector_table.append((i+1, line))
            vector_count += 1
            if vector_count <= 20:  # 只显示前20个
                print(f"  {i+1:4d}: {line}")
    
    print(f"向量表条目总数: {len(vector_table)}")
    print("=" * 80)
    
    # 分析函数
    print("函数分析:")
    function_count = 0
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检测函数开始
        if re.match(r'^sub_[0-9A-Fa-f]+\s', line):
            if current_function:
                functions.append(current_function)
            
            func_name = line.split()[0]
            current_function = {
                'name': func_name,
                'start_line': i + 1,
                'address': func_name.replace('sub_', '0x'),
                'instructions': []
            }
            function_count += 1
            
            if function_count <= 50:  # 只显示前50个函数
                print(f"  {function_count:3d}: {func_name} (行 {i+1})")
        
        # 检测函数结束
        elif line.startswith('; End of function'):
            if current_function:
                current_function['end_line'] = i + 1
                functions.append(current_function)
                current_function = None
        
        # 收集函数内的指令
        elif current_function and line and not line.startswith(';'):
            current_function['instructions'].append(line)
    
    print(f"函数总数: {len(functions)}")
    print("=" * 80)
    
    # 分析数据段
    print("数据段分析:")
    data_count = 0
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检测数据定义
        if (line.startswith('DCD') or line.startswith('DCB') or 
            line.startswith('dword_') or line.startswith('byte_') or
            line.startswith('word_')):
            data_sections.append((i+1, line))
            data_count += 1
            
            if data_count <= 30:  # 只显示前30个数据定义
                print(f"  {i+1:4d}: {line[:60]}...")
    
    print(f"数据定义总数: {len(data_sections)}")
    print("=" * 80)
    
    # 分析字符串常量
    print("字符串常量分析:")
    string_count = 0
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检测字符串定义
        if re.search(r'DCB\s+"[^"]*"', line):
            constants.append((i+1, line))
            string_count += 1
            
            if string_count <= 20:  # 只显示前20个字符串
                print(f"  {i+1:4d}: {line}")
    
    print(f"字符串常量总数: {len(constants)}")
    print("=" * 80)
    
    # 统计信息
    print("统计信息:")
    print(f"  总函数数量: {len(functions)}")
    print(f"  总数据段数: {len(data_sections)}")
    print(f"  总字符串数: {len(constants)}")
    print(f"  向量表条目: {len(vector_table)}")
    
    # 分析内存布局
    print("\n内存布局分析:")
    memory_regions = []
    for i, line in enumerate(lines):
        line = line.strip()
        if 'AREA' in line and ('0x' in line or 'CODE' in line):
            memory_regions.append((i+1, line))
    
    for line_num, region in memory_regions:
        print(f"  {line_num:4d}: {region}")
    
    return {
        'functions': functions,
        'data_sections': data_sections,
        'constants': constants,
        'vector_table': vector_table,
        'memory_regions': memory_regions
    }

def generate_function_list(analysis_result):
    """生成函数列表文件"""
    functions = analysis_result['functions']
    
    with open('asm_functions_list.txt', 'w', encoding='utf-8') as f:
        f.write("AT32F403AVG汇编文件函数列表\n")
        f.write("=" * 50 + "\n\n")
        
        for i, func in enumerate(functions, 1):
            f.write(f"{i:3d}. {func['name']}\n")
            f.write(f"     地址: {func['address']}\n")
            f.write(f"     行号: {func['start_line']}")
            if 'end_line' in func:
                f.write(f" - {func['end_line']}")
            f.write(f"\n")
            f.write(f"     指令数: {len(func['instructions'])}\n")
            if func['instructions']:
                f.write(f"     首指令: {func['instructions'][0]}\n")
            f.write("\n")
    
    print(f"函数列表已保存到: asm_functions_list.txt")

if __name__ == "__main__":
    asm_file = "keil/AT32F403AVG-FLASH-J201.asm"
    
    try:
        result = analyze_asm_file(asm_file)
        generate_function_list(result)
        
        print("\n分析完成！")
        print("请查看生成的 asm_functions_list.txt 文件获取详细的函数列表。")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {asm_file}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)
