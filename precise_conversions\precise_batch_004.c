// 精确转换批次 4 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19368
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_19368(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x180003;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x180002;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x180006;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R6, #0
    // R6 = 0;
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =dword_180004
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180006
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_193F6
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_193f6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016790;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x8016790
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016790
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184B4
    // 调用函数: sub_184B4();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1941C
    // 条件跳转
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8016790
    // 内存加载操作
    // LDR     R1, [R0,#4]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8016790
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // B       locret_1942C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1942E
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_1942e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016790;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x180000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8016794;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x180003;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x180002;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS.W  R0, #0x180000
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x30100302
    // 内存加载操作
    // BL      sub_184E0
    // 调用函数: sub_184E0();
    // LDR     R1, =0x8016794
    // 内存加载操作
    // LDR     R0, =0x8016790
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1945E
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_1945e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8016790;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // MOV     R0, SP
    // BL      sub_1847E
    // 调用函数: sub_1847E();
    // LDR     R0, =0x8016790
    // 内存加载操作
    // LDR     R0, [R0,#4]
    // 内存加载操作
    // STR     R0, [SP,#0x10+var_10]
    // 内存存储操作
    // MOV     R1, SP
    // LDR     R0, =0x8016790
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_183AC
    // 调用函数: sub_183AC();
    // POP     {R0-R2,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1949C
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_1949c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x801629C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDR     R0, =0x801629C
    // 内存加载操作
    // MOVS    R1, #0x34 ; '4'
    // R1 = 0x34;
    // MLA.W   R0, R1, R4, R0
    // LDR     R0, [R0,#4]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     locret_194D8
    // 条件跳转
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_194BA
    // 条件跳转
    // MOVS    R2, #0
    // R2 = 0;
    // B       loc_194BC
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_194DA
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_194da(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x19;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1F= -0x1F
    // var_1E= -0x1E
    // var_1D= -0x1D
    // var_1C= -0x1C
    // var_1B= -0x1B
    // var_1A= -0x1A
    // var_19= -0x19
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19644
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_19644(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x801629C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // BL      sub_193F6
    // 调用函数: sub_193F6();
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_1949C
    // 调用函数: sub_1949C();
    // MOVS    R1, R5
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x801629C
    // 内存加载操作
    // MOVS    R2, #0x34 ; '4'
    // R2 = 0x34;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1A7F0
    // 调用函数: sub_1A7F0();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_196A8
 * @note 指令数: 2, 标签数: 0
 */
void precise_func_196a8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000258;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x20000258
    // 内存加载操作
    // B.W     locret_1A814
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_196B4
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_196b4(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LSLS    R2, R0, #0x1E
    // BEQ     loc_196C8
    // 条件跳转
    // LDRB.W  R2, [R0],#1
    // LDRB.W  R3, [R1],#1
    // CBZ     R2, loc_196F2
    // CMP     R2, R3
    // 比较操作
    // BEQ     sub_196B4
    // 条件跳转
    // B       loc_196F2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_196F6
 * @note 指令数: 21, 标签数: 1
 */
void precise_func_196f6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007FE8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x208CC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x40013800;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LSLS    R0, R5, #0xF
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40013800
    // 内存加载操作
    // BL      sub_1A972
    // 调用函数: sub_1A972();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1973C
    // 条件跳转
    // LDR.W   R1, =(a7DigitalInputS+0x208CC) ; "ut Out Of Service 2-5\",1,11343,\r\n\"A"...
    // 内存加载操作
    // LDR.W   R0, =0x40013800
    // 内存加载操作
    // BL      sub_1A94A
    // 调用函数: sub_1A94A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1973C
    // 条件跳转
    // LDR.W   R0, =0x20007FE8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1972E
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR.W   R2, =0x20007FE8
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19894
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_19894(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x208CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40004400;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007FEC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40004400
    // 内存加载操作
    // BL      sub_1A972
    // 调用函数: sub_1A972();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_198D8
    // 条件跳转
    // LDR.W   R1, =(a7DigitalInputS+0x208CC) ; "ut Out Of Service 2-5\",1,11343,\r\n\"A"...
    // 内存加载操作
    // LDR.W   R0, =0x40004400
    // 内存加载操作
    // BL      sub_1A94A
    // 调用函数: sub_1A94A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_198D8
    // 条件跳转
    // LDR.W   R0, =0x20007FEC
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_198CA
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR.W   R2, =0x20007FEC
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19A38
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_19a38(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x208CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40004800;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007FF0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, #2
    // R4 = 2;
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40004800
    // 内存加载操作
    // BL      sub_1A972
    // 调用函数: sub_1A972();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_19A7E
    // 条件跳转
    // LDR.W   R1, =(a7DigitalInputS+0x208CC) ; "ut Out Of Service 2-5\",1,11343,\r\n\"A"...
    // 内存加载操作
    // LDR.W   R0, =0x40004800
    // 内存加载操作
    // BL      sub_1A94A
    // 调用函数: sub_1A94A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_19A7E
    // 条件跳转
    // LDR.W   R0, =0x20007FF0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_19A70
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR.W   R2, =0x20007FF0
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19BF0
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_19bf0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x208CC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40005000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007FF4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x40005000
    // 内存加载操作
    // BL      sub_1A972
    // 调用函数: sub_1A972();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_19C34
    // 条件跳转
    // LDR.W   R1, =(a7DigitalInputS+0x208CC) ; "ut Out Of Service 2-5\",1,11343,\r\n\"A"...
    // 内存加载操作
    // LDR.W   R0, =0x40005000
    // 内存加载操作
    // BL      sub_1A94A
    // 调用函数: sub_1A94A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_19C34
    // 条件跳转
    // LDR.W   R0, =0x20007FF4
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_19C26
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR.W   R2, =0x20007FF4
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // BLX     R2
    // 调用函数: R2();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19DBE
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_19dbe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015CFC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR.W   R0, =0x8015CFC
    // 内存加载操作
    // LDR     R0, [R0,#0x2C]
    // 内存加载操作
    // BL      sub_1A8F4
    // 调用函数: sub_1A8F4();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_19DCE
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_19dce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x26;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x180005;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C0011;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2C140401;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH.W  {R4-R8,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, #0
    // R6 = 0;
    // CMP     R4, #0
    // 比较操作
    // BNE     loc_19E06
    // 条件跳转
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x1C0011
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x180005
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, #0x26 ; '&'
    // R0 = 0x26;
    // BL      sub_1824C
    // 调用函数: sub_1824C();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR.W   R0, =0x2C140401
    // 内存加载操作
    // BL      sub_184E0
    // 调用函数: sub_184E0();
    // B       loc_19E9E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A014
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_1a014(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // MOVS    R0, #1
    // R0 = 1;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A01A
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_1a01a(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015CFC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC0006;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDR     R1, =0xC0006
    // 内存加载操作
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R2, #0x2C ; ','
    // R2 = 0x2C;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1A94A
    // 调用函数: sub_1A94A();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_1A048
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A084
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_1a084(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8015CFC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R1, #0x80
    // R1 = 0x80;
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R2, #0x2C ; ','
    // R2 = 0x2C;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1A972
    // 调用函数: sub_1A972();
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_1A0B4
    // 条件跳转
    // MOVS    R1, #0x40 ; '@'
    // R1 = 0x40;
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R2, #0x2C ; ','
    // R2 = 0x2C;
    // MLA.W   R0, R2, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1A972
    // 调用函数: sub_1A972();
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_1A0B4
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_1A0B6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A0B8
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_1a0b8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015CFC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC0006;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R1, =0xC0006
    // 内存加载操作
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R3, #0x2C ; ','
    // R3 = 0x2C;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1A918
    // 调用函数: sub_1A918();
    // MOVS    R0, R4
    // BL      sub_188AE
    // 调用函数: sub_188AE();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A0D6
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_1a0d6(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015CFC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC0007;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xC0006;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R1, =0xC0006
    // 内存加载操作
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R3, #0x2C ; ','
    // R3 = 0x2C;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1A918
    // 调用函数: sub_1A918();
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R1, =0xC0007
    // 内存加载操作
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R3, #0x2C ; ','
    // R3 = 0x2C;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_1A918
    // 调用函数: sub_1A918();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A100
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_1a100(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_1A124
    // 条件跳转
    // UXTB    R6, R6
    // 数据扩展操作
    // CMP     R6, #0
    // 比较操作
    // BEQ     loc_1A11C
    // 条件跳转
    // MOVS    R0, R4
    // BL      sub_1A160
    // 调用函数: sub_1A160();
    // B       locret_1A12A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A12C
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1a12c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0
    // R2 = 0;
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, R4
    // BL      sub_1A100
    // 调用函数: sub_1A100();
    // MOVS    R0, #1
    // R0 = 1;
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A13E
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_1a13e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015CFC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R1, #0x2C ; ','
    // R1 = 0x2C;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x24]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R3, #0x2C ; ','
    // R3 = 0x2C;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0x20]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A160
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_1a160(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015CFC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R1, #0x2C ; ','
    // R1 = 0x2C;
    // MLA.W   R0, R1, R4, R0
    // LDR     R1, [R0,#0x24]
    // 内存加载操作
    // UXTH    R1, R1
    // 数据扩展操作
    // LDR     R0, =0x8015CFC
    // 内存加载操作
    // MOVS    R3, #0x2C ; ','
    // R3 = 0x2C;
    // MLA.W   R0, R3, R4, R0
    // LDR     R0, [R0,#0x20]
    // 内存加载操作
    // BL      sub_184CE
    // 调用函数: sub_184CE();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A182
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_1a182(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8015CFC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, =0x8015CFC
    // 内存加载操作
    // MOVS    R3, #0x2C ; ','
    // R3 = 0x2C;
    // MLA.W   R2, R3, R0, R2
    // LDR     R2, [R2,#4]
    // 内存加载操作
    // STR     R1, [R2]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A1FA
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1a1fa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R0, R4
    // BL      sub_1A278
    // 调用函数: sub_1A278();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1A210
    // 条件跳转
    // MOVS.W  R0, #0xFFFFFFFF
    // B       locret_1A22C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A22E
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1a22e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // BL      sub_1A266
    // 调用函数: sub_1A266();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_1A242
    // 条件跳转
    // MOVS.W  R0, #0xFFFFFFFF
    // B       locret_1A264
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A266
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_1a266(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // LDRH    R0, [R1,#4]
    // 内存加载操作
    // LDRH    R2, [R1,#6]
    // 内存加载操作
    // CMP     R0, R2
    // 比较操作
    // BNE     loc_1A274
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_1A276
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A278
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_1a278(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // LDRH    R0, [R1,#4]
    // 内存加载操作
    // MOVS    R2, R0
    // ADDS    R2, R2, #1
    // 算术运算
    // LDRH    R0, [R1,#8]
    // 内存加载操作
    // UXTH    R2, R2
    // 数据扩展操作
    // CMP     R2, R0
    // 比较操作
    // BNE     loc_1A28C
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R2, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A29C
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1a29c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDRH    R1, [R0,#4]
    // 内存加载操作
    // LDRH    R2, [R0,#6]
    // 内存加载操作
    // CMP     R1, R2
    // 比较操作
    // BCC     loc_1A2AE
    // LDRH    R1, [R0,#4]
    // 内存加载操作
    // LDRH    R0, [R0,#6]
    // 内存加载操作
    // SUBS    R0, R1, R0
    // 算术运算
    // UXTH    R0, R0
    // 数据扩展操作
    // B       locret_1A2BA
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A2BC
 * @note 指令数: 7, 标签数: 1
 */
void precise_func_1a2bc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // ADD.W   R3, R0, #1
    // LSLS    R1, R0, #0x1E
    // BEQ     loc_1A2CE
    // 条件跳转
    // LDRB.W  R1, [R0],#1
    // CBZ     R1, loc_1A2EE
    // LSLS    R1, R0, #0x1E
    // BNE     loc_1A2C4
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A2F4
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_1a2f4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x7FF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_40= -0x40
    // var_3C= -0x3C
    // PUSH.W  {R3-R7,R10,R11,LR}
    // 栈操作
    // VPUSH   {D8-D10}
    // MOV     R5, R2
    // PUSH    {R0,R1}
    // 栈操作
    // UBFX.W  R0, R1, #0x14, #0xB
    // MOVW    R1, #0x7FF
    // R1 = 0x7FF;
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_1A326
    // 条件跳转
    // LDR     R0, [SP,#0x40+var_3C]
    // 内存加载操作
    // MOVS    R4, #0
    // R4 = 0;
    // LSLS    R0, R0, #0xC
    // ITT EQ
    // LDREQ   R0, [SP,#0x40+var_40]
    // CMPEQ   R0, #0
    // BNE     loc_1A320
    // 条件跳转
    // LDR     R0, [SP,#0x40+var_3C]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BMI     loc_1A358
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A5DC
 * @note 指令数: 2, 标签数: 0
 */
uint32_t precise_func_1a5dc(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #2
    // R0 = 2;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A5E0
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_1a5e0(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
    // MOVS    R0, #1
    // R0 = 1;
    // MOVS    R6, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A61E
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_1a61e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x180005;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x180003;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x180002;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180002
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180003
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =dword_180004
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x180005
    // 内存加载操作
    // BL      sub_17DF4
    // 调用函数: sub_17DF4();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A698
 * @note 指令数: 17, 标签数: 0
 */
uint32_t precise_func_1a698(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#2]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#4]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#5]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // STRB    R1, [R0,#6]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // STRB    R1, [R0,#7]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A6BA
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_1a6ba(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x400;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x800;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x4000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R2, [R0,#0x1C]
    // 内存加载操作
    // BICS.W  R2, R2, #0x800
    // STR     R2, [R0,#0x1C]
    // 内存存储操作
    // LDRB    R2, [R1]
    // 内存加载操作
    // CMP     R2, #0
    // 比较操作
    // BNE     loc_1A6E2
    // 条件跳转
    // LDR     R2, [R0]
    // 内存加载操作
    // BICS.W  R2, R2, #0x8000
    // STR     R2, [R0]
    // 内存存储操作
    // LDR     R2, [R0]
    // 内存加载操作
    // BICS.W  R2, R2, #0x4000
    // STR     R2, [R0]
    // 内存存储操作
    // LDR     R2, [R0]
    // 内存加载操作
    // BICS.W  R2, R2, #0x400
    // STR     R2, [R0]
    // 内存存储操作
    // B       loc_1A740
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A7CC
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_1a7cc(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xD;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0]
    // 内存加载操作
    // BFI.W   R2, R1, #0xD, #1
    // STR     R2, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A7D8
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_1a7d8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0,#4]
    // 内存加载操作
    // BFI.W   R2, R1, #2, #1
    // STR     R2, [R0,#4]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A7E4
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_1a7e4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0]
    // 内存加载操作
    // BFI.W   R2, R1, #6, #1
    // STR     R2, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A7F0
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_1a7f0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTH    R1, R1
    // 数据扩展操作
    // STR     R1, [R0,#0xC]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A7F6
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_1a7f6(uint16_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, [R0,#0xC]
    // 内存加载操作
    // UXTH    R0, R0
    // 数据扩展操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A7FC
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_1a7fc(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R3, [R2,#8]
    // 内存加载操作
    // TST     R3, R1
    // 比较操作
    // BNE     loc_1A80C
    // 条件跳转
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R0, R3
    // B       loc_1A810
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A818
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_1a818(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40016400;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40016800;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40013800;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // PUSH.W  {R4-R9,LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOVS    R6, R2
    // MOVS    R7, R3
    // MOV     R0, SP
    // BL      sub_17FE6
    // 调用函数: sub_17FE6();
    // LDR     R0, =0x40013800
    // 内存加载操作
    // CMP     R4, R0
    // 比较操作
    // BEQ     loc_1A844
    // 条件跳转
    // LDR     R0, =0x40016000
    // 内存加载操作
    // CMP     R4, R0
    // 比较操作
    // BEQ     loc_1A844
    // 条件跳转
    // LDR     R0, =0x40016400
    // 内存加载操作
    // CMP     R4, R0
    // 比较操作
    // BEQ     loc_1A844
    // 条件跳转
    // LDR     R0, =0x40016800
    // 内存加载操作
    // CMP     R4, R0
    // 比较操作
    // BNE     loc_1A84A
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A8AC
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_1a8ac(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x400;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // CMP     R1, #0
    // 比较操作
    // BNE     loc_1A8C4
    // 条件跳转
    // LDR     R2, [R0,#0xC]
    // 内存加载操作
    // BICS.W  R2, R2, #0x200
    // STR     R2, [R0,#0xC]
    // 内存存储操作
    // LDR     R2, [R0,#0xC]
    // 内存加载操作
    // BICS.W  R2, R2, #0x400
    // STR     R2, [R0,#0xC]
    // 内存存储操作
    // B       locret_1A8F2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A8F4
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_1a8f4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xD;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0,#0xC]
    // 内存加载操作
    // BFI.W   R2, R1, #0xD, #1
    // STR     R2, [R0,#0xC]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A900
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_1a900(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0,#0xC]
    // 内存加载操作
    // BFI.W   R2, R1, #3, #1
    // STR     R2, [R0,#0xC]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A90C
 * @note 指令数: 5, 标签数: 0
 */
uint32_t precise_func_1a90c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // UXTB    R1, R1
    // 数据扩展操作
    // LDR     R2, [R0,#0xC]
    // 内存加载操作
    // BFI.W   R2, R1, #2, #1
    // STR     R2, [R0,#0xC]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A918
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_1a918(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5}
    // 栈操作
    // UXTB    R2, R2
    // 数据扩展操作
    // CMP     R2, #1
    // 比较操作
    // BNE     loc_1A934
    // 条件跳转
    // LSRS    R3, R1, #0x10
    // LDR     R3, [R3,R0]
    // 内存加载操作
    // MOVS    R4, #1
    // R4 = 1;
    // ANDS.W  R5, R1, #0x1F
    // LSLS    R4, R5
    // ORRS    R3, R4
    // LSRS    R4, R1, #0x10
    // STR     R3, [R4,R0]
    // 内存存储操作
    // B       loc_1A946
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1A94A
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_1a94a(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, R0
    // LSRS    R0, R1, #0x10
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // ANDS.W  R3, R1, #0x1F
    // LSRS    R0, R3
    // LSLS    R0, R0, #0x1F
    // BPL     loc_1A95E
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_1A960
    // 无条件跳转
}

