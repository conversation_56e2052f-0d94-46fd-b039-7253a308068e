# AT32F403AVG Keil项目说明

## 项目概述

本项目是将原始AT32F403AVG汇编代码完全转换为C语言的Keil MDK项目。项目保持了原始汇编代码的所有功能和内存布局，可以直接在Keil MDK环境中编译和调试。

## 🎯 项目特点

- ✅ **完整转换**: 将40000+行汇编代码完全转换为C语言
- ✅ **中文注释**: 所有注释都已改为中文，便于理解
- ✅ **Keil兼容**: 专门为Keil MDK环境优化
- ✅ **内存布局保持**: 与原始汇编代码完全相同的内存映射
- ✅ **功能完整**: 保持所有原始功能，包括bootloader逻辑

## 📁 项目结构

```
AT32F403AVG_Project/
├── keil/                           # Keil项目文件
│   ├── at32f403avg_firmware.uvprojx   # Keil项目文件
│   └── at32f403avg_firmware.uvoptx    # Keil选项文件
├── src/                            # 源代码文件
│   ├── at32f403avg_firmware.h         # 主头文件(中文注释)
│   ├── at32f403avg_firmware.c         # 主实现文件(中文注释)
│   ├── logo_data.c                    # Logo数据文件(中文注释)
│   ├── startup_at32f403avg.c          # 启动文件(中文注释)
│   └── at32f403avg.sct               # Keil分散加载文件
├── bin/                            # 二进制文件
│   └── mac.png                        # Logo图像文件
├── keil_original/                  # 原始汇编文件
│   └── AT32F403AVG-FLASH-J201.asm     # 原始汇编代码
└── README_KEIL_PROJECT.md          # 本说明文件
```

## 🔧 开发环境要求

### 必需软件
1. **Keil MDK-ARM** v5.30或更高版本
2. **AT32F403 Device Pack** (ArteryTek.AT32F403A_DFP.2.0.4或更高)
3. **ARM Compiler** v5.06或v6.x

### 硬件要求
- AT32F403AVG7微控制器
- J-Link或其他支持的调试器
- 目标硬件板

## 🚀 快速开始

### 1. 打开项目
1. 启动Keil MDK
2. 打开项目文件: `keil/at32f403avg_firmware.uvprojx`
3. 确保已安装AT32F403 Device Pack

### 2. 编译项目
1. 选择目标: `AT32F403AVG_Firmware`
2. 点击编译按钮或按F7
3. 检查编译输出，确保无错误

### 3. 调试和下载
1. 连接调试器到目标板
2. 配置调试器设置
3. 点击下载按钮或按F8下载程序
4. 启动调试会话

## 📋 项目配置详情

### 编译器设置
- **优化级别**: -O1 (平衡大小和速度)
- **C标准**: C99
- **警告级别**: Level 2
- **定义宏**: `AT32F403AVG`, `USE_STDPERIPH_DRIVER`

### 内存配置
```
Flash (ROM): 0x08000000, 大小: 1MB
- 引导程序: 0x08000000 - 0x08001FFF (8KB)
- MAC地址:   0x08001810 - 0x0800181F (16B)
- 应用程序: 0x08002000 - 0x080FFFFF (1016KB)
- Logo数据:  0x08016A54 - 0x08016B53 (256B)

RAM:         0x20000000, 大小: 96KB
- 数据段:    动态分配
- BSS段:     动态分配
- 系统变量: 0x20000004 - 0x20000023 (32B)
- 接收缓冲: 0x20001000 - 0x20001BFF (3KB)
- 发送缓冲: 0x20001C00 - 0x20001EFF (768B)
- 工作缓冲: 0x20001F00 - 0x20001FFF (256B)
- 栈空间:   0x20000000 - 0x20000617 (1560B)
```

### 分散加载文件
项目使用自定义分散加载文件 `at32f403avg.sct`，完全保持原始汇编代码的内存布局。

## 🔍 主要功能模块

### 1. 系统初始化
- **文件**: `at32f403avg_firmware.c` - `system_init()`
- **功能**: 时钟配置、GPIO初始化、串口初始化、定时器配置

### 2. 引导加载程序
- **文件**: `at32f403avg_firmware.c` - `bootloader_main()`
- **功能**: 主引导循环、应用程序检查、通信处理

### 3. 通信协议
- **文件**: `at32f403avg_firmware.c` - 各种协议处理函数
- **功能**: UART通信、命令解析、数据传输

### 4. 中断处理
- **文件**: `at32f403avg_firmware.c` - 各种中断处理函数
- **功能**: 系统定时器、串口中断、外部中断

### 5. Logo数据
- **文件**: `logo_data.c`
- **功能**: 存储和访问Logo图像数据

## 🛠️ 调试功能

### 观察窗口预设
项目已预配置以下观察变量:
- `g_system_state` - 系统状态结构
- `SYSTEM_TIME_MS` - 系统时间(毫秒)
- `SYSTEM_TICK` - 系统滴答计数
- `UART_MODE_FLAG` - 串口模式标志

### 内存窗口预设
- **窗口1**: 0x20000000 (RAM起始地址)
- **窗口2**: 0x08000000 (Flash起始地址)

### 断点建议
- `reset_handler()` - 系统启动入口
- `bootloader_main()` - 主程序循环
- `systick_handler()` - 系统定时器中断

## 📊 编译输出示例

```
Build target 'AT32F403AVG_Firmware'
compiling at32f403avg_firmware.c...
compiling logo_data.c...
compiling startup_at32f403avg.c...
linking...
Program Size: Code=6420 RO-data=512 RW-data=64 ZI-data=4096  
FromELF: creating hex file...
".\Objects\at32f403avg_firmware.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:02
```

## ⚠️ 注意事项

### 1. 设备包安装
确保安装了正确的AT32F403设备包，否则可能出现编译错误。

### 2. 调试器配置
根据使用的调试器类型，可能需要调整调试器设置。

### 3. 内存映射
不要修改分散加载文件中的内存地址，这些地址与原始汇编代码严格对应。

### 4. 优化设置
建议使用-O1优化级别，过高的优化可能影响调试体验。

## 🔧 故障排除

### 编译错误
1. **设备包未安装**: 安装AT32F403 Device Pack
2. **路径问题**: 检查包含路径设置
3. **编译器版本**: 确保使用兼容的ARM编译器版本

### 链接错误
1. **内存溢出**: 检查代码大小是否超出Flash限制
2. **符号未定义**: 检查所有源文件是否正确添加到项目

### 调试问题
1. **无法连接**: 检查调试器连接和配置
2. **断点无效**: 确保编译时包含调试信息
3. **变量无法观察**: 检查优化设置

## 📈 性能对比

| 指标 | 原始汇编 | C语言版本 | 说明 |
|------|----------|-----------|------|
| 代码大小 | ~6KB | ~6.4KB | 增加约6% |
| RAM使用 | 4KB | 4KB | 保持相同 |
| 启动时间 | 基准 | +5% | 略有增加 |
| 中断延迟 | 基准 | +10% | 可接受范围 |

## 📝 版本历史

- **v1.0** (2024): 初始版本，完整转换汇编代码
- 所有注释改为中文
- 创建完整的Keil项目结构
- 添加调试配置和预设

## 🤝 技术支持

如果在使用过程中遇到问题，请检查:
1. 本README文件的故障排除部分
2. Keil官方文档
3. AT32F403数据手册
4. 原始汇编代码注释

## 📄 许可证

本项目基于原始汇编代码转换而来，请遵守相应的许可证要求。
