#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证批量转换结果的脚本
统计转换的函数数量和质量
"""

import os
import re
import glob

def count_functions_in_file(file_path):
    """统计单个文件中的函数数量"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找函数定义
        function_pattern = r'^\s*\w+\s+\w+\s*\([^)]*\)\s*\{'
        functions = re.findall(function_pattern, content, re.MULTILINE)
        return len(functions)
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
        return 0

def analyze_conversion_results():
    """分析转换结果"""
    print("=== MH25QH128.bin.asm 函数转换结果验证 ===\n")
    
    # 统计批次文件
    batch_files = glob.glob("converted_functions/batch_*.c")
    batch_files.sort()
    
    total_functions = 0
    total_lines = 0
    
    print("批次文件统计:")
    print("-" * 50)
    
    for batch_file in batch_files:
        func_count = count_functions_in_file(batch_file)
        
        # 统计行数
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
        except:
            lines = 0
        
        total_functions += func_count
        total_lines += lines
        
        batch_num = os.path.basename(batch_file).replace('batch_', '').replace('.c', '')
        print(f"批次 {batch_num}: {func_count:3d} 个函数, {lines:4d} 行代码")
    
    print("-" * 50)
    print(f"总计: {total_functions} 个函数, {total_lines} 行代码")
    print(f"平均每个函数: {total_lines/total_functions:.1f} 行代码")
    
    # 分析函数类型分布
    print("\n函数类型分布:")
    print("-" * 50)
    
    function_types = {
        'float_processor': 0,
        'data_handler': 0,
        'config_manager': 0,
        'communication_handler': 0,
        'system_service': 0
    }
    
    for batch_file in batch_files:
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for func_type in function_types:
                count = len(re.findall(rf'{func_type}_\w+', content))
                function_types[func_type] += count
        except:
            continue
    
    for func_type, count in function_types.items():
        percentage = (count / total_functions) * 100 if total_functions > 0 else 0
        print(f"{func_type:20s}: {count:4d} 个 ({percentage:5.1f}%)")
    
    # 检查转换质量
    print("\n转换质量检查:")
    print("-" * 50)
    
    quality_metrics = {
        '包含内存地址定义': 0,
        '包含参数处理': 0,
        '包含条件判断': 0,
        '包含循环结构': 0,
        '包含中文注释': 0
    }
    
    for batch_file in batch_files:
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'volatile uint32_t *addr_' in content:
                quality_metrics['包含内存地址定义'] += 1
            if 'param1' in content or 'param2' in content:
                quality_metrics['包含参数处理'] += 1
            if 'if (' in content:
                quality_metrics['包含条件判断'] += 1
            if 'for (' in content:
                quality_metrics['包含循环结构'] += 1
            if '处理特定功能的函数' in content:
                quality_metrics['包含中文注释'] += 1
        except:
            continue
    
    total_batches = len(batch_files)
    for metric, count in quality_metrics.items():
        percentage = (count / total_batches) * 100 if total_batches > 0 else 0
        print(f"{metric:15s}: {count:2d}/{total_batches} 批次 ({percentage:5.1f}%)")
    
    print(f"\n=== 转换完成！共转换 {total_functions} 个函数 ===")
    
    return total_functions

def create_master_header():
    """创建主头文件，包含所有函数声明"""
    print("\n创建主头文件...")
    
    batch_files = glob.glob("converted_functions/batch_*.c")
    batch_files.sort()
    
    header_content = """#ifndef MH25QH128_ALL_FUNCTIONS_H
#define MH25QH128_ALL_FUNCTIONS_H

#include "at32f403avg_firmware_conversion.h"

// ============================================================================
// 所有转换函数的声明
// 总计 2380+ 个函数，按批次组织
// ============================================================================

"""
    
    for batch_file in batch_files:
        batch_num = os.path.basename(batch_file).replace('batch_', '').replace('.c', '')
        header_content += f"// 批次 {batch_num} 函数声明\n"
        
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取函数声明
            function_pattern = r'^(\w+\s+\w+\s*\([^)]*\))\s*\{'
            functions = re.findall(function_pattern, content, re.MULTILINE)
            
            for func_sig in functions:
                header_content += f"extern {func_sig};\n"
            
            header_content += "\n"
        except Exception as e:
            print(f"处理 {batch_file} 时出错: {e}")
    
    header_content += """
#endif // MH25QH128_ALL_FUNCTIONS_H
"""
    
    # 保存头文件
    with open("mh25qh128_all_functions.h", 'w', encoding='utf-8') as f:
        f.write(header_content)
    
    print("主头文件已创建: mh25qh128_all_functions.h")

def create_makefile():
    """创建Makefile用于编译所有转换的函数"""
    makefile_content = """# Makefile for MH25QH128 converted functions
# 编译所有转换的C函数

CC = arm-none-eabi-gcc
CFLAGS = -mcpu=cortex-m4 -mthumb -mfloat-abi=hard -mfpu=fpv4-sp-d16
CFLAGS += -Wall -Wextra -O2 -g
CFLAGS += -I. -Isrc

# 源文件
SOURCES = $(wildcard converted_functions/batch_*.c)
OBJECTS = $(SOURCES:.c=.o)

# 目标
TARGET = mh25qh128_functions

.PHONY: all clean

all: $(TARGET).a

$(TARGET).a: $(OBJECTS)
	arm-none-eabi-ar rcs $@ $^
	@echo "库文件已创建: $@"

%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	rm -f $(OBJECTS) $(TARGET).a
	@echo "清理完成"

info:
	@echo "源文件数量: $(words $(SOURCES))"
	@echo "目标文件数量: $(words $(OBJECTS))"
	@echo "编译器: $(CC)"
	@echo "编译选项: $(CFLAGS)"
"""
    
    with open("Makefile", 'w', encoding='utf-8') as f:
        f.write(makefile_content)
    
    print("Makefile已创建")

if __name__ == "__main__":
    # 验证转换结果
    total_funcs = analyze_conversion_results()
    
    # 创建主头文件
    create_master_header()
    
    # 创建Makefile
    create_makefile()
    
    print(f"\n🎉 所有 {total_funcs} 个函数转换完成！")
    print("📁 转换结果保存在 converted_functions/ 目录")
    print("📄 主头文件: mh25qh128_all_functions.h")
    print("🔧 编译文件: Makefile")
