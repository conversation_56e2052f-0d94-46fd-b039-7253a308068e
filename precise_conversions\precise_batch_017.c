// 精确转换批次 17 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FB08
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_4fb08(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xCDEF89AB;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x45670123;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x40022004;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40022010;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x18
    // BPL     loc_4FB22
    // LDR     R0, =0x45670123
    // 内存加载操作
    // LDR     R1, =0x40022004
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, =0xCDEF89AB
    // 内存加载操作
    // LDR     R1, =0x40022004
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_4FB24
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FB2C
 * @note 指令数: 8, 标签数: 0
 */
uint32_t precise_func_4fb2c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40022010;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x40022010
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80
    // R1 = 0x80;
    // ORRS    R1, R0
    // LDR     R0, =0x40022010
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FB3C
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_4fb3c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007368;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40022014;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40022010;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007368
    // 内存加载操作
    // STR     R1, [R2,#0x1C]
    // 内存存储操作
    // LDR     R1, =0x40022010
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #2
    // R2 = 2;
    // ORRS    R2, R1
    // LDR     R1, =0x40022010
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // LDR     R1, =0x40022014
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R1, =0x40022010
    // 内存加载操作
    // LDR     R1, [R1]
    // 内存加载操作
    // MOVS    R2, #0x40 ; '@'
    // R2 = 0x40;
    // ORRS    R2, R1
    // LDR     R1, =0x40022010
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FB60
 * @note 指令数: 11, 标签数: 0
 */
uint32_t precise_func_4fb60(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007368;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x40022010;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x20007368
    // 内存加载操作
    // STR     R2, [R3,#0x1C]
    // 内存存储操作
    // LDR     R2, =0x40022010
    // 内存加载操作
    // LDR     R2, [R2]
    // 内存加载操作
    // MOVS    R3, #1
    // R3 = 1;
    // ORRS    R3, R2
    // LDR     R2, =0x40022010
    // 内存加载操作
    // STR     R3, [R2]
    // 内存存储操作
    // STRH    R1, [R0]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FB76
 * @note 指令数: 18, 标签数: 1
 */
void precise_func_4fb76(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x4002200C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // BL      sub_49FE4
    // 调用函数: sub_49FE4();
    // MOVS    R5, R0
    // LDR     R0, =0x4002200C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1F
    // BPL     loc_4FBA2
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // CMP     R4, R0
    // 比较操作
    // BEQ     loc_4FB80
    // 条件跳转
    // CMP     R4, #0
    // 比较操作
    // BEQ     loc_4FB9E
    // 条件跳转
    // BL      sub_49FE4
    // 调用函数: sub_49FE4();
    // SUBS    R0, R0, R5
    // 算术运算
    // CMP     R4, R0
    // 比较操作
    // BCS     loc_4FB80
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FBCC
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_4fbcc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007368;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x4002200C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x4002200C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1B
    // BPL     loc_4FBE2
    // LDR     R0, =0x20007368
    // 内存加载操作
    // LDR     R0, [R0,#0x1C]
    // 内存加载操作
    // MOVS    R1, #2
    // R1 = 2;
    // ORRS    R1, R0
    // LDR     R0, =0x20007368
    // 内存加载操作
    // STR     R1, [R0,#0x1C]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FC1C
 * @note 指令数: 17, 标签数: 1
 */
void precise_func_4fc1c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000930;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20A;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // CMP     R4, #0x10
    // 比较操作
    // BGE     loc_4FC3E
    // 条件跳转
    // LDR     R0, =0x20000930
    // 内存加载操作
    // UXTB    R5, R5
    // 数据扩展操作
    // LDR     R1, =unk_20A0
    // 内存加载操作
    // MULS    R1, R5
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, =0x20A
    // 内存加载操作
    // MULS    R1, R4
    // MOVS    R2, #0
    // R2 = 0;
    // STRB    R2, [R0,R1]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_4FC24
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FD56
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_4fd56(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x11;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x12;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // var_17= -0x17
    // var_12= -0x12
    // var_11= -0x11
    // var_10= -0x10
    // var_F= -0xF
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FE3C
 * @note 指令数: 32, 标签数: 0
 */
void precise_func_4fe3c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000787C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x19;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200077D4;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x800E3A9;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x800E2FD;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R0, R4
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4FD56
    // 调用函数: sub_4FD56();
    // LDR     R1, =0x800E2FD
    // 内存加载操作
    // LDR     R0, =0x200077D4
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // BL      sub_47C70
    // 调用函数: sub_47C70();
    // LDR     R1, =0x800E3A9
    // 内存加载操作
    // LDR     R0, =0x200077D4
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // LDR     R0, [R0,R2]
    // 内存加载操作
    // BL      sub_47736
    // 调用函数: sub_47736();
    // LDR     R0, =0x2000787C
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x19
    // LSRS    R0, R0, #0x19
    // LDR     R1, =0x2000787C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000787C
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4FE82
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000787C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FF04
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_4ff04(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078C6;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200076D0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x200076D0
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4FF30
    // 条件跳转
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_48386
    // 调用函数: sub_48386();
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_48386
    // 调用函数: sub_48386();
    // LDR     R0, =0x200078C6
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4FF2A
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_48386
    // 调用函数: sub_48386();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FFFC
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_4fffc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077D8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078C7;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // CPSID   I
    // LDR     R0, =0x200078C7
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_5001E
    // 条件跳转
    // LDR     R0, =0x200077D8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // CMP     R0, R1
    // 比较操作
    // BEQ     loc_5001E
    // 条件跳转
    // LDR     R0, =0x200077D8
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x200077D8
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5005C
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_5005c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078C7;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200077D8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4}
    // 栈操作
    // MOVS    R1, #0x40 ; '@'
    // R1 = 0x40;
    // LDRB    R1, [R0,R1]
    // 内存加载操作
    // MOVS    R2, #1
    // R2 = 1;
    // LDR     R3, =0x200078C7
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // STRB    R2, [R3,R1]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x200077D8
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // MOVS    R4, #4
    // R4 = 4;
    // MULS    R4, R1
    // STR     R2, [R3,R4]
    // 内存存储操作
    // MOVS    R2, #0
    // R2 = 0;
    // LDR     R3, =0x200078C7
    // 内存加载操作
    // UXTB    R1, R1
    // 数据扩展操作
    // STRB    R2, [R3,R1]
    // 内存存储操作
    // POP     {R4}
    // 栈操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50090
 * @note 指令数: 39, 标签数: 0
 */
void precise_func_50090(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078C6;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200069B0;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R6, R1
    // MOVS    R4, #0
    // R4 = 0;
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #1
    // 比较操作
    // BNE     loc_500E4
    // 条件跳转
    // LDR     R0, =0x200069B0
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x44 ; 'D'
    // R1 = 0x44;
    // MULS    R1, R4
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x200069B0
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0x44 ; 'D'
    // R2 = 0x44;
    // MULS    R2, R4
    // STR     R1, [R0,R2]
    // 内存存储操作
    // LDR     R0, =0x200069B0
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x44 ; 'D'
    // R1 = 0x44;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // STRB    R6, [R0,#3]
    // 内存存储操作
    // LDR     R0, =0x200069B0
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x44 ; 'D'
    // R1 = 0x44;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // BL      sub_52E4C
    // 调用函数: sub_52E4C();
    // MOVS    R2, #1
    // R2 = 1;
    // MOVS    R1, #1
    // R1 = 1;
    // MOVS    R0, #3
    // R0 = 3;
    // BL      sub_4830E
    // 调用函数: sub_4830E();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078C6
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_50134
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5013C
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_5013c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x44;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200069B0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000784C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000784C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #2
    // R2 = 2;
    // MULS    R2, R4
    // STRH    R0, [R1,R2]
    // 内存存储操作
    // LDR     R0, =0x200069B0
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x44 ; 'D'
    // R1 = 0x44;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // BL      sub_5005C
    // 调用函数: sub_5005C();
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50180
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_50180(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_18= -0x18
    // arg_0=  0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_502B0
 * @note 指令数: 39, 标签数: 0
 */
void precise_func_502b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000751C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R0, #0x40 ; '@'
    // R0 = 0x40;
    // LDRB    R6, [R4,R0]
    // 内存加载操作
    // LDR     R0, =0x2000751C
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MULS    R1, R6
    // ADDS    R0, R0, R1
    // 算术运算
    // BL      sub_52D26
    // 调用函数: sub_52D26();
    // MOVS    R5, R0
    // LDRH    R0, [R4,#8]
    // 内存加载操作
    // STRH    R0, [R5,#4]
    // 内存存储操作
    // LDRB    R0, [R4,#0xC]
    // 内存加载操作
    // STRB    R0, [R5,#1]
    // 内存存储操作
    // MOVS    R0, #0x28 ; '('
    // R0 = 0x28;
    // LDRB    R0, [R4,R0]
    // 内存加载操作
    // STRB    R0, [R5,#2]
    // 内存存储操作
    // LDRB    R0, [R4,#0xA]
    // 内存加载操作
    // STRB    R0, [R5,#3]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // STRB    R0, [R5]
    // 内存存储操作
    // LDRB    R0, [R5,#1]
    // 内存加载操作
    // CMP     R0, #5
    // 比较操作
    // BNE     loc_50300
    // 条件跳转
    // LDR     R0, =0x2000732C
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // MOVS    R1, #0x20 ; ' '
    // R1 = 0x20;
    // MULS    R1, R6
    // ADDS    R0, R0, R1
    // 算术运算
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // MOVS    R3, #1
    // R3 = 1;
    // LDRB    R2, [R5,#2]
    // 内存加载操作
    // LDRH    R1, [R5,#4]
    // 内存加载操作
    // MOVS    R0, R5
    // ADDS    R0, R0, #6
    // 算术运算
    // BL      sub_50180
    // 调用函数: sub_50180();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5034C
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_5034c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000751C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x2000751C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // BL      sub_52D00
    // 调用函数: sub_52D00();
    // MOVS    R5, R0
    // LDRB    R0, [R5]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     locret_50376
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x2000751C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0x14
    // R2 = 0x14;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // BL      sub_52D4E
    // 调用函数: sub_52D4E();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50378
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_50378(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000751C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x2000751C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // BL      sub_52D00
    // 调用函数: sub_52D00();
    // MOVS    R5, R0
    // LDRB    R0, [R5]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_50396
    // 条件跳转
    // MOVS    R0, R5
    // B       locret_50398
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_503A0
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_503a0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007508;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R2,R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // LDR     R0, =0x20007508
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R1, #0x14
    // R1 = 0x14;
    // MULS    R1, R4
    // ADDS    R0, R0, R1
    // 算术运算
    // BL      sub_52CAA
    // 调用函数: sub_52CAA();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_503C8
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20007508
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R2, #0x14
    // R2 = 0x14;
    // MULS    R2, R4
    // ADDS    R0, R0, R2
    // 算术运算
    // BL      sub_52D4E
    // 调用函数: sub_52D4E();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_503EC
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_503ec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000784C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200059FC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x204;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000784E;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6}
    // 栈操作
    // MOVS    R3, #0x40 ; '@'
    // R3 = 0x40;
    // LDRB    R3, [R0,R3]
    // 内存加载操作
    // MOVS    R4, #1
    // R4 = 1;
    // LDR     R5, =0x2000784C
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // MOVS    R6, #2
    // R6 = 2;
    // MULS    R6, R3
    // STRH    R4, [R5,R6]
    // 内存存储操作
    // LDR     R4, =0x2000784E
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // MOVS    R5, #2
    // R5 = 2;
    // MULS    R5, R3
    // STRH    R2, [R4,R5]
    // 内存存储操作
    // LDR     R4, =0x200059FC
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // MOVS    R5, #0x204
    // R5 = 0x204;
    // MULS    R5, R3
    // ADDS    R4, R4, R5
    // 算术运算
    // LDR     R5, =0x200077DC
    // 内存加载操作
    // UXTB    R3, R3
    // 数据扩展操作
    // MOVS    R6, #4
    // R6 = 4;
    // MULS    R6, R3
    // STR     R4, [R5,R6]
    // 内存存储操作
    // POP     {R4-R6}
    // 栈操作
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50428
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_50428(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x26;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_26= -0x26
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
    // arg_4=  4
    // arg_8=  8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50544
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_50544(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // arg_0=  0
    // arg_4=  4
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50582
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_50582(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x44;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50680
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_50680(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_506F8
 * @note 指令数: 36, 标签数: 1
 */
void precise_func_506f8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007744;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007748;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007758;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007750;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000774C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #4
    // 比较操作
    // BGE     loc_50740
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x2000775C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R2,R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007744
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R2,R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007748
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R2,R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x2000774C
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R2,R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007750
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R2,R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007754
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R2,R0]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007758
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // STRB    R1, [R2,R0]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_506FE
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5075C
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_5075c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007658;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078A5;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200078A7;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // BL      sub_53E60
    // 调用函数: sub_53E60();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078A7
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007658
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078A5
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BL      sub_506F8
    // 调用函数: sub_506F8();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_508AC
 * @note 指令数: 22, 标签数: 0
 */
void precise_func_508ac(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000734C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R1,R2,R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x1C+var_18]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_50922
    // 条件跳转
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_508EE
    // 条件跳转
    // LDR     R0, =0x2000734C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // LDRSB   R0, [R0,R4]
    // CMP     R0, #0
    // 比较操作
    // BPL     loc_508D4
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000734C
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // B       locret_50B26
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50B28
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_50b28(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007788;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R6, R0
    // MOVS    R7, #1
    // R7 = 1;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // LSLS    R7, R1
    // MOVS    R5, R7
    // LDR     R0, =0x20007788
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // LDRB    R0, [R0,R6]
    // 内存加载操作
    // TST     R0, R5
    // 比较操作
    // BEQ     loc_50B56
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_50B58
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50B8C
 * @note 指令数: 33, 标签数: 0
 */
void precise_func_50b8c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007780;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007764;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R6, R0
    // MOVS    R7, #1
    // R7 = 1;
    // UXTB    R4, R4
    // 数据扩展操作
    // MOVS    R0, R4
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // LSLS    R7, R1
    // MOVS    R5, R7
    // LDR     R0, =0x20007780
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // LDRB    R0, [R0,R6]
    // 内存加载操作
    // TST     R0, R5
    // 比较操作
    // BEQ     loc_50BD2
    // 条件跳转
    // LDR     R0, =0x20007764
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // LDRB    R0, [R0,R6]
    // 内存加载操作
    // BICS    R0, R5
    // LDR     R1, =0x20007764
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // STRB    R0, [R1,R6]
    // 内存存储操作
    // LDR     R0, =0x20007780
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // LDRB    R0, [R0,R6]
    // 内存加载操作
    // BICS    R0, R5
    // LDR     R1, =0x20007780
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // STRB    R0, [R1,R6]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50D08
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_50d08(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50FC4
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_50fc4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078A6;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x200078A6
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_50FCA
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_50fca(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078A6;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x200078A6
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_51004
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_51004(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078CF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x200078CF
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #5
    // 比较操作
    // BLT     locret_51094
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_51096
 * @note 指令数: 22, 标签数: 1
 */
void precise_func_51096(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007570;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3F800000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007590;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007580;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // CMP     R0, #4
    // 比较操作
    // BGE     loc_510C6
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007590
    // 内存加载操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0x3F800000
    // R1 = 0x3F800000;
    // LDR     R2, =0x20007570
    // 内存加载操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0x3F800000
    // R1 = 0x3F800000;
    // LDR     R2, =0x20007580
    // 内存加载操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_5109C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_510DA
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_510da(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x800F271;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // BL      sub_51096
    // 调用函数: sub_51096();
    // BL      sub_53F56
    // 调用函数: sub_53F56();
    // LDR     R0, =0x800F271
    // 内存加载操作
    // BL      sub_53FB0
    // 调用函数: sub_53FB0();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_51210
 * @note 指令数: 30, 标签数: 0
 */
void precise_func_51210(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078CA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200078CC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_1B= -0x1B
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0xC
    // 算术运算
    // LDR     R0, =0x200078CC
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // MOVS    R6, R0
    // MOVS    R0, #1
    // R0 = 1;
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // LDR     R0, =0x200078CC
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #8
    // R1 = 8;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // LDR     R0, [SP,#0x20+var_20]
    // 内存加载操作
    // LSLS    R0, R1
    // MOVS    R5, R0
    // LDR     R0, =0x200078CA
    // 内存加载操作
    // UXTB    R6, R6
    // 数据扩展操作
    // LDRB    R0, [R0,R6]
    // 内存加载操作
    // TST     R0, R5
    // 比较操作
    // BEQ     loc_5124A
    // 条件跳转
    // MOVS    R0, #0xFF
    // R0 = 0xFF;
    // LDR     R1, =0x200077E8
    // 内存加载操作
    // LDR     R2, =0x200078CC
    // 内存加载操作
    // LDRB    R2, [R2]
    // 内存加载操作
    // STRB    R0, [R1,R2]
    // 内存存储操作
    // B       loc_51254
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5149A
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_5149a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078D0;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R1, =0x200078D0
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_514A0
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_514a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078D0;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x200078D0
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_514EC
 * @note 指令数: 44, 标签数: 1
 */
void precise_func_514ec(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3F800000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200075D0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200073E8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007408;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200073C8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // MOVS    R1, #0
    // R1 = 0;
    // MOVS    R0, R1
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #8
    // 比较操作
    // BGE     loc_51546
    // 条件跳转
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x200073A8
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x200075D0
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0x3F800000
    // R1 = 0x3F800000;
    // LDR     R2, =0x200073C8
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x20007408
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x200073E8
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #4
    // R3 = 4;
    // MULS    R3, R0
    // STR     R1, [R2,R3]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R2, =0x200075E0
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // MOVS    R3, #2
    // R3 = 2;
    // MULS    R3, R0
    // STRH    R1, [R2,R3]
    // 内存存储操作
    // ADDS    R0, R0, #1
    // 算术运算
    // B       loc_514F2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5156E
 * @note 指令数: 29, 标签数: 1
 */
void precise_func_5156e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078D8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078D9;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200078DF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200078DE;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200078D6;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // BL      sub_514EC
    // 调用函数: sub_514EC();
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #0
    // 比较操作
    // BNE     loc_515AA
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078D6
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078DE
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078D8
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078D9
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078DF
    // 内存加载操作
    // UXTB    R4, R4
    // 数据扩展操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_51578
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_51654
 * @note 指令数: 31, 标签数: 0
 */
void precise_func_51654(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078DC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200078DD;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200078DB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R7,LR}
    // 栈操作
    // LDR     R0, =0x20007700
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x3E8
    // R1 = 0x3E8;
    // CMP     R0, R1
    // 比较操作
    // BLT     loc_5169C
    // 条件跳转
    // LDR     R0, =0x20007700
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x3E8
    // R1 = 0x3E8;
    // SUBS    R0, R0, R1
    // 算术运算
    // LDR     R1, =0x20007700
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078DB
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200078DC
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x200078DC
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x200078DC
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x1E
    // 比较操作
    // BLT     loc_51696
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078DC
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200078DD
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       loc_5169C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_519F4
 * @note 指令数: 54, 标签数: 0
 */
void precise_func_519f4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007854;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x5A;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007850;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007862;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #0x18
    // 算术运算
    // LDR     R0, =0x2000785E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4C1AA
    // 调用函数: sub_4C1AA();
    // LDR     R1, =0x20007850
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007860
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4C1AA
    // 调用函数: sub_4C1AA();
    // LDR     R1, =0x20007852
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007862
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4C1AA
    // 调用函数: sub_4C1AA();
    // LDR     R1, =0x20007854
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007864
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4C1AA
    // 调用函数: sub_4C1AA();
    // LDR     R1, =0x20007856
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000786A
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000785C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007866
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4C1AA
    // 调用函数: sub_4C1AA();
    // LDR     R1, =0x20007858
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007868
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // ADDS    R0, #0x30 ; '0'
    // 算术运算
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_4C1AA
    // 调用函数: sub_4C1AA();
    // MOVS    R1, #0x2000
    // R1 = 0x2000;
    // ADDS    R0, R0, R1
    // 算术运算
    // LDR     R1, =0x2000785A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007850
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x5A ; 'Z'
    // 比较操作
    // BLT     loc_51A6A
    // 条件跳转
    // MOVS    R0, #0x59 ; 'Y'
    // R0 = 0x59;
    // LDR     R1, =0x20007850
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_51BF8
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_51bf8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077E0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200078C8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // PUSH    {R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #0x14
    // 算术运算
    // MOVS    R6, R0
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x200077E0
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_480BE
    // 调用函数: sub_480BE();
    // CMP     R0, #0
    // 比较操作
    // BPL     loc_51C14
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078C8
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       loc_51E50
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_51E58
 * @note 指令数: 40, 标签数: 0
 */
void precise_func_51e58(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200077E0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8016078;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000786A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007860;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007864;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078C8
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200076D8
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // LDR     R2, =0x8016078
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x8015FA8
    // 内存加载操作
    // BL      sub_47FB4
    // 调用函数: sub_47FB4();
    // LDR     R1, =0x200077E0
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_51BF8
    // 调用函数: sub_51BF8();
    // LDR     R0, =0x200078C8
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LSLS    R0, R0, #0x1E
    // BMI     loc_51EAE
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000785E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007860
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007862
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20007864
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000786A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20007866
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x7E2
    // 内存加载操作
    // LDR     R1, =0x20007868
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_51F14
 * @note 指令数: 64, 标签数: 0
 */
void precise_func_51f14(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007872;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007874;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007878;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000787A;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // LDR     R0, =0x2000785E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000786C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007860
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000786E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007862
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20007870
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007864
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20007872
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007866
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20007874
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007868
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20007876
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000786A
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x2000787A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007878
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDRSH   R0, [R0,R1]
    // NEGS    R1, R0
    // MOVS    R6, R1
    // SXTH    R6, R6
    // 数据扩展操作
    // CMP     R6, #1
    // 比较操作
    // BLT     loc_5204C
    // 条件跳转
    // LDR     R0, =0x2000786E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, R6
    // 算术运算
    // LDR     R1, =0x2000786E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000786E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x3C ; '<'
    // 比较操作
    // BLT     loc_51F96
    // 条件跳转
    // LDR     R0, =0x20007870
    // 内存加载操作
    // LDRH    R1, [R0]
    // 内存加载操作
    // STR     R1, [SP,#0x18+var_18]
    // 内存存储操作
    // LDR     R0, =0x2000786E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x3C ; '<'
    // R1 = 0x3C;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // LDR     R1, [SP,#0x18+var_18]
    // 内存加载操作
    // ADDS    R0, R1, R0
    // 算术运算
    // LDR     R1, =0x20007870
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x2000786E
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x3C ; '<'
    // R1 = 0x3C;
    // BL      sub_46376
    // 调用函数: sub_46376();
    // LDR     R0, =0x2000786E
    // 内存加载操作
    // STRH    R1, [R0]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_521D0
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_521d0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x28;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_526D4
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_526d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200076F8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007882;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200076F8
    // 内存加载操作
    // BL      sub_45616
    // 调用函数: sub_45616();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200076F8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007882
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_526EC
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_526ec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200076F8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x3C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xFFFF;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007882;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x200076F8
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0x3C ; '<'
    // 比较操作
    // BLT     locret_52710
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200076F8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20007882
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0xFFFF
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BEQ     locret_52710
    // 条件跳转
    // LDR     R0, =0x20007882
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // ADDS    R0, R0, #1
    // 算术运算
    // LDR     R1, =0x20007882
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_52720
 * @note 指令数: 48, 标签数: 0
 */
void precise_func_52720(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078A0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000781A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200078A2;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x40000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x40000
    // R1 = 0x40000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R1, =0x8015D50
    // 内存加载操作
    // LDR     R0, =0x8015D4C
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_470BC
    // 调用函数: sub_470BC();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007648
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x20007650
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000781A
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078A2
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078A1
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078A0
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20007650
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007648
    // 内存加载操作
    // BL      sub_45616
    // 调用函数: sub_45616();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_5278C
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_5278c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078A0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078A1;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x200078A0
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_527AA
    // 条件跳转
    // LDR     R0, =0x200078A1
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_527AA
    // 条件跳转
    // LDR     R0, =0x200078A0
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x200078A1
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_527B4
    // 无条件跳转
}

