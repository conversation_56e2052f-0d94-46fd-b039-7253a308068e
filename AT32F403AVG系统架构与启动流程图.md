# AT32F403AVG系统架构与启动流程图

## 🏗️ 系统整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    AT32F403AVG嵌入式系统架构                      │
├─────────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 用户界面    │ │ 业务逻辑    │ │ 数据处理    │ │ BASIOT Logo │ │
│  │ 模块        │ │ 模块        │ │ 模块        │ │ 显示模块    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  协议层 (Protocol Layer)                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ UART协议    │ │ 自定义协议  │ │ 命令处理    │ │ 数据封装    │ │
│  │ 处理        │ │ 栈          │ │ 引擎        │ │ 解析        │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  系统服务层 (System Service Layer)                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 任务调度    │ │ 中断管理    │ │ 内存管理    │ │ 错误处理    │ │
│  │ 系统        │ │ 系统        │ │ 系统        │ │ 系统        │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  硬件抽象层 (Hardware Abstraction Layer)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ GPIO驱动    │ │ UART驱动    │ │ 定时器驱动  │ │ 时钟管理    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  硬件层 (Hardware Layer)                                         │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              AT32F403AVG微控制器                             │ │
│  │  ARM Cortex-M4F @ 240MHz | 1MB Flash | 96KB RAM           │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 系统启动流程图

```
┌─────────────┐
│   上电复位   │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│ 向量表加载   │ ← Flash地址: 0x08000000
│ 栈指针设置   │   栈地址: 0x20000618
└──────┬──────┘
       │
       ▼
┌─────────────┐
│Reset_Handler│ ← 复位处理函数入口
└──────┬──────┘
       │
       ▼
┌─────────────┐
│ 时钟系统配置 │ ← sub_8000C64 (47条指令)
│ - PLL配置   │
│ - 时钟分频   │
│ - 外设时钟   │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│FPU协处理器  │ ← sub_8000D48 (12条指令)
│配置         │
│ - 启用FPU   │
│ - CPACR设置 │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│系统复位处理 │ ← sub_8000D5C (25条指令)
│ - RAM初始化 │
│ - BSS清零   │
│ - DATA复制  │
└──────┬──────┘
       │
       ▼
┌─────────────┐
│构造函数调用 │ ← sub_8000D20 (18条指令)
└──────┬──────┘
       │
       ▼
┌─────────────┐
│主应用循环   │ ← main_application_loop()
│启动         │   sub_80004C4 (449条指令)
└─────────────┘
```

## 🔄 主循环执行流程

```
┌─────────────────────────────────────────────────────────────────┐
│                      主应用循环 (无限循环)                        │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
                 ┌─────────────┐
                 │系统任务管理 │ ← system_task_manager()
                 └──────┬──────┘
                        │
                        ▼
                 ┌─────────────┐
                 │数据完整性   │ ← data_integrity_validator()
                 │验证         │
                 └──────┬──────┘
                        │
                        ▼
                 ┌─────────────┐
                 │GPIO状态     │ ← gpio_status_monitor()
                 │监控         │
                 └──────┬──────┘
                        │
                        ▼
                 ┌─────────────┐
                 │计数器管理   │ ← counter_a, counter_c检查
                 └──────┬──────┘
                        │
                        ▼
                 ┌─────────────┐
                 │模式切换     │ ← 模式0/1切换逻辑
                 │控制         │
                 └──────┬──────┘
                        │
                        ▼
                 ┌─────────────┐
                 │状态机处理   │ ← 8状态状态机
                 └──────┬──────┘
                        │
                        ▼
                 ┌─────────────┐
                 │应用程序     │ ← function_pointer检查
                 │跳转检查     │
                 └──────┬──────┘
                        │
                        ▼
                 ┌─────────────┐
                 │循环计数     │ ← loop_counter管理
                 │管理         │
                 └──────┬──────┘
                        │
                        └─────────────────────────────────────────┐
                                                                  │
                                                                  ▼
                                                         ┌─────────────┐
                                                         │返回循环开始 │
                                                         └─────────────┘
```

## 🔧 中断处理架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        中断向量表                                │
│                    (Flash: 0x08000000)                         │
├─────────────────────────────────────────────────────────────────┤
│ 栈指针 (SP)     │ 0x20000618                                    │
│ 复位处理        │ Reset_Handler                                 │
│ NMI处理         │ nmi_interrupt_handler                         │
│ 硬件错误        │ hardfault_error_handler                       │
│ 内存管理错误    │ memmanage_error_handler                       │
│ 总线错误        │ busfault_error_handler                        │
│ 使用错误        │ usagefault_error_handler                      │
│ 保留            │ [4个保留位置]                                 │
│ SVC处理         │ svc_service_handler                           │
│ 调试监控        │ debugmon_service_handler                      │
│ 保留            │ [1个保留位置]                                 │
│ PendSV处理      │ pendsv_service_handler                        │
│ SysTick处理     │ systick_service_handler                       │
│ 外部中断        │ [68个外部中断处理函数]                        │
└─────────────────────────────────────────────────────────────────┘
```

## 💾 内存映射图

```
┌─────────────────────────────────────────────────────────────────┐
│                        Flash存储器 (1MB)                         │
├─────────────────────────────────────────────────────────────────┤
│ 0x08000000 │ 向量表 (1KB)                                       │
│ 0x08000400 │ 引导加载程序 (7KB)                                 │
│ 0x08002000 │ 应用程序代码 (1016KB)                              │
│ 0x08001810 │ MAC地址存储                                        │
│ 0x8016A54  │ BASIOT Logo数据 (256字节)                          │
│ 0x080FFFFF │ Flash结束                                          │
└─────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────┐
│                         RAM存储器 (96KB)                         │
├─────────────────────────────────────────────────────────────────┤
│ 0x20000000 │ 系统变量区                                         │
│ 0x20000004 │ counter_b (计数器B)                                │
│ 0x20000008 │ buffer_index (缓冲区索引)                          │
│ 0x2000000A │ counter_a (计数器A)                                │
│ 0x2000000C │ counter_c (计数器C)                                │
│ 0x20000010 │ status_flag (状态标志)                             │
│ 0x20000011 │ mode_flag (模式标志)                               │
│ 0x20000100 │ 数据缓冲区 (3072字节)                              │
│ 0x20001001 │ function_pointer (函数指针)                        │
│ 0x20017000 │ 栈区域 (1560字节)                                  │
│ 0x20000618 │ 栈顶指针                                           │
│ 0x20017FFF │ RAM结束                                            │
└─────────────────────────────────────────────────────────────────┘
```

## 📡 通信协议栈架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        应用层协议                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ G0B1命令    │ │ INCO命令    │ │ 引导命令    │ │ 回显命令    │ │
│  │ 处理        │ │ 处理        │ │ 处理        │ │ 处理        │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        协议处理层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 包解析      │ │ CRC校验     │ │ 数据封装    │ │ 错误处理    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        传输层                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ UART1处理   │ │ UART2处理   │ │ 缓冲区管理  │ │ 流控制      │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        物理层                                    │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    UART硬件接口                             │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## ⚡ 任务调度时序图

```
时间轴 (ms):  0    10    20    30    40    50    60    70    80    90   100
             │     │     │     │     │     │     │     │     │     │     │
10ms任务:    ●─────●─────●─────●─────●─────●─────●─────●─────●─────●─────●
             │     │     │     │     │     │     │     │     │     │     │
100ms任务:   ●─────────────────────────────────────────────────────────────●
             │                                                             │
1000ms任务:  ●─────────────────────────────────────────────────────────────┐
             │                                                             │
主循环:      ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●

任务类型:
● 10ms任务:   按键扫描、LED更新、GPIO监控
● 100ms任务:  系统状态检查、温度监控、电压监控
● 1000ms任务: 参数保存、性能统计、自检
● 主循环:     连续执行，处理通信和状态机
```

## 🛡️ 错误处理流程图

```
┌─────────────┐
│ 错误检测    │
└──────┬──────┘
       │
       ▼
┌─────────────┐    是    ┌─────────────┐
│ 可恢复错误? │ ────────→ │ 执行恢复    │
└──────┬──────┘          │ 策略        │
       │ 否              └──────┬──────┘
       ▼                        │
┌─────────────┐                 │
│ 记录错误    │                 │
│ 信息        │                 │
└──────┬──────┘                 │
       │                        │
       ▼                        │
┌─────────────┐                 │
│ 系统复位    │                 │
│ 或安全模式  │                 │
└──────┬──────┘                 │
       │                        │
       └────────────────────────┘
                │
                ▼
       ┌─────────────┐
       │ 继续正常    │
       │ 运行        │
       └─────────────┘
```

## 📊 系统性能监控

```
┌─────────────────────────────────────────────────────────────────┐
│                        性能监控仪表板                            │
├─────────────────────────────────────────────────────────────────┤
│ CPU使用率:    ████████░░ 80%                                    │
│ 内存使用:     ██████░░░░ 62.5%                                  │
│ Flash使用:    ████████░░ 80%                                    │
│ 栈使用:       ███░░░░░░░ 30%                                    │
├─────────────────────────────────────────────────────────────────┤
│ 主循环频率:   1000 Hz                                           │
│ 中断响应:     < 10 μs                                           │
│ 通信延迟:     < 5 ms                                            │
│ 任务切换:     < 100 μs                                          │
├─────────────────────────────────────────────────────────────────┤
│ 系统温度:     45°C                                              │
│ 供电电压:     3.3V                                              │
│ 运行时间:     123456 秒                                         │
│ 错误计数:     0                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## 📝 架构总结

AT32F403AVG系统采用分层架构设计，从底层硬件到上层应用形成清晰的层次结构。系统启动流程经过精心设计，确保每个阶段都能正确初始化。主循环采用状态机模式，提供稳定可靠的运行机制。中断处理系统完整覆盖所有可能的异常情况，通信协议栈支持多种命令类型，任务调度系统保证实时性要求。

**架构特点**:
- 🏗️ **分层设计**: 清晰的模块化架构
- 🚀 **高效启动**: 优化的启动流程
- 🔄 **稳定运行**: 可靠的主循环机制
- ⚡ **实时响应**: 快速的中断处理
- 📡 **完整通信**: 全面的协议支持
- 🛡️ **安全可靠**: 完善的错误处理
