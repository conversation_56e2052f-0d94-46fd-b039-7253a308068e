// 大规模手工转换批次 6 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_1CFB8
 * @note 指令数: 4
 */
uint32_t func_1cfb8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000817D = (volatile uint32_t *)0x2000817D;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1CFF4
 * @note 指令数: 4
 */
uint32_t func_1cff4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000817D = (volatile uint32_t *)0x2000817D;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1CFFE
 * @note 指令数: 4
 */
uint32_t func_1cffe(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200080EE = (volatile uint32_t *)0x200080EE;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1D008
 * @note 指令数: 29
 */
void func_1d008(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_20008168 = (volatile uint32_t *)0x20008168;
    volatile uint32_t *addr_20008078 = (volatile uint32_t *)0x20008078;
    volatile uint32_t *addr_20007CD8 = (volatile uint32_t *)0x20007CD8;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1D052
 * @note 指令数: 62
 */
void func_1d052(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20007F58 = (volatile uint32_t *)0x20007F58;
    volatile uint32_t *addr_F8 = (volatile uint32_t *)0xF8;
    volatile uint32_t *addr_20007F60 = (volatile uint32_t *)0x20007F60;
    volatile uint32_t *addr_20008078 = (volatile uint32_t *)0x20008078;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D102
 * @note 指令数: 118
 */
void func_1d102(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20008168 = (volatile uint32_t *)0x20008168;
    volatile uint32_t *addr_20007F58 = (volatile uint32_t *)0x20007F58;
    volatile uint32_t *addr_20008078 = (volatile uint32_t *)0x20008078;
    volatile uint32_t *addr_20007F60 = (volatile uint32_t *)0x20007F60;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D220
 * @note 指令数: 114
 */
void func_1d220(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20008168 = (volatile uint32_t *)0x20008168;
    volatile uint32_t *addr_2000816A = (volatile uint32_t *)0x2000816A;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_2000807C = (volatile uint32_t *)0x2000807C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D310
 * @note 指令数: 13
 */
uint32_t func_1d310(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;
    volatile uint32_t *addr_20008167 = (volatile uint32_t *)0x20008167;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1D328
 * @note 指令数: 147
 */
void func_1d328(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2B = (volatile uint32_t *)0x2B;
    volatile uint32_t *addr_20007F60 = (volatile uint32_t *)0x20007F60;
    volatile uint32_t *addr_2000816A = (volatile uint32_t *)0x2000816A;
    volatile uint32_t *addr_200068D5 = (volatile uint32_t *)0x200068D5;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D472
 * @note 指令数: 32
 */
void func_1d472(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200080EC = (volatile uint32_t *)0x200080EC;
    volatile uint32_t *addr_20008074 = (volatile uint32_t *)0x20008074;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D500
 * @note 指令数: 126
 */
void func_1d500(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20007280 = (volatile uint32_t *)0x20007280;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D672
 * @note 指令数: 84
 */
void func_1d672(void)
{
    // 内存地址定义
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_9600 = (volatile uint32_t *)0x9600;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_22 = (volatile uint32_t *)0x22;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D74E
 * @note 指令数: 60
 */
void func_1d74e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18E = (volatile uint32_t *)0x18E;
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_31 = (volatile uint32_t *)0x31;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D80A
 * @note 指令数: 93
 */
void func_1d80a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20008090 = (volatile uint32_t *)0x20008090;
    volatile uint32_t *addr_200080F8 = (volatile uint32_t *)0x200080F8;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1D938
 * @note 指令数: 17
 */
void func_1d938(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_2000808C = (volatile uint32_t *)0x2000808C;
    volatile uint32_t *addr_20008173 = (volatile uint32_t *)0x20008173;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1D964
 * @note 指令数: 14
 */
void func_1d964(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_2000808C = (volatile uint32_t *)0x2000808C;
    volatile uint32_t *addr_20008173 = (volatile uint32_t *)0x20008173;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1D98C
 * @note 指令数: 72
 */
void func_1d98c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10000 = (volatile uint32_t *)0x10000;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;
    volatile uint32_t *addr_20007280 = (volatile uint32_t *)0x20007280;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1DA48
 * @note 指令数: 12
 */
void func_1da48(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200080F8 = (volatile uint32_t *)0x200080F8;
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;
    volatile uint32_t *addr_20007280 = (volatile uint32_t *)0x20007280;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1DA6A
 * @note 指令数: 145
 */
void func_1da6a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_60 = (volatile uint32_t *)0x60;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1DC0C
 * @note 指令数: 63
 */
uint16_t func_1dc0c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20007DCC = (volatile uint32_t *)0x20007DCC;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_28 = (volatile uint32_t *)0x28;

    // 局部变量
    uint16_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1DCA8
 * @note 指令数: 19
 */
void func_1dca8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007DCC = (volatile uint32_t *)0x20007DCC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1DCD8
 * @note 指令数: 16
 */
void func_1dcd8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007DCC = (volatile uint32_t *)0x20007DCC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1DCFA
 * @note 指令数: 32
 */
void func_1dcfa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007DB8 = (volatile uint32_t *)0x20007DB8;
    volatile uint32_t *addr_20A = (volatile uint32_t *)0x20A;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1DD4E
 * @note 指令数: 18
 */
void func_1dd4e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20008090 = (volatile uint32_t *)0x20008090;
    volatile uint32_t *addr_204 = (volatile uint32_t *)0x204;
    volatile uint32_t *addr_200080FA = (volatile uint32_t *)0x200080FA;

    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1DD7E
 * @note 指令数: 111
 */
void func_1dd7e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_55 = (volatile uint32_t *)0x55;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_FFFF = (volatile uint32_t *)0xFFFF;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1DE9E
 * @note 指令数: 36
 */
void func_1de9e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_38 = (volatile uint32_t *)0x38;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1DEEA
 * @note 指令数: 110
 */
void func_1deea(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_60 = (volatile uint32_t *)0x60;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1DFEE
 * @note 指令数: 41
 */
void func_1dfee(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20007DB8 = (volatile uint32_t *)0x20007DB8;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1E0A0
 * @note 指令数: 84
 */
float func_1e0a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007B3C = (volatile uint32_t *)0x20007B3C;
    volatile uint32_t *addr_20007ABC = (volatile uint32_t *)0x20007ABC;
    volatile uint32_t *addr_62 = (volatile uint32_t *)0x62;
    volatile uint32_t *addr_2000813A = (volatile uint32_t *)0x2000813A;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1E18C
 * @note 指令数: 66
 */
float func_1e18c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_F2177617 = (volatile uint32_t *)0xF2177617;
    volatile uint32_t *addr_20006540 = (volatile uint32_t *)0x20006540;
    volatile uint32_t *addr_2000813F = (volatile uint32_t *)0x2000813F;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1E240
 * @note 指令数: 511
 */
float func_1e240(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_20007B3C = (volatile uint32_t *)0x20007B3C;
    volatile uint32_t *addr_20006540 = (volatile uint32_t *)0x20006540;
    volatile uint32_t *addr_20008138 = (volatile uint32_t *)0x20008138;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1E8A8
 * @note 指令数: 213
 */
float func_1e8a8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_F2177617 = (volatile uint32_t *)0xF2177617;
    volatile uint32_t *addr_2000813F = (volatile uint32_t *)0x2000813F;
    volatile uint32_t *addr_81 = (volatile uint32_t *)0x81;
    volatile uint32_t *addr_2000813C = (volatile uint32_t *)0x2000813C;

    // 局部变量
    float result = 0.0f;

    // 浮点数操作
    if (index >= 0x10) {
        return 0.0f;
    }
    volatile float *float_array = (volatile float *)0x20007584;
    result = float_array[index];
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1EB6C
 * @note 指令数: 14
 */
void func_1eb6c(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1EB98
 * @note 指令数: 60
 */
uint32_t func_1eb98(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007D40 = (volatile uint32_t *)0x20007D40;
    volatile uint32_t *addr_200080D0 = (volatile uint32_t *)0x200080D0;
    volatile uint32_t *addr_200080CC = (volatile uint32_t *)0x200080CC;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1EC2A
 * @note 指令数: 144
 */
void func_1ec2a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200080DA = (volatile uint32_t *)0x200080DA;
    volatile uint32_t *addr_20008155 = (volatile uint32_t *)0x20008155;
    volatile uint32_t *addr_200080E0 = (volatile uint32_t *)0x200080E0;
    volatile uint32_t *addr_20008156 = (volatile uint32_t *)0x20008156;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1ED8C
 * @note 指令数: 158
 */
void func_1ed8c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200080D8 = (volatile uint32_t *)0x200080D8;
    volatile uint32_t *addr_200080CC = (volatile uint32_t *)0x200080CC;
    volatile uint32_t *addr_20008156 = (volatile uint32_t *)0x20008156;
    volatile uint32_t *addr_200080CA = (volatile uint32_t *)0x200080CA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1EF1E
 * @note 指令数: 152
 */
uint32_t func_1ef1e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20007D40 = (volatile uint32_t *)0x20007D40;
    volatile uint32_t *addr_200080D0 = (volatile uint32_t *)0x200080D0;
    volatile uint32_t *addr_200078F8 = (volatile uint32_t *)0x200078F8;
    volatile uint32_t *addr_200080D2 = (volatile uint32_t *)0x200080D2;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1F0A6
 * @note 指令数: 379
 */
void func_1f0a6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200080DA = (volatile uint32_t *)0x200080DA;
    volatile uint32_t *addr_20008155 = (volatile uint32_t *)0x20008155;
    volatile uint32_t *addr_200080E0 = (volatile uint32_t *)0x200080E0;
    volatile uint32_t *addr_20008156 = (volatile uint32_t *)0x20008156;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1F550
 * @note 指令数: 14
 */
void func_1f550(uint32_t param0)
{
    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_1F57C
 * @note 指令数: 11
 */
uint32_t func_1f57c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000816C = (volatile uint32_t *)0x2000816C;
    volatile uint32_t *addr_2000816B = (volatile uint32_t *)0x2000816B;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1F598
 * @note 指令数: 7
 */
uint32_t func_1f598(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_1F5A8
 * @note 指令数: 102
 */
void func_1f5a8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_EA60 = (volatile uint32_t *)0xEA60;
    volatile uint32_t *addr_8016938 = (volatile uint32_t *)0x8016938;
    volatile uint32_t *addr_8015598 = (volatile uint32_t *)0x8015598;
    volatile uint32_t *addr_2DB = (volatile uint32_t *)0x2DB;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1F6C0
 * @note 指令数: 139
 */
void func_1f6c0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_EA60 = (volatile uint32_t *)0xEA60;
    volatile uint32_t *addr_8016938 = (volatile uint32_t *)0x8016938;
    volatile uint32_t *addr_8015598 = (volatile uint32_t *)0x8015598;
    volatile uint32_t *addr_2DB = (volatile uint32_t *)0x2DB;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1F82C
 * @note 指令数: 91
 */
void func_1f82c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1F90A
 * @note 指令数: 57
 */
void func_1f90a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_EA60 = (volatile uint32_t *)0xEA60;
    volatile uint32_t *addr_8016938 = (volatile uint32_t *)0x8016938;
    volatile uint32_t *addr_8015598 = (volatile uint32_t *)0x8015598;
    volatile uint32_t *addr_2DD = (volatile uint32_t *)0x2DD;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1F994
 * @note 指令数: 39
 */
void func_1f994(void)
{
    // 内存地址定义
    volatile uint32_t *addr_EA60 = (volatile uint32_t *)0xEA60;
    volatile uint32_t *addr_9C = (volatile uint32_t *)0x9C;
    volatile uint32_t *addr_8015FB0 = (volatile uint32_t *)0x8015FB0;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1F9EE
 * @note 指令数: 69
 */
void func_1f9ee(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1FAA8
 * @note 指令数: 49
 */
void func_1faa8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_EA60 = (volatile uint32_t *)0xEA60;
    volatile uint32_t *addr_8016190 = (volatile uint32_t *)0x8016190;
    volatile uint32_t *addr_8015DAC = (volatile uint32_t *)0x8015DAC;
    volatile uint32_t *addr_1DC = (volatile uint32_t *)0x1DC;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1FB1E
 * @note 指令数: 80
 */
void func_1fb1e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_EA60 = (volatile uint32_t *)0xEA60;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;
    volatile uint32_t *addr_B5 = (volatile uint32_t *)0xB5;
    volatile uint32_t *addr_1900 = (volatile uint32_t *)0x1900;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_1FBDA
 * @note 指令数: 97
 */
void func_1fbda(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_B4 = (volatile uint32_t *)0xB4;
    volatile uint32_t *addr_EA60 = (volatile uint32_t *)0xEA60;
    volatile uint32_t *addr_B6 = (volatile uint32_t *)0xB6;
    volatile uint32_t *addr_2000816B = (volatile uint32_t *)0x2000816B;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

