// 大规模手工转换批次 20 - 精确复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工转换函数 - 对应 sub_74360
 * @note 指令数: 121
 */
void func_74360(void)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_8011D70 = (volatile uint32_t *)0x8011D70;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74470
 * @note 指令数: 89
 */
void func_74470(void)
{
    // 内存地址定义
    volatile uint32_t *addr_10000 = (volatile uint32_t *)0x10000;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_200036F2 = (volatile uint32_t *)0x200036F2;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74534
 * @note 指令数: 40
 */
void func_74534(void)
{
    // 内存地址定义
    volatile uint32_t *addr_8011D88 = (volatile uint32_t *)0x8011D88;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_8012464 = (volatile uint32_t *)0x8012464;
    volatile uint32_t *addr_2000373C = (volatile uint32_t *)0x2000373C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74598
 * @note 指令数: 66
 */
uint32_t func_74598(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_90 = (volatile uint32_t *)0x90;
    volatile uint32_t *addr_2000373B = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *addr_8011F2C = (volatile uint32_t *)0x8011F2C;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_74638
 * @note 指令数: 83
 */
uint32_t func_74638(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_2000012C = (volatile uint32_t *)0x2000012C;
    volatile uint32_t *addr_8011F54 = (volatile uint32_t *)0x8011F54;
    volatile uint32_t *addr_20003574 = (volatile uint32_t *)0x20003574;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_746F8
 * @note 指令数: 74
 */
uint32_t func_746f8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20003574 = (volatile uint32_t *)0x20003574;
    volatile uint32_t *addr_8011F7C = (volatile uint32_t *)0x8011F7C;
    volatile uint32_t *addr_2000373B = (volatile uint32_t *)0x2000373B;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_74798
 * @note 指令数: 13
 */
void func_74798(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_747B0
 * @note 指令数: 13
 */
void func_747b0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_747D0
 * @note 指令数: 13
 */
void func_747d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74810
 * @note 指令数: 121
 */
void func_74810(void)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_8011D28 = (volatile uint32_t *)0x8011D28;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_200036EE = (volatile uint32_t *)0x200036EE;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74938
 * @note 指令数: 96
 */
void func_74938(void)
{
    // 内存地址定义
    volatile uint32_t *addr_10000 = (volatile uint32_t *)0x10000;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_200036EE = (volatile uint32_t *)0x200036EE;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74A14
 * @note 指令数: 121
 */
void func_74a14(void)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_200031A8 = (volatile uint32_t *)0x200031A8;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74B24
 * @note 指令数: 96
 */
void func_74b24(void)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_200031A8 = (volatile uint32_t *)0x200031A8;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74BF8
 * @note 指令数: 145
 */
void func_74bf8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_2B = (volatile uint32_t *)0x2B;
    volatile uint32_t *addr_200031B2 = (volatile uint32_t *)0x200031B2;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74D3C
 * @note 指令数: 132
 */
void func_74d3c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200031B2 = (volatile uint32_t *)0x200031B2;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74E60
 * @note 指令数: 115
 */
void func_74e60(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_8011DE8 = (volatile uint32_t *)0x8011DE8;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_74F58
 * @note 指令数: 88
 */
void func_74f58(void)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_75018
 * @note 指令数: 80
 */
uint32_t func_75018(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_90 = (volatile uint32_t *)0x90;
    volatile uint32_t *addr_2000373B = (volatile uint32_t *)0x2000373B;
    volatile uint32_t *addr_D = (volatile uint32_t *)0xD;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_750C6
 * @note 指令数: 13
 */
void func_750c6(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_750EC
 * @note 指令数: 136
 */
void func_750ec(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003614 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_75210
 * @note 指令数: 103
 */
void func_75210(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003614 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_75304
 * @note 指令数: 59
 */
uint32_t func_75304(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_8011E78 = (volatile uint32_t *)0x8011E78;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_75394
 * @note 指令数: 64
 */
void func_75394(void)
{
    // 内存地址定义
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_75428
 * @note 指令数: 59
 */
uint32_t func_75428(void)
{
    // 内存地址定义
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_8011CE0 = (volatile uint32_t *)0x8011CE0;
    volatile uint32_t *addr_8011C04 = (volatile uint32_t *)0x8011C04;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_754B8
 * @note 指令数: 61
 */
void func_754b8(void)
{
    // 内存地址定义
    volatile uint32_t *addr_8012378 = (volatile uint32_t *)0x8012378;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_20003738 = (volatile uint32_t *)0x20003738;
    volatile uint32_t *addr_2000373A = (volatile uint32_t *)0x2000373A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7557C
 * @note 指令数: 367
 */
void func_7557c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_801213C = (volatile uint32_t *)0x801213C;
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_801217C = (volatile uint32_t *)0x801217C;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_758C8
 * @note 指令数: 39
 */
void func_758c8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003740 = (volatile uint32_t *)0x20003740;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_75920
 * @note 指令数: 61
 */
uint32_t func_75920(void)
{
    // 内存地址定义
    volatile uint32_t *addr_78 = (volatile uint32_t *)0x78;
    volatile uint32_t *addr_20003736 = (volatile uint32_t *)0x20003736;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_759C0
 * @note 指令数: 41
 */
void func_759c0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20003736 = (volatile uint32_t *)0x20003736;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_801219C = (volatile uint32_t *)0x801219C;
    volatile uint32_t *addr_21 = (volatile uint32_t *)0x21;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_75A40
 * @note 指令数: 47
 */
uint32_t func_75a40(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_20003580 = (volatile uint32_t *)0x20003580;
    volatile uint32_t *addr_200032C0 = (volatile uint32_t *)0x200032C0;
    volatile uint32_t *addr_2000373F = (volatile uint32_t *)0x2000373F;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_75AE0
 * @note 指令数: 225
 */
void func_75ae0(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200036B0 = (volatile uint32_t *)0x200036B0;
    volatile uint32_t *addr_80116F0 = (volatile uint32_t *)0x80116F0;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_75CFC
 * @note 指令数: 106
 */
void func_75cfc(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_200036B0 = (volatile uint32_t *)0x200036B0;
    volatile uint32_t *addr_2000373E = (volatile uint32_t *)0x2000373E;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_75E3C
 * @note 指令数: 378
 */
void func_75e3c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000373F = (volatile uint32_t *)0x2000373F;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_200036B0 = (volatile uint32_t *)0x200036B0;
    volatile uint32_t *addr_200036FA = (volatile uint32_t *)0x200036FA;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_762E4
 * @note 指令数: 21
 */
uint32_t func_762e4(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7630C
 * @note 指令数: 26
 */
uint32_t func_7630c(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_76340
 * @note 指令数: 22
 */
uint32_t func_76340(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_76398
 * @note 指令数: 3
 */
void func_76398(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 基本处理逻辑
    result = param0;}

/**
 * @brief 手工转换函数 - 对应 sub_7639E
 * @note 指令数: 87
 */
void func_7639e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;

    // 局部变量

    // 条件分支逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief 手工转换函数 - 对应 sub_7644C
 * @note 指令数: 11
 */
uint32_t func_7644c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_76462
 * @note 指令数: 5
 */
uint32_t func_76462(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7646C
 * @note 指令数: 24
 */
void func_7646c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000371F = (volatile uint32_t *)0x2000371F;
    volatile uint32_t *addr_2000371E = (volatile uint32_t *)0x2000371E;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7649E
 * @note 指令数: 24
 */
void func_7649e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003721 = (volatile uint32_t *)0x20003721;
    volatile uint32_t *addr_20003722 = (volatile uint32_t *)0x20003722;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_764D0
 * @note 指令数: 24
 */
void func_764d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003725 = (volatile uint32_t *)0x20003725;
    volatile uint32_t *addr_20003724 = (volatile uint32_t *)0x20003724;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76502
 * @note 指令数: 24
 */
void func_76502(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003728 = (volatile uint32_t *)0x20003728;
    volatile uint32_t *addr_20003727 = (volatile uint32_t *)0x20003727;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76534
 * @note 指令数: 24
 */
void func_76534(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000372D = (volatile uint32_t *)0x2000372D;
    volatile uint32_t *addr_2000372E = (volatile uint32_t *)0x2000372E;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76566
 * @note 指令数: 3
 */
uint8_t func_76566(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20003730 = (volatile uint32_t *)0x20003730;

    // 局部变量
    uint8_t result = 0;

    // 基本处理逻辑
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_7656C
 * @note 指令数: 24
 */
void func_7656c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000372B = (volatile uint32_t *)0x2000372B;
    volatile uint32_t *addr_2000372A = (volatile uint32_t *)0x2000372A;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_7659E
 * @note 指令数: 6
 */
uint32_t func_7659e(void)
{
    // 内存地址定义
    volatile uint32_t *addr_200035A4 = (volatile uint32_t *)0x200035A4;

    // 局部变量
    uint32_t result = 0;

    // 函数调用逻辑
    extern void external_function(void);
    external_function();
    result = param0;
    return result;
}

/**
 * @brief 手工转换函数 - 对应 sub_765D0
 * @note 指令数: 234
 */
void func_765d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_200036E0 = (volatile uint32_t *)0x200036E0;
    volatile uint32_t *addr_20003730 = (volatile uint32_t *)0x20003730;
    volatile uint32_t *addr_20003726 = (volatile uint32_t *)0x20003726;
    volatile uint32_t *addr_20003729 = (volatile uint32_t *)0x20003729;

    // 局部变量

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief 手工转换函数 - 对应 sub_76820
 * @note 指令数: 40
 */
uint32_t func_76820(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t result = 0;

    // 数组操作和条件判断
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

