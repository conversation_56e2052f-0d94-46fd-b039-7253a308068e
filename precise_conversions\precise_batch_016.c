// 精确转换批次 16 - 完全对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DBB0
 * @note 指令数: 18, 标签数: 0
 */
void precise_func_4dbb0(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007794;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078AD;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015F00;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8015FF0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R2, =0x8015FF0
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x8015F00
    // 内存加载操作
    // BL      sub_47FB4
    // 调用函数: sub_47FB4();
    // LDR     R1, =0x20007794
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078AD
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_4DB34
    // 调用函数: sub_4DB34();
    // CMP     R0, #0
    // 比较操作
    // BNE     loc_4DBD8
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200078AD
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_4DBE8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DBEA
 * @note 指令数: 26, 标签数: 0
 */
void precise_func_4dbea(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007794;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078AD;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015E14;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_480BE
    // 调用函数: sub_480BE();
    // LDR     R0, =0x8015E14
    // 内存加载操作
    // BL      sub_49244
    // 调用函数: sub_49244();
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // STR     R1, [SP,#0x10+var_10]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADDS    R0, R0, #1
    // 算术运算
    // MOVS    R2, R0
    // LDR     R1, =0x8015E14
    // 内存加载操作
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4803A
    // 调用函数: sub_4803A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4DC22
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200078AD
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_4DC28
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DC2A
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_4dc2a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078AD;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #0x18
    // 算术运算
    // MOVS    R4, R0
    // LDR     R0, =0x200078AD
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_4DC3C
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_4DC6C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DC70
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4dc70(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078AD;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R6, R2
    // LDR     R0, =0x200078AD
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_4DC86
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_4DCB2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DCB4
 * @note 指令数: 4, 标签数: 0
 */
void precise_func_4dcb4(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x58;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x60;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_60= -0x60
    // var_5C= -0x5C
    // var_58= -0x58
    // var_54= -0x54
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DD4C
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4dd4c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078AD;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R4-R6,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R5, R0
    // MOVS    R4, R1
    // MOVS    R6, R2
    // LDR     R0, =0x200078AD
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_4DD62
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_4DD8E
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DD90
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_4dd90(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007794;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200078AD;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8015E04;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // PUSH    {R4,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // MOVS    R1, #0
    // R1 = 0;
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_480BE
    // 调用函数: sub_480BE();
    // LDR     R0, =0x8015E04
    // 内存加载操作
    // BL      sub_49244
    // 调用函数: sub_49244();
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // STR     R1, [SP,#0x10+var_10]
    // 内存存储操作
    // MOVS    R3, #0
    // R3 = 0;
    // ADDS    R0, R0, #1
    // 算术运算
    // MOVS    R2, R0
    // LDR     R1, =0x8015E04
    // 内存加载操作
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_4803A
    // 调用函数: sub_4803A();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4DDCA
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200078AD
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_4DDD2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DDE0
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4dde0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007794;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x20007794
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // BL      sub_480EE
    // 调用函数: sub_480EE();
    // UXTB    R0, R0
    // 数据扩展操作
    // POP     {R4,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DDF0
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_4ddf0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8015FF0;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // LDR     R0, =0x8015FF0
    // 内存加载操作
    // LDRB    R0, [R0,#2]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DDF8
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_4ddf8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078AD;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R1, R0
    // LDR     R0, =0x200078AD
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DE1C
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_4de1c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, R0
    // CPSID   I
    // BL      sub_4FB08
    // 调用函数: sub_4FB08();
    // MOVS    R0, R4
    // BL      sub_4FB3C
    // 调用函数: sub_4FB3C();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DE54
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_4de54(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // CPSID   I
    // BL      sub_4FB08
    // 调用函数: sub_4FB08();
    // MOVS    R2, R4
    // MOVS    R3, #0
    // R3 = 0;
    // MOVS    R1, R5
    // MOVS    R0, #2
    // R0 = 2;
    // BL      sub_4FA64
    // 调用函数: sub_4FA64();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4DE78
    // 条件跳转
    // BL      sub_4FB2C
    // 调用函数: sub_4FB2C();
    // MOVS    R0, #0
    // R0 = 0;
    // B       locret_4DE80
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DE8C
 * @note 指令数: 3, 标签数: 0
 */
void precise_func_4de8c(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_4C234
    // 调用函数: sub_4C234();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4DE94
 * @note 指令数: 43, 标签数: 0
 */
void precise_func_4de94(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000787C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200078D4;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x55;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200077F0;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R4, R0
    // LDR     R0, =0x8001800
    // 内存加载操作
    // STR     R0, [SP,#0x18+var_18]
    // 内存存储操作
    // LDR     R0, =0x8001804
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, =0x803F800
    // 内存加载操作
    // MOVS    R7, R0
    // LDR     R0, =0x803F804
    // 内存加载操作
    // MOVS    R6, R0
    // MOVS    R0, #0
    // R0 = 0;
    // MVNS    R0, R0
    // LDR     R1, =0x200077F0
    // 内存加载操作
    // STR     R0, [R1]
    // 内存存储操作
    // LDR     R0, [SP,#0x18+var_18]
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_4DEEC
    // 条件跳转
    // LDR     R0, [R5]
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_4DEEC
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200078D4
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR     R1, =0x2000787E
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x80
    // R0 = 0x80;
    // LDR     R1, =0x20007880
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0x55 ; 'U'
    // R0 = 0x55;
    // LDR     R1, =0x2000787C
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_48368
    // 调用函数: sub_48368();
    // MOVS    R0, #0
    // R0 = 0;
    // BL      sub_48368
    // 调用函数: sub_48368();
    // B       loc_4DFC2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E058
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_4e058(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000787E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x8001804;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // LDR     R0, =0x2000787E
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // UXTB    R0, R0
    // 数据扩展操作
    // CMP     R0, #0x80
    // 比较操作
    // BNE     loc_4E0B8
    // 条件跳转
    // LDR     R0, =0x8001800
    // 内存加载操作
    // MOVS    R4, R0
    // LDR     R0, =0x8001804
    // 内存加载操作
    // MOVS    R5, R0
    // LDR     R0, [R4]
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // CMP     R0, R1
    // 比较操作
    // BNE     loc_4E080
    // 条件跳转
    // LDR     R0, [R5]
    // 内存加载操作
    // MOVS    R1, #0
    // R1 = 0;
    // MVNS    R1, R1
    // CMP     R0, R1
    // 比较操作
    // BEQ     loc_4E0AE
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E170
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4e170(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078D4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x200078D4
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4E17E
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // B       locret_4E180
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E188
 * @note 指令数: 4, 标签数: 0
 */
uint32_t precise_func_4e188(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078D1;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x200078D1
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E1BC
 * @note 指令数: 19, 标签数: 0
 */
void precise_func_4e1bc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x803F804;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8001804;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x803F800;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,R5,LR}
    // 栈操作
    // LDR     R4, =0x8001800
    // 内存加载操作
    // MOVS    R0, R4
    // LDR     R4, =0x8001804
    // 内存加载操作
    // MOVS    R1, R4
    // LDR     R4, =0x803F800
    // 内存加载操作
    // MOVS    R2, R4
    // LDR     R4, =0x803F804
    // 内存加载操作
    // MOVS    R3, R4
    // LDR     R4, [R0]
    // 内存加载操作
    // MOVS    R5, #0
    // R5 = 0;
    // MVNS    R5, R5
    // CMP     R4, R5
    // 比较操作
    // BNE     loc_4E1E2
    // 条件跳转
    // LDR     R4, [R1]
    // 内存加载操作
    // MOVS    R5, #0
    // R5 = 0;
    // MVNS    R5, R5
    // CMP     R4, R5
    // 比较操作
    // BEQ     loc_4E1F6
    // 条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E206
 * @note 指令数: 3, 标签数: 0
 */
uint32_t precise_func_4e206(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078B9;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // LDR     R0, =0x200078B9
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E20C
 * @note 指令数: 12, 标签数: 0
 */
void precise_func_4e20c(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_5075C
    // 调用函数: sub_5075C();
    // BL      sub_4D1E0
    // 调用函数: sub_4D1E0();
    // BL      sub_44038
    // 调用函数: sub_44038();
    // BL      sub_4C7F6
    // 调用函数: sub_4C7F6();
    // BL      sub_49FFE
    // 调用函数: sub_49FFE();
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_4DE94
    // 调用函数: sub_4DE94();
    // BL      sub_510DA
    // 调用函数: sub_510DA();
    // BL      sub_5156E
    // 调用函数: sub_5156E();
    // BL      sub_4E93C
    // 调用函数: sub_4E93C();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E236
 * @note 指令数: 24, 标签数: 0
 */
void precise_func_4e236(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_4E354
    // 调用函数: sub_4E354();
    // BL      sub_4E39E
    // 调用函数: sub_4E39E();
    // BL      sub_4E1BC
    // 调用函数: sub_4E1BC();
    // BL      sub_4848E
    // 调用函数: sub_4848E();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_47EC4
    // 调用函数: sub_47EC4();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // CPSIE   I
    // BL      sub_4B0AC
    // 调用函数: sub_4B0AC();
    // BL      sub_4C08A
    // 调用函数: sub_4C08A();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_51E58
    // 调用函数: sub_51E58();
    // CPSID   I
    // BL      sub_455AC
    // 调用函数: sub_455AC();
    // BL      sub_48148
    // 调用函数: sub_48148();
    // BL      sub_526D4
    // 调用函数: sub_526D4();
    // BL      sub_4F610
    // 调用函数: sub_4F610();
    // BL      sub_4E20C
    // 调用函数: sub_4E20C();
    // BL      sub_4B3B4
    // 调用函数: sub_4B3B4();
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4E28A
    // 条件跳转
    // MOVS    R0, #1
    // R0 = 1;
    // BL      sub_4B9BA
    // 调用函数: sub_4B9BA();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E298
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_4e298(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_45652
    // 调用函数: sub_45652();
    // BL      sub_526EC
    // 调用函数: sub_526EC();
    // BL      sub_50D08
    // 调用函数: sub_50D08();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_4817A
    // 调用函数: sub_4817A();
    // BL      sub_44EE4
    // 调用函数: sub_44EE4();
    // BL      sub_4F6A4
    // 调用函数: sub_4F6A4();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E2BC
 * @note 指令数: 5, 标签数: 0
 */
void precise_func_4e2bc(uint32_t param0)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_4D6F4
    // 调用函数: sub_4D6F4();
    // BL      sub_4B1A4
    // 调用函数: sub_4B1A4();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E2CC
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_4e2cc(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_46B74
    // 调用函数: sub_46B74();
    // BL      sub_521D0
    // 调用函数: sub_521D0();
    // BL      sub_4CF18
    // 调用函数: sub_4CF18();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_51210
    // 调用函数: sub_51210();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_51654
    // 调用函数: sub_51654();
    // BL      sub_527C8
    // 调用函数: sub_527C8();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // BL      sub_4E968
    // 调用函数: sub_4E968();
    // BL      sub_48456
    // 调用函数: sub_48456();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E304
 * @note 指令数: 21, 标签数: 1
 */
void precise_func_4e304(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_4E236
    // 调用函数: sub_4E236();
    // BL      sub_4E298
    // 调用函数: sub_4E298();
    // BL      sub_4E058
    // 调用函数: sub_4E058();
    // BL      sub_45652
    // 调用函数: sub_45652();
    // BL      sub_4E2BC
    // 调用函数: sub_4E2BC();
    // BL      sub_4E058
    // 调用函数: sub_4E058();
    // BL      sub_45652
    // 调用函数: sub_45652();
    // BL      sub_4E298
    // 调用函数: sub_4E298();
    // BL      sub_4E058
    // 调用函数: sub_4E058();
    // BL      sub_45652
    // 调用函数: sub_45652();
    // BL      sub_4E2BC
    // 调用函数: sub_4E2BC();
    // BL      sub_4E058
    // 调用函数: sub_4E058();
    // BL      sub_45652
    // 调用函数: sub_45652();
    // BL      sub_4E298
    // 调用函数: sub_4E298();
    // BL      sub_4E058
    // 调用函数: sub_4E058();
    // BL      sub_45652
    // 调用函数: sub_45652();
    // BL      sub_4E2CC
    // 调用函数: sub_4E2CC();
    // BL      sub_4E058
    // 调用函数: sub_4E058();
    // BL      sub_45652
    // 调用函数: sub_45652();
    // B       loc_4E30A
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E354
 * @note 指令数: 10, 标签数: 0
 */
void precise_func_4e354(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x3C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_14= -0x14
    // var_10= -0x10
    // var_C= -0xC
    // var_8= -8
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E39E
 * @note 指令数: 15, 标签数: 1
 */
void precise_func_4e39e(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x30;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8002040;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
    // CMP     R4, #0x30 ; '0'
    // 比较操作
    // BCS     loc_4E3BE
    // LSLS    R0, R4, #2
    // LDR     R1, =0x8002040
    // 内存加载操作
    // LDR     R0, [R0,R1]
    // 内存加载操作
    // MOVS    R1, #0x20000000
    // R1 = 0x20000000;
    // MOVS    R2, #4
    // R2 = 4;
    // MULS    R2, R4
    // STR     R0, [R1,R2]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // B       loc_4E3A6
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E410
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4e410(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x48;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x60;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x44;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_68= -0x68
    // var_64= -0x64
    // var_60= -0x60
    // var_48= -0x48
    // var_44= -0x44
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E7C8
 * @note 指令数: 17, 标签数: 0
 */
void precise_func_4e7c8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x74;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x62;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x6C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x7A;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x68;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // CMP     R0, #0x62 ; 'b'
    // 比较操作
    // BEQ     loc_4E7F8
    // 条件跳转
    // CMP     R0, #0x68 ; 'h'
    // 比较操作
    // BEQ     loc_4E7EA
    // 条件跳转
    // CMP     R0, #0x6C ; 'l'
    // 比较操作
    // BEQ     loc_4E806
    // 条件跳转
    // CMP     R0, #0x74 ; 't'
    // 比较操作
    // BEQ     loc_4E806
    // 条件跳转
    // CMP     R0, #0x7A ; 'z'
    // 比较操作
    // BNE     loc_4E806
    // 条件跳转
    // LDR     R2, [R1]
    // 内存加载操作
    // STR     R2, [R1]
    // 内存存储操作
    // LDR     R0, [R2]
    // 内存加载操作
    // ADDS    R2, R2, #4
    // 算术运算
    // STR     R2, [R1]
    // 内存存储操作
    // MOVS    R1, #0
    // R1 = 0;
    // BX      LR
    // 函数返回
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E814
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_4e814(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x6F;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #8
    // 算术运算
    // MOVS    R4, R0
    // LDR     R0, [R4,#0xC]
    // 内存加载操作
    // STR     R0, [SP,#0x20+var_20]
    // 内存存储操作
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x20+var_18]
    // 内存加载操作
    // CMP     R0, #0x6F ; 'o'
    // 比较操作
    // BNE     loc_4E82A
    // 条件跳转
    // MOVS    R5, #8
    // R5 = 8;
    // B       loc_4E838
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E90C
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_4e90c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // MOVS    R6, R1
    // MOVS    R4, R2
    // MOVS    R7, #0
    // R7 = 0;
    // CMP     R3, #0
    // 比较操作
    // BEQ     loc_4E938
    // 条件跳转
    // MOVS    R5, R3
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E93C
 * @note 指令数: 14, 标签数: 0
 */
void precise_func_4e93c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1E;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007818;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000789B;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007618;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0x1E
    // R0 = 0x1E;
    // LDR     R1, =0x20007818
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000789B
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007618
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x20007618
    // 内存加载操作
    // BL      sub_45616
    // 调用函数: sub_45616();
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E95A
 * @note 指令数: 7, 标签数: 0
 */
uint32_t precise_func_4e95a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007618;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000789B;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编指令转换
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007618
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #1
    // R0 = 1;
    // LDR     R1, =0x2000789B
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // BX      LR
    // 函数返回

    return result;
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E968
 * @note 指令数: 15, 标签数: 0
 */
void precise_func_4e968(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007618;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000789B;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007818;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {LR}
    // 栈操作
    // LDR     R0, =0x2000789B
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4E98E
    // 条件跳转
    // LDR     R0, =0x20007818
    // 内存加载操作
    // LDRH    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x20007618
    // 内存加载操作
    // LDRH    R1, [R1]
    // 内存加载操作
    // CMP     R0, R1
    // 比较操作
    // BCS     loc_4E986
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x2000789B
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // B       locret_4E994
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E9A4
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4e9a4(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R3-R7,LR}
    // 栈操作
    // MOVS    R6, R0
    // MOVS    R7, R1
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R5, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4E9F8
 * @note 指令数: 6, 标签数: 0
 */
void precise_func_4e9f8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_10= -0x10
    // var_F= -0xF
    // var_A= -0xA
    // var_9= -9
    // var_8= -8
    // var_7= -7
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4EA3A
 * @note 指令数: 50, 标签数: 0
 */
void precise_func_4ea3a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200051FC;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x801109E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
    // PUSH    {R0,R1,R4-R7,LR}
    // 栈操作
    // SUB     SP, SP, #4
    // 算术运算
    // MOVS    R6, R2
    // MOVS    R5, R3
    // MOVS    R4, #0
    // R4 = 0;
    // LDR     R0, =0x801109E
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // LDR     R1, =0x200051FC
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // LDR     R0, =0x801109E
    // 内存加载操作
    // LDRB    R0, [R0,#1]
    // 内存加载操作
    // LDR     R1, =0x200051FC
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x20+var_1C]
    // 内存加载操作
    // LDR     R1, =0x200051FC
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // MOV     R0, SP
    // LDRB    R0, [R0,#0x20+var_18]
    // 内存加载操作
    // LDR     R1, =0x200051FC
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // MOVS    R0, R5
    // UXTH    R0, R0
    // 数据扩展操作
    // LSRS    R0, R0, #8
    // LDR     R1, =0x200051FC
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // MOVS    R0, R5
    // LDR     R1, =0x200051FC
    // 内存加载操作
    // STRB    R0, [R1,R4]
    // 内存存储操作
    // ADDS    R4, R4, #1
    // 算术运算
    // UXTH    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_4EA9A
    // 条件跳转
    // UXTH    R5, R5
    // 数据扩展操作
    // LDR     R0, =0x200051FC
    // 内存加载操作
    // ADDS    R7, R0, R4
    // 算术运算
    // MOVS    R2, R5
    // MOVS    R1, R6
    // MOVS    R0, R7
    // BL      sub_47F58
    // 调用函数: sub_47F58();
    // UXTH    R5, R5
    // 数据扩展操作
    // ADDS    R4, R4, R5
    // 算术运算
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4EACA
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4eaca(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x28;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
    // var_18= -0x18
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4EBE8
 * @note 指令数: 16, 标签数: 0
 */
void precise_func_4ebe8(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x4C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x34;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_68= -0x68
    // var_64= -0x64
    // var_60= -0x60
    // var_5C= -0x5C
    // var_50= -0x50
    // var_4C= -0x4C
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F2EC
 * @note 指令数: 9, 标签数: 0
 */
void precise_func_4f2ec(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x34;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x24;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_38= -0x38
    // var_34= -0x34
    // var_32= -0x32
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F45C
 * @note 指令数: 20, 标签数: 0
 */
void precise_func_4f45c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x5C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x64;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x4C;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_68= -0x68
    // var_64= -0x64
    // var_60= -0x60
    // var_5C= -0x5C
    // var_58= -0x58
    // var_54= -0x54
    // var_50= -0x50
    // var_4C= -0x4C
    // var_48= -0x48
    // var_44= -0x44
    // var_40= -0x40
    // var_3C= -0x3C
    // var_38= -0x38
    // var_34= -0x34
    // var_30= -0x30
    // var_2C= -0x2C
    // var_28= -0x28
    // var_24= -0x24
    // var_20= -0x20
    // var_1C= -0x1C
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F5CC
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_4f5cc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200076C8;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000325;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200076C8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // LDR     R0, =0x20000325
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4F5E2
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20000325
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F610
 * @note 指令数: 27, 标签数: 0
 */
void precise_func_4f610(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200078C1;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200076B8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007846;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007848;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200076C8;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // BL      sub_4E9F8
    // 调用函数: sub_4E9F8();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200078C1
    // 内存加载操作
    // STRB    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200076B8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007846
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x20007848
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200076C0
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200076B8
    // 内存加载操作
    // BL      sub_455DA
    // 调用函数: sub_455DA();
    // MOVS    R1, #1
    // R1 = 1;
    // LDR     R0, =0x200076C8
    // 内存加载操作
    // BL      sub_45616
    // 调用函数: sub_45616();
    // MOVS    R0, #0
    // R0 = 0;
    // LDR     R1, =0x200076C8
    // 内存加载操作
    // STRH    R0, [R1]
    // 内存存储操作
    // POP     {R0,PC}
    // 栈操作
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F6A4
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_4f6a4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000325;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xB4;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4-R6,LR}
    // 栈操作
    // LDR     R0, =0x20000325
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0
    // 比较操作
    // BEQ     loc_4F6B4
    // 条件跳转
    // MOVS    R0, #0xB4
    // R0 = 0xB4;
    // MOVS    R6, R0
    // B       loc_4F6B8
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F974
 * @note 指令数: 7, 标签数: 0
 */
void precise_func_4f974(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20000323;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R7,LR}
    // 栈操作
    // LDR     R0, =0x20000323
    // 内存加载操作
    // LDRB    R0, [R0]
    // 内存加载操作
    // CMP     R0, #0xFF
    // 比较操作
    // BNE     loc_4F984
    // 条件跳转
    // BL      sub_5288C
    // 调用函数: sub_5288C();
    // B       locret_4F988
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F990
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_4f990(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R5, R0
    // MOVS    R4, R1
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #4
    // 比较操作
    // BGE     locret_4F9A8
    // 条件跳转
    // MOVS    R1, R4
    // UXTH    R1, R1
    // 数据扩展操作
    // MOVS    R0, R5
    // UXTB    R0, R0
    // 数据扩展操作
    // BL      sub_528F6
    // 调用函数: sub_528F6();
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F9AA
 * @note 指令数: 8, 标签数: 0
 */
void precise_func_4f9aa(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // LSLS    R1, R0, #1
    // LSRS    R1, R1, #0x18
    // CMP     R1, #0xFF
    // 比较操作
    // BEQ     loc_4F9C6
    // 条件跳转
    // CMP     R1, #0
    // 比较操作
    // BEQ     loc_4F9BA
    // 条件跳转
    // MOVS    R0, #0
    // R0 = 0;
    // B       loc_4F9C2
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4F9D4
 * @note 指令数: 21, 标签数: 0
 */
void precise_func_4f9d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x80000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20000;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R4,LR}
    // 栈操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x20000
    // R1 = 0x20000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x80000
    // R1 = 0x80000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // LDR     R0, =0x40021014
    // 内存加载操作
    // LDR     R0, [R0]
    // 内存加载操作
    // MOVS    R1, #0x100000
    // R1 = 0x100000;
    // ORRS    R1, R0
    // LDR     R0, =0x40021014
    // 内存加载操作
    // STR     R1, [R0]
    // 内存存储操作
    // MOVS    R0, #0
    // R0 = 0;
    // MOVS    R4, R0
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FA24
 * @note 指令数: 11, 标签数: 0
 */
void precise_func_4fa24(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // PUSH    {R3-R5,LR}
    // 栈操作
    // MOVS    R4, R0
    // MOVS    R5, R1
    // UXTB    R4, R4
    // 数据扩展操作
    // CMP     R4, #4
    // 比较操作
    // BGE     locret_4FA5A
    // 条件跳转
    // UXTB    R5, R5
    // 数据扩展操作
    // CMP     R5, #0
    // 比较操作
    // BEQ     loc_4FA3A
    // 条件跳转
    // MOVS    R2, #1
    // R2 = 1;
    // B       loc_4FA3C
    // 无条件跳转
}

/**
 * @brief 精确转换的函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_4FA64
 * @note 指令数: 13, 标签数: 0
 */
void precise_func_4fa64(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007368;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x18;

    // 局部变量
    uint32_t temp = 0;

    // 汇编指令转换
    // var_18= -0x18
    // PUSH    {R0,R4-R7,LR}
    // 栈操作
    // PUSH    {R2,R3}
    // 栈操作
    // MOVS    R7, R1
    // MOVS    R5, #1
    // R5 = 1;
    // MOVS    R4, #0
    // R4 = 0;
    // MOVS    R6, #0
    // R6 = 0;
    // LDR     R0, =0x20007368
    // 内存加载操作
    // LDRB    R0, [R0,#0x18]
    // 内存加载操作
    // CMP     R0, #1
    // 比较操作
    // BNE     loc_4FA7C
    // 条件跳转
    // MOVS    R0, #2
    // R0 = 2;
    // B       locret_4FAFC
    // 无条件跳转
}

