// 完整精确转换批次 42 - 100%对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77508
 * @note 指令数: 70, 标签数: 9
 * @note 内存引用: 12, 函数调用: 3
 */
void precise_func_77508(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x803F000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000374A;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x803F010;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20000130;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_772B0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_772B0();
    sub_772B0();
    sub_772B0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_775A0
 * @note 指令数: 57, 标签数: 0
 * @note 内存引用: 15, 函数调用: 9
 */
void precise_func_775a0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x7D0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003746;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003748;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003624;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x2000360C;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003744;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x801226C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_79B10(void);
    extern void sub_77430(void);
    extern void sub_789CE(void);
    extern void sub_78944(void);
    extern void sub_78A0A(void);
    extern void sub_79DC0(void);
    extern void sub_77508(void);

    // 汇编逻辑实现

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_79B10();
    sub_79DC0();
    sub_78A0A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7762C
 * @note 指令数: 20, 标签数: 2
 * @note 内存引用: 3, 函数调用: 2
 */
void precise_func_7762c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x8011700;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xB8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20003743;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_76D64(void);
    extern void sub_76B68(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76B68();
    sub_76D64();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77688
 * @note 指令数: 157, 标签数: 24
 * @note 内存引用: 11, 函数调用: 22
 */
void precise_func_77688(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003604;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x7D0;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003624;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2000360C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20003744;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003749;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xB8;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7737C(void);
    extern void sub_772E8(void);
    extern void sub_76FA8(void);
    extern void sub_76566(void);
    extern void sub_788F0(void);
    extern void sub_77178(void);
    extern void sub_79DC0(void);
    extern void sub_7762C(void);
    extern void sub_78C4A(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_78C4A();
    sub_7737C();
    sub_77178();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77834
 * @note 指令数: 8, 标签数: 2
 * @note 内存引用: 0, 函数调用: 0
 */
uint32_t precise_func_77834(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77844
 * @note 指令数: 7, 标签数: 0
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_77844(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE000E180;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1B;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_77852
 * @note 指令数: 27, 标签数: 0
 * @note 内存引用: 8, 函数调用: 7
 */
void precise_func_77852(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20001F00;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x801249C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x12;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x1B;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_77844(void);
    extern void sub_762E4(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_77844();
    sub_77844();
    sub_77844();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_778A4
 * @note 指令数: 981, 标签数: 51
 * @note 内存引用: 111, 函数调用: 68
 */
void precise_func_778a4(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x12C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x54;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x38;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x2C;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x803B000;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x7C;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x200036F0;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_77362(void);
    extern void sub_7832C(void);
    extern void sub_7B812(void);
    extern void sub_77852(void);
    extern void sub_7CF74(void);
    extern void sub_7D00C(void);
    extern void sub_77350(void);
    extern void sub_76820(void);
    extern void sub_762E4(void);
    extern void sub_7CFC0(void);
    extern void sub_7B440(void);
    extern void sub_77178(void);
    extern void sub_7B6D4(void);
    extern void sub_780E4(void);
    extern void sub_7CB84(void);
    extern void sub_79DFC(void);
    extern void sub_79DC0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_762E4();
    sub_762E4();
    sub_7CB84();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_780E4
 * @note 指令数: 73, 标签数: 9
 * @note 内存引用: 13, 函数调用: 4
 */
void precise_func_780e4(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003614;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000374A;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x803F010;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20000130;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x803F004;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7D00C(void);
    extern void sub_7CFC0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7D00C();
    sub_7CFC0();
    sub_7CFC0();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78198
 * @note 指令数: 29, 标签数: 2
 * @note 内存引用: 7, 函数调用: 1
 */
void precise_func_78198(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200035FC;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200035F4;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003741;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x200035EC;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003742;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7CF1A(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7CF1A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_781E4
 * @note 指令数: 49, 标签数: 5
 * @note 内存引用: 3, 函数调用: 1
 */
void precise_func_781e4(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20001FF8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x84;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_762E4(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_762E4();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7824C
 * @note 指令数: 30, 标签数: 3
 * @note 内存引用: 4, 函数调用: 1
 */
void precise_func_7824c(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x12;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x72;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003739;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_77416(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_77416();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78298
 * @note 指令数: 46, 标签数: 4
 * @note 内存引用: 4, 函数调用: 1
 */
void precise_func_78298(uint32_t param0, uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20001FF8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x84;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_762E4(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_762E4();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7832C
 * @note 指令数: 533, 标签数: 66
 * @note 内存引用: 51, 函数调用: 15
 */
void precise_func_7832c(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0xA8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20003660;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xB;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20003735;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20003737;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x200031B2;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7B378(void);
    extern void sub_78298(void);
    extern void sub_76820(void);
    extern void sub_762E4(void);
    extern void sub_76870(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_76870();
    sub_762E4();
    sub_762E4();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7877C
 * @note 指令数: 23, 标签数: 0
 * @note 内存引用: 5, 函数调用: 3
 */
void precise_func_7877c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200035FC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200035F4;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x3E8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200035EC;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20003742;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_789CE(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_789CE();
    sub_789CE();
    sub_789CE();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_787E8
 * @note 指令数: 127, 标签数: 17
 * @note 内存引用: 11, 函数调用: 5
 */
void precise_func_787e8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1A;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x200035FC;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200035F4;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x23;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x19;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xB8;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7CF92(void);
    extern void sub_7832C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7CF92();
    sub_7832C();
    sub_7832C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_788F0
 * @note 指令数: 3, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_788f0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003741;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78930
 * @note 指令数: 10, 标签数: 0
 * @note 内存引用: 0, 函数调用: 0
 */
uint32_t precise_func_78930(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_78944
 * @note 指令数: 30, 标签数: 1
 * @note 内存引用: 6, 函数调用: 1
 */
void precise_func_78944(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x1C;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x18;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xC;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x7895C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7D07C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7D07C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7897C
 * @note 指令数: 17, 标签数: 4
 * @note 内存引用: 0, 函数调用: 0
 */
uint32_t precise_func_7897c(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_7899E
 * @note 指令数: 1, 标签数: 0
 * @note 内存引用: 0, 函数调用: 0
 */
void precise_func_7899e(void)
{
    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_789A0
 * @note 指令数: 15, 标签数: 0
 * @note 内存引用: 3, 函数调用: 2
 */
void precise_func_789a0(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x200036EC;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200035B4;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x2000368C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7E000(void);
    extern void sub_789CE(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7E000();
    sub_789CE();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_789C2
 * @note 指令数: 3, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_789c2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003680;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_789C8
 * @note 指令数: 3, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_789c8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003688;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_789CE
 * @note 指令数: 28, 标签数: 5
 * @note 内存引用: 2, 函数调用: 2
 */
void precise_func_789ce(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20003674;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20003670;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_7644C(void);
    extern void sub_78930(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_7644C();
    sub_78930();
}

