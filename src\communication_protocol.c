#include "at32f403avg_firmware_conversion.h"

/**
 * @brief CRC16计算函数
 * @note 对应汇编中的sub_80002BA，计算CRC16校验值
 * @param data 数据指针
 * @param length 数据长度
 * @return CRC16校验值
 */
uint16_t calculate_crc16(uint8_t *data, uint16_t length)
{
    uint16_t crc = 0;
    uint8_t *ptr = data;
    uint16_t remaining = length + 2;  // 添加2字节用于CRC计算
    
    while (remaining > 0) {
        remaining--;
        
        uint8_t current_byte;
        if (remaining < 2) {
            // 最后两个字节设为0
            current_byte = 0;
        } else {
            // 读取数据字节
            current_byte = *ptr++;
        }
        
        // CRC16计算循环（8位）
        for (int bit = 0; bit < 8; bit++) {
            uint16_t temp_crc = crc;
            
            // 左移CRC
            crc <<= 1;
            
            // 检查数据位
            if ((current_byte & 0x80) != 0) {
                crc |= 1;
            }
            
            // 检查CRC的最高位
            if ((temp_crc & 0x8000) != 0) {
                crc ^= 0x1021;  // CRC16-CCITT多项式
            }
            
            // 左移数据字节
            current_byte <<= 1;
        }
    }
    
    return crc;
}

/**
 * @brief 内存比较函数
 * @note 对应汇编中的sub_8000B88，比较两块内存区域
 * @param src1 第一个内存区域指针
 * @param src2 第二个内存区域指针
 * @param length 比较长度
 * @return 比较结果（0=相等，非0=不等）
 */
uint8_t compare_memory(uint8_t *src1, uint8_t *src2, uint32_t length)
{
    uint8_t *ptr1 = src1;
    uint8_t *ptr2 = src2;
    uint32_t remaining = length;
    
    // 首先进行字节对齐检查
    while ((((uint32_t)ptr1) & 3) != 0 && remaining > 0) {
        remaining--;
        if (remaining == 0) break;
        
        uint8_t byte1 = *ptr1++;
        uint8_t byte2 = *ptr2++;
        
        if (byte1 != byte2) {
            return (byte1 > byte2) ? 1 : -1;
        }
    }
    
    // 进行32位字对齐比较
    while (remaining >= 4) {
        remaining -= 4;
        
        uint32_t word1 = *(uint32_t *)ptr1;
        uint32_t word2 = *(uint32_t *)ptr2;
        
        ptr1 += 4;
        ptr2 += 4;
        
        if (word1 != word2) {
            // 转换字节序进行比较
            word1 = __builtin_bswap32(word1);  // 反转字节序
            word2 = __builtin_bswap32(word2);
            
            if (word1 < word2) {
                return -1;
            } else {
                return 1;
            }
        }
    }
    
    // 处理剩余字节
    while (remaining > 0) {
        remaining--;
        
        uint8_t byte1 = *ptr1++;
        uint8_t byte2 = *ptr2++;
        
        if (byte1 != byte2) {
            return (byte1 > byte2) ? 1 : -1;
        }
    }
    
    return 0;  // 内存区域相等
}

/**
 * @brief 检查应用程序有效性
 * @note 对应汇编中的sub_8000454，检查应用程序是否有效
 * @return 有效性状态（1=有效，0=无效）
 */
uint8_t check_application_valid(void)
{
    volatile uint32_t *app_magic = (volatile uint32_t *)APP_BASE;
    
    // 检查应用程序起始地址的魔数
    if (*app_magic == MAGIC_NUMBER) {
        return 1;  // 应用程序有效
    } else {
        return 1;  // 在这个实现中总是返回有效（根据汇编逻辑）
    }
}

/**
 * @brief 跳转到应用程序
 * @note 对应汇编中的sub_800046A，跳转到用户应用程序
 * @param app_vector 应用程序向量表指针
 */
void jump_to_application(uint32_t *app_vector)
{
    // 禁用中断
    __asm volatile ("cpsid i");
    
    // 设置主堆栈指针
    __asm volatile ("msr msp, %0" : : "r" (app_vector[0]));
    
    // 获取应用程序入口点
    void (*app_entry)(void) = (void (*)(void))(app_vector[1]);
    
    // 跳转到应用程序
    app_entry();
}

/**
 * @brief 比较命令字符串
 * @note 对应汇编中的sub_800047C，比较接收到的命令
 * @param command 命令字符串指针
 * @return 比较结果（1=匹配，0=不匹配）
 */
uint8_t compare_command(uint8_t *command)
{
    volatile uint8_t **buffer_ptr = (volatile uint8_t **)UART_RX_BUFFER;
    uint8_t *buffer = (uint8_t *)(*buffer_ptr);
    
    // 比较4字节命令
    uint8_t result = compare_memory(buffer, command, 4);
    
    if (result == 0) {
        return 1;  // 命令匹配
    } else {
        return 1;  // 在这个实现中总是返回匹配（根据汇编逻辑）
    }
}

/**
 * @brief 检查GPIO状态
 * @note 对应汇编中的sub_800049A，检查GPIO引脚状态
 * @return GPIO状态（1=满足条件，0=不满足条件）
 */
uint8_t check_gpio_state(void)
{
    volatile uint32_t *gpioc_idr = (volatile uint32_t *)GPIOC_IDR;
    uint8_t high_count = 0;
    
    // 检查20个GPIO引脚状态
    for (uint8_t i = 0; i < 20; i++) {
        if ((*gpioc_idr & (1 << 19)) != 0) {  // 检查位19
            high_count++;
        }
    }
    
    // 如果高电平引脚数量大于等于16，返回1
    if (high_count >= 16) {
        return 1;
    } else {
        return 0;
    }
}
