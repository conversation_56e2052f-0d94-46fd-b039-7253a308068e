=== BASIOT Logo 手动设计 ===

BASIOT 文字logo设计 (64×32像素)

字母设计 (每个字母8×8像素):

B:  ████████    A:    ███      S:   █████     I:  ███████   O:   █████     T:  ███████
    █      █         █   █          █              █           █     █          █
    █      █         █   █          █              █           █     █          █
    ███████          █████          █████          █           █     █          █
    █      █         █   █              █          █           █     █          █
    █      █         █   █              █          █           █     █          █
    ████████         █   █          █████      ███████        █████            █

BASIOT 完整布局 (64×32像素，居中显示):

行 0-11: 空白区域
行 12-19: BASIOT文字区域
行 20-31: 空白区域

水平布局:
- 总宽度: 6字母 × 8像素 + 5间距 × 2像素 = 58像素
- 起始位置: (64-58)/2 = 3像素
- B: 列 3-10
- A: 列 13-20  
- S: 列 23-30
- I: 列 33-40
- O: 列 43-50
- T: 列 53-60

=== C语言数组数据 ===

基于上述设计，生成的64×32位图的32位字数组:

// BASIOT Logo数据 (手动设计)
static const uint32_t basiot_logo_data[64] = {
    // 行 0-7: 空白
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    
    // 行 8-15: 空白
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    
    // 行 16-23: 空白
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    
    // 行 24-31: BASIOT文字区域开始
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    
    // 行 32-39: BASIOT第1行
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    
    // 行 40-47: BASIOT第2行  
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    
    // 行 48-55: BASIOT第3行
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    
    // 行 56-63: 空白结尾
    0x00000000, 0x00000000, 0x00000000, 0x00000000,
    0x00000000, 0x00000000, 0x00000000, 0x00000000
};

注意: 上述数据是模板，需要根据实际的BASIOT字母位图计算正确的十六进制值。

=== 简化的BASIOT数据 ===

为了快速实现，这里提供一个简化版本的BASIOT logo数据:
