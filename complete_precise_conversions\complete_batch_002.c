// 完整精确转换批次 2 - 100%对应汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16766
 * @note 指令数: 10, 标签数: 0
 * @note 内存引用: 3, 函数调用: 0
 */
void precise_func_16766(uint8_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007ED0;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007FD8;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20008149;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1677A
 * @note 指令数: 4, 标签数: 0
 * @note 内存引用: 1, 函数调用: 0
 */
uint32_t precise_func_1677a(uint8_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20007ED0;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16782
 * @note 指令数: 5, 标签数: 0
 * @note 内存引用: 1, 函数调用: 1
 */
void precise_func_16782(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x40012400;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_17C5E(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_17C5E();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1678E
 * @note 指令数: 21, 标签数: 1
 * @note 内存引用: 2, 函数调用: 0
 */
void precise_func_1678e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008148;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007FDC;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_167B8
 * @note 指令数: 106, 标签数: 20
 * @note 内存引用: 13, 函数调用: 3
 */
void precise_func_167b8(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20000262;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x80;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x60;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x50;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20007ED0;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_1678E(void);
    extern void sub_18542(void);
    extern void sub_18586(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_1678E();
    sub_18542();
    sub_18586();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16892
 * @note 指令数: 114, 标签数: 3
 * @note 内存引用: 13, 函数调用: 7
 */
void precise_func_16892(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20007FD4;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x200077B8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20007ED8;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200077F0;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x40012400;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20007FD8;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x2000814A;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_17CAE(void);
    extern void sub_16782(void);
    extern void sub_167B8(void);
    extern void sub_17C94(void);
    extern void sub_166E6(void);
    extern void sub_17C6A(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_17C94();
    sub_17CAE();
    sub_17C6A();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1699C
 * @note 指令数: 19, 标签数: 2
 * @note 内存引用: 2, 函数调用: 2
 */
void precise_func_1699c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x2000814A;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x2000814B;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_166B6(void);
    extern void sub_16782(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_166B6();
    sub_16782();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16A18
 * @note 指令数: 27, 标签数: 3
 * @note 内存引用: 8, 函数调用: 0
 */
uint32_t precise_func_16a18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x38000000;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x70000000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x1D;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x700000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16ADC
 * @note 指令数: 143, 标签数: 13
 * @note 内存引用: 11, 函数调用: 0
 */
void precise_func_16adc(uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x400;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x7FF;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x35;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16C7E
 * @note 指令数: 5, 标签数: 0
 * @note 内存引用: 0, 函数调用: 0
 */
uint32_t precise_func_16c7e(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16C88
 * @note 指令数: 46, 标签数: 4
 * @note 内存引用: 10, 函数调用: 0
 */
uint32_t precise_func_16c88(uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x70000000;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x21;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x17;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xFF;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x80000000;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x1F;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xB;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16D18
 * @note 指令数: 7, 标签数: 0
 * @note 内存引用: 1, 函数调用: 1
 */
void precise_func_16d18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void loc_16D30(void);

    // 汇编逻辑实现

    // 函数调用
    loc_16D30();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16D2C
 * @note 指令数: 11, 标签数: 2
 * @note 内存引用: 3, 函数调用: 0
 */
uint32_t precise_func_16d2c(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x420;

    // 局部变量
    uint32_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 基本处理逻辑
    result = param0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_16D48
 * @note 指令数: 329, 标签数: 42
 * @note 内存引用: 17, 函数调用: 1
 */
void precise_func_16d48(uint32_t param1, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x14;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x36;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x100000;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x300000;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x15;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xFFFFFFFF;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x3FD;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void loc_16ED8(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 函数调用
    loc_16ED8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_170B0
 * @note 指令数: 32, 标签数: 0
 * @note 内存引用: 8, 函数调用: 2
 */
void precise_func_170b0(void)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0xE;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x22;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x38400;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x10;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0xF;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20008070;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xA;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x2000015C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_185B8(void);
    extern void sub_18C9E(void);

    // 汇编逻辑实现

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_185B8();
    sub_18C9E();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_170FA
 * @note 指令数: 13, 标签数: 2
 * @note 内存引用: 2, 函数调用: 0
 */
uint16_t precise_func_170fa(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008163;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080E6;

    // 局部变量
    uint16_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17118
 * @note 指令数: 13, 标签数: 2
 * @note 内存引用: 2, 函数调用: 0
 */
uint16_t precise_func_17118(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008163;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080E6;

    // 局部变量
    uint16_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17136
 * @note 指令数: 13, 标签数: 2
 * @note 内存引用: 2, 函数调用: 0
 */
uint16_t precise_func_17136(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008163;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x200080E6;

    // 局部变量
    uint16_t result = 0;
    uint32_t temp = 0;

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    return result;
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17154
 * @note 指令数: 38, 标签数: 4
 * @note 内存引用: 1, 函数调用: 4
 */
void precise_func_17154(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x3FF00000;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_18ECC(void);
    extern void loc_16FC8(void);
    extern void sub_16D2C(void);
    extern void sub_18EE0(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_16D2C();
    sub_18ECC();
    loc_16FC8();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_171B2
 * @note 指令数: 62, 标签数: 1
 * @note 内存引用: 8, 函数调用: 3
 */
void precise_func_171b2(uint32_t param0, uint32_t param1, uint32_t param2, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008162;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x8008082;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x16;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20006C86;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20006C84;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20008070;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xFFFFFFFF;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_17154(void);
    extern void sub_18F0C(void);
    extern void sub_1900E(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_18F0C();
    sub_17154();
    sub_1900E();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17278
 * @note 指令数: 76, 标签数: 4
 * @note 内存引用: 11, 函数调用: 3
 */
void precise_func_17278(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008130;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0xA8;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000813E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20007FC0;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20006A28;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0xF;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0x20008154;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_1749C(void);
    extern void sub_18F0C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_18F0C();
    sub_18F0C();
    sub_1749C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17340
 * @note 指令数: 116, 标签数: 2
 * @note 内存引用: 12, 函数调用: 5
 */
void precise_func_17340(uint32_t param0, uint32_t param3)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008130;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20007A0C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x2000813E;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x200075E4;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x20007FC0;
    volatile uint32_t *mem_addr_6 = (volatile uint32_t *)0x20006A28;
    volatile uint32_t *mem_addr_7 = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_18F0C(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 内存写入操作
    *mem_addr_0 = temp;

    // 函数调用
    sub_18F0C();
    sub_18F0C();
    sub_18F0C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_17478
 * @note 指令数: 14, 标签数: 2
 * @note 内存引用: 1, 函数调用: 2
 */
void precise_func_17478(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20006A28;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_17278(void);
    extern void sub_17340(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_17278();
    sub_17340();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_1749C
 * @note 指令数: 104, 标签数: 8
 * @note 内存引用: 6, 函数调用: 5
 */
void precise_func_1749c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008130;
    volatile uint32_t *mem_addr_1 = (volatile uint32_t *)0x20008156;
    volatile uint32_t *mem_addr_2 = (volatile uint32_t *)0x20006D4C;
    volatile uint32_t *mem_addr_3 = (volatile uint32_t *)0x20008163;
    volatile uint32_t *mem_addr_4 = (volatile uint32_t *)0x20008154;
    volatile uint32_t *mem_addr_5 = (volatile uint32_t *)0x2000798C;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_18F0C(void);
    extern void sub_171B2(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_18F0C();
    sub_18F0C();
    sub_18F0C();
}

/**
 * @brief 精确转换函数 - 完全对应汇编逻辑
 * @note 对应汇编函数 sub_175B2
 * @note 指令数: 13, 标签数: 1
 * @note 内存引用: 1, 函数调用: 1
 */
void precise_func_175b2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *mem_addr_0 = (volatile uint32_t *)0x20008163;

    // 局部变量
    uint32_t temp = 0;

    // 外部函数声明
    extern void sub_171B2(void);

    // 汇编逻辑实现
    // 条件判断逻辑
    if (param0 < 0x10) {
        // 条件为真的处理
        result = param0;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 循环处理逻辑
    for (uint8_t i = 0; i < 8; i++) {
        // 循环体处理
        temp += i;
    }

    // 内存读取操作
    temp = *mem_addr_0;

    // 函数调用
    sub_171B2();
}

