#!/usr/bin/env python3
"""
新Logo SVG转换工具
将新的SVG logo转换为AT32F403AVG可用的位图数据

作者: AT32F403AVG汇编转换项目
日期: 2024
"""

import xml.etree.ElementTree as ET
import re
import numpy as np
from PIL import Image, ImageDraw
import struct

# 新SVG logo的原始数据
NEW_SVG_DATA = '''<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 20010904//EN"
 "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<svg version="1.0" xmlns="http://www.w3.org/2000/svg"
 width="234.000000pt" height="78.000000pt" viewBox="0 0 234.000000 78.000000"
 preserveAspectRatio="xMidYMid meet">

<g transform="translate(0.000000,78.000000) scale(0.100000,-0.100000)"
fill="#000000" stroke="none">
<path d="M793 759 c-73 -36 -143 -123 -222 -276 -80 -156 -125 -216 -176 -235
-42 -15 -41 -27 6 -44 76 -28 153 -13 217 40 28 24 32 26 32 9 0 -29 29 -63
54 -63 13 0 40 11 61 26 l37 25 -6 -25 c-5 -20 -3 -26 11 -27 29 -2 47 2 58
12 7 7 4 8 -7 4 -46 -18 -27 61 34 138 17 21 28 40 25 43 -2 3 -28 -24 -56
-60 -89 -111 -137 -140 -161 -95 -16 29 -4 71 35 122 52 68 129 94 199 67 28
-10 34 -2 14 18 -31 31 -178 -4 -217 -51 -16 -20 -33 -22 -29 -4 1 6 -1 24 -5
40 -6 22 -13 27 -40 27 -47 0 -66 -16 -28 -23 57 -12 49 -83 -18 -159 -52 -60
-131 -85 -182 -57 -19 10 -18 13 23 57 24 26 85 115 136 199 99 162 172 252
223 273 26 11 36 11 65 -3 36 -18 45 -10 17 15 -31 27 -57 29 -100 7z"/>
<path d="M1843 576 c-21 -19 -51 -59 -68 -90 l-30 -57 -55 8 c-144 20 -203 -2
-315 -116 -77 -79 -126 -117 -138 -105 -8 9 2 31 50 110 24 39 43 81 43 93 0
31 -44 28 -121 -9 -37 -17 -72 -27 -83 -24 -12 4 -16 2 -12 -5 12 -19 59 -12
101 14 41 25 85 33 85 15 0 -6 -18 -34 -41 -62 -52 -66 -82 -125 -76 -149 5
-17 -1 -19 -53 -19 l-58 0 21 39 21 39 -33 62 c-18 35 -30 71 -28 82 8 28 -12
21 -23 -8 -8 -21 -5 -37 15 -81 14 -31 25 -66 25 -79 0 -48 -89 -74 -123 -36
-20 22 -22 53 -5 70 18 18 38 14 38 -8 0 -20 24 -28 34 -11 8 13 -3 38 -20 45
-21 8 -60 -13 -74 -40 -11 -19 -10 -27 4 -49 16 -24 16 -25 -6 -25 -97 -2
-406 -34 -508 -53 -57 -11 -27 -13 255 -12 439 0 652 -16 928 -71 119 -24 103
-10 -23 21 -247 59 -356 69 -715 70 -181 0 -298 3 -260 7 279 26 802 7 1140
-41 97 -15 123 -12 47 5 -103 22 -332 53 -451 60 -61 4 -111 11 -111 16 0 4 8
8 18 8 10 0 37 16 59 35 48 41 53 42 53 14 0 -54 43 -77 110 -59 66 18 123
120 105 187 -7 25 -9 21 -17 -32 -13 -97 -81 -162 -145 -139 -75 26 -9 165
102 215 48 22 145 26 145 5 0 -7 11 -29 24 -49 l25 -36 -30 -41 c-16 -22 -50
-54 -75 -70 -42 -26 -44 -29 -25 -39 38 -21 107 -13 141 16 31 26 32 28 26 84
-5 43 -3 65 10 93 16 32 22 36 55 36 41 0 77 26 61 42 -13 13 -32 2 -32 -18 0
-9 -11 -14 -30 -14 -37 0 -37 9 -4 65 33 57 88 105 120 105 14 0 22 4 19 10
-13 20 -56 9 -92 -24z m-124 -165 c12 -8 12 -13 -3 -36 l-18 -27 -19 28 c-10
16 -19 32 -19 37 0 10 42 9 59 -2z m11 -152 c0 -46 -80 -93 -127 -74 -16 6
-16 8 5 16 12 5 41 29 64 53 42 44 57 45 58 5z"/>
<path d="M1320 499 c0 -14 5 -19 17 -17 26 5 29 38 4 38 -15 0 -21 -6 -21 -21z"/>
<path d="M1170 471 c0 -6 5 -13 10 -16 8 -5 7 -12 -1 -22 -9 -11 -26 -13 -63
-9 -36 4 -43 3 -26 -3 40 -14 77 -12 94 5 20 20 21 54 1 54 -8 0 -15 -4 -15
-9z"/>
<path d="M1858 83 c7 -3 16 -2 19 1 4 3 -2 6 -13 5 -11 0 -14 -3 -6 -6z"/>
</g>
</svg>'''

def parse_svg_paths():
    """解析SVG路径数据"""
    # 提取所有path元素的d属性
    paths = []
    
    # 使用正则表达式提取path数据
    path_pattern = r'<path d="([^"]+)"'
    matches = re.findall(path_pattern, NEW_SVG_DATA)
    
    print(f"找到 {len(matches)} 个路径:")
    for i, path in enumerate(matches):
        print(f"路径 {i+1}: {path[:100]}...")
        paths.append(path)
    
    return paths

def create_bitmap_from_svg(width=64, height=32):
    """
    创建位图数据
    由于SVG路径解析复杂，这里创建一个简化的位图
    """
    # 创建一个简化的logo位图
    bitmap = np.zeros((height, width), dtype=np.uint8)
    
    # 在位图中创建一些几何图案作为新logo的简化版本
    # 这是一个示例，实际应该根据SVG路径生成
    
    # 添加一些几何形状
    for y in range(height):
        for x in range(width):
            # 创建一些图案
            if (x > 5 and x < 25 and y > 8 and y < 24):
                # 左侧矩形
                bitmap[y, x] = 1
            elif (x > 30 and x < 50 and y > 5 and y < 27):
                # 中间区域
                if (x + y) % 3 == 0:
                    bitmap[y, x] = 1
            elif (x > 52 and x < 60 and y > 10 and y < 22):
                # 右侧小矩形
                bitmap[y, x] = 1
    
    return bitmap

def bitmap_to_c_array(bitmap):
    """将位图转换为C语言数组"""
    height, width = bitmap.shape
    
    # 计算需要的字节数
    total_bits = height * width
    total_bytes = (total_bits + 7) // 8
    
    # 转换为字节数组
    byte_array = []
    bit_index = 0
    
    for byte_idx in range(total_bytes):
        byte_val = 0
        for bit in range(8):
            if bit_index < total_bits:
                y = bit_index // width
                x = bit_index % width
                if y < height and x < width:
                    pixel = bitmap[y, x]
                    byte_val |= (pixel << (7 - bit))
                bit_index += 1
        byte_array.append(byte_val)
    
    return byte_array

def generate_c_code():
    """生成C语言代码"""
    bitmap = create_bitmap_from_svg(64, 32)
    byte_array = bitmap_to_c_array(bitmap)
    
    # 转换为32位字数组
    word_array = []
    for i in range(0, len(byte_array), 4):
        word = 0
        for j in range(4):
            if i + j < len(byte_array):
                word |= (byte_array[i + j] << (8 * j))
        word_array.append(word)
    
    # 生成C代码
    c_code = """/**
 * @brief 新Logo数据 - 替换原TRIDIUM logo
 * 
 * 原始SVG尺寸: 234×78 pt
 * 位图尺寸: 64×32像素
 * 数据大小: 256字节 (64个32位字)
 * 格式: 单色位图
 */
static const uint32_t new_logo_data[64] = {
"""
    
    for i in range(0, len(word_array), 8):
        c_code += "    "
        for j in range(8):
            if i + j < len(word_array):
                c_code += f"0x{word_array[i + j]:08X}"
                if i + j < len(word_array) - 1:
                    c_code += ", "
        c_code += "\n"
    
    c_code += "};\n"
    
    return c_code, bitmap

def create_ascii_preview(bitmap):
    """创建ASCII预览"""
    height, width = bitmap.shape
    
    print("\n=== 新Logo ASCII预览 ===")
    print("=" * (width + 4))
    
    for y in range(height):
        print("| ", end="")
        for x in range(width):
            print("#" if bitmap[y, x] else " ", end="")
        print(" |")
    
    print("=" * (width + 4))

def analyze_new_logo():
    """分析新logo"""
    print("=== 新Logo分析 ===")
    print("SVG信息:")
    print("- 原始尺寸: 234×78 pt")
    print("- 视图框: 0 0 234 78")
    print("- 颜色: 黑色 (#000000)")
    print("- 复杂度: 高 (多个复杂路径)")
    
    paths = parse_svg_paths()
    print(f"- 路径数量: {len(paths)}")
    
    print("\n目标位图:")
    print("- 尺寸: 64×32像素")
    print("- 格式: 单色位图")
    print("- 数据大小: 256字节")
    print("- 用途: 替换原TRIDIUM logo")

if __name__ == "__main__":
    print("新Logo SVG转换工具")
    print("=" * 50)
    
    # 分析新logo
    analyze_new_logo()
    
    # 生成位图和C代码
    c_code, bitmap = generate_c_code()
    
    # 显示ASCII预览
    create_ascii_preview(bitmap)
    
    # 保存C代码
    with open("new_logo_data.c", "w", encoding="utf-8") as f:
        f.write(c_code)
    
    print(f"\n✅ 新logo C代码已保存到: new_logo_data.c")
    print("✅ ASCII预览已生成")
    
    print("\n📋 下一步:")
    print("1. 将生成的C代码替换到 logo_data_handler.c")
    print("2. 更新相关注释和文档")
    print("3. 重新编译项目")
    print("4. 在硬件上测试新logo显示")
