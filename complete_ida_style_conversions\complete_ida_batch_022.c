// 完整IDA风格转换批次 22 - 专业级转换
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_78A0A
 * @note 指令数: 28
 * @note 类型: control_function
 */
void ida_78a0a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000367C = (volatile uint32_t *)0x2000367C;
    volatile uint32_t *addr_20003678 = (volatile uint32_t *)0x20003678;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_78A46
 * @note 指令数: 162
 * @note 类型: array_access
 */
void ida_78a46(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20000170 = (volatile uint32_t *)0x20000170;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_2000367C = (volatile uint32_t *)0x2000367C;
    volatile uint32_t *addr_3E8 = (volatile uint32_t *)0x3E8;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_78B94
 * @note 指令数: 39
 * @note 类型: array_access
 */
uint32_t ida_78b94(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003684 = (volatile uint32_t *)0x20003684;
    volatile uint32_t *addr_20003688 = (volatile uint32_t *)0x20003688;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;
    volatile uint32_t *addr_200036EC = (volatile uint32_t *)0x200036EC;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_78C1C
 * @note 指令数: 23
 * @note 类型: simple_function
 */
uint32_t ida_78c1c(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_8001804 = (volatile uint32_t *)0x8001804;
    volatile uint32_t *addr_8001800 = (volatile uint32_t *)0x8001800;
    volatile uint32_t *addr_2000371B = (volatile uint32_t *)0x2000371B;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_78C4A
 * @note 指令数: 3
 * @note 类型: simple_function
 */
uint8_t ida_78c4a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_2000371B = (volatile uint32_t *)0x2000371B;

    // 局部变量
    uint8_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_78C5C
 * @note 指令数: 750
 * @note 类型: array_access
 */
void ida_78c5c(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200036D4 = (volatile uint32_t *)0x200036D4;
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_79350
 * @note 指令数: 393
 * @note 类型: array_access
 */
void ida_79350(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_200036D4 = (volatile uint32_t *)0x200036D4;
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_200036DA = (volatile uint32_t *)0x200036DA;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_796F4
 * @note 指令数: 43
 * @note 类型: array_access
 */
uint32_t ida_796f4(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200036D4 = (volatile uint32_t *)0x200036D4;
    volatile uint32_t *addr_8001804 = (volatile uint32_t *)0x8001804;
    volatile uint32_t *addr_20003660 = (volatile uint32_t *)0x20003660;
    volatile uint32_t *addr_2000371D = (volatile uint32_t *)0x2000371D;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79754
 * @note 指令数: 141
 * @note 类型: control_function
 */
void ida_79754(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2000371D = (volatile uint32_t *)0x2000371D;
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;
    volatile uint32_t *addr_8001830 = (volatile uint32_t *)0x8001830;
    volatile uint32_t *addr_58 = (volatile uint32_t *)0x58;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_7988A
 * @note 指令数: 6
 * @note 类型: simple_function
 */
uint32_t ida_7988a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200036DC = (volatile uint32_t *)0x200036DC;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_798C8
 * @note 指令数: 144
 * @note 类型: lookup_table
 */
void ida_798c8(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8001804 = (volatile uint32_t *)0x8001804;
    volatile uint32_t *addr_20003660 = (volatile uint32_t *)0x20003660;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_8001810 = (volatile uint32_t *)0x8001810;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79A44
 * @note 指令数: 22
 * @note 类型: control_function
 */
void ida_79a44(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8011548 = (volatile uint32_t *)0x8011548;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79A72
 * @note 指令数: 30
 * @note 类型: control_function
 */
void ida_79a72(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_80000 = (volatile uint32_t *)0x80000;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_40000 = (volatile uint32_t *)0x40000;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79ABC
 * @note 指令数: 25
 * @note 类型: control_function
 */
uint32_t ida_79abc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8011CC8 = (volatile uint32_t *)0x8011CC8;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79AF4
 * @note 指令数: 12
 * @note 类型: control_function
 */
uint32_t ida_79af4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20000 = (volatile uint32_t *)0x20000;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_8011CCC = (volatile uint32_t *)0x8011CCC;
    volatile uint32_t *addr_8011CC8 = (volatile uint32_t *)0x8011CC8;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79B10
 * @note 指令数: 277
 * @note 类型: control_function
 */
void ida_79b10(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_D7 = (volatile uint32_t *)0xD7;
    volatile uint32_t *addr_9F = (volatile uint32_t *)0x9F;
    volatile uint32_t *addr_BF = (volatile uint32_t *)0xBF;
    volatile uint32_t *addr_5FF = (volatile uint32_t *)0x5FF;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79DC0
 * @note 指令数: 21
 * @note 类型: control_function
 */
uint32_t ida_79dc0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80113E0 = (volatile uint32_t *)0x80113E0;
    volatile uint32_t *addr_80113DC = (volatile uint32_t *)0x80113DC;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79DFC
 * @note 指令数: 13
 * @note 类型: control_function
 */
void ida_79dfc(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_AE = (volatile uint32_t *)0xAE;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_AF = (volatile uint32_t *)0xAF;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79E1C
 * @note 指令数: 34
 * @note 类型: control_function
 */
void ida_79e1c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_8011730 = (volatile uint32_t *)0x8011730;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_79E70
 * @note 指令数: 43
 * @note 类型: control_function
 */
void ida_79e70(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8011730 = (volatile uint32_t *)0x8011730;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_4C = (volatile uint32_t *)0x4C;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_79ED8
 * @note 指令数: 53
 * @note 类型: lookup_table
 */
uint32_t ida_79ed8(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8011730 = (volatile uint32_t *)0x8011730;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量
    uint32_t result = 0;

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_79F54
 * @note 指令数: 68
 * @note 类型: computation
 */
void ida_79f54(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_48000014 = (volatile uint32_t *)0x48000014;
    volatile uint32_t *addr_300 = (volatile uint32_t *)0x300;
    volatile uint32_t *addr_BF = (volatile uint32_t *)0xBF;
    volatile uint32_t *addr_FF7F = (volatile uint32_t *)0xFF7F;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_79FE2
 * @note 指令数: 71
 * @note 类型: computation
 */
void ida_79fe2(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_48000014 = (volatile uint32_t *)0x48000014;
    volatile uint32_t *addr_BF = (volatile uint32_t *)0xBF;
    volatile uint32_t *addr_FF7F = (volatile uint32_t *)0xFF7F;
    volatile uint32_t *addr_80 = (volatile uint32_t *)0x80;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7A090
 * @note 指令数: 32
 * @note 类型: control_function
 */
void ida_7a090(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_15 = (volatile uint32_t *)0x15;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_5C = (volatile uint32_t *)0x5C;
    volatile uint32_t *addr_75 = (volatile uint32_t *)0x75;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7A0E0
 * @note 指令数: 20
 * @note 类型: computation
 */
uint32_t ida_7a0e0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_7A106
 * @note 指令数: 1
 * @note 类型: simple_function
 */
void ida_7a106(void)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7A108
 * @note 指令数: 10
 * @note 类型: computation
 */
void ida_7a108(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_38000000 = (volatile uint32_t *)0x38000000;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_7A120
 * @note 指令数: 4
 * @note 类型: simple_function
 */
uint32_t ida_7a120(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7A13A
 * @note 指令数: 193
 * @note 类型: computation
 */
void ida_7a13a(void)
{
    // 内存地址定义
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_16 = (volatile uint32_t *)0x16;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7A2BE
 * @note 指令数: 46
 * @note 类型: computation
 */
uint32_t ida_7a2be(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_200000 = (volatile uint32_t *)0x200000;

    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7A320
 * @note 指令数: 86
 * @note 类型: computation
 */
uint32_t ida_7a320(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_7F800000 = (volatile uint32_t *)0x7F800000;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;
    volatile uint32_t *addr_17 = (volatile uint32_t *)0x17;

    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7A3D0
 * @note 指令数: 29
 * @note 类型: computation
 */
uint32_t ida_7a3d0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_7F = (volatile uint32_t *)0x7F;
    volatile uint32_t *addr_80000000 = (volatile uint32_t *)0x80000000;

    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7A40C
 * @note 指令数: 53
 * @note 类型: computation
 */
uint32_t ida_7a40c(void)
{
    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_7A476
 * @note 指令数: 1
 * @note 类型: simple_function
 */
void ida_7a476(void)
{
    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7A478
 * @note 指令数: 22
 * @note 类型: array_access
 */
void ida_7a478(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_20003700 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *addr_20003702 = (volatile uint32_t *)0x20003702;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7A4A4
 * @note 指令数: 40
 * @note 类型: control_function
 */
void ida_7a4a4(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_801239C = (volatile uint32_t *)0x801239C;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7A510
 * @note 指令数: 493
 * @note 类型: array_access
 */
void ida_7a510(void)
{
    // 内存地址定义
    volatile uint32_t *addr_2E = (volatile uint32_t *)0x2E;
    volatile uint32_t *addr_80123A8 = (volatile uint32_t *)0x80123A8;
    volatile uint32_t *addr_40 = (volatile uint32_t *)0x40;
    volatile uint32_t *addr_28 = (volatile uint32_t *)0x28;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7A960
 * @note 指令数: 149
 * @note 类型: array_access
 */
void ida_7a960(void)
{
    // 内存地址定义
    volatile uint32_t *addr_50 = (volatile uint32_t *)0x50;
    volatile uint32_t *addr_20003706 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *addr_80116E0 = (volatile uint32_t *)0x80116E0;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_7B2F8
 * @note 指令数: 53
 * @note 类型: lookup_table
 */
void ida_7b2f8(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_80123CC = (volatile uint32_t *)0x80123CC;
    volatile uint32_t *addr_3C = (volatile uint32_t *)0x3C;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_7B370
 * @note 指令数: 2
 * @note 类型: computation
 */
void ida_7b370(void)
{
    // 内存地址定义
    volatile uint32_t *addr_44 = (volatile uint32_t *)0x44;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7B378
 * @note 指令数: 60
 * @note 类型: array_access
 */
uint32_t ida_7b378(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003706 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *addr_1F4 = (volatile uint32_t *)0x1F4;
    volatile uint32_t *addr_2000374F = (volatile uint32_t *)0x2000374F;
    volatile uint32_t *addr_200033A4 = (volatile uint32_t *)0x200033A4;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7B440
 * @note 指令数: 328
 * @note 类型: array_access
 */
uint32_t ida_7b440(void)
{
    // 内存地址定义
    volatile uint32_t *addr_20000978 = (volatile uint32_t *)0x20000978;
    volatile uint32_t *addr_20003706 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *addr_32 = (volatile uint32_t *)0x32;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7B6D4
 * @note 指令数: 5
 * @note 类型: control_function
 */
uint32_t ida_7b6d4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003538 = (volatile uint32_t *)0x20003538;
    volatile uint32_t *addr_20000130 = (volatile uint32_t *)0x20000130;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7B6F0
 * @note 指令数: 63
 * @note 类型: array_access
 */
uint32_t ida_7b6f0(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003706 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *addr_2000374F = (volatile uint32_t *)0x2000374F;
    volatile uint32_t *addr_200033A4 = (volatile uint32_t *)0x200033A4;
    volatile uint32_t *addr_20003751 = (volatile uint32_t *)0x20003751;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7B774
 * @note 指令数: 34
 * @note 类型: array_access
 */
uint32_t ida_7b774(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20003700 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *addr_20000978 = (volatile uint32_t *)0x20000978;
    volatile uint32_t *addr_20003706 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *addr_20003702 = (volatile uint32_t *)0x20003702;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7B7BA
 * @note 指令数: 34
 * @note 类型: array_access
 */
void ida_7b7ba(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_2000374B = (volatile uint32_t *)0x2000374B;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_200036B4 = (volatile uint32_t *)0x200036B4;
    volatile uint32_t *addr_20003706 = (volatile uint32_t *)0x20003706;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_7B7FE
 * @note 指令数: 9
 * @note 类型: control_function
 */
void ida_7b7fe(void)
{
    // 内存地址定义
    volatile uint32_t *addr_E1 = (volatile uint32_t *)0xE1;
    volatile uint32_t *addr_200033A4 = (volatile uint32_t *)0x200033A4;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7B812
 * @note 指令数: 73
 * @note 类型: array_access
 */
uint32_t ida_7b812(uint16_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20000978 = (volatile uint32_t *)0x20000978;
    volatile uint32_t *addr_20003706 = (volatile uint32_t *)0x20003706;
    volatile uint32_t *addr_30 = (volatile uint32_t *)0x30;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7B8CC
 * @note 指令数: 92
 * @note 类型: array_access
 */
uint32_t ida_7b8cc(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_20003704 = (volatile uint32_t *)0x20003704;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_2C = (volatile uint32_t *)0x2C;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_7B9B4
 * @note 指令数: 157
 * @note 类型: array_access
 */
uint32_t ida_7b9b4(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_20003700 = (volatile uint32_t *)0x20003700;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20003702 = (volatile uint32_t *)0x20003702;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

