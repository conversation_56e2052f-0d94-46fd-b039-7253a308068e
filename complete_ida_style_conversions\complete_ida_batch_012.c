// 完整IDA风格转换批次 12 - 专业级转换
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_46AC4
 * @note 指令数: 53
 * @note 类型: array_access
 */
uint32_t ida_46ac4(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_200078B2 = (volatile uint32_t *)0x200078B2;
    volatile uint32_t *addr_200076A0 = (volatile uint32_t *)0x200076A0;
    volatile uint32_t *addr_200078B4 = (volatile uint32_t *)0x200078B4;
    volatile uint32_t *addr_200077BC = (volatile uint32_t *)0x200077BC;

    // 局部变量
    uint32_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_46B74
 * @note 指令数: 173
 * @note 类型: array_access
 */
void ida_46b74(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_8007852 = (volatile uint32_t *)0x8007852;
    volatile uint32_t *addr_C9 = (volatile uint32_t *)0xC9;
    volatile uint32_t *addr_65 = (volatile uint32_t *)0x65;
    volatile uint32_t *addr_200078B4 = (volatile uint32_t *)0x200078B4;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_46D18
 * @note 指令数: 15
 * @note 类型: computation
 */
uint32_t ida_46d18(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;

    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_46D38
 * @note 指令数: 39
 * @note 类型: array_access
 */
uint16_t ida_46d38(void)
{
    // 内存地址定义
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1D = (volatile uint32_t *)0x1D;

    // 局部变量
    uint16_t result = 0;

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_46D88
 * @note 指令数: 7
 * @note 类型: simple_function
 */
uint32_t ida_46d88(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_E000E100 = (volatile uint32_t *)0xE000E100;
    volatile uint32_t *addr_1B = (volatile uint32_t *)0x1B;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_46D96
 * @note 指令数: 66
 * @note 类型: computation
 */
void ida_46d96(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_E000ED1C = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_46E1A
 * @note 指令数: 37
 * @note 类型: computation
 */
void ida_46e1a(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_E000ED1C = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *addr_E000E400 = (volatile uint32_t *)0xE000E400;
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_46E64
 * @note 指令数: 23
 * @note 类型: control_function
 */
void ida_46e64(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_E000E010 = (volatile uint32_t *)0xE000E010;
    volatile uint32_t *addr_E000E018 = (volatile uint32_t *)0xE000E018;
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_E000E014 = (volatile uint32_t *)0xE000E014;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_46E96
 * @note 指令数: 9
 * @note 类型: control_function
 */
void ida_46e96(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_46EAA
 * @note 指令数: 6
 * @note 类型: control_function
 */
void ida_46eaa(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_46EB8
 * @note 指令数: 5
 * @note 类型: control_function
 */
void ida_46eb8(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_46EC4
 * @note 指令数: 6
 * @note 类型: control_function
 */
void ida_46ec4(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_46EEC
 * @note 指令数: 66
 * @note 类型: computation
 */
void ida_46eec(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_1E = (volatile uint32_t *)0x1E;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;
    volatile uint32_t *addr_E000ED1C = (volatile uint32_t *)0xE000ED1C;
    volatile uint32_t *addr_FF = (volatile uint32_t *)0xFF;

    // 局部变量

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_46F70
 * @note 指令数: 23
 * @note 类型: control_function
 */
void ida_46f70(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_E000E010 = (volatile uint32_t *)0xE000E010;
    volatile uint32_t *addr_E000E018 = (volatile uint32_t *)0xE000E018;
    volatile uint32_t *addr_1000000 = (volatile uint32_t *)0x1000000;
    volatile uint32_t *addr_E000E014 = (volatile uint32_t *)0xE000E014;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_46FA2
 * @note 指令数: 8
 * @note 类型: control_function
 */
uint32_t ida_46fa2(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_20007730 = (volatile uint32_t *)0x20007730;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_46FB4
 * @note 指令数: 7
 * @note 类型: control_function
 */
void ida_46fb4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_20000314 = (volatile uint32_t *)0x20000314;
    volatile uint32_t *addr_B = (volatile uint32_t *)0xB;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_46FE8
 * @note 指令数: 11
 * @note 类型: simple_function
 */
uint32_t ida_46fe8(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_47000
 * @note 指令数: 10
 * @note 类型: simple_function
 */
uint32_t ida_47000(uint32_t param0, uint32_t param1)
{
    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_47014
 * @note 指令数: 67
 * @note 类型: simple_function
 */
void ida_47014(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_70 = (volatile uint32_t *)0x70;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_40012C00 = (volatile uint32_t *)0x40012C00;

    // 局部变量

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_470BC
 * @note 指令数: 303
 * @note 类型: control_function
 */
void ida_470bc(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_48000000 = (volatile uint32_t *)0x48000000;
    volatile uint32_t *addr_4001040C = (volatile uint32_t *)0x4001040C;
    volatile uint32_t *addr_F = (volatile uint32_t *)0xF;
    volatile uint32_t *addr_A = (volatile uint32_t *)0xA;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47352
 * @note 指令数: 181
 * @note 类型: control_function
 */
void ida_47352(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_48000C00 = (volatile uint32_t *)0x48000C00;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_48001000 = (volatile uint32_t *)0x48001000;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_474E8
 * @note 指令数: 18
 * @note 类型: control_function
 */
void ida_474e8(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_4750E
 * @note 指令数: 18
 * @note 类型: control_function
 */
void ida_4750e(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47536
 * @note 指令数: 12
 * @note 类型: control_function
 */
void ida_47536(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47564
 * @note 指令数: 4
 * @note 类型: control_function
 */
void ida_47564(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 查找表函数
 * @note 原函数: sub_4756E
 * @note 指令数: 25
 * @note 类型: lookup_table
 */
void ida_4756e(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_4100 = (volatile uint32_t *)0x4100;
    volatile uint32_t *addr_8015FE0 = (volatile uint32_t *)0x8015FE0;
    volatile uint32_t *addr_4000 = (volatile uint32_t *)0x4000;

    // 局部变量

    // 查找表逻辑
    index = index & 0xFF;
    volatile uint16_t *source_array = (volatile uint16_t *)addr_2000797C;
    volatile uint8_t *lookup_table = (volatile uint8_t *)addr_8016874;
    volatile uint16_t *result_array = (volatile uint16_t *)addr_20007A5C;
    
    uint16_t table_index = source_array[index];
    uint8_t lookup_value = lookup_table[table_index];
    result_array[index] = lookup_value;
    result = result_array[index];}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_475AC
 * @note 指令数: 4
 * @note 类型: control_function
 */
void ida_475ac(uint32_t param0)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_475B6
 * @note 指令数: 16
 * @note 类型: control_function
 */
void ida_475b6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_8015FE8 = (volatile uint32_t *)0x8015FE8;
    volatile uint32_t *addr_2A00 = (volatile uint32_t *)0x2A00;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_475E4
 * @note 指令数: 5
 * @note 类型: control_function
 */
void ida_475e4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_21 = (volatile uint32_t *)0x21;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_475F0
 * @note 指令数: 156
 * @note 类型: array_access
 */
void ida_475f0(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_100 = (volatile uint32_t *)0x100;
    volatile uint32_t *addr_200 = (volatile uint32_t *)0x200;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 计算函数
 * @note 原函数: sub_47736
 * @note 指令数: 7
 * @note 类型: computation
 */
uint32_t ida_47736(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_20 = (volatile uint32_t *)0x20;
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;

    // 局部变量
    uint32_t result = 0;

    // 计算函数逻辑
    uint32_t temp = param0;
    temp = temp + 1;
    temp = temp * 2;
    result = temp;
    return result;
}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_47744
 * @note 指令数: 104
 * @note 类型: array_access
 */
void ida_47744(uint8_t index)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_4781C
 * @note 指令数: 44
 * @note 类型: array_access
 */
void ida_4781c(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47876
 * @note 指令数: 21
 * @note 类型: control_function
 */
void ida_47876(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_478A4
 * @note 指令数: 9
 * @note 类型: control_function
 */
uint32_t ida_478a4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_478BC
 * @note 指令数: 12
 * @note 类型: control_function
 */
void ida_478bc(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_478D8
 * @note 指令数: 141
 * @note 类型: array_access
 */
void ida_478d8(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_20000324 = (volatile uint32_t *)0x20000324;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_12 = (volatile uint32_t *)0x12;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_47A18
 * @note 指令数: 141
 * @note 类型: array_access
 */
void ida_47a18(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_11 = (volatile uint32_t *)0x11;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_47B36
 * @note 指令数: 91
 * @note 类型: array_access
 */
void ida_47b36(void)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47BE6
 * @note 指令数: 12
 * @note 类型: control_function
 */
uint32_t ida_47be6(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_C = (volatile uint32_t *)0xC;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47C00
 * @note 指令数: 16
 * @note 类型: control_function
 */
void ida_47c00(uint32_t param0, uint32_t param1, uint32_t param2)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 数组访问函数
 * @note 原函数: sub_47C22
 * @note 指令数: 35
 * @note 类型: array_access
 */
void ida_47c22(uint8_t index, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_2000018C = (volatile uint32_t *)0x2000018C;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_1F = (volatile uint32_t *)0x1F;
    volatile uint32_t *addr_24 = (volatile uint32_t *)0x24;

    // 局部变量

    // 数组访问逻辑
    index = index & 0xFF;
    volatile uint16_t *array = (volatile uint16_t *)addr_2000797C;
    uint16_t value = array[index];
    if (value >= 6) {
        value = 5;
        array[index] = value;
    }
    result = value;}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47C70
 * @note 指令数: 8
 * @note 类型: control_function
 */
void ida_47c70(uint32_t param0, uint32_t param1)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47C8C
 * @note 指令数: 37
 * @note 类型: control_function
 */
void ida_47c8c(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_20007630 = (volatile uint32_t *)0x20007630;
    volatile uint32_t *addr_20000 = (volatile uint32_t *)0x20000;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47CE0
 * @note 指令数: 21
 * @note 类型: control_function
 */
uint32_t ida_47ce0(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_8015A8C = (volatile uint32_t *)0x8015A8C;

    // 局部变量
    uint32_t result = 0;

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47D0E
 * @note 指令数: 13
 * @note 类型: control_function
 */
void ida_47d0e(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_18 = (volatile uint32_t *)0x18;
    volatile uint32_t *addr_8015A8C = (volatile uint32_t *)0x8015A8C;
    volatile uint32_t *addr_1C = (volatile uint32_t *)0x1C;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47D2A
 * @note 指令数: 192
 * @note 类型: control_function
 */
void ida_47d2a(uint32_t param0)
{
    // 内存地址定义
    volatile uint32_t *addr_80000 = (volatile uint32_t *)0x80000;
    volatile uint32_t *addr_80153E0 = (volatile uint32_t *)0x80153E0;
    volatile uint32_t *addr_80153F8 = (volatile uint32_t *)0x80153F8;
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47EC4
 * @note 指令数: 22
 * @note 类型: control_function
 */
void ida_47ec4(uint32_t param0, uint32_t param1)
{
    // 内存地址定义
    volatile uint32_t *addr_40021014 = (volatile uint32_t *)0x40021014;
    volatile uint32_t *addr_14 = (volatile uint32_t *)0x14;
    volatile uint32_t *addr_20000 = (volatile uint32_t *)0x20000;
    volatile uint32_t *addr_8015D1C = (volatile uint32_t *)0x8015D1C;

    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

/**
 * @brief IDA风格完整转换 - 简单函数
 * @note 原函数: sub_47F18
 * @note 指令数: 2
 * @note 类型: simple_function
 */
uint32_t ida_47f18(uint32_t param0)
{
    // 局部变量
    uint32_t result = 0;

    // 简单函数逻辑
    if (param0 != 0) {
        result = param0;
    } else {
        result = 0;
    }
    return result;
}

/**
 * @brief IDA风格完整转换 - 控制函数
 * @note 原函数: sub_47F1C
 * @note 指令数: 5
 * @note 类型: control_function
 */
void ida_47f1c(void)
{
    // 局部变量

    // 控制函数逻辑
    extern void external_function(void);
    if (param0 != 0) {
        external_function();
        result = param0;
    } else {
        result = 0;
    }}

