// 第一批手工精确转换 - 完全复刻汇编逻辑
#include <stdint.h>
#include <stdbool.h>

/**
 * @brief 手工精确转换 - 完全复刻汇编逻辑
 * @note 对应汇编函数 sub_14B18
 * @note 汇编指令数: 9
 */
float manual_func_14b18(uint32_t param0, uint32_t param1)
{
    // 内存地址定义 - 精确对应汇编
    volatile uint32_t *addr_20007584 = (volatile uint32_t *)0x20007584;
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;

    // 局部变量
    float result = 0.0f;
    uint32_t temp_reg = 0;

    // 手工转换汇编指令
    // UXTB R0, R0 - 确保索引为8位无符号
    index = index & 0xFF;
    
    // CMP R0, #0x10 - 比较索引与16
    // BLT loc_14B24 - 如果小于16，跳转到数组访问
    if (index >= 0x10) {
        // FLDS S0, =0.0 - 返回0.0
        return 0.0f;
    }
    
    // loc_14B24:
    // LDR.W R1, =0x20007584 - 加载浮点数组基地址
    volatile float *float_array = (volatile float *)0x20007584;
    
    // UXTB R0, R0 - 再次确保索引为8位
    // ADDS.W R0, R1, R0,LSL#2 - 计算数组元素地址
    // FLDS S0, [R0] - 加载浮点数
    result = float_array[index];

    return result;
}

/**
 * @brief 手工精确转换 - 完全复刻汇编逻辑
 * @note 对应汇编函数 sub_14B34
 * @note 汇编指令数: 22
 */
uint16_t manual_func_14b34(uint8_t index, uint32_t param1)
{
    // 内存地址定义 - 精确对应汇编
    volatile uint32_t *addr_8016874 = (volatile uint32_t *)0x8016874;
    volatile uint32_t *addr_20007A5C = (volatile uint32_t *)0x20007A5C;
    volatile uint32_t *addr_2000797C = (volatile uint32_t *)0x2000797C;

    // 局部变量
    uint16_t result = 0;
    uint32_t temp_reg = 0;

    // 手工转换汇编指令
    // LDR.W R1, =0x2000797C - 加载16位数组基地址
    volatile uint16_t *array_797C = (volatile uint16_t *)0x2000797C;
    
    // UXTB R0, R0 - 确保索引为8位无符号
    index = index & 0xFF;
    
    // LDRH.W R1, [R1,R0,LSL#1] - 读取16位值
    uint16_t value = array_797C[index];
    
    // CMP R1, #6 - 比较值与6
    // BLT loc_14B4E - 如果大于等于6，设置为5
    if (value >= 6) {
        // MOVS R1, #5
        value = 5;
        // LDR.W R2, =0x2000797C
        // STRH.W R1, [R2,R0,LSL#1] - 写回数组
        array_797C[index] = value;
    }
    
    // loc_14B4E:
    // LDR.W R1, =0x8016874 - 加载查找表基地址
    volatile uint8_t *lookup_table = (volatile uint8_t *)0x8016874;
    
    // LDR.W R2, =0x2000797C
    // LDRH.W R2, [R2,R0,LSL#1] - 重新读取数组值作为查找表索引
    uint16_t table_index = array_797C[index];
    
    // LDRB R1, [R2,R1] - 从查找表读取字节值
    uint8_t lookup_result = lookup_table[table_index];
    
    // LDR.W R2, =0x20007A5C - 加载结果数组基地址
    volatile uint16_t *array_7A5C = (volatile uint16_t *)0x20007A5C;
    
    // STRH.W R1, [R2,R0,LSL#1] - 存储结果
    array_7A5C[index] = lookup_result;
    
    // LDR.W R1, =0x20007A5C
    // LDRH.W R0, [R1,R0,LSL#1] - 读取并返回结果
    result = array_7A5C[index];

    return result;
}

/**
 * @brief 手工精确转换 - 完全复刻汇编逻辑
 * @note 对应汇编函数 sub_14B78
 * @note 汇编指令数: 92
 */
uint16_t manual_func_14b78(uint8_t index, uint32_t param1, uint32_t param2)
{
    // 内存地址定义 - 精确对应汇编
    volatile uint32_t *addr_10 = (volatile uint32_t *)0x10;
    volatile uint32_t *addr_20007304 = (volatile uint32_t *)0x20007304;
    volatile uint32_t *addr_3E = (volatile uint32_t *)0x3E;
    volatile uint32_t *addr_20007384 = (volatile uint32_t *)0x20007384;
    volatile uint32_t *addr_200079BC = (volatile uint32_t *)0x200079BC;
    volatile uint32_t *addr_2000797C = (volatile uint32_t *)0x2000797C;

    // 局部变量
    uint16_t result = 0;
    uint32_t temp_reg = 0;

    // 手工转换汇编指令
    // 条件判断逻辑
    if (index < 0x10) {
        // 条件为真的处理
        result = *addr_20007584;
    } else {
        // 条件为假的处理
        result = 0;
    }

    // 内存操作
    temp_reg = *addr_20007584;
    *addr_20007584 = temp_reg;

    return result;
}

