# AT32F403AVG ASM to C Conversion

## 概述

本项目将原始的AT32F403AVG汇编代码完全转换为C语言实现。转换保持了原始功能和内存布局，确保与原始ASM代码的兼容性。

## 文件结构

### 转换后的C源文件

```
src/
├── at32f403avg_firmware.h      # 主头文件，包含所有定义和声明
├── at32f403avg_firmware.c      # 主实现文件，包含所有核心函数
├── logo_data.c                 # Logo图像数据（从ASM提取）
├── startup_at32f403avg.c       # 启动代码和中断向量表
└── at32f403avg.ld             # 链接脚本（内存布局）
```

### 构建文件

```
Makefile                        # 构建配置
README_ASM_TO_C_CONVERSION.md   # 本文档
```

### 原始文件

```
keil/AT32F403AVG-FLASH-J201.asm # 原始汇编文件
bin/mac.png                     # Logo图像文件
```

## 转换详情

### 1. 内存布局转换

**原始ASM内存布局：**
- Boot loader: `0x08000000` - `0x08001FFF` (8KB)
- Application: `0x08002000` - `0x080FFFFF` (1016KB)  
- MAC Address: `0x08001810`
- RAM: `0x20000000` - `0x20017FFF` (96KB)
- 初始栈指针: `0x20000618`

**C版本内存布局：**
- 在链接脚本 `at32f403avg.ld` 中完全保持相同的内存布局
- 使用MEMORY指令定义各个内存区域
- 保持原始的栈指针位置和大小

### 2. 中断向量表转换

**原始ASM：**
```asm
DCD 0x20000618  ; Initial SP
DCD Reset_Handler
DCD NMI_Handler
; ... 更多中断向量
```

**C版本：**
```c
const vector_table_t vector_table = {
    .initial_sp = 0x20000618,
    .reset_handler = reset_handler,
    .nmi_handler = nmi_handler,
    // ... 完整的中断向量表
};
```

### 3. 核心函数转换

#### GPIO配置函数 (sub_8000240)
```c
void gpio_config(int8_t pin, uint8_t mode) {
    // 完全保持原始ASM逻辑
    // 负数引脚使用GPIOB，正数使用GPIOA
}
```

#### 延时函数 (sub_800026A)
```c
uint32_t delay_ms(uint32_t ms) {
    // 使用SysTick定时器实现延时
    // 保持原始的参数检查和配置
}
```

#### CRC计算函数 (sub_80002BA)
```c
uint16_t calculate_crc(const uint8_t* data, uint16_t length) {
    // 完全保持原始的CRC算法
    // 包括多项式0x1021和所有位操作
}
```

#### 系统定时器中断 (sub_8000308)
```c
void systick_handler(void) {
    // 保持原始的定时器处理逻辑
    // 包括UART状态检查和GPIO切换
}
```

### 4. 数据结构转换

#### 全局变量映射
```c
// 原始ASM地址 -> C变量
#define SYSTEM_TICK_ADDR        0x2000000A  // uint16_t
#define SYSTEM_TIME_MS_ADDR     0x20000004  // uint32_t
#define UART_MODE_FLAG_ADDR     0x20000011  // uint8_t
// ... 更多变量映射
```

#### 寄存器访问宏
```c
#define REG32(addr)             (*(volatile uint32_t*)(addr))
#define REG16(addr)             (*(volatile uint16_t*)(addr))
#define REG8(addr)              (*(volatile uint8_t*)(addr))
```

### 5. Logo数据转换

**原始ASM (第40137-40144行)：**
```asm
DCD 0x646F0E60,0x3733366A,0x584B0000,0x36312D4D,0xE3010050,0xF1120834,0x2212011F,0x45055
; ... 更多数据
```

**C版本：**
```c
const uint32_t logo_data[] = {
    0x646F0E60, 0x3733366A, 0x584B0000, 0x36312D4D, 
    0xE3010050, 0xF1120834, 0x2212011F, 0x45055,
    // ... 完整的logo数据数组
};
```

### 6. 启动序列转换

**原始ASM启动：**
- 设置栈指针
- 初始化RAM
- 跳转到主程序

**C版本启动：**
```c
void reset_handler(void) {
    init_ram_sections();    // 初始化RAM段
    system_init();          // 系统初始化
    bootloader_main();      // 跳转到主程序
}
```

## 构建说明

### 前提条件

1. ARM GCC工具链 (`arm-none-eabi-gcc`)
2. Make工具
3. OpenOCD或其他烧录工具（可选）

### 构建命令

```bash
# 编译所有文件
make all

# 清理构建文件
make clean

# 显示大小信息
make size

# 生成反汇编
make disasm

# 显示帮助
make help
```

### 输出文件

- `build/at32f403avg_firmware.elf` - ELF可执行文件
- `build/at32f403avg_firmware.hex` - Intel HEX格式
- `build/at32f403avg_firmware.bin` - 二进制格式
- `build/at32f403avg_firmware.map` - 内存映射文件

## 功能验证

### 1. 内存布局验证
- 检查链接器映射文件确认内存布局正确
- 验证栈指针和各段地址与原始ASM一致

### 2. 功能验证
- 验证GPIO配置功能
- 测试UART通信
- 检查定时器中断处理
- 验证CRC计算结果

### 3. 代码大小比较
- 比较C版本与原始ASM的代码大小
- 确保关键功能的性能没有显著下降

## 注意事项

1. **寄存器访问**：所有硬件寄存器访问都使用volatile指针，确保编译器不会优化掉关键的硬件操作。

2. **中断处理**：所有中断处理函数都保持与原始ASM相同的行为，包括寄存器保存和恢复。

3. **内存对齐**：所有数据结构都保持适当的内存对齐，确保硬件访问的正确性。

4. **编译优化**：使用`-Os`优化级别平衡代码大小和性能。

5. **调试信息**：包含完整的调试信息(`-g3`)以便调试。

## 已知限制

1. 某些特定的汇编优化可能在C版本中无法完全复现
2. 中断延迟可能略有不同
3. 代码大小可能略大于原始ASM版本

## 后续改进

1. 添加单元测试验证各个函数的正确性
2. 优化关键路径的性能
3. 添加更详细的错误处理
4. 改进代码文档和注释
